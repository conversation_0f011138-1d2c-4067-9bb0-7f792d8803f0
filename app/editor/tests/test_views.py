from app.auth.types import User
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.drafts.tests.utils import (
    prepare_draft_from_template,
    prepare_draft_from_version,
    prepare_draft_standalone,
)
from app.editor.enums import EditSessionType
from app.editor.tests.utils import get_initiate_edit_session_draft_url
from app.lib.datetime_utils import utc_now
from app.templates.tests.utils import prepare_template
from app.tests.common import (
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
)

TEST_UUID_1 = '14b7066b-cd16-4af4-88c5-58285c6c8644'
TEST_UUID_2 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
TEST_UUID_3 = '57fabbd0-f085-4c11-890b-74a5892272a6'
TEST_UUID_4 = '2c705883-70ad-4ff2-833f-d72bd06d4ee7'


async def test_initiate_edit_draft_from_version(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )
    draft = await prepare_draft_from_version(
        version_id=version.id,
        role_id=user.role_id,
    )

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': draft.id,
            'type': EditSessionType.draft,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{draft.id}'
    )


async def test_initiate_edit_draft_from_template(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    template = await prepare_template(User.from_row(user))
    draft = await prepare_draft_from_template(
        template=template,
        user=User.from_row(user),
    )

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': draft.id,
            'type': EditSessionType.draft,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{draft.id}'
    )


async def test_initiate_edit_draft_standalone(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    draft = await prepare_draft_standalone(user=User.from_row(user))

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': draft.id,
            'type': EditSessionType.draft,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{draft.id}'
    )


async def test_initiate_edit_template(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    template = await prepare_template(User.from_row(user))

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': template.id,
            'type': EditSessionType.template,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{template.id}'
    )


async def test_initiate_viewer_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user)

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': document.id,
            'type': EditSessionType.viewer_document,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{document.id}'
    )


async def test_initiate_viewer_version(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user)
    version = await prepare_document_version(
        id=TEST_UUID_1,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        content=b'version-1',
    )

    response = await client.post(
        get_initiate_edit_session_draft_url(),
        json={
            'entity_id': version.id,
            'type': EditSessionType.viewer_version,
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    data = await response.json()
    assert 'access_token' in data
    assert (
        data['wopi_url']
        == f'https://localhost/collabora/browser/55317ef/cool.html?WOPISrc=http://web:8000/editor/session/{version.id}'
    )
