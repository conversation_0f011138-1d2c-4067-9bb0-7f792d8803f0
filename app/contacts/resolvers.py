from hiku.engine import Context, pass_context

from api.graph.low_level import types as low_level_types
from api.graph.utils import get_graph_user
from app.contacts import utils as contacts
from app.contacts.types import ContactRecipient
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.types import DataMapping


@pass_context
async def resolve_contacts(
    ctx: Context,
    options: DataMapping,
) -> low_level_types.ContactsList:
    """Resolve a list of contacts that should be shown to user."""

    user = get_graph_user(ctx)
    if not user:
        return low_level_types.ContactsList(
            count=0,
            contact_ids=(),
        )

    use_es: bool = get_flag(FeatureFlags.ENABLE_CONTACT_ES_SEARCH)

    return await contacts.get_contacts_for_search(
        graph_ctx=ctx,
        options=options,
        user=user,
        use_es=use_es,
    )


@pass_context
async def resolve_contact_recipients(
    ctx: Context,
    options: DataMapping,
) -> list[ContactRecipient]:
    """
    Resolve a list of recipients that should be shown to user when he/she is filling a document
    """

    user = get_graph_user(ctx)
    if not user:
        return []

    search_query: str | None = options.get('search')
    limit: int = options['limit']
    offset: int = options['offset']
    use_es: bool = not get_flag(FeatureFlags.DISABLE_CONTACT_RECIPIENT_ES_SEARCH)
    if not search_query:
        return []

    return await contacts.get_contact_recipients_for_search(
        graph_ctx=ctx,
        company_id=user.company_id,
        search_query=search_query,
        limit=limit,
        offset=offset,
        use_es=use_es,
    )


@pass_context
async def resolve_contact_persons(
    ctx: Context,
    options: DataMapping,
) -> low_level_types.ContactPersonsList:
    """Resolve a list of contact persons that should be shown to user."""

    user = get_graph_user(ctx)
    if not user:
        return low_level_types.ContactPersonsList(count=0, contact_person_ids=())

    use_es: bool = get_flag(FeatureFlags.ENABLE_CONTACT_PERSON_ES_SEARCH)

    return await contacts.get_contact_persons_for_search(
        graph_ctx=ctx,
        options=options,
        user=user,
        use_es=use_es,
    )
