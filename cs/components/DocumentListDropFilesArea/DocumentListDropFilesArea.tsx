import React from 'react';
import { useSelector } from 'react-redux';

import { SvgBorder } from '@vchasno/ui-kit';

import cn from 'classnames';
import DocumentListDropFilesBanner from 'components/DocumentListDropFilesArea/DocumentListDropFilesBanner';
import { useDocumentsDNDBanner } from 'components/DocumentListDropFilesArea/useDocumentsDNDBanner';
import { useUploadActions } from 'components/uploader/useUploadActions';
import { useDocumentProcessesContext } from 'contexts/documentProcesses';
import { useDocumentUploadDropzone } from 'hooks/useDocumentUploadDropzone';
import { getCurrentUserRole } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';

import css from './DocumentListDropFilesArea.css';

export interface DocumentListDropFilesAreaProps {
    className?: string;
}

const DocumentListDropFilesArea: React.FC<DocumentListDropFilesAreaProps> = ({
    className,
    children,
}) => {
    const { isNewUpload } = useDocumentProcessesContext();
    const [isBannerClosed, setBannerClose] = useDocumentsDNDBanner();
    const uploadActions = useUploadActions();
    const rolePermission = useSelector(getCurrentUserRole);
    const {
        getRootProps,
        getInputProps,
        isDragActive,
    } = useDocumentUploadDropzone({
        onDrop: (acceptedFiles) => {
            eventTracking.sendToGTM({
                event: 'doc_upload_add',
                category: 'drag_and_drop',
            });
            uploadActions.setSelectedFilesAC(acceptedFiles);
            uploadActions.onShowPopup('documents-drop-area');
        },
        noClick: true,
        noKeyboard: true,
        disabled: !isNewUpload,
    });

    if (!isNewUpload) {
        return <div className={className}>{children}</div>;
    }

    if (!rolePermission.isAdmin && !rolePermission.canUploadDocument) {
        return <div className={className}>{children}</div>;
    }

    if (!isBannerClosed) {
        return (
            <>
                <div className={css.bannerContainer}>
                    <DocumentListDropFilesBanner
                        onClose={() => {
                            setBannerClose(true);
                        }}
                    />
                </div>
                <div className={className}>{children}</div>
            </>
        );
    }

    return (
        <div
            {...getRootProps()}
            className={cn(
                css.root,
                {
                    [css.dropBoxActive]: isDragActive,
                },
                className,
            )}
        >
            <SvgBorder
                color={
                    isDragActive ? 'var(--link-color)' : 'var(--default-border)'
                }
                animation="border-offset"
                animationDurationSec={1.2}
                dashoffset={16}
                animationPlay={isDragActive}
                className={css.dropAreaMask}
            />
            <input {...getInputProps()} />
            {children}
        </div>
    );
};

export default DocumentListDropFilesArea;
