.badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    background-color: var(--light-blue-bg);
    border-radius: var(--border-radius);
    font-size: 14px;
    gap: 10px;
}

.notAllowed {
    text-decoration: line-through;
}

.badgeIcon {
    display: flex;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    color: var(--grey-color);
    transition: color 0.3s, background-color 0.3s;
}

.deleteIcon:hover {
    background-color: var(--grey-color);
    color: #fff;
    cursor: pointer;
}

.badgeTitle {
    flex-grow: 1;
}
