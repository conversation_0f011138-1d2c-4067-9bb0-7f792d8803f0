import React from 'react';

import cn from 'classnames';
import ScrollIntoView from 'components/ScrollIntoView';
import Icon from 'ui/icon';

import ArrowDownSVG from '../../icons/arrow-down.svg';
import CheckedSVG from '../../icons/checked.svg';

import css from './CollapsedBar.css';

export interface CollapsedBarProps {
    isShow?: boolean;
    onToggle?: (isShow: boolean) => void;
    title: React.ReactNode | string;
    children: React.ReactNode;
    className?: string;
    subTitle?: string;
    initialIsShow?: boolean;
    topDivider?: boolean;
    bottomDivider?: boolean;
    doneIcon?: boolean;
}

const CollapsedBar: React.FC<CollapsedBarProps> = ({
    className,
    subTitle,
    title,
    children,
    topDivider = false,
    bottomDivider = false,
    doneIcon = false,
    isShow,
    onToggle,
}) => {
    const [isShowState, setIsShowState] = React.useState(isShow ?? false);

    React.useEffect(() => {
        if (isShow !== undefined) {
            setIsShowState(isShow);
        }
    }, [isShow]);

    const toggleState = () => {
        if (onToggle === undefined) {
            setIsShowState(!isShowState);
            return;
        }

        onToggle(!isShowState);
    };

    return (
        <section
            onClick={!isShowState ? toggleState : undefined}
            className={cn(css.root, className, {
                [css.isShow]: isShowState,
                [css.topDivider]: topDivider,
                [css.bottomDivider]: bottomDivider,
            })}
        >
            <header
                className={css.bar}
                onClick={isShowState ? toggleState : undefined}
            >
                <div className={css.infoBox}>
                    <h3 className={css.title}>
                        {title}
                        {doneIcon && (
                            <span className={css.checkedIcon}>
                                <Icon glyph={CheckedSVG} />
                            </span>
                        )}
                    </h3>
                    {subTitle && (
                        <span className={css.subTitle}>{subTitle}</span>
                    )}
                </div>
                <div className={css.iconBox}>
                    <Icon glyph={ArrowDownSVG} />
                </div>
            </header>
            {isShowState && (
                <ScrollIntoView className={css.content}>
                    {children}
                </ScrollIntoView>
            )}
        </section>
    );
};

export default CollapsedBar;
