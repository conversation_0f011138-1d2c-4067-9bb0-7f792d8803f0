import { useQuery } from '@tanstack/react-query';

import { GET_COLLABORA_DOCUMENT_VIEWER_DATA } from 'lib/queriesConstants';
import { getDocumentEditData } from 'services/documents/ts/api';

interface CollaboraOptions {
    docId: string;
    versionId?: string;
    draftId?: string;
    enabled?: boolean;
}

export const useCollaboraQuery = ({
    enabled = false,
    docId,
    draftId,
    versionId,
}: CollaboraOptions) => {
    return useQuery({
        queryFn: () =>
            getDocumentEditData({
                type: draftId
                    ? 'draft'
                    : versionId
                    ? 'viewer_version'
                    : 'viewer_document',
                entityId: draftId || versionId || docId,
                canEdit: false,
            }),
        enabled,
        queryKey: [
            GET_COLLABORA_DOCUMENT_VIEWER_DATA,
            { docId, versionId, draftId },
        ],
        staleTime: Infinity, // need to change when token will be expired
        select: (data) =>
            ({
                url: data.wopi_url,
                token: data.access_token,
            } as const),
    });
};
