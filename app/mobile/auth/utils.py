import logging

from aiohttp import web

from app.auth import two_factor
from app.auth import types as auth_types
from app.auth import utils as auth_utils
from app.auth.enums import AuthFactor
from app.auth.two_factor import send_2fa_code_mobile
from app.events import utils as events_utils
from app.events.models import LoginEvent
from app.events.user_actions import utils as user_actions_utils
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib import helpers
from app.lib.database import DBConnection
from app.mobile import constants as constants
from app.mobile.auth import db as mobile_auth_db
from app.mobile.auth import types as mobile_auth_types
from app.registration import emailing as registration_emailing
from app.registration import types as registration_types
from app.registration import utils as registration_utils
from app.services import services

logger = logging.getLogger(__name__)


def _get_redis_key(token_hash: str) -> str:
    """
    Get redis key for mobile auth token.
    """
    return f'{constants.MOBILE_REDIS_PREFIX}:{token_hash}'


async def save_access_token(
    *,
    user_id: str,
    access_token_hash: str,
    refresh_token_id: str,
    is_2fa_required: bool,
    expires_in: int,
) -> None:
    """
    Save access token to redis.
    """
    access_token = mobile_auth_types.MobileAuthAccessToken(
        user_id=user_id,
        auth_refresh_token_id=refresh_token_id,
        token_hash=access_token_hash,
        status=mobile_auth_types.MobileAuthAccessTokenStatus.pending
        if is_2fa_required
        else mobile_auth_types.MobileAuthAccessTokenStatus.ok,
    )

    await services.redis.setex(
        _get_redis_key(token_hash=access_token_hash),
        value=access_token.to_redis(),
        time=expires_in,
    )


async def get_access_token(value: str) -> mobile_auth_types.MobileAuthAccessToken | None:
    """
    Try to get valid access token by token string.
    """

    token_hash = helpers.str_to_hash(value)
    auth_token = await services.redis.get(_get_redis_key(token_hash=token_hash))
    if not auth_token:
        return None

    return mobile_auth_types.MobileAuthAccessToken.from_redis(auth_token)


async def process_mobile_login(
    conn: DBConnection, ctx: mobile_auth_types.MobileLoginContext
) -> None:
    """
    Save token to redis and send 2fa if required.
    """

    # save tokens
    refresh_token = await mobile_auth_db.insert_mobile_auth_refresh_token(
        conn=conn,
        user_id=ctx.user.id,
        token_hash=ctx.refresh_token.token_hash,
        access_token_hash=ctx.access_token.token_hash,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.pending
        if ctx.is_2fa_required
        else mobile_auth_types.MobileAuthRefreshTokenStatus.ready,
    )
    await save_access_token(
        user_id=ctx.user.id,
        access_token_hash=ctx.access_token.token_hash,
        refresh_token_id=refresh_token.id,
        is_2fa_required=ctx.is_2fa_required,
        expires_in=ctx.expires_in,
    )
    await auth_utils.update_user_is_logged_once(conn, user=ctx.user)

    # send code
    if ctx.is_2fa_required:
        await send_2fa_code_mobile(
            user_email=ctx.user.email,
            user_id=ctx.user.id,
            user_phone=ctx.user.phone,
            first_factor=AuthFactor.email,
        )


async def process_mobile_token_refresh(
    conn: DBConnection, ctx: mobile_auth_types.RefreshCtx
) -> None:
    """
    Remove old token, save new token.
    """

    # remove old token
    await services.redis.delete(
        _get_redis_key(token_hash=ctx.old_access_token_hash),
    )

    # save new token
    await save_access_token(
        user_id=ctx.user_id,
        access_token_hash=ctx.access_token.token_hash,
        refresh_token_id=ctx.auth_refresh_token_id,
        is_2fa_required=False,
        expires_in=ctx.expires_in,
    )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        id=ctx.auth_refresh_token_id,
        # mark as verified if needed on 2fa verified
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.ready
        if ctx.mark_as_verified
        else None,
        # update access token hash to currently used
        access_token_hash=ctx.access_token.token_hash,
    )


async def process_mobile_logout_all(conn: DBConnection, user_id: str) -> int:
    """
    Remove all access tokens and change their status to `cancelled`.
    """

    tokens = await mobile_auth_db.select_mobile_auth_refresh_tokens(
        conn=conn,
        user_id=user_id,
    )
    for token in tokens:
        await services.redis.delete(
            _get_redis_key(token_hash=token.access_token_hash),
        )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        where_user_id=user_id,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.canceled,
    )

    return len(tokens)


async def process_mobile_logout(conn: DBConnection, ctx: mobile_auth_types.LogoutCtx) -> None:
    """
    Remove current access token and deprecate refresh token.
    """

    await services.redis.delete(
        _get_redis_key(token_hash=ctx.refresh_token.access_token_hash),
    )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        id=ctx.refresh_token.id,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.canceled,
    )


async def process_verify_2fa(conn: DBConnection, ctx: mobile_auth_types.RefreshCtx) -> None:
    """
    Remove 2fa code and refresh access token.
    """

    await two_factor.reset_2fa_otp_mobile(user_id=ctx.user_id, email=ctx.user_email)
    await process_mobile_token_refresh(conn, ctx)


async def mobile_login(
    conn: DBConnection, request: web.Request, user: auth_types.BaseUser
) -> mobile_auth_types.MobileLoginContext:
    """
    Login to the mobile app, return access token context.
    """
    from app.mobile.auth.validators import validate_mobile_login

    ctx = await validate_mobile_login(
        conn=conn,
        user=user,
    )

    await process_mobile_login(conn, ctx)

    if get_flag(FeatureFlags.ENABLE_ROLES_SYNC_ON_LOGIN):
        await auth_utils.try_to_syncronously_sync_user_roles(
            request_source=user_actions_utils.get_event_source(request),
            user=user,
        )

    login_event = await LoginEvent.make(
        user=ctx.user,
        source=events_utils.get_source(request),
    )
    events_utils.register_event(login_event, request)

    return ctx


async def mobile_registration(
    conn: DBConnection,
    request: web.Request,
    ctx: registration_types.RegistrationCtx,
) -> mobile_auth_types.MobileLoginContext:
    """
    Perform the registration process from mobile app for a new user by email and password.

    1. Create user from given registration context.
    2. Login user and store the mobile auth & refresh token.
    3. Send registration jobs and email confirmation request.
    """

    # Get the ID of the dealer who referred the user
    referrer_id = await registration_utils.get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ctx.referrer,
    )
    user = await registration_utils.create_user_on_registration(
        conn=conn,
        email=ctx.email,
        password=ctx.password,
        phone=None,
        first_name=None,
        second_name=None,
        last_name=None,
        promo_code=ctx.promo_code,
        trial_auto_enable=ctx.trial_auto_enable,
        pending_referrer_role_id=referrer_id,
        source=ctx.source,
        is_email_confirmed=False,
        is_registration_completed=False,
        registration_method=ctx.registration_method,
    )

    login_ctx = await mobile_login(conn=conn, request=request, user=user)

    # Send any necessary jobs related to the registration process
    await registration_utils.send_registration_jobs(user=user, cookies=request.cookies)

    await registration_emailing.send_confirmation_email(
        email=ctx.email,
        redirect_url=ctx.redirect_url,
    )

    return login_ctx
