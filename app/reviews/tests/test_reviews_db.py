from app.models import select_all
from app.reviews.db import (
    insert_review_request,
    select_review,
    select_review_request,
    update_review_request,
    update_review_setting,
)
from app.reviews.enums import (
    ReviewRequestStatus,
    ReviewType,
)
from app.reviews.tables import review_setting_table
from app.reviews.tests.utils import insert_review, select_reviews
from app.tests.common import (
    cleanup_on_teardown,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
)


async def test_db_reviews(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    user_2 = await prepare_user_data(app, email='<EMAIL>')

    doc = await prepare_document_data(app, user)

    try:
        async with app['db'].acquire() as conn:
            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user.role_id,
                    'type': ReviewType.approve,
                },
            )
            await insert_review(conn, {'document_id': doc.id, 'role_id': user.role_id})
            _review = await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user.role_id,
                    'type': ReviewType.reject,
                },
            )

            review = await select_review(conn, _review.id)
            assert review.type == ReviewType.reject

            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_2.role_id,
                    'type': ReviewType.reject,
                },
            )
            await insert_review(conn, {'document_id': doc.id, 'role_id': user_2.role_id})

            reviews = await select_reviews(
                conn=conn,
                edrpou=user.company_edrpou,
                document_ids=[doc.id],
            )
            assert len(reviews) == 2

            review_map = {r.role_id: r.type for r in reviews}
            assert review_map[user.role_id] == ReviewType.reject
            assert not review_map[user_2.role_id]
    finally:
        await cleanup_on_teardown(app)


async def test_db_review_request(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(app, user)

    data_1 = {
        'document_id': doc.id,
        'from_role_id': user.role_id,
        'to_role_id': user.role_id,
    }
    data_2 = data_1.copy()
    data_2['status'] = ReviewRequestStatus.deleted.value

    try:
        async with app['db'].acquire() as conn:
            _review_request = await insert_review_request(conn, data_1)
            review_request_id = _review_request.id

            review_request = await select_review_request(conn, review_request_id)
            assert review_request.status == ReviewRequestStatus.active

            _review_request = await insert_review_request(conn, data_2)
            assert review_request_id == _review_request.id

            review_request = await select_review_request(conn, _review_request.id)
            assert review_request.status == ReviewRequestStatus.deleted

            await update_review_request(
                conn, review_request_id, {'status': ReviewRequestStatus.active.value}
            )

            review_request = await select_review_request(conn, review_request_id)
            assert review_request.status == ReviewRequestStatus.active

    finally:
        await cleanup_on_teardown(app)


async def test_update_review_setting(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(app, user)

    try:
        async with app['db'].acquire() as conn:
            data = {
                'document_id': doc.id,
                'is_required': True,
            }
            await update_review_setting(conn, user, data)

            settings = await select_all(conn, review_setting_table.select())
            assert len(settings) == 1
            assert settings[0].is_required
            assert settings[0].is_parallel

            data = {
                'document_id': doc.id,
                'is_required': False,
            }
            await update_review_setting(conn, user, data)

            settings = await select_all(conn, review_setting_table.select())
            assert len(settings) == 1
            assert not settings[0].is_required
            assert settings[0].is_parallel
    finally:
        await cleanup_on_teardown(app)
