import React from 'react';

import cn from 'classnames';

import css from './BorderList.css';

export interface BorderListProps {
    className?: string;
}

const BorderList: React.FC<React.PropsWithChildren<BorderListProps>> = ({
    children,
    className,
}) => {
    return (
        <ul className={cn(css.root, className)}>
            {React.Children.map(children, (child, index) => {
                if (React.isValidElement(child) && child.type === 'li') {
                    return React.cloneElement(child, {
                        ...child.props,
                        className: cn(child.props.className, css.listItem, {
                            [css.firstItem]: index === 0,
                            [css.hasPrev]: index - 1 >= 0,
                            [css.lastItem]:
                                index === React.Children.count(children) - 1,
                        }),
                    });
                }
                return null;
            })}
        </ul>
    );
};

export default BorderList;
