"""Views for the events feature."""

import io
from http import HTT<PERSON>tatus
from typing import assert_never

from aiohttp import web

from api.downloads.utils import stream_file_buffer
from api.public.decorators import (
    api_handler,
)
from api.utils import api_response, is_public_api_request
from app.auth.decorators import login_required
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.events import document_actions, user_actions, utils
from app.events.db import (
    select_actions_report,
    select_actions_report_file_by_report_id,
    select_events_by_edrpou,
)
from app.events.enums import ActionsReportType
from app.events.validators import (
    validate_create_report_request,
    validate_download_actions_report_file,
    validate_event_export,
)
from app.lib.helpers import client_rate_limit
from app.services import services

PAGE_SIZE = 100


@api_handler
async def get_events(request: web.Request, user: User) -> web.Response:
    ctx = validate_event_export({**request.rel_url.query})

    validate_user_permission(user, {'can_download_actions'})

    async with services.events_db.acquire() as conn:
        events = await select_events_by_edrpou(
            conn,
            user.company_edrpou,
            date_from=ctx.date_from,
            date_to=ctx.date_to,
            seqnum_offset=ctx.cursor,
            limit=PAGE_SIZE + 1,
        )

        resp = {
            'results': [event.as_report_row().as_json() for event in events[:PAGE_SIZE]],
            'next_cursor': events[-2].seqnum if len(events) > PAGE_SIZE else None,
        }
    return api_response(request, resp)


async def report_request(
    request: web.Request,
    user: User,
    report_type: ActionsReportType,
) -> str:
    """
    Start report generation process for both types of actions.
    """

    data = await validate_create_report_request(
        request=request,
        user=user,
        report_type=report_type,
    )

    match report_type:
        case ActionsReportType.documents:
            report_id = await document_actions.utils.start_actions_report(
                user=user,
                date_from=data.date_from,
                date_to=data.date_to,
                from_public_api=is_public_api_request(request),
            )

        case ActionsReportType.users:
            report_id = await user_actions.utils.start_actions_report(
                user=user,
                date_from=data.date_from,
                date_to=data.date_to,
                from_public_api=is_public_api_request(request),
            )

        case _:
            assert_never(report_type)

    return report_id


@login_required()
async def create_document_actions_report_request_internal(
    request: web.Request,
    user: User,
) -> web.Response:
    """Start user actions report generation process (internal API)"""
    await report_request(request, user, ActionsReportType.documents)
    return web.Response(status=HTTPStatus.CREATED)


@api_handler
async def create_document_actions_report_request_public(
    request: web.Request,
    user: User,
) -> web.Response:
    """Start document actions report generation process (public API)"""

    report_id = await report_request(request, user, ActionsReportType.documents)

    return web.json_response({'report_id': report_id}, status=HTTPStatus.CREATED)


@login_required()
async def create_user_actions_report_request_internal(
    request: web.Request,
    user: User,
) -> web.Response:
    """Start document actions report generation process (internal API)"""
    await report_request(request, user, ActionsReportType.users)
    return web.Response(status=HTTPStatus.CREATED)


@api_handler
async def create_user_actions_report_request_public(
    request: web.Request,
    user: User,
) -> web.Response:
    """Start document actions report generation process (public API)"""

    report_id = await report_request(request, user, ActionsReportType.users)

    return web.json_response({'report_id': report_id}, status=HTTPStatus.CREATED)


@api_handler
async def get_actions_report_status_public(request: web.Request, user: User) -> web.Response:
    """Route to get actions report status (public API)"""
    report_id = request.match_info['report_id']
    async with services.events_db.acquire() as conn:
        # Check if the report exists
        report = await select_actions_report(conn, report_id=report_id, company_id=user.company_id)
        if not report:
            return web.json_response(
                {'status': 'not_found', 'detail': 'Звіт не знайдено'}, status=HTTPStatus.NOT_FOUND
            )
        # Check if file exists
        file = await select_actions_report_file_by_report_id(
            conn, report_id=report_id, company_id=user.company_id
        )
        if file:
            return web.json_response({'status': 'ready', 'filename': file.filename})
        return web.json_response({'status': 'pending'}, status=HTTPStatus.ACCEPTED)


async def download_actions_report_file(
    request: web.Request,
    user: User,
) -> web.StreamResponse:
    """
    Download an action report file from SS

    This endpoint is common for both user and document actions.
    """
    async with services.events_db.acquire() as conn:
        file = await validate_download_actions_report_file(conn, request, user)

    content = await utils.download_actions_report_file(
        file_id=file.id,
        report_id=file.report_id,
        company_id=file.company_id,
    )

    # todo: stream directly from S3
    buffer = io.BytesIO(content)
    return await stream_file_buffer(request, file_name=file.filename, raw_buffer=buffer)


@client_rate_limit(limit=5)
@api_handler
async def download_actions_report_file_public(
    request: web.Request, user: User
) -> web.StreamResponse:
    """Public API version of download_actions_report_file"""
    return await download_actions_report_file(request, user)


@client_rate_limit(limit=5)
@login_required()
async def download_actions_report_file_internal(
    request: web.Request, user: User
) -> web.StreamResponse:
    """Internal API version of download_actions_report_file"""
    return await download_actions_report_file(request, user)
