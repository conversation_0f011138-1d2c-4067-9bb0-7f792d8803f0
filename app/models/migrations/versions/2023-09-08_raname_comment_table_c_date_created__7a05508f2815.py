"""Raname comment_table.c.date_created index

Revision ID: 7a05508f2815
Revises: fd954554078d
Create Date: 2023-09-08 12:29:14.040772

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7a05508f2815'
down_revision = 'fd954554078d'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')  # hack for skipping transaction
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS ix_date_created;
        """
    )
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS ix_comments_date_created
        ON comments (date_created);
        """
    )


def downgrade():
    op.execute('COMMIT')  # hack for skipping transaction
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS ix_date_created
        ON comments (date_created);
        """
    )
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS ix_comments_date_created;
        """
    )
