import React from 'react';

import cn from 'classnames';
import PropTypes from 'prop-types';

import SignLine from '../ui/signLine/signLine';

import { XmlData } from '../../../records/xml';

import css from './statement.css';

const Statement = ({ data }) => (
    <div className={css.root}>
        <div className={css.headerWrapper}>
            <div className={css.textCenter}>
                <img
                    alt="МетІнвест"
                    height={30}
                    src={`${config.STATIC_HOST}/images/metinvest_bg.png`}
                />
            </div>
            <table className={css.headerTable}>
                <thead>
                    <tr>
                        <th className={css.headerType}>{data.type}</th>
                        <th className={css.headerDate}>{data.date}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td className={css.textCenter} colSpan={2}>
                            до договору про надання послуг з перевезення вантажу
                            та транспортно-експедиційного обслуговування №{' '}
                            {data.agreementName}
                            <br />
                            від {data.agreementDate}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <table className={css.infoTable}>
            <tbody>
                <tr>
                    <th scope="row">1. Замовник</th>
                    <td>{data.extra.customer}</td>
                </tr>
                <tr>
                    <th scope="row">2.1. Адреса завантаження</th>
                    <td>{data.extra.senderAddress}</td>
                </tr>
                <tr>
                    <th scope="row">2.2. Вантажовідправник</th>
                    <td>{data.extra.senderName}</td>
                </tr>
                <tr>
                    <th scope="row">2.3. Відповідальна особа</th>
                    <td>{data.extra.senderPerson}</td>
                </tr>
                <tr>
                    <th scope="row">2.4. Дата та час завантаження</th>
                    <td>{data.extra.sentAt}</td>
                </tr>
                <tr>
                    <th scope="row">2.5. Адреса розвантаження</th>
                    <td>{data.extra.recipientAddress}</td>
                </tr>
                <tr>
                    <th scope="row">2.6. Вантажоодержувач</th>
                    <td>{data.extra.recipientName}</td>
                </tr>
                <tr>
                    <th scope="row">2.7. Відповідальна особа</th>
                    <td>{data.extra.recipientPerson}</td>
                </tr>
                <tr>
                    <th scope="row">2.8. Дата та час розвантаження</th>
                    <td>{data.extra.receivedAt}</td>
                </tr>
            </tbody>
        </table>

        <div className={css.detailsWrapper}>
            3. Характер вантажу (необхідне підкреслити)
            <table className={css.detailsTable}>
                <thead>
                    <tr>
                        <th className={css.detailsCell}>3.1. Найменування</th>
                        <th className={css.detailsCell}>3.2. Вага вантажу</th>
                        <th className={css.detailsCell}>
                            3.3. Довжина вантажу
                        </th>
                        <th className={css.detailsCell}>
                            3.4. Спосіб навантаження
                        </th>
                        <th className={css.detailsCell}>3.5. Примітка</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td className={css.detailsMultiline}>
                            {data.extra.cargoName}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.cargoWeight}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.cargoLength}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.cargoMode}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.cargoNote}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div className={css.detailsWrapper}>
            4. Вимоги до транспортного засобу
            <table className={css.detailsTable}>
                <thead>
                    <tr>
                        <th className={css.detailsCell}>
                            4.1. Вид транспортного засобу
                        </th>
                        <th className={css.detailsCell}>
                            4.2. Вантажопідйо
                            <wbr />
                            мність
                        </th>
                        <th className={css.detailsCell}>4.3. Довжина</th>
                        <th className={css.detailsCell}>
                            4.4. Додаткові вимоги
                        </th>
                        <th className={css.detailsCell}>&nbsp;</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td className={css.textCenter}>
                            {data.extra.vehicleType}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.vehicleWeight}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.vehicleLength}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.vehicleNote}
                        </td>
                        <th className={css.textCenter}>&nbsp;</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <div className={css.detailsWrapper}>
            <b>5. Вартість послуги</b> {data.extra.paymentAmount}
        </div>
        <div className={css.detailsWrapper}>
            <b>6. Форма та строк оплати</b> {data.extra.paymentType}
        </div>

        <div className={css.detailsWrapper}>
            <b>
                7. Інформація про транспортний засіб (необхідно ввести фактичні
                дані авто)
            </b>
            <table className={css.detailsTable}>
                <thead>
                    <tr>
                        <th className={css.detailsCell}>7.1. ФІО водія</th>
                        <th className={css.detailsCell}>
                            7.2. Державний номер автомобіля
                        </th>
                        <th className={css.detailsCell}>
                            7.3. Державний номер напівпричипа
                        </th>
                        <th className={css.detailsCell}>
                            7.4. Контактний тел. водія
                        </th>
                        <th className={css.detailsCell}>
                            7.5. Марка автомобіля
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td className={css.textCenter}>
                            {data.extra.driverName}
                        </td>
                        <td className={css.textCenter}>
                            {data.extra.vehiclePlate}
                        </td>
                        <td className={css.textCenter}>{data.extra.vehicle}</td>
                        <td className={css.textCenter}>
                            {data.extra.driverPhone}
                        </td>
                        <th className={css.textCenter}>
                            {data.extra.vehicleModel}
                        </th>
                    </tr>
                </tbody>
            </table>
        </div>

        <div className={css.detailsWrapper}>
            <b>8. Паспортні дані</b>
            <table className={css.detailsTable}>
                <thead>
                    <tr>
                        <th className={cn(css.detailsCell, css.textLeft)}>
                            8.1. Серія, номер
                        </th>
                        <th className={css.detailsCell4}>
                            8.2. Ким та коли виданий
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{data.extra.driverPassportNumber}</td>
                        <td>{data.extra.driverPassportInfo}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div className={css.detailsWrapper}>
            <b>9. Посвідчення водія</b>
            <table className={css.detailsTable}>
                <thead>
                    <tr>
                        <th className={cn(css.detailsCell, css.textLeft)}>
                            9.1. Серія, номер
                        </th>
                        <th className={css.detailsCell4}>
                            9.2. Ким та коли видано
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{data.extra.driverLicenseNumber}</td>
                        <td>{data.extra.driverLicenseInfo}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <table className={css.footerTable}>
            <thead>
                <tr>
                    <th>Замовник</th>
                    <th className={css.buffer}>&nbsp;</th>
                    <th>Виконавець</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td className={css.textCenter}>
                        {data.owner.fullName}
                        <br />
                        <SignLine full />
                    </td>
                    <td className={css.buffer} />
                    <td className={css.textCenter}>
                        {data.partner.fullName}
                        <br />
                        <SignLine full />
                        <SignLine full />
                        <small>м.п.</small>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
);

Statement.propTypes = {
    data: PropTypes.instanceOf(XmlData).isRequired,
};

export default Statement;
