import pytest

from app.auth.db import insert_token, select_user_by_token_hash
from app.auth.enums import RoleStatus
from app.auth.helpers import generate_hash_sha512
from app.contacts.db import insert_contact_person_phone
from app.contacts.tables import contact_table
from app.contacts.tests.utils import (
    prepare_contact,
    prepare_contact_person,
    with_contact_recipients_es,
)
from app.flags import FeatureFlags
from app.models import select_all
from app.tests.common import (
    assert_list_any_order,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_company_data,
    prepare_contacts,
    prepare_coworker_role,
    prepare_user_data,
)

COMPANY_ID = '10000000-0000-0000-0000-000000000001'
GET_CONTACT_RECIPIENTS_QUERY = """
    query AllContactRecipients($search: String!) {
        allContactRecipients(search: $search) {
            edrpou
            name
            email
            userName
            isMainRecipient
        }
    }
"""

GET_CONTACT_QUERY = """
    query AllContacts($search: String!) {
        allContacts(search: $search) {
            count
            contacts {
                name
                companyId
                edrpou
                persons {
                    email
                    firstName
                    lastName
                    mainRecipient
                }
            }
        }
    }
"""

GET_CONTACT_PERSONS_QUERY = """
    query AllContactPersons($search: String!) {
        allContactPersons(search: $search) {
            contact_persons {
                email
                firstName
                secondName
                lastName
                mainRecipient
                contact {
                    companyId
                }
            }
        }
    }
"""


@pytest.mark.parametrize(
    'search, expected',
    [
        pytest.param(
            '99999999',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_edrpou_1',
        ),
        pytest.param(
            '99999998',
            [
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_by_edrpou_2',
        ),
        pytest.param(
            'Укр',
            [
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_name_common',
        ),
        pytest.param(
            'BabyShark',
            [
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_by_name_one_contact',
        ),
        pytest.param(
            '<EMAIL>',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_email',
        ),
        pytest.param(
            'Шевченко',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ТОВ "УкрЛітПромПостач"',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': False,
                },
            ],
            id='search_by_last_name',
        ),
    ],
)
async def test_resolve_contact_recipients_db(aiohttp_client, test_flags, search, expected):
    """
    Check that basic database search of contact recipients works
    """
    test_flags[FeatureFlags.DISABLE_CONTACT_RECIPIENT_ES_SEARCH.value] = True

    app, client, user1 = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user1)

    # First contact
    contact1 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999999',
        name='ТОВ "УкрЛітПромПостач"',
        date_created='2021-01-01',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Леся',
        last_name='Українка',
        date_created='2021-01-01T12:00:00',
        main_recipient=True,
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Тарас',
        last_name='Шевченко',
        date_created='2021-01-01T13:00:00',
    )

    # Second contact
    await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999998',
        name='ПАТ "УкрBabySharkCorp"',
        date_created='2021-01-02',
    )

    # Getting registered and unregistered contacts using field isRegistered
    data = await fetch_graphql(
        client=client,
        query="""
            query AllContactRecipients($search: String!) {
                allContactRecipients(search: $search) {
                    edrpou
                    name
                    email
                    userName
                    isMainRecipient
                }
            }
        """,
        variables={'search': search},
        headers=headers,
    )

    assert data['allContactRecipients'] == expected


@pytest.mark.parametrize(
    'search, expected',
    [
        pytest.param(
            '99999999',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': False,
                },
                # One item without email is contact, another is company,
                # frontend should be able to handle it
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_by_edrpou_1',
        ),
        pytest.param(
            '99999998',
            [
                # One item without email is contact, another is company,
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999998',
                    'name': None,
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_by_edrpou_2',
        ),
        pytest.param(
            'Укр',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': False,
                },
                # One item without email is contact, another is company,
                # frontend should be able to handle it
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_by_name_common',
        ),
        pytest.param(
            'Нова компанія',
            [
                {
                    'edrpou': '99999997',
                    'name': 'Нова компанія (2) копія.word.pdf',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
            id='search_company_only',
        ),
        pytest.param(
            'Шевченко',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_user_name',
        ),
        pytest.param(
            '<EMAIL>',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': False,
                },
            ],
            id='search_by_email',
        ),
        pytest.param(
            'Шевченко УкрЛітПромПостач',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_last_name_and_company_name',
        ),
        pytest.param(
            'Шевченко 99999999',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
            ],
            id='search_by_last_name_and_edrpou',
        ),
        pytest.param(
            'Укр Шевченко',
            [
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Тарас Шевченко',
                    'isMainRecipient': True,
                },
                # УкрBabySharkCorp should not be in the list
                # because it does not have a contact person with
                # last name Шевченко
            ],
            id='search_by_last_name_and_common_name',
        ),
    ],
)
async def test_resolve_contact_recipients_es(aiohttp_client, test_flags, search, expected):
    """
    Check that basic elasticsearch search of contact recipients works
    """
    test_flags[FeatureFlags.DISABLE_CONTACT_RECIPIENT_ES_SEARCH.value] = False

    app, client, user1 = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user1)

    # First contact
    contact1 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999999',
        name='ТОВ "УкрЛітПромПостач"',
        date_created='2021-01-01',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Леся',
        last_name='Українка',
        date_created='2021-01-01T12:00:00',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Тарас',
        last_name='Шевченко',
        date_created='2021-01-01T13:00:00',
        main_recipient=True,
    )

    # Second contact
    contact2 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999998',
        name='ПАТ "УкрBabySharkCorp"',
        date_created='2021-01-02',
    )

    company1_id = await prepare_company_data(
        app,
        edrpou='99999999',
        name='ДП Українська література',
        date_created='2021-01-03',
    )
    company2_id = await prepare_company_data(
        app,
        edrpou='99999998',
        date_created='2021-01-04',
    )
    company3_id = await prepare_company_data(
        app,
        edrpou='99999997',
        name='Нова компанія (2) копія.word.pdf',
        date_created='2021-01-05',
    )

    async with with_contact_recipients_es(
        companies_ids=[company1_id, company2_id, company3_id],
        contacts_ids=[contact1.id, contact2.id],
    ):
        data = await fetch_graphql(
            client=client,
            query=GET_CONTACT_RECIPIENTS_QUERY,
            variables={'search': search},
            headers=headers,
        )

    # We can't guarantee stable order between tests, so we use assert_list_any_order
    # instead of equal operator
    assert assert_list_any_order(data['allContactRecipients'], expected)


async def test_resolve_contact_recipients_es_with_roles(aiohttp_client, test_flags):
    test_flags[FeatureFlags.DISABLE_CONTACT_RECIPIENT_ES_SEARCH.value] = False

    app, client, user1 = await prepare_client(
        aiohttp_client,
        company_edrpou='11111111',
        email='<EMAIL>',
        first_name='Тестовий',
        last_name='Користувач',
        company_name='ТОВ "Тестова компанія"',
    )
    company_id_1 = user1.company_id
    coworker1 = await prepare_user_data(
        app,
        company_id=company_id_1,
        email='<EMAIL>',
        first_name='Михайло',
        last_name='Грушевський',
    )
    coworker2 = await prepare_user_data(
        app,
        company_id=company_id_1,
        email='<EMAIL>',
        first_name='Іван',
        last_name='Франко',
    )

    # First contact
    contact1 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999999',
        name='ТОВ "УкрЛітПромПостач"',
        date_created='2021-01-01',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Леся',
        last_name='Українка',
        date_created='2021-01-01T12:00:00',
    )

    company_id_2 = await prepare_company_data(
        app,
        edrpou='99999999',
        name='ДП Українська література',
        date_created='2021-01-03',
    )
    recipient1 = await prepare_user_data(
        app,
        company_id=company_id_2,
        email='<EMAIL>',
        first_name='Іван',
        last_name='Нечуй-Левицький',
    )

    # another company
    company_id_3 = await prepare_company_data(
        app,
        edrpou='99999998',
        name='ПАТ "УкрBabySharkCorp"',
        date_created='2021-01-04',
    )
    recipient2 = await prepare_user_data(
        app,
        company_id=company_id_3,
        email='<EMAIL>',
        first_name='Марко',
        last_name='Вовчок',
    )

    async with with_contact_recipients_es(
        contacts_ids=[contact1.id],
        companies_ids=[company_id_1, company_id_2, company_id_3],
        roles_ids=[
            user1.role_id,
            coworker1.role_id,
            coworker2.role_id,
            recipient1.role_id,
            recipient2.role_id,
        ],
    ):

        async def _do_search(query: str):
            headers = prepare_auth_headers(user1)
            data = await fetch_graphql(
                client=client,
                query=GET_CONTACT_RECIPIENTS_QUERY,
                variables={'search': query},
                headers=headers,
            )
            return data['allContactRecipients']

        data = await _do_search('99999998')
        assert assert_list_any_order(
            data=data,
            expected=[
                {
                    'edrpou': '99999998',
                    'name': 'ПАТ "УкрBabySharkCorp"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
        )

        data = await _do_search('99999999')
        assert assert_list_any_order(
            data=data,
            expected=[
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': '<EMAIL>',
                    'userName': 'Леся Українка',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '99999999',
                    'name': 'ДП Українська література',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
        )

        data = await _do_search('11111111')
        assert assert_list_any_order(
            data=data,
            expected=[
                {
                    'edrpou': '11111111',
                    'name': 'ТОВ "Тестова компанія"',
                    'email': '<EMAIL>',
                    'userName': 'Тестовий Користувач',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '11111111',
                    'name': 'ТОВ "Тестова компанія"',
                    'email': '<EMAIL>',
                    'userName': 'Михайло Грушевський',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '11111111',
                    'name': 'ТОВ "Тестова компанія"',
                    'email': '<EMAIL>',
                    'userName': 'Іван Франко',
                    'isMainRecipient': False,
                },
                {
                    'edrpou': '11111111',
                    'name': 'ТОВ "Тестова компанія"',
                    'email': None,
                    'userName': None,
                    'isMainRecipient': False,
                },
            ],
        )


async def test_resolve_contact_recipients_es_order(aiohttp_client, test_flags):
    test_flags[FeatureFlags.DISABLE_CONTACT_RECIPIENT_ES_SEARCH.value] = False

    app, client, user1 = await prepare_client(
        aiohttp_client,
        company_edrpou='99991111',
        email='<EMAIL>',
        first_name='Тестовий',
        last_name='Користувач',
        company_name='ТОВ "Тестова компанія"',
    )
    company_id_1 = user1.company_id
    coworker1 = await prepare_user_data(
        app,
        company_id=company_id_1,
        email='<EMAIL>',
        first_name='Михайло',
        last_name='Грушевський',
    )
    coworker2 = await prepare_user_data(
        app,
        company_id=company_id_1,
        email='<EMAIL>',
        first_name='Іван',
        last_name='Франко',
    )

    # First contact
    contact1 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999999',
        name='ТОВ "УкрЛітПромПостач"',
        date_created='2021-01-01',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Леся',
        last_name='Українка',
        date_created='2021-01-01T12:00:00',
    )

    company_id_2 = await prepare_company_data(
        app,
        edrpou='99999999',
        name='ДП Українська література',
        date_created='2021-01-03',
    )
    recipient1 = await prepare_user_data(
        app,
        company_id=company_id_2,
        email='<EMAIL>',
        first_name='Іван',
        last_name='Нечуй-Левицький',
    )

    # another company
    company_id_3 = await prepare_company_data(
        app,
        edrpou='99999998',
        name='ПАТ "УкрBabySharkCorp"',
        date_created='2021-01-04',
    )
    recipient2 = await prepare_user_data(
        app,
        company_id=company_id_3,
        email='<EMAIL>',
        first_name='Марко',
        last_name='Вовчок',
    )

    async with with_contact_recipients_es(
        contacts_ids=[contact1.id],
        companies_ids=[company_id_1, company_id_2, company_id_3],
        roles_ids=[
            user1.role_id,
            coworker1.role_id,
            coworker2.role_id,
            recipient1.role_id,
            recipient2.role_id,
        ],
    ):
        headers = prepare_auth_headers(user1)
        data = await fetch_graphql(
            client=client,
            query=GET_CONTACT_RECIPIENTS_QUERY,
            # should return all different types of items
            variables={'search': '9999'},
            headers=headers,
        )
        items: list = data['allContactRecipients']

        assert len(items) == 8
        # we should guarantee stable order between types of items: company, contact, role,
        # but can't guarantee order for items of the same type

        # Contact
        assert items[0]['edrpou'] == '99999999'
        assert items[0]['name'] == 'ДП Українська література'
        assert items[0]['email'] is None

        # Contact persons
        assert items[1]['edrpou'] == '99999999'
        assert items[1]['name'] == 'ДП Українська література'
        assert items[1]['email'] == '<EMAIL>'

        # The next 3 should be companies, we don't know the order
        expected_companies = ('99991111', '99999998', '99999999')
        assert items[2]['edrpou'] in expected_companies
        assert items[2]['email'] is None

        assert items[3]['edrpou'] in expected_companies
        assert items[3]['email'] is None

        assert items[4]['edrpou'] in expected_companies
        assert items[4]['email'] is None

        # Last 3 should be roles of a current company, we don't know the order
        expected_emails = ('<EMAIL>', '<EMAIL>', '<EMAIL>')
        assert items[5]['edrpou'] == '99991111'
        assert items[5]['email'] in expected_emails

        assert items[6]['edrpou'] == '99991111'
        assert items[6]['email'] in expected_emails

        assert items[7]['edrpou'] == '99991111'
        assert items[7]['email'] in expected_emails


@pytest.mark.parametrize('es_enabled', [True, False])
async def test_contact_registration_field_and_option(aiohttp_client, test_flags, es_enabled):
    """
    Check that field and option isRegistered works correctly.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_ES_SEARCH.value] = es_enabled

    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    async with app['db'].acquire() as conn:
        reg_edrpous, unreg_edrpous = await prepare_contacts(app, conn, user)
        contacts = await select_all(conn, contact_table.select())

    async with with_contact_recipients_es(
        contacts_ids=[c.id for c in contacts],
    ):
        # Getting registered and unregistered contacts using field isRegistered
        query = '{ allContacts { contacts { isRegistered, edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        reg_edrpous_field = {
            contact['edrpou']
            for contact in data['allContacts']['contacts']
            if contact['isRegistered']
        }
        unreg_edrpous_field = {
            contact['edrpou']
            for contact in data['allContacts']['contacts']
            if not contact['isRegistered']
        }

        # Getting registered contacts using options isRegistered
        query = '{ allContacts(isRegistered: true){ contacts { edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        reg_edrpous_option = {contact['edrpou'] for contact in data['allContacts']['contacts']}

        # Getting unregistered contacts using option isRegistered
        query = '{ allContacts(isRegistered: false){ contacts { edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        unreg_edrpous_option = {contact['edrpou'] for contact in data['allContacts']['contacts']}

        # Getting count of registered contacts using options isRegistered
        query = '{ allContacts(isRegistered: true){ count } }'
        count_registred = await fetch_graphql(client, query, headers)

        # Getting count of unregistered contacts using options isRegistered
        query = '{ allContacts(isRegistered: false){ count } }'
        count_unregistred = await fetch_graphql(client, query, headers)

        assert reg_edrpous == reg_edrpous_field == reg_edrpous_option
        assert unreg_edrpous == unreg_edrpous_field == unreg_edrpous_option
        assert len(unreg_edrpous) == count_unregistred['allContacts']['count']
        assert len(reg_edrpous) == count_registred['allContacts']['count']


@pytest.mark.parametrize(
    'search, expected',
    [
        pytest.param('<EMAIL>', 1, id='search_by_email'),
        pytest.param('<EMAIL>', 0, id='search_by_missing_email'),
        pytest.param('Леся', 1, id='search_by_first_name'),
        pytest.param('Григорій', 0, id='search_by_missing_first_name'),
        pytest.param('Григорович', 1, id='search_by_second_name'),
        pytest.param('Савич', 0, id='search_by_missing_second_name'),
        pytest.param('Франко', 1, id='search_by_last_name'),
        pytest.param('Сковорода', 0, id='search_by_missing_last_name'),
        pytest.param('88888888', 1, id='search_by_edrpou'),
        pytest.param('999', 1, id='search_by_edrpou_prefix'),
        pytest.param('33333333', 0, id='search_by_invalid_edrpou'),
        pytest.param('ДП Українська література', 1, id='search_by_company_name'),
        pytest.param('Missing company name', 0, id='search_by_missing_company_name'),
        pytest.param('380631234567', 1, id='search_by_phone'),
        pytest.param('380509999999', 0, id='search_by_missing_phone'),
        pytest.param('', 2, id='empty_search'),
    ],
)
@pytest.mark.parametrize('es_enabled', [True, False])
async def test_contacts_search(aiohttp_client, test_flags, es_enabled, search, expected):
    """
    Check that search works correctly by contact and person fields.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_ES_SEARCH.value] = es_enabled

    app, client, user = await prepare_client(aiohttp_client, company_id=COMPANY_ID)
    headers = prepare_auth_headers(user)

    async with app['db'].acquire() as conn:
        # First contact with 2 contact persons
        contact1 = await prepare_contact(
            company_id=COMPANY_ID,
            edrpou='99999999',
            name='УкрЛіт',
        )
        contact_person_1 = await prepare_contact_person(
            contact_id=contact1.id,
            email='<EMAIL>',
            first_name='Леся',
            last_name='Українка',
            main_recipient=True,
        )
        await insert_contact_person_phone(
            conn=conn,
            person_id=contact_person_1.id,
            phone='+380631234567',
        )
        await prepare_contact_person(
            contact_id=contact1.id,
            email='<EMAIL>',
            first_name='Тарас',
            second_name='Григорович',
            last_name='Шевченко',
            main_recipient=False,
        )
        # Second contact with 1 contact person
        await prepare_company_data(
            app,
            edrpou='88888888',
            name='ДП Українська література',
        )
        contact2 = await prepare_contact(
            company_id=COMPANY_ID,
            edrpou='88888888',
            name='ДП Українська література',
        )
        await prepare_contact_person(
            contact_id=contact1.id,
            email='<EMAIL>',
            first_name='Іван',
            second_name='Якович',
            last_name='Франко',
            main_recipient=True,
        )

    async with with_contact_recipients_es(
        contacts_ids=[contact1.id, contact2.id],
    ):
        result = await fetch_graphql(
            client=client,
            query=GET_CONTACT_QUERY,
            variables={'search': search},
            headers=headers,
        )

    assert len(result['allContacts']['contacts']) == expected
    assert result['allContacts']['count'] == expected


@pytest.mark.parametrize(
    'email, is_email_hidden, expected',
    [
        ('<EMAIL>', False, '<EMAIL>'),
        ('<EMAIL>', True, None),
    ],
)
@pytest.mark.parametrize('es_enabled', [True, False])
async def test_resolve_contact_email(
    aiohttp_client,
    test_flags,
    es_enabled,
    email,
    is_email_hidden,
    expected,
):
    """
    Check there is no contact person's email in response for person with hidden email.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_ES_SEARCH.value] = es_enabled

    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    contact = await prepare_contact(
        company_id=user.company_id,
        edrpou='99999999',
        name='УкрЛіт',
    )
    await prepare_contact_person(
        contact_id=contact.id,
        email=email,
        first_name='Іван',
        second_name='Якович',
        last_name='Франко',
        main_recipient=True,
        is_email_hidden=is_email_hidden,
    )
    person2 = await prepare_contact_person(
        contact_id=contact.id,
        email='<EMAIL>',
        first_name='Тарас',
        second_name='Григорович',
        last_name='Шевченко',
        main_recipient=False,
    )
    query = '{ allContacts { contacts { persons { email isEmailHidden } } } }'
    async with with_contact_recipients_es(contacts_ids=[contact.id]):
        response = await fetch_graphql(client, query, headers)

    assert len(response['allContacts']['contacts']) == 1
    persons = response['allContacts']['contacts'][0]['persons']
    result = [set(person.items()) for person in persons]

    assert {('email', expected), ('isEmailHidden', is_email_hidden)} in result
    assert {('email', person2.email), ('isEmailHidden', False)} in result


@pytest.mark.parametrize(
    'search, is_email_hidden, expected',
    [
        ('smartweb.com.ua', False, {'<EMAIL>', '<EMAIL>'}),
        ('smartweb.com.ua', True, {'<EMAIL>', None}),
        ('c1@s', True, set()),
        ('c1@s', False, {'<EMAIL>', '<EMAIL>'}),
    ],
)
@pytest.mark.parametrize('es_enabled', [True, False])
async def test_search_hidden_contact_email(
    aiohttp_client,
    test_flags,
    es_enabled,
    search,
    is_email_hidden,
    expected,
):
    """
    Check search by hidden email returns no contacts.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_ES_SEARCH.value] = es_enabled

    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    contact = await prepare_contact(
        company_id=user.company_id,
        edrpou='99999999',
        name='УкрЛіт',
    )
    await prepare_contact_person(
        contact_id=contact.id,
        email='<EMAIL>',
        main_recipient=True,
        is_email_hidden=is_email_hidden,
    )
    await prepare_contact_person(
        contact_id=contact.id,
        email='<EMAIL>',
        main_recipient=False,
    )
    async with with_contact_recipients_es(contacts_ids=[contact.id]):
        response = await fetch_graphql(
            client=client,
            query=GET_CONTACT_QUERY,
            variables={'search': search},
            headers=headers,
        )
    if not expected:
        assert len(response['allContacts']['contacts']) == 0
        return

    assert len(response['allContacts']['contacts']) == 1
    persons = response['allContacts']['contacts'][0]['persons']
    assert len(persons) == len(expected)
    result = {person['email'] for person in persons}
    assert result == expected


@pytest.mark.parametrize('es_enabled', [True, False])
async def test_contact_persons_access_after_invite(aiohttp_client, test_flags, es_enabled):
    """
    Check that contact persons are accessible after user invites another user to the company.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_PERSON_ES_SEARCH.value] = es_enabled

    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(
        app,
        company_edrpou='88887777',
        email='<EMAIL>',
    )
    user1_headers = prepare_auth_headers(user1)
    user2_headers = prepare_auth_headers(user2)
    link_name = 'contact'
    search_query = '999'

    contact1 = await prepare_contact(
        company_id=user1.company_id,
        edrpou='99999999',
        name='УкрЛіт',
    )
    await prepare_contact_person(
        contact_id=contact1.id,
        email='<EMAIL>',
        first_name='Леся',
        last_name='Українка',
        main_recipient=True,
    )
    company_id = await prepare_company_data(
        app,
        edrpou='99999999',
        name='ДП Українська література',
    )

    async with with_contact_recipients_es(
        companies_ids=[company_id],
        contacts_ids=[contact1.id],
    ):
        # 1. User #2 has no access to User #1 contacts
        for headers, expected_count_contacts in [
            (user1_headers, 1),
            (user2_headers, 0),
        ]:
            result = await fetch_graphql(
                client=client,
                query=GET_CONTACT_PERSONS_QUERY,
                variables={'search': search_query},
                headers=headers,
            )
            contact_persons = result['allContactPersons']['contact_persons']
            assert len(contact_persons) == expected_count_contacts

    # 2. User #2 creates his own contacts
    contact2 = await prepare_contact(
        company_id=user2.company_id,
        edrpou='99999999',
        name='Українська література',
    )
    await prepare_contact_person(
        contact_id=contact2.id,
        email='<EMAIL>',
        first_name='Тарас',
        last_name='Шевченко',
        main_recipient=True,
    )

    async with with_contact_recipients_es(
        companies_ids=[company_id],
        contacts_ids=[contact1.id, contact2.id],
    ):
        for headers, user, expected_count_contacts in [
            (user1_headers, user1, 1),
            (user2_headers, user2, 1),
        ]:
            result = await fetch_graphql(
                client=client,
                query=GET_CONTACT_PERSONS_QUERY,
                variables={'search': search_query},
                headers=headers,
            )
            contact_persons = result['allContactPersons']['contact_persons']
            assert len(contact_persons) == expected_count_contacts
            assert contact_persons[0][link_name]['companyId'] == user.company_id

        async with app['db'].acquire() as conn:
            # User #1 invites User #2 to shared EDRPOU 00000000
            user2_new_role = await prepare_coworker_role(
                conn,
                {
                    'company_id': user1.company_id,
                    'status': RoleStatus.active,
                    'user_id': user2.id,
                },
            )
            user2_new_role_id = user2_new_role.id

            # 3. User #2 does not see contacts of User #1 without switching to a new role.
            for headers, user, expected_count_contacts in [
                (user1_headers, user1, 1),
                (user2_headers, user2, 1),
            ]:
                result = await fetch_graphql(
                    client=client,
                    query=GET_CONTACT_PERSONS_QUERY,
                    variables={'search': search_query},
                    headers=headers,
                )
                contact_persons = result['allContactPersons']['contact_persons']
                assert len(contact_persons) == expected_count_contacts
                assert contact_persons[0][link_name]['companyId'] == user.company_id

            # User #2 activates a new role
            response = await client.post(
                f'/internal-api/roles/{user2_new_role_id}/activate',
                headers=user2_headers,
            )
            assert response.status == 200

            # Prepare User #2 headers for a new role
            user2_token = await insert_token(conn, {'role_id': user2_new_role_id})
            user2_coworker = await select_user_by_token_hash(
                conn,
                token_hash=generate_hash_sha512(user2_token),
                raw_token=user2_token,
            )

        user2_coworker_headers = prepare_auth_headers(user2_coworker)

        # User #2 now can access to contacts of User #1
        for headers, _, expected_count_contacts in [
            (user1_headers, user1, 1),
            (user2_coworker_headers, user2_coworker, 1),
        ]:
            result = await fetch_graphql(
                client=client,
                query=GET_CONTACT_PERSONS_QUERY,
                variables={'search': search_query},
                headers=headers,
            )
            contact_persons = result['allContactPersons']['contact_persons']
            assert len(contact_persons) == expected_count_contacts
            assert contact_persons[0][link_name]['companyId'] == user1.company_id


@pytest.mark.parametrize(
    'search, expected',
    [
        pytest.param(
            '<EMAIL>',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Леся',
                    'secondName': 'Петрівна',
                    'lastName': 'Українка',
                    'mainRecipient': True,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_email',
        ),
        pytest.param('<EMAIL>', [], id='search_by_missing_email'),
        pytest.param(
            'Леся',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Леся',
                    'secondName': 'Петрівна',
                    'lastName': 'Українка',
                    'mainRecipient': True,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_first_name',
        ),
        pytest.param('Іван', [], id='search_by_missing_first_name'),
        pytest.param(
            'Григорович',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Тарас',
                    'secondName': 'Григорович',
                    'lastName': 'Шевченко',
                    'mainRecipient': False,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_second_name',
        ),
        pytest.param('Якович', [], id='search_by_missing_second_name'),
        pytest.param(
            'Шевченко',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Тарас',
                    'secondName': 'Григорович',
                    'lastName': 'Шевченко',
                    'mainRecipient': False,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_last_name',
        ),
        pytest.param('Франко', [], id='search_by_missing_last_name'),
        pytest.param(
            '99999999',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Леся',
                    'secondName': 'Петрівна',
                    'lastName': 'Українка',
                    'mainRecipient': True,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
                {
                    'email': '<EMAIL>',
                    'firstName': 'Тарас',
                    'secondName': 'Григорович',
                    'lastName': 'Шевченко',
                    'mainRecipient': False,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_edrpou',
        ),
        pytest.param('12345678', [], id='search_by_invalid_edrpou'),
        pytest.param(
            'company name',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Леся',
                    'secondName': 'Петрівна',
                    'lastName': 'Українка',
                    'mainRecipient': True,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
                {
                    'email': '<EMAIL>',
                    'firstName': 'Тарас',
                    'secondName': 'Григорович',
                    'lastName': 'Шевченко',
                    'mainRecipient': False,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_company_name',
        ),
        pytest.param('Missing company name', [], id='search_by_missing_company_name'),
        pytest.param(
            '380631234567',
            [
                {
                    'email': '<EMAIL>',
                    'firstName': 'Леся',
                    'secondName': 'Петрівна',
                    'lastName': 'Українка',
                    'mainRecipient': True,
                    'contact': {
                        'companyId': COMPANY_ID,
                    },
                },
            ],
            id='search_by_phone',
        ),
        pytest.param('380509999999', [], id='search_by_missing_phone'),
    ],
)
@pytest.mark.parametrize('es_enabled', [True, False])
async def test_contact_persons_search(aiohttp_client, test_flags, es_enabled, search, expected):
    """
    Check that contact persons search works correctly.
    """
    test_flags[FeatureFlags.ENABLE_CONTACT_PERSON_ES_SEARCH.value] = es_enabled

    app, client, user = await prepare_client(aiohttp_client, company_id=COMPANY_ID)
    headers = prepare_auth_headers(user)

    async with app['db'].acquire() as conn:
        contact = await prepare_contact(
            company_id=COMPANY_ID,
            edrpou='99999999',
            name='Test company name',
        )
        contact_person_1 = await prepare_contact_person(
            contact_id=contact.id,
            email='<EMAIL>',
            first_name='Леся',
            second_name='Петрівна',
            last_name='Українка',
            main_recipient=True,
        )
        await prepare_contact_person(
            contact_id=contact.id,
            email='<EMAIL>',
            first_name='Тарас',
            second_name='Григорович',
            last_name='Шевченко',
            main_recipient=False,
        )
        await insert_contact_person_phone(
            conn=conn,
            person_id=contact_person_1.id,
            phone='+380631234567',
        )

    async with with_contact_recipients_es(
        companies_ids=[COMPANY_ID],
        contacts_ids=[contact.id],
    ):
        result = await fetch_graphql(
            client=client,
            query=GET_CONTACT_PERSONS_QUERY,
            variables={'search': search},
            headers=headers,
        )
        contact_persons = result['allContactPersons']['contact_persons']
        assert assert_list_any_order(contact_persons, expected)
