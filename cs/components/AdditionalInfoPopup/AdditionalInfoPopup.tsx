import React from 'react';
import MediaQuery from 'react-responsive';

import { MEDIA_WIDTH } from 'lib/constants';
import { reloadPage } from 'lib/navigation';
import { t } from 'ttag';

import Icon from '../ui/icon/icon';
import PopupMobile from '../ui/popup/mobile/popup';
import Popup from '../ui/popup/popup';

import FlexBox from '../FlexBox/FlexBox';
import AdditionalInfoForm from '../SuccessVerifiedUser/components/AdditionalInfoForm/AdditionalInfoForm';
import { useAdditionalInfoPopupDisplayEffect } from './hooks/useAdditionalInfoPopupDisplayEffect';

import HappyManIcon from './images/happy-man.svg';

import css from './AdditionalInfoPopup.css';

const AdditionalInfoPopup: React.FC = () => {
    const isActive = useAdditionalInfoPopupDisplayEffect();

    const onSuccessSubmit = () => {
        reloadPage();
    };

    const content = (
        <FlexBox className={css.root} gap={0}>
            <FlexBox className={css.formContainer} direction="column" gap={24}>
                <FlexBox
                    className={css.titleContainer}
                    direction="column"
                    gap={8}
                >
                    <h2>{t`Максимально підлаштовуємо «Вчасно» під ваші потреби`}</h2>
                    <h4>{t`Розкажіть про себе, щоб оптимізувати сервіс для вашої роботи.`}</h4>
                </FlexBox>
                <AdditionalInfoForm
                    onSuccessSubmit={onSuccessSubmit}
                    isColumnView
                />
            </FlexBox>
            <div className={css.imageContainer}>
                <div className={css.image}>
                    <Icon glyph={HappyManIcon} />
                </div>
            </div>
        </FlexBox>
    );

    return (
        <>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup
                    active={isActive}
                    onClose={() => undefined}
                    rounded
                    hiddenCloseButton
                    withoutPadding
                >
                    {content}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile active={isActive} hiddenCloseButton>
                    {content}
                </PopupMobile>
            </MediaQuery>
        </>
    );
};

export default AdditionalInfoPopup;
