from app.app import create_app
from app.services import services
from app.tests.common import cleanup_on_teardown
from app.tokens.db import insert_short_registration_token
from app.tokens.utils import generate_jwt_token

TEST_EDRPOU = '12345678'
TEST_EMAIL = '<EMAIL>'
TEST_PAYLOAD = {
    'edrpou': TEST_EDRPOU,
    'email': TEST_EMAIL,
}


async def test_short_invite_token(aiohttp_client):
    app = create_app()
    client = await aiohttp_client(app)

    try:
        async with app['db'].acquire() as conn:
            short_token = await insert_short_registration_token(conn, dict(TEST_PAYLOAD))

        response = await client.get(f'/invite/{short_token}')
        assert response.status == 200

        jwt_token = generate_jwt_token(
            dict(TEST_PAYLOAD, token=short_token),
            services.config.tokens.private_key,
        )

        assert response.url.path == '/auth/registration', response.url
        assert response.url.query['token'] == jwt_token, response.url
    finally:
        await cleanup_on_teardown(app)
