import logging
from typing import Any

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import J<PERSON>NB, insert

from api.errors import Code, ServerError
from app.auth.db import select_company_by_role_id, select_coworkers
from app.auth.enums import RoleStatus
from app.auth.tables import (
    company_table,
    is_active_filter,
    role_table,
    user_active_role_company_join,
    user_table,
)
from app.contacts.enums import ContactRecipientIndexationEntity
from app.contacts.tables import (
    CONTACT_INDEX_COLUMNS,
    CONTACT_PERSON_INDEX_COLUMNS,
    contact_person_phone_table,
    contact_person_table,
    contact_recipients_indexation_table,
    contact_table,
    contact_user_join,
)
from app.contacts.types import (
    Contact,
    ContactBase,
    ContactDetails,
    <PERSON>ForIndexation,
    Contact<PERSON>erson,
    ContactRecipient,
    ContactRecipientIndexationBatch,
    ContactRecipientIndexationItem,
    SoftCreatedContact,
)
from app.documents.tables import (
    document_recipients_table,
    document_table,
)
from app.lib import regexp, validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.enums import DocumentStatus
from app.lib.helpers import split_comma_separated_emails
from app.lib.types import DataDict, StrList
from app.models import exists, select_all, select_one
from app.notifications.tables import unsubscription_table

logger = logging.getLogger(__name__)


# fmt: off

CONTACT_TABLES_JOIN = (
    contact_table
    .outerjoin(
        contact_person_table,
        contact_table.c.id == contact_person_table.c.contact_id,
    )
)

# filter that checks that contact edrpou is registered in service
# WARNING: should be used as filter in queries with contact in FROM clause
CONTACT_EDRPOU_IS_REGISTERED_FILTER = sa.exists(
    sa.select([company_table.c.id])
    .select_from(
        user_table
        .join(role_table, role_table.c.user_id == user_table.c.id)
        .join(company_table, company_table.c.id == role_table.c.company_id)
    )
    .where(
        sa.and_(
            is_active_filter,
            # WARNING: semi-join with contact table
            contact_table.c.edrpou == company_table.c.edrpou,
        )
    )
)

# fmt: on


async def select_contact(
    conn: DBConnection,
    *,
    contact_edrpou: str,
    company_id: str,
) -> Contact | None:
    """Check if company (by EDRPOU) is already in company's contacts list."""
    row = await select_one(
        conn,
        (
            contact_table.select().where(
                sa.and_(
                    contact_table.c.edrpou == contact_edrpou,
                    contact_table.c.company_id == company_id,
                )
            )
        ),
    )
    return Contact.from_row(row) if row else None


async def select_contact_person(
    conn: DBConnection,
    *,
    email: str | None,
    edrpou: str,
    company_id: str,
) -> ContactPerson | None:
    """Select contact person by email and edrpou."""
    if not email:
        return None

    row = await select_one(
        conn=conn,
        query=(
            sa.select([contact_person_table])
            .select_from(
                contact_table.join(
                    contact_person_table,
                    contact_person_table.c.contact_id == contact_table.c.id,
                )
            )
            .where(
                sa.and_(
                    contact_table.c.edrpou == edrpou,
                    contact_table.c.company_id == company_id,
                    contact_person_table.c.email == email,
                )
            )
        ),
    )
    return ContactPerson.from_row(row) if row else None


async def select_contact_person_by_id(
    conn: DBConnection, contact_person_id: str, *, with_contact: bool = False
) -> DBRow | None:
    selectable = [contact_person_table]
    select_from = contact_person_table
    if with_contact:
        selectable.extend(
            [
                contact_table.c.company_id.label('company_id'),
                contact_table.c.edrpou.label('contact_edrpou'),
            ]
        )
        select_from = select_from.join(
            contact_table, contact_table.c.id == contact_person_table.c.contact_id
        )

    return await select_one(
        conn,
        (
            sa.select(selectable)
            .select_from(select_from)
            .where(contact_person_table.c.id == contact_person_id)
        ),
    )


async def select_missing_by_invoice(conn: DBConnection, user_edrpou: str) -> list[DBRow]:
    return await select_all(
        conn,
        (
            sa.select([document_table.c.number, document_recipients_table.c.edrpou])
            .select_from(
                document_table.outerjoin(
                    document_recipients_table,
                    document_recipients_table.c.document_id == document_table.c.id,
                )
            )
            .where(
                sa.and_(
                    document_table.c.edrpou_owner == user_edrpou,
                    document_table.c.status_id == DocumentStatus.uploaded.value,
                    document_table.c.number.isnot(None),
                    document_recipients_table.c.edrpou.isnot(None),
                    document_recipients_table.c.emails.is_(None),
                )
            )
            .group_by(document_table.c.number, document_recipients_table.c.edrpou)
        ),
    )


async def select_phone(conn: DBConnection, *, contact_person_id: str, phone: str) -> DBRow:
    """Get contragent user's phone by phone."""
    return await select_one(
        conn,
        (
            contact_person_phone_table.select().where(
                sa.and_(
                    contact_person_phone_table.c.contact_person_id == contact_person_id,
                    contact_person_phone_table.c.phone == phone,
                )
            )
        ),
    )


async def insert_contact_raw(conn: DBConnection, data: DataDict) -> Contact:
    row = await select_one(
        conn=conn,
        query=(insert(contact_table).values(data).returning(contact_table)),
    )

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return Contact.from_row(row)


async def insert_contacts(conn: DBConnection, data: list[DataDict]) -> list[Contact]:
    """
    Insert/update contacts in database

    TODO: merge it with upsert_contacts, because both of them do almost the same things
    """

    stmt = insert(contact_table).values(data).returning(contact_table)
    stmt = stmt.on_conflict_do_update(
        index_elements=[getattr(contact_table.c, column) for column in CONTACT_INDEX_COLUMNS],
        set_={
            'name': stmt.excluded.name,
            'short_name': stmt.excluded.short_name,
            'date_sync': sa.text('now()'),
        },
    )

    rows = await select_all(conn, stmt)

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.id for row in rows],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return [Contact.from_row(row) for row in rows]


async def insert_contact_person_raw(conn: DBConnection, data: DataDict) -> ContactPerson:
    row = await select_one(
        conn=conn,
        query=(insert(contact_person_table).values(data).returning(contact_person_table)),
    )

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.contact_id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return ContactPerson.from_row(row)


async def insert_contact_person_phone(conn: DBConnection, *, person_id: str, phone: str) -> None:
    if not phone or not person_id:
        return

    await conn.execute(
        insert(contact_person_phone_table)
        .values(phone=phone, contact_person_id=person_id)
        .on_conflict_do_nothing()
    )


async def update_contact_person_phones(conn: DBConnection, *, person_id: str, phone: str) -> None:
    if not phone or not person_id:
        return

    # Delete previous phones
    await conn.execute(
        contact_person_phone_table.delete().where(
            contact_person_phone_table.c.contact_person_id == person_id
        )
    )

    # insert new phones
    await conn.execute(
        insert(contact_person_phone_table)
        .values(phone=phone, contact_person_id=person_id)
        .on_conflict_do_nothing()
    )


async def insert_signer_to_contacts(
    conn: DBConnection,
    details: ContactDetails,
    company_id: str,
) -> None:
    """Save signer info to Contact > ContactPerson > ContactPhone"""

    async with conn.begin():
        contact_data = {
            'company_id': company_id,
            'edrpou': details.edrpou,
            'accountant_email': details.email,
        }
        if details.company_name:
            contact_data['name'] = details.company_name

        contact_id = await conn.scalar(
            insert(contact_table)
            .values(contact_data)
            .returning(contact_table.c.id)
            .on_conflict_do_update(
                index_elements=[
                    getattr(contact_table.c, column) for column in CONTACT_INDEX_COLUMNS
                ],
                set_=contact_data,
            )
        )

        await insert_contact_recipients_indexation(
            conn=conn,
            entities_ids=[contact_id],
            entity_type=ContactRecipientIndexationEntity.contact,
        )

        if contact_id:
            contact_person_data = {
                'contact_id': contact_id,
                'email': details.email,
                'is_email_hidden': details.is_email_hidden,
                'main_recipient': True,
            }
            if details.first_name:
                contact_person_data['first_name'] = details.first_name
            if details.second_name:
                contact_person_data['second_name'] = details.second_name
            if details.last_name:
                contact_person_data['last_name'] = details.last_name

            contact_person_id = await conn.scalar(
                insert(contact_person_table)
                .values(contact_person_data)
                .returning(contact_person_table.c.id)
                .on_conflict_do_update(
                    index_elements=[
                        getattr(contact_person_table.c, column)
                        for column in CONTACT_PERSON_INDEX_COLUMNS
                    ],
                    set_=contact_person_data,
                )
            )

            if details.phone:
                await insert_contact_person_phone(
                    conn=conn,
                    phone=details.phone,
                    person_id=contact_person_id,
                )


async def update_accountant_email(
    conn: DBConnection, *, contact: DataDict, email: str, company_id: str
) -> None:
    """Update contact's accountant email."""
    edrpou = validators.validate_edrpou(str(contact['edrpou']))
    if not edrpou:
        return

    row = await select_one(
        conn=conn,
        query=(
            contact_table.update()
            .where(
                sa.and_(
                    contact_table.c.edrpou == edrpou,
                    contact_table.c.company_id == company_id,
                )
            )
            .values(accountant_email=email)
            .returning(contact_table.c.id)
        ),
    )

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )


async def update_contact_person(
    conn: DBConnection,
    contact_person_id: str,
    data: DataDict,
) -> ContactPerson | None:
    if not contact_person_id or not data:
        return None

    row = await select_one(
        conn=conn,
        query=(
            contact_person_table.update()
            .where(contact_person_table.c.id == contact_person_id)
            .values(**data)
            .returning(contact_person_table)
        ),
    )

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.contact_id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return ContactPerson.from_row(row) if row else None


async def update_contact_sync_date(conn: DBConnection, contact: DataDict, company_id: str) -> None:
    """Update sync date."""
    edrpou = validators.validate_edrpou(str(contact['edrpou']))
    if not edrpou:
        return
    await conn.execute(
        contact_table.update()
        .where(
            sa.and_(
                contact_table.c.edrpou == edrpou,
                contact_table.c.company_id == company_id,
            )
        )
        .values(date_sync=sa.text('now()'))
    )


async def update_main_recipient(
    conn: DBConnection,
    company_id: str | None,
    details: ContactDetails,
) -> None:
    """
    Save given contact details as main recipient for given contact user.

    Mostly we use this function when document is sent to recipient
    """

    # Handle case for sign_sessions, when that function
    # can be called in context without company_id
    if not company_id:
        return

    # For now we don't support contacts without email (for example user registered by
    # phone number), so we skip them
    if not details.email:
        return

    if not details.edrpou:
        # TODO[AK]: Need updates in business logic - deleting coworker
        # by admin - should reassign doc's owner to new coworker or himself
        # For now - owner can be deleted role, no sense adding to contact
        logger.warning(
            'Unhandled exception updating main recipient',
            extra={'user_edrpou': company_id, 'details': details._asdict()},
        )
        return

    # TODO[ID]: Better handling of comma separated emails
    for item in reversed(split_comma_separated_emails(details.email)):
        item_details = details._replace(email=item)
        await _update_main_recipient(
            conn=conn,
            company_id=company_id,
            details=item_details,
        )


async def delete_all_contacts(conn: DBConnection, company_id: str) -> list[str]:
    """Remove all contacts for specified company_id."""
    rows = await conn.execute(
        contact_table.delete()
        .where(contact_table.c.company_id == company_id)
        .returning(contact_table.c.id)
    )
    return [row.id for row in rows]


async def select_contact_by_id(conn: DBConnection, contact_id: str) -> Contact | None:
    row = await select_one(
        conn, (sa.select([contact_table]).where(contact_table.c.id == contact_id))
    )
    return Contact.from_row(row) if row else None


async def select_unregistered_contacts_for_invite(
    conn: DBConnection,
    company_id: str,
    offset: int | None = None,
    limit: int | None = None,
) -> list[DBRow]:
    """select edrpou and emails of all persons of unregistered companies"""
    sub_query = (
        sa.select(
            [
                contact_table.c.edrpou,
                contact_person_table.c.email,
                company_table.c.email_domains.cast(sa.String).label('email_domains'),
            ]
        )
        .select_from(
            contact_user_join.join(
                contact_person_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            ).outerjoin(
                unsubscription_table,
                unsubscription_table.c.email == contact_person_table.c.email,
            )
        )
        .where(
            sa.and_(
                contact_table.c.company_id == company_id,
                unsubscription_table.c.id.is_(None),
            )
        )
        .group_by(
            contact_table.c.edrpou,
            contact_person_table.c.email,
            contact_person_table.c.date_created,
            company_table.c.email_domains.cast(sa.String),
        )
        .having(
            sa.or_(
                sa.func.every(user_table.c.registration_completed.isnot(True)),
                sa.func.every(role_table.c.status != RoleStatus.active),
            )
        )
        .limit(limit)
        .offset(offset)
        .order_by(contact_person_table.c.date_created)
        .alias('unregistered_contacts')
    )
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    sub_query.c.edrpou,
                    sub_query.c.email,
                    sub_query.c.email_domains.cast(JSONB).label('email_domains'),
                ]
            ).select_from(sub_query)
        ),
    )


async def select_contacts_for_graph(
    conn: DBConnection,
    select_source: sa.Table,
    filters: list[Any],
    limit: int | None = None,
    offset: int | None = None,
) -> list[DBRow]:
    query = (
        sa.select([sa.distinct(contact_table.c.edrpou), contact_table.c.id])
        .select_from(select_source)
        .where(sa.and_(*filters))
        .order_by(contact_table.c.edrpou)
        .group_by(contact_table.c.edrpou, contact_table.c.id)
    )

    if limit is not None:
        query = query.limit(limit)

    if offset is not None:
        query = query.offset(offset)

    return await select_all(conn, query)


async def select_count_contacts_for_graph(
    conn: DBConnection,
    select_source: sa.Table,
    filters: list[Any],
) -> DBRow:
    query = (
        sa.select([sa.func.count(sa.distinct(contact_table.c.id)).label('count')])
        .select_from(select_source)
        .where(sa.and_(*filters))
        .group_by(contact_table.c.id)
    )
    query = sa.alias(query)

    query = sa.select([sa.func.count(query.c.count).label('count')]).select_from(query)

    return await select_one(conn, query)


async def select_contact_persons_for_graph(
    conn: DBConnection,
    filters: list[Any],
    limit: int,
    offset: int,
) -> list[DBRow]:
    select_source = CONTACT_TABLES_JOIN.outerjoin(
        contact_person_phone_table,
        contact_person_phone_table.c.contact_person_id == contact_person_table.c.id,
    )
    query = (
        sa.select([contact_person_table.c.id])
        .select_from(select_source)
        .where(sa.and_(*filters))
        .group_by(
            contact_person_table.c.id,
            contact_person_table.c.main_recipient,
            contact_person_table.c.email,
        )
        .order_by(
            sa.nullslast(contact_person_table.c.main_recipient.desc()),
            contact_person_table.c.email,
        )
    )
    query = query.limit(limit).offset(offset)
    return await select_all(conn, query)


async def select_count_contact_persons_for_graph(
    conn: DBConnection,
    current_user_company_id: str,
    current_user_edrpou: str,
    filters: list[Any],
) -> DBRow:
    query = (
        sa.select([sa.func.count(sa.distinct(contact_person_table.c.id)).label('count')])
        .select_from(
            contact_table.outerjoin(
                contact_person_table,
                contact_table.c.id == contact_person_table.c.contact_id,
            )
        )
        .where(
            sa.and_(
                contact_table.c.company_id == current_user_company_id,
                contact_table.c.edrpou != current_user_edrpou,
                contact_person_table.c.id.isnot(None),
                *filters,
            )
        )
        .group_by(contact_person_table.c.id)
    )
    query = sa.alias(query)

    query = sa.select([sa.func.count(query.c.count).label('count')]).select_from(query)

    return await select_one(conn, query)


async def select_count_unregistered_contacts(conn: DBConnection, company_id: str) -> int:
    """select one value of the number of unregistered companies in contacts"""
    companies = sa.alias(
        sa.select([sa.func.count(contact_table.c.edrpou).label('count')])
        .select_from(contact_user_join)
        .where(contact_table.c.company_id == company_id)
        .group_by(contact_table.c.edrpou)
        .having(
            sa.or_(
                sa.func.every(user_table.c.registration_completed.isnot(True)),
                sa.func.every(role_table.c.status != RoleStatus.active),
            )
        )
    )

    result = await select_one(
        conn,
        (sa.select([sa.func.count(companies.c.count).label('count')]).select_from(companies)),
    )

    return result.count


async def upsert_contacts(conn: DBConnection, contacts: list[ContactBase]) -> list[Contact]:
    # Ensure that no rows proposed for insertion within the same command
    # have duplicate constrained values
    unique_contacts: set[tuple[str, str]] = set()
    data = []
    for contact in contacts:
        key = (contact.company_id, contact.edrpou)
        if key not in unique_contacts:
            unique_contacts.add(key)
            data.append(
                {
                    'company_id': contact.company_id,
                    'edrpou': contact.edrpou,
                    'name': contact.name,
                    'short_name': contact.short_name,
                }
            )

    query = insert(contact_table).values(data)
    query = query.on_conflict_do_update(
        index_elements=[getattr(contact_table.c, column) for column in CONTACT_INDEX_COLUMNS],
        set_={
            'name': sa.func.coalesce(contact_table.c.name, query.excluded.name),
            'short_name': sa.func.coalesce(contact_table.c.short_name, query.excluded.short_name),
        },
    ).returning(contact_table)

    rows = await select_all(conn, query)

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.id for row in rows],
        entity_type=ContactRecipientIndexationEntity.contact,
    )
    return [Contact.from_row(row) for row in rows]


async def _update_main_recipient(
    conn: DBConnection,
    company_id: str,
    details: ContactDetails,
) -> None:
    """
    Save given contact person as main recipient for given user
    """

    exists_person = await select_contact_person(
        conn=conn,
        email=details.email,
        edrpou=details.edrpou,
        company_id=company_id,
    )

    # If there is already contact person in database, but not main recipient or
    # there is no contact person for document recipient in owner contacts list,
    # update main recipient
    if exists_person and exists_person.main_recipient:
        return

    # Don't add itself to contacts when invited as coworker
    coworkers = await select_coworkers(conn, company_id=company_id)
    if details.email in {item.email for item in coworkers}:
        return

    contact = await select_contact(conn, contact_edrpou=details.edrpou, company_id=company_id)

    async with conn.begin():
        # First remove previous main recipient for given contact
        if contact:
            await reset_contact_main_recipient(conn, contact_id=contact.id)

        # After mark current contact person as main recipient if it exists
        if exists_person is not None:
            await conn.execute(
                contact_person_table.update()
                .where(contact_person_table.c.id == exists_person.id)
                .values(main_recipient=True)
            )
        # Otherwise create new contact & contact person in database
        else:
            await insert_signer_to_contacts(
                conn=conn,
                details=details,
                company_id=company_id,
            )

    return


async def select_contact_email_by_edrpou(
    conn: DBConnection, edrpous: StrList, company_id: str
) -> list[DBRow]:
    """
    Select one email from contacts list for contacting given edrpous.
    Visible emails and main_recipient emails will be always first
    for selection.
    """
    if not edrpous:
        return []

    query = (
        sa.select(
            [
                # This field will be first item in edrpou, because there is
                # DISTINCT ON clause over contact_table.c.edrpou.
                contact_person_table.c.email,
                contact_table.c.edrpou,
                contact_person_table.c.is_email_hidden,
                contact_person_table.c.main_recipient,
            ]
        )
        .select_from(
            contact_table.outerjoin(
                contact_person_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            )
        )
        .order_by(
            contact_table.c.edrpou,
            # Visible emails will be on top or results
            sa.desc(contact_person_table.c.is_email_hidden.is_(False)),
            # main_recipient == True is on top of results
            sa.desc(contact_person_table.c.main_recipient.is_(True)),
        )
        .where(
            sa.and_(
                contact_table.c.edrpou.in_(edrpous),
                contact_table.c.company_id == company_id,
            )
        )
    ).distinct(contact_table.c.edrpou)
    return await select_all(conn, query)


async def select_contacts_by_ids(
    conn: DBConnection, contacts_ids: list[str], company_id: str
) -> list[Contact]:
    if not contacts_ids:
        return []

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([contact_table]).where(
                sa.and_(
                    contact_table.c.company_id == company_id,
                    contact_table.c.id.in_(contacts_ids),
                )
            )
        ),
    )
    return [Contact.from_row(row) for row in rows]


async def select_contacts_by_edrpous(conn: DBConnection, edrpous: StrList) -> list[DBRow]:
    """
    Select contacts by edrpous.

    Note: this function is used only for indexation of documents
    """
    query = (
        sa.select([contact_table, company_table.c.edrpou.label('owner_edrpou')])
        .select_from(
            contact_table.join(company_table, company_table.c.id == contact_table.c.company_id)
        )
        .where(sa.and_(contact_table.c.edrpou.in_(edrpous), company_table.c.edrpou.in_(edrpous)))
    )
    return await select_all(conn, query)


async def exist_unregistered_contacts(conn: DBConnection, role_id: str) -> bool:
    """Check if role have companies with unregistered contacts"""
    company = await select_company_by_role_id(conn, role_id)
    if not company:
        logger.info('Company by role does not exist', extra={'role_id': role_id})
        raise ServerError(code=Code.error_500)

    registered_contacts = (
        sa.select([contact_table.c.id])
        .select_from(
            contact_table.join(company_table, contact_table.c.edrpou == company_table.c.edrpou)
            .join(
                role_table,
                sa.and_(
                    role_table.c.company_id == company_table.c.id,
                    role_table.c.status == RoleStatus.active,
                ),
            )
            .join(
                user_table,
                sa.and_(
                    role_table.c.user_id == user_table.c.id,
                    user_table.c.registration_completed.is_(True),
                ),
            )
        )
        .where(contact_table.c.company_id == company.id)
        .alias('registered_contacts')
    )

    return await exists(
        conn=conn,
        select_from=contact_table,
        clause=sa.and_(
            contact_table.c.company_id == company.id,
            contact_table.c.id.notin_(registered_contacts),
        ),
    )


async def select_next_company_by_contact(
    conn: DBConnection, contact_edrpou: str, offset: int
) -> DBRow | None:
    """
    Reverse lookup to find all contacts in which the given EDRPOU is used.

    WARNING: that function doesn't return contacts for given EDRPOU, it returns
    contacts for different companies that have that EDRPOU in their contacts list.
    """
    filters = [contact_table.c.edrpou == contact_edrpou]

    return await select_one(
        conn=conn,
        query=(
            sa.select(
                [
                    contact_table.c.id,
                    contact_table.c.company_id,
                    contact_table.c.short_name,
                    contact_table.c.name,
                ]
            )
            .where(sa.and_(*filters))
            .order_by(contact_table.c.date_created, contact_table.c.id)
            .offset(offset)
            .limit(1)
        ),
    )


async def reset_contact_main_recipient(conn: DBConnection, contact_id: str) -> None:
    """
    Remove main_recipient flag for all contact persons for given contact_id.
    """

    await conn.execute(
        contact_person_table.update()
        .values(main_recipient=False)
        .where(
            sa.and_(
                contact_person_table.c.contact_id == contact_id,
                contact_person_table.c.main_recipient.is_(True),
            )
        )
    )


async def reset_contacts_main_recipient(
    conn: DBConnection,
    company_id: str,
    contacts_edrpous: set[str],
) -> None:
    await conn.execute(
        contact_person_table.update()
        .values(main_recipient=False)
        .where(
            sa.and_(
                contact_table.c.id == contact_person_table.c.contact_id,
                contact_table.c.company_id == company_id,
                contact_table.c.edrpou.in_(contacts_edrpous),
                contact_person_table.c.main_recipient.is_(True),
            )
        )
    )


async def update_contact(
    conn: DBConnection,
    contact_id: str,
    data: DataDict,
) -> None:
    """Update contact by id."""
    await conn.execute(
        contact_table.update().where(contact_table.c.id == contact_id).values(**data)
    )

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[contact_id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )


async def soft_create_contact(
    conn: DBConnection,
    company_id: str,
    contact_edrpou: str,
    contact_name: str | None = None,
) -> SoftCreatedContact:
    """
    Select or create contact by edrpou for a given company_id.
    """
    row = await select_one(
        conn=conn,
        query=(
            contact_table.select().where(
                sa.and_(
                    contact_table.c.edrpou == contact_edrpou,
                    contact_table.c.company_id == company_id,
                )
            )
        ),
    )
    if row:
        return SoftCreatedContact.from_db(row, is_new=False)

    row = await select_one(
        conn=conn,
        query=(
            insert(contact_table)
            .values(
                edrpou=contact_edrpou,
                company_id=company_id,
                name=contact_name,
            )
            .returning(contact_table)
        ),
    )
    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.id],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return SoftCreatedContact.from_db(row, is_new=True)


async def delete_contact_person(conn: DBConnection, contact_person_id: str) -> None:
    await conn.execute(
        contact_person_table.delete().where(contact_person_table.c.id == contact_person_id)
    )


async def insert_contact_persons(
    conn: DBConnection,
    data: list[DataDict],
) -> list[ContactPerson]:
    stmt = insert(contact_person_table).values(data).returning(contact_person_table)
    stmt = stmt.on_conflict_do_update(
        index_elements=[
            getattr(contact_person_table.c, column) for column in CONTACT_PERSON_INDEX_COLUMNS
        ],
        set_={
            'first_name': stmt.excluded.first_name,
            'last_name': stmt.excluded.last_name,
            'second_name': stmt.excluded.second_name,
            'main_recipient': stmt.excluded.main_recipient,
            'date_updated': sa.text('now()'),
        },
    )
    rows = await select_all(conn, stmt)

    await insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[row.contact_id for row in rows],
        entity_type=ContactRecipientIndexationEntity.contact,
    )

    return [ContactPerson.from_row(row) for row in rows]


async def insert_contact_persons_phones(
    conn: DBConnection,
    data: list[DataDict],
) -> None:
    await conn.execute(insert(contact_person_phone_table).values(data).on_conflict_do_nothing())


async def select_contact_recipients_for_search(
    conn: DBConnection,
    *,
    company_id: str,
    search_query: str,
    limit: int,
    offset: int,
) -> list[ContactRecipient]:
    filters = [
        contact_table.c.company_id == company_id,
    ]

    if validators.validate_edrpou(search_query):
        filters.append(contact_table.c.edrpou == search_query)
    elif maybe_email := regexp.search_email_re.search(search_query):
        search_query = maybe_email.group()
        filters.append(
            sa.and_(
                contact_person_table.c.is_email_hidden.is_(False),
                contact_person_table.c.email == search_query,
            )
        )
    elif search_query.isdigit():
        search_pattern = f'%{search_query}%'
        filters.append(contact_table.c.edrpou.ilike(search_pattern))
    else:
        search_pattern = f'%{search_query}%'
        filters.append(
            sa.or_(
                contact_table.c.name.ilike(search_pattern),
                contact_table.c.short_name.ilike(search_pattern),
                sa.and_(
                    contact_person_table.c.is_email_hidden.is_(False),
                    contact_person_table.c.email.ilike(search_pattern),
                ),
                contact_person_table.c.first_name.ilike(search_pattern),
                contact_person_table.c.second_name.ilike(search_pattern),
                contact_person_table.c.last_name.ilike(search_pattern),
            )
        )

    query = (
        sa.select(
            [
                contact_table.c.edrpou,
                contact_table.c.name,
                contact_person_table.c.email,
                contact_person_table.c.first_name,
                contact_person_table.c.second_name,
                contact_person_table.c.last_name,
                contact_person_table.c.main_recipient,
            ]
        )
        .select_from(
            contact_table.outerjoin(
                contact_person_table,
                contact_table.c.id == contact_person_table.c.contact_id,
            )
        )
        .where(sa.and_(*filters))
        .order_by(
            contact_table.c.date_created.desc(),
            contact_person_table.c.date_created.desc(),
        )
        .limit(limit)
        .offset(offset)
    )

    rows = await select_all(conn=conn, query=query)

    return [ContactRecipient.from_row(row) for row in rows]


async def select_contact_persons_for_search(
    *,
    conn: DBConnection,
    current_user_company_id: str,
    current_user_edrpou: str,
    search_filters: list[Any],
    limit: int,
    offset: int,
    search_query: str | None,
) -> tuple[int, tuple[str, ...]]:
    """Select contact persons count and ids."""

    contact_persons = await select_contact_persons_for_graph(
        conn=conn,
        filters=search_filters,
        limit=limit,
        offset=offset,
    )

    count_filters = []
    if search_query:
        count_filters = _determine_count_contact_persons_filters(search_query)

    contact_persons_count = await select_count_contact_persons_for_graph(
        conn=conn,
        current_user_company_id=current_user_company_id,
        current_user_edrpou=current_user_edrpou,
        filters=count_filters,
    )

    contact_person_ids = tuple(contact.id for contact in contact_persons)
    return contact_persons_count.count, contact_person_ids


def _determine_count_contact_persons_filters(search_query: str) -> list[Any]:
    """Return filters for counting contact persons for a given `search_query`."""
    return (
        [contact_person_table.c.email == search_query]
        if pv.EmailOrNoneAdapter.validate_python(search_query)
        else [contact_person_table.c.email.ilike(f'%{search_query}%')]
    )


async def select_contacts_for_indexation(
    conn: DBConnection,
    *,
    contacts_ids: list[str],
) -> list[ContactForIndexation]:
    """
    Select contact persons for indexation by contacts ids.
    """
    is_registered_company = sa.exists(
        sa.select([company_table.c.id])
        .select_from(user_table.join(role_table, role_table.c.user_id == user_table.c.id))
        .where(
            sa.and_(
                is_active_filter,
                role_table.c.company_id == company_table.c.id,
            )
        )
    )
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    # contact attributes
                    contact_table.c.id.label('contact_id'),
                    contact_table.c.edrpou.label('contact_company_edrpou'),
                    contact_table.c.name.label('contact_company_name'),
                    contact_table.c.short_name.label('contact_company_name_short'),
                    contact_table.c.company_id.label('contact_owner_id'),
                    contact_table.c.date_created.label('contact_date_created'),
                    # company attributes
                    company_table.c.name.label('company_name_extra'),
                    is_registered_company.label('company_registered'),
                    # contact persons attributes (be careful, they can be null)
                    contact_person_table.c.id.label('person_id'),
                    contact_person_table.c.email.label('person_email'),
                    contact_person_table.c.first_name.label('person_first_name'),
                    contact_person_table.c.second_name.label('person_second_name'),
                    contact_person_table.c.last_name.label('person_last_name'),
                    contact_person_table.c.is_email_hidden.label('person_is_email_hidden'),
                    contact_person_table.c.date_created.label('person_date_created'),
                    contact_person_table.c.main_recipient.label('person_main_recipient'),
                    # contact persons phones attributes (be careful, they can be null)
                    contact_person_phone_table.c.phone.label('person_phone'),
                ]
            )
            .select_from(
                contact_table.outerjoin(
                    company_table,
                    company_table.c.edrpou == contact_table.c.edrpou,
                )
                .outerjoin(
                    contact_person_table,
                    contact_person_table.c.contact_id == contact_table.c.id,
                )
                .outerjoin(
                    contact_person_phone_table,
                    contact_person_phone_table.c.contact_person_id == contact_person_table.c.id,
                )
            )
            .where(sa.and_(contact_table.c.id.in_(contacts_ids)))
        ),
    )
    return [ContactForIndexation.from_row(row) for row in rows]


async def insert_contact_recipients_indexation(
    conn: DBConnection,
    *,
    entities_ids: list[str],
    entity_type: ContactRecipientIndexationEntity,
) -> None:
    """
    Insert contact recipients indexation items
    """
    if not entities_ids:
        return

    data = [
        {
            'entity_id': entity_id,
            'entity_type': entity_type,
        }
        for entity_id in entities_ids
    ]
    query = insert(contact_recipients_indexation_table).values(data)
    await conn.execute(query)


async def select_contact_recipient_indexation_batch(
    conn: DBConnection,
    *,
    limit: int,
    min_iteration: int,
    max_iteration: int,
    cursor: int,
) -> ContactRecipientIndexationBatch:
    """
    Select and lock contact recipients indexation items for processing
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([contact_recipients_indexation_table])
            .where(
                sa.and_(
                    contact_recipients_indexation_table.c.seqnum > cursor,
                    contact_recipients_indexation_table.c.iteration >= min_iteration,
                    contact_recipients_indexation_table.c.iteration <= max_iteration,
                )
            )
            .order_by(contact_recipients_indexation_table.c.seqnum)
            .limit(limit)
            .with_for_update(skip_locked=True)
        ),
    )
    items = [ContactRecipientIndexationItem.from_row(row) for row in rows]
    return ContactRecipientIndexationBatch(items=items)


async def delete_contact_recipient_indexation(conn: DBConnection, *, items_ids: list[str]) -> None:
    """
    Delete contact recipients indexation items by ids
    """
    if not items_ids:
        return

    await conn.execute(
        contact_recipients_indexation_table.delete().where(
            contact_recipients_indexation_table.c.id.in_(items_ids)
        )
    )


async def increase_contact_recipient_indexation_iteration(
    conn: DBConnection,
    *,
    items_ids: list[str],
) -> None:
    """
    Increase errors count for contact recipients indexation items
    """
    if not items_ids:
        return

    await conn.execute(
        contact_recipients_indexation_table.update()
        .where(contact_recipients_indexation_table.c.id.in_(items_ids))
        .values(errors=contact_recipients_indexation_table.c.iteration + 1)
    )


async def select_contacts_ids_for_reindexation(
    conn: DBConnection,
    *,
    cursor: str,
    limit: int,
    company_id: str | None = None,
    contact_edrpou: str | None = None,
) -> list[str]:
    """
    Select IDs of contacts for contact recipients re indexation by contacts.

    For iterating over contacts we sort by PK (id) and use last_contact_id
    as cursor
    """
    filters = [contact_table.c.id > cursor]
    if company_id:
        filters.append(contact_table.c.company_id == company_id)
    if contact_edrpou:
        filters.append(contact_table.c.edrpou == contact_edrpou)

    query = (
        sa.select([contact_table.c.id])
        .select_from(contact_table)
        .where(sa.and_(*filters))
        .order_by(contact_table.c.id)
        .limit(limit)
    )
    rows = await select_all(conn=conn, query=query)
    return [row.id for row in rows]


async def select_contact_names_for_graph(
    conn: DBConnection,
    edrpous: list[str],
    company_id: str,
) -> list[DBRow]:
    """
    Select contact names by edrpous.
    """
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    contact_table.c.edrpou,
                    contact_table.c.short_name,
                    contact_table.c.name,
                ]
            )
            .select_from(contact_table)
            .where(
                sa.and_(
                    contact_table.c.edrpou.in_(edrpous),
                    contact_table.c.company_id == company_id,
                )
            )
        ),
    )


async def exists_email_in_recipients_contacts(
    conn: DBConnection,
    email: str,
    user_id: str,
) -> bool:
    """
    Check if email exists in contacts.
    """
    companies = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.id, company_table.c.edrpou])
            .select_from(user_active_role_company_join)
            .where(user_table.c.id == user_id)
        ),
    )

    companies_ids = [company.id for company in companies]
    companies_edrpous = [company.edrpou for company in companies]

    return await exists(
        conn=conn,
        select_from=(
            contact_table.join(
                contact_person_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            )
        ),
        clause=sa.and_(
            contact_person_table.c.email == email,
            # Update only contacts with EDRPOU of companies in which user
            # has active roles
            contact_table.c.edrpou.in_(companies_edrpous),
            # Ignore user's own contacts
            contact_table.c.company_id.notin_(companies_ids),
        ),
    )
