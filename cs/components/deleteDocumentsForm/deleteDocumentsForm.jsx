import React from 'react';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import StatusButton from '../ui/StatusButton/StatusButton';
import Button from '../ui/button/button';

import css from './deleteDocumentsForm.css';

const DeleteDocumentsForm = ({
    error,
    finished,
    onDeleteDocuments,
    onRestart,
    started,
}) => (
    <div className={css.root}>
        <p className={css.help}>
            {t`Ви можете видалити всі свої документи, що не підписані обома
            сторонами.`}{' '}
            <b>
                {t`Зауважте, що ця операція повністю видалить ваші документи з
                сервісу ${config.BRAND_NAME}.`}
            </b>
        </p>
        <div className={css.buttonContainer}>
            {finished ? (
                <StatusButton
                    onClick={onRestart}
                    title={t`Натисніть, щоб спробувати ще раз`}
                >
                    {t`Видалено`}
                </StatusButton>
            ) : (
                <Button
                    isLoading={started}
                    onClick={onDeleteDocuments}
                    theme="red"
                    width="full"
                >
                    {t`Видалити документи`}
                </Button>
            )}
        </div>
        {error ? (
            <p className={css.error}>
                {t`Виникла помилка при видаленні документів`}
            </p>
        ) : null}
    </div>
);

DeleteDocumentsForm.propTypes = {
    error: PropTypes.bool,
    finished: PropTypes.bool,
    onDeleteDocuments: PropTypes.func.isRequired,
    onRestart: PropTypes.func.isRequired,
    started: PropTypes.bool,
};

export default DeleteDocumentsForm;
