import React, { useCallback, useEffect, useRef, useState } from 'react';

import { asyncPredicateByRequestId } from 'components/DiiaSigner/utils';
import { DiiaRequestSignResponse, SignAlgo } from 'services/diia/types';

import DiiaFormContent from './DiiaFormContent/DiiaFormContent';

const QR_CODE_DURATION = 180_000;
const SIGNATURES_REQ_PERIOD = 1_500;

export type DiiaSignState =
    | 'Initial'
    | 'LinkLoad'
    | 'LinkPending'
    | 'Success'
    | 'LinkExpired'
    | 'Error';

export interface DiiaSignerProps {
    retrieveLink: () => Promise<DiiaRequestSignResponse>;
    onSuccess: () => void;
    title: string;
    generateSubtitle: (mobile: boolean) => string;
    logError: (message: string) => void;

    asyncPredicate?: (requestId?: string) => Promise<boolean>;
    onAlgoChange?: (signAlgo: SignAlgo) => void;
}

const DiiaSigner: React.FC<React.PropsWithChildren<DiiaSignerProps>> = ({
    asyncPredicate = asyncPredicateByRequestId,
    ...props
}) => {
    const diiaRequestIdRef = useRef('');
    const [diiaState, setDiiaState] = useState<DiiaSignState>('Initial');
    const [diiaQR, setDiiaQR] = useState('');
    const [diiaUrl, setDiiaUrl] = useState('');
    const [errorMessage, setErrorMessage] = useState('');

    // update refs when props change to avoid stale closures in useEffect
    const retrieveLinkRef = useRef(props.retrieveLink);
    useEffect(() => {
        retrieveLinkRef.current = props.retrieveLink;
    }, [props.retrieveLink]);

    const asyncPredicateCbRef = useRef(asyncPredicate);
    useEffect(() => {
        asyncPredicateCbRef.current = asyncPredicate;
    }, [asyncPredicate]);

    const onSuccessCbRef = useRef(props.onSuccess);
    useEffect(() => {
        onSuccessCbRef.current = props.onSuccess;
    }, [props.onSuccess]);

    const logErrorCbRef = useRef(props.logError);
    useEffect(() => {
        logErrorCbRef.current = props.logError;
    }, [props.logError]);

    useEffect(() => {
        async function effect() {
            if (diiaState === 'Initial') {
                try {
                    setDiiaState('LinkLoad');
                    const response = await retrieveLinkRef.current();

                    diiaRequestIdRef.current = response.request_id;

                    setDiiaQR(response.qr);
                    setDiiaUrl(response.url);
                    setDiiaState('LinkPending');
                } catch (e) {
                    setErrorMessage(e.message);
                    setDiiaState('Error');
                }
            }
        }
        effect();
    }, [diiaState]);

    useEffect(() => {
        if (diiaState === 'LinkPending') {
            const interval = setInterval(async () => {
                try {
                    const shouldSucceed = await asyncPredicateCbRef.current(
                        diiaRequestIdRef.current,
                    );

                    if (shouldSucceed) {
                        setDiiaState('Success');
                    }
                } catch (e) {
                    setErrorMessage(e.message);
                    setDiiaState('Error');
                }
            }, SIGNATURES_REQ_PERIOD);

            // expire link on frontend a little quicker to avoid states where
            // link is valid in UI but in fact expired
            const adjustedTimeout = QR_CODE_DURATION * 0.8;

            const timeout = setTimeout(() => {
                setDiiaState('LinkExpired');
            }, adjustedTimeout);

            return () => {
                clearInterval(interval);
                clearTimeout(timeout);
            };
        }
    }, [diiaState]);

    useEffect(() => {
        if (diiaState === 'Success') {
            onSuccessCbRef.current();
        }
    }, [diiaState]);

    const refreshLink = useCallback(() => {
        setDiiaState('Initial');
    }, []);

    useEffect(() => {
        if (errorMessage !== '') {
            logErrorCbRef.current(errorMessage);
        }
    }, [errorMessage]);

    return (
        <DiiaFormContent
            state={diiaState}
            diiaQR={diiaQR}
            onRefreshLink={refreshLink}
            errorMessage={errorMessage}
            diiaUrl={diiaUrl}
            title={props.title}
            generateSubtitle={props.generateSubtitle}
            onAlgoChange={props.onAlgoChange}
        />
    );
};

export default DiiaSigner;
