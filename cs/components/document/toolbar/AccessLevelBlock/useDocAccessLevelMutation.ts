import { useDispatch } from 'react-redux';

import { useMutation } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import documentActionCreators from 'components/document/documentActionCreators';
import { updateDocument } from 'services/documents/ts/api';
import { DocumentAccessLevel } from 'services/documents/ts/types';
import { t } from 'ttag';

// DOCUMENTS_PRIVATE_ACCESS
export const useDocAccessLevelMutation = () => {
    const dispatch = useDispatch();

    return useMutation({
        mutationFn: (
            params: [Parameters<typeof updateDocument>[0], DocumentAccessLevel],
        ) =>
            updateDocument(params[0], {
                access_settings: { level: params[1] },
            }),
        onSuccess: (_, variables) => {
            snackbarToast.success(t`Доступ до документу успішно змінено`);
            dispatch(
                documentActionCreators.patchDocumentAccessLevel(variables[1]),
            );
        },
        onError: () => {
            snackbarToast.error(
                t`Виникла помилка при зміні доступу до документу`,
            );
        },
    });
};
