import sqlalchemy as sa
from hiku.engine import (
    Context,
    pass_context,
)

from api.graph.constants import (
    DB_ENGINE_KEY,
)
from api.graph.utils import get_graph_user
from app.document_versions.db import build_available_versions_filter
from app.document_versions.tables import document_version_table
from app.lib.helpers import group_list
from app.lib.types import (
    StrList,
)
from app.models import select_all


@pass_context
async def resolve_document_versions_for_document(
    ctx: Context, fields_ids: StrList
) -> list[StrList]:
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in fields_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.select([document_version_table.c.id, document_version_table.c.document_id])
                .select_from(document_version_table)
                .where(
                    sa.and_(
                        document_version_table.c.document_id.in_(fields_ids),
                        build_available_versions_filter(user.company_edrpou),
                    )
                )
                .order_by(document_version_table.c.date_created.desc())
            ),
        )

    mapping = group_list(rows, lambda r: r.document_id)

    return [[row.id for row in mapping[field_id]] for field_id in fields_ids]
