from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime

from app.directories.types import Directory
from app.documents.types import Document
from app.lib.database import DBRow
from app.uploads.types import File


@dataclass(frozen=True)
class DocumentArchive:
    id: str
    company_id: str
    document_id: str
    created_by: str
    date_created: datetime
    date_updated: datetime

    @staticmethod
    def from_row(row: DBRow) -> DocumentArchive:
        return DocumentArchive(
            id=row.id,
            company_id=row.company_id,
            document_id=row.document_id,
            created_by=row.created_by,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass(frozen=True)
class ArchiveDocumentsCtx:
    documents: list[Document] | list[File]
    directory: Directory | None


@dataclass(frozen=True)
class UploadHistoredDocumentCtx:
    directory: Directory | None


@dataclass(frozen=True)
class ArchiveList:
    """Resolver type to return documents and directories in same list"""

    count: int  # total items count in archive_list
    document_ids: tuple[str, ...]
    directory_ids: tuple[str, ...]
