import I from 'immutable';

import actions from './statisticsUsersListActions';

const initState = {
    users: new I.List(),
    searchString: '',
    errorMessage: '',
};

const statisticsUsersListReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.STATISTICS_USERS_LIST__START_LOAD_DATA:
            return {
                ...state,
                searchString: action.searchString,
            };
        case actions.STATISTICS_USERS_LIST__FINISH_LOAD_DATA:
            return {
                ...state,
                users: action.users,
            };
        case actions.STATISTICS_USERS_LIST__SHOW_ERROR:
            return {
                ...state,
                errorMessage: action.err.message,
            };
        default:
            return state;
    }
};

export default statisticsUsersListReducer;
