"""Add is_logged_once

Revision ID: 29070c9b9b98
Revises: db96e3ca5570
Create Date: 2024-12-12 11:37:49.396036

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '29070c9b9b98'
down_revision = 'db96e3ca5570'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'users', sa.Column('is_logged_once', sa.<PERSON>(), server_default='0', nullable=False)
    )


def downgrade():
    op.drop_column('users', 'is_logged_once')
