import type { FC } from 'react';

import type { CustomViewRadioButtonProps } from '../RadioButton';

export interface OptionProps<T = string> {
    value: T;
    label: string;
    hint?: string;
}

export type OptionsProps<T> = Array<OptionProps<T>>;

export type OnChangeCallbackType<T = string> = (value: T) => void;

export interface RadioGroupProps<T = string> {
    disabled?: boolean;
    isOutlined?: boolean;
    isInlineType?: boolean;
    value: T;
    options: OptionsProps<T>;
    onChange: OnChangeCallbackType<T>;
    CustomViewRadioButton?: FC<CustomViewRadioButtonProps>;
    className?: string;
    optionClassName?: string;
}
