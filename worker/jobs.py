import os

from app.contacts.sync.jobs import (
    sync_contacts_collect_from_api,
    sync_contacts_first_step,
    sync_contacts_second_step,
    sync_contacts_set_recipients_step,
    sync_contacts_third_step,
)
from worker import auth as auth_jobs
from worker import emailing as emailing_jobs
from worker import esputnik
from worker import topics as t
from worker.analytics import jobs as analytics
from worker.auth.jobs import sync_company_info_from_youcontrol
from worker.banner.jobs import activate_banner
from worker.billing.jobs import (
    activate_company_extensions,
    activate_company_rates,
    create_initial_free_company_rate,
    deactivate_company_extensions_trial,
    deactivate_company_rates,
    delete_billing_accounts,
    fetch_privatbank_transactions,
    fetch_pumb_transactions,
    generate_employees_limit_reached_event,
    generate_esputnik_rate_expiring_event,
    generate_esputnik_tov_trial_expiring_event,
    migrate_to_new_rate,
    on_document_charge_event,
    process_activate_bank_transaction,
    process_bill,
    sa_activate_trial_company_rate,
    send_esputnik_event_to_emails,
    send_trial_enabled_event,
)
from worker.comments import jobs as comments
from worker.companies import jobs as companies
from worker.contacts import jobs as contacts
from worker.crm.jobs import (
    crm_send_event,
    crm_sync_rates,
)
from worker.delay_queue.jobs import process_delayed_task
from worker.document_antivirus.jobs import antivirus_check, antivirus_check_draft
from worker.document_automation.jobs import (
    apply_document_authomation_template,
    find_document_automation,
    start_document_automation,
)
from worker.document_revoke.jobs import (
    send_revoke_completed_notifications_job,
    send_revoke_initiated_notifications_job,
    send_revoke_rejected_notifications_job,
)
from worker.documents.jobs import (
    autosend_document_job,
    create_documents_role_access,
    delete_document_access_source,
    reindex_documents,
    reindex_documents_with_reviews,
    remove_documents_from_index,
    schedule_daily_notification_about_finished_documents,
    send_daily_notification_about_finished_documents,
    send_delete_request_notifications_job,
    send_document_finish_status_to_edi,
    send_document_status_callback,
    send_document_view_limit_notification,
    send_documents_to_indexator,
    send_edi_request,
    send_reject_document_notifications_job,
    send_to_indexator_by_ids,
    send_to_indexator_by_listings_ids,
    set_sign_session_document_status,
    sync_recipients_date_received,
    update_docs_date_delivered,
    update_sign_session_documents_date_delivered,
)
from worker.downloading.jobs import multi_download_archive, prepare_archived_documents_download
from worker.drafts.jobs import cleanup_old_drafts
from worker.emailing.jobs import (
    invite_coworkers,
    send_daily_feedbacks_job,
    send_document_to_recipient_job,
    send_email_to_document_owner,
    send_first_notification_to_signers,
    send_notification_about_document_access,
    send_notification_about_sign_process,
    send_notification_to_signers,
    send_payment_successful_email,
    send_reminder_about_new_documents_job,
    send_reminder_about_token_expiration_job,
    send_reminders_to_unregistered_users,
    send_signed_by_coworker_documents_job,
)
from worker.events import jobs as events_jobs
from worker.flows import jobs as flows
from worker.groups.jobs import groups_changed
from worker.migrations import jobs as migrations_jobs
from worker.migrations.jobs import (
    create_document_actions_table_partition_job,
    create_user_actions_table_partition_job,
    migrate_sign_review_permission,
)
from worker.mobile import jobs as mobile
from worker.notifications.jobs import (
    cleanup_unsubscriptions,
    send_sms_to_document_recipient,
)
from worker.profile import jobs as profile
from worker.reviews import jobs as reviews
from worker.roles import jobs as roles_jobs
from worker.signatures import jobs as signatures
from worker.tags.jobs import (
    create_document_access_on_document_tag__part1,
    create_document_access_on_document_tag__part2,
    create_document_access_on_role_tag_job,
    create_documents_tags_by_contact,
)
from worker.temp.jobs import remove_old_s3_files
from worker.trigger_notification import jobs as trigger_notification
from worker.trigger_notification.jobs import (
    create_trigger_notification,
    delete_fulfilled_trigger_notification,
    delete_old_trigger_notification,
    update_trigger_notification_about_invite,
)
from worker.types import JobFunction, WorkerType
from worker.vchasno_profile import jobs as vchasno_profile

# We are separating some jobs from main jobs list in order to
# load balance some high-loaded topics by deploying workers
# which subscribed only to that topics to avoid timeout/lock
ANTIVIRUS_JOBS: dict[str, JobFunction] = {
    t.ANTIVIRUS_CHECK: antivirus_check,
    t.ANTIVIRUS_CHECK_DRAFT: antivirus_check_draft,
}

REPORT_JOBS: dict[str, JobFunction] = {
    # Generate reports about documents/users actions
    t.USER_ACTIONS_REPORT_PREPARE: events_jobs.prepare_user_actions_report_files,
    t.DOCUMENT_ACTIONS_REPORT_PREPARE: events_jobs.prepare_document_actions_report_files,
    t.ACTIONS_REPORT_SEND_EMAIL: events_jobs.send_actions_report_email,
    # Generate archives with multiple documents
    t.MULTI_DOWNLOAD: multi_download_archive,
    t.ARCHIVED_DOCUMENTS_DOWNLOAD: prepare_archived_documents_download,
    t.CLEANUP_OLD_ACTIONS_REPORT_FILES: events_jobs.cleanup_old_actions_report_files,
    t.REMOVE_ACTIONS_REPORT_FILE: events_jobs.remove_actions_report_file,
}

DOCUMENT_UPDATE_JOBS: dict[str, JobFunction] = {
    t.UPDATE_DOCS_DATE_DELIVERED: update_docs_date_delivered,
    t.UPDATE_SIGN_SESSION_DOCS_DATE_DELIVERED: update_sign_session_documents_date_delivered,
}

COMMENT_JOBS: dict[str, JobFunction] = {
    t.INDEX_COMMENTS: comments.index_comments,
    t.REMOVE_COMMENTS_FROM_INDEX: comments.remove_comments_from_index,
    t.SEND_COMMENTS_TO_INDEX: comments.send_comments_to_indexation,
}

GENERAL_JOBS: dict[str, JobFunction] = {
    t.UPDATE_COMPANY_ENTITY_COUNT: companies.update_company_entity_count,
    t.CREATE_INITIAL_FREE_RATE: create_initial_free_company_rate,
    t.MIGRATE_TO_NEW_RATE: migrate_to_new_rate,
    t.FILL_COMPANIES_STATS: companies.fill_companies_stats,
    t.BILLS: process_bill,
    t.FETCH_PRIVATBANK_TRANSACTIONS: fetch_privatbank_transactions,
    t.FETCH_PUMB_TRANSACTIONS: fetch_pumb_transactions,
    t.PROCESS_BANK_TRANSACTION: process_activate_bank_transaction,
    t.SEND_PAYMENT_SUCCESSFULL_EMAIL: send_payment_successful_email,
    t.DELETE_BILLING_ACCOUNTS: delete_billing_accounts,
    t.REMIND_ABOUT_EMPLOYEES_EXTENSION_BILL_WHEN_TRIAL_EXPIRES: (
        emailing_jobs.remind_about_employees_extension_bill_when_trial_expires
    ),
    t.SEND_GOOGLE_ANALYTICS_EVENT_FOR_BILL_PAYMENT: (
        analytics.send_google_analytics_event_for_bill_payment
    ),
    t.ACTIVATE_COMPANY_RATES: activate_company_rates,
    t.ACTIVATE_COMPANY_EXTENSIONS: activate_company_extensions,
    t.DEACTIVATE_COMPANY_EXTENSIONS_TRIAL: deactivate_company_extensions_trial,
    t.DEACTIVATE_COMPANY_RATES: deactivate_company_rates,
    t.SA_ACTIVATE_COMPANIES_RATES: sa_activate_trial_company_rate,
    t.UPDATE_HAS_ROLE_SIGNED_DOCUMENTS: signatures.update_has_role_signed_documents,
    t.CREATE_DOCUMENTS_TAGS_BY_CONTACT: create_documents_tags_by_contact,
    t.SEND_TO_INDEXATOR_BY_IDS: send_to_indexator_by_ids,
    t.SEND_TO_INDEXATOR_BY_LISTINGS_IDS: send_to_indexator_by_listings_ids,
    t.REMOVE_DOCUMENTS_FROM_INDEX: remove_documents_from_index,
    t.AUTOSEND_DOCUMENT: autosend_document_job,
    t.SYNC_RECIPIENTS_DATE_RECEIVED: sync_recipients_date_received,
    t.SEND_DOCUMENT_VIEW_LIMIT_NOTIFICATION: send_document_view_limit_notification,
    t.CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG: create_document_access_on_role_tag_job,
    t.CREATE_DOCUMENT_ACCESS_ON_DOCUMENT_TAG_PART_1: create_document_access_on_document_tag__part1,
    t.CREATE_DOCUMENT_ACCESS_ON_DOCUMENT_TAG_PART_2: create_document_access_on_document_tag__part2,
    t.DELETE_DOCUMENT_ACCESS_SOURCE: delete_document_access_source,
    t.DOCUMENT_SIGNATURE_ADDED_EVENT: signatures.send_signature_to_vchasno_projects,
    t.DOCUMENTS_REJECTED_EVENT: signatures.send_documents_rejected_to_hrs,
    # Document Automation
    t.START_DOCUMENT_AUTOMATION: start_document_automation,
    t.FIND_DOCUMENT_AUTOMATION: find_document_automation,
    t.APPLY_DOCUMENT_AUTOMATION_TEMPLATE: apply_document_authomation_template,
    # APPLY_DOCUMENT_TEMPLATE is deprecated
    t.APPLY_DOCUMENT_TEMPLATE: apply_document_authomation_template,
    t.INVITE_COWORKERS: invite_coworkers,
    t.UPDATE_SIGN_SESSION_DOCUMENT_STATUS: set_sign_session_document_status,
    # reviews
    t.SEND_REVIEW_REJECT_NOTIFICATION: reviews.send_review_rejected_notification,
    t.SEND_REVIEW_REQUEST_TELEGRAM: reviews.send_review_request_telegram,
    t.SEND_REVIEW_REQUEST_TRIGGER_NOTIFICATION: reviews.send_review_request_trigger_notification,
    t.START_SENDING_PENDING_REVIEW_REMINDERS: reviews.start_sending_pending_review_reminders,
    t.SEND_PENDING_REVIEW_REMINDER: reviews.send_pending_review_reminder,
    t.SEND_COMMENT_NOTIFICATION: comments.send_comment_notification,
    t.SEND_EMAIL_TO_DOCUMENT_OWNER: send_email_to_document_owner,
    t.SEND_FIRST_NOTIFICATION_TO_SIGNERS: send_first_notification_to_signers,
    t.SEND_NOTIFICATION_TO_SIGNERS: send_notification_to_signers,
    t.SEND_NOTIFICATION_ABOUT_DOCUMENT_ACCESS: send_notification_about_document_access,
    t.SEND_MULTILATERAL_DOCUMENT_TO_RECIPIENTS: flows.send_multilateral_document_to_recipients,
    t.SEND_MULTILATERAL_DOCUMENT_NOTIFICATION: flows.send_multilateral_document_notification,
    t.SEND_DOCUMENT_TO_RECIPIENT: send_document_to_recipient_job,
    t.SEND_SIGN_PROCESS_NOTIFICATION: send_notification_about_sign_process,
    t.SEND_SIGNED_BY_COWORKERS_DOCUMENTS: send_signed_by_coworker_documents_job,
    t.SEND_REMINDERS_TO_UNREGISTERED_USERS: send_reminders_to_unregistered_users,
    t.SEND_SMS_TO_DOCUMENT_RECEIVER: send_sms_to_document_recipient,
    t.SEND_DOCUMENT_STATUS_CALLBACK: send_document_status_callback,
    t.SEND_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS: (
        send_daily_notification_about_finished_documents
    ),
    t.SCHEDULE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS: (
        schedule_daily_notification_about_finished_documents
    ),
    t.SEND_ACCEPTED_DOCUMENTS: reviews.send_accepted_documents_job,
    t.SEND_DAILY_FEEDBACKS: send_daily_feedbacks_job,
    t.SEND_REMINDER_ABOUT_NEW_DOCUMENTS: send_reminder_about_new_documents_job,
    t.SEND_DELETE_REQUEST_NOTIFICATIONS: send_delete_request_notifications_job,
    t.SEND_REJECT_DOCUMENT_NOTIFICATIONS: send_reject_document_notifications_job,
    t.SEND_REMINDER_ABOUT_TOKEN_EXPIRATION: send_reminder_about_token_expiration_job,
    t.SEND_REVOKE_INITIATED_NOTIFICATIONS: send_revoke_initiated_notifications_job,
    t.SEND_REVOKE_REJECTED_NOTIFICATIONS: send_revoke_rejected_notifications_job,
    t.SEND_REVOKE_COMPLETED_NOTIFICATIONS: send_revoke_completed_notifications_job,
    t.SYNC_CONTACTS_FIRST_STEP: sync_contacts_first_step,
    t.SYNC_CONTACTS_SECOND_STEP: sync_contacts_second_step,
    t.SYNC_CONTACTS_THIRD_STEP: sync_contacts_third_step,
    t.SYNC_CONTACTS_COLLECT_FROM_API: sync_contacts_collect_from_api,
    t.SYNC_CONTACTS_SET_RECIPIENTS: sync_contacts_set_recipients_step,
    t.ESPUTNIK_UPDATE_CONTACT: esputnik.update_esputnik_fields,
    t.ESPUTNIK_ADD_CONTACT: esputnik.add_new_esputnik_contact,
    t.ESPUTNIK_COMPANY_CHECK_EVENT: esputnik.generate_esputnik_company_check_event,
    t.ESPUTNIK_GENERATE_EVENT: esputnik.generate_esputnik_event,
    t.ESPUTNIK_DOCUMENTS_SENT_EVENT: esputnik.generate_esputnik_documents_sent_event,
    t.ESPUTNIK_FIRST_INCOMING_SIGNING_EVENT: (
        esputnik.generate_esputnik_first_incoming_signing_event
    ),
    t.ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT: generate_esputnik_tov_trial_expiring_event,
    t.ESPUTNIK_GENERATE_RATE_EXPIRING_EVENT: generate_esputnik_rate_expiring_event,
    t.ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT: (
        esputnik.generate_esputnik_company_registration_event
    ),
    t.ESPUTNIK_GENERATE_EMPLOYEES_LIMIT_REACHED_EVENT: generate_employees_limit_reached_event,
    t.ESPUTNIK_SEND_EVENT_TO_USERS: send_esputnik_event_to_emails,
    t.ESPUTNIK_SEND_TRIAL_ENABLED_EVENT: send_trial_enabled_event,
    t.ESPUTNIK_NEW_POSITION_SET_EVENT: esputnik.esputnik_new_position_set_event,
    t.ON_DOCUMENT_CHARGE_EVENT: on_document_charge_event,
    t.CRM_SEND_EVENT: crm_send_event,
    t.CRM_SYNC_RATES: crm_sync_rates,
    t.DELETE_OLD_TRIGGER_NOTIFICATION: delete_old_trigger_notification,
    t.CREATE_TRIGGER_NOTIFICATION: create_trigger_notification,
    t.NOTIFICATION_ABOUT_COMPANY_REGISTRATION: (
        trigger_notification.create_about_company_registration
    ),
    t.NOTIFICATION_INVITE_COMPANIES: trigger_notification.create_invite_companies,
    t.UPDATE_NOTIFICATION_INVITE_COMPANIES: update_trigger_notification_about_invite,
    t.DELETE_FULFILLED_TRIGGER_NOTIFICATION: delete_fulfilled_trigger_notification,
    t.ACTIVATE_BANNER: activate_banner,
    t.CREATE_DOCUMENTS_ROLE_ACCESS: create_documents_role_access,
    t.CLEANUP_UNSUBSCRIPTIONS: cleanup_unsubscriptions,
    t.EDI_SEND_DOCUMENT_FINISH_STATUS: send_document_finish_status_to_edi,
    t.EDI_SEND_EDI_REQUEST: send_edi_request,
    t.SEND_DOCUMENTS_TO_INDEXATOR: send_documents_to_indexator,
    t.REINDEX_DOCUMENTS: reindex_documents,
    t.REINDEX_REVIEWED_DOCUMENTS: reindex_documents_with_reviews,
    t.SYNC_COMPANY_INFO_FROM_YOUCONTROL: sync_company_info_from_youcontrol,
    t.GROUPS_CHANGED: groups_changed,
    # contacts
    t.INVITE_UNREGISTERED_CONTACTS: contacts.invite_unregistered_contacts,
    t.INDEX_CONTACT_RECIPIENTS: contacts.index_contact_recipients,
    t.DELETE_CONTACTS_FROM_INDEX: contacts.delete_contacts_from_index,
    t.DELETE_CONTACTS_PERSONS_FROM_INDEX: contacts.delete_contacts_persons_from_index,
    t.REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS: contacts.reindex_contact_recipients_by_contacts,
    t.REINDEX_CONTACTS_RECIPIENTS_BY_COMPANIES: contacts.reindex_contact_recipients_by_companies,
    t.REINDEX_CONTACTS_RECIPIENTS_BY_ROLES: contacts.reindex_contact_recipients_by_roles,
    # roles
    t.RECALCULATE_SIGNATURES_COUNT_FOR_USEFUL_META: (
        roles_jobs.recalculate_signatures_count_for_useful_meta
    ),
    t.RECALCULATE_REVIEWS_COUNT_FOR_USEFUL_META: (
        roles_jobs.recalculate_reviews_count_for_useful_meta
    ),
    t.CLEANUP_OLD_MOBILE_NOTIFICATIONS: mobile.cleanup_old_mobile_notifications,
    t.DELETE_REVIEW_REQUESTS_DUPLICATES: reviews.delete_review_requests_duplicates,
    t.USER_EMAIL_CHANGE_UPDATE_RECIPIENTS: profile.user_email_change_update_recipients,
    t.USER_EMAIL_CHANGE_UPDATE_AUTOMATIONS: profile.user_email_change_update_automations,
    t.USER_EMAIL_CHANGE_UPDATE_CONTACTS: profile.user_email_change_update_contacts,
    t.USER_EMAIL_CHANGE_NOTIFY_ADMINS: profile.user_email_change_notify_admins,
    t.MIGRATION_ENABLE_PHONE_AUTH: profile.migration_enable_phone_auth,
    t.START_VCHASNO_PROFILE_SYNC: vchasno_profile.start_vchasno_profile_sync,
    t.SEND_VCHASNO_PROFILE_SYNC: vchasno_profile.send_vchasno_profile_sync,
    # Drafts
    t.CLEANUP_OLD_DRAFTS: cleanup_old_drafts,
    # SYSTEM JOBS
    t.PROCESS_DELAYED_TASK: process_delayed_task,
    t.CREATE_DOCUMENT_ACTIONS_TABLE_PARTITION: create_document_actions_table_partition_job,
    t.CREATE_USER_ACTIONS_TABLE_PARTITION: create_user_actions_table_partition_job,
    t.MIGRATE_LATEST_RECIPIENTS: migrations_jobs.migrate_latest_recipient,
    t.MIGRATE_SIGN_REVIEW_PERMISSION: migrate_sign_review_permission,
    # Email reminders
    t.INITIATE_LOW_BALANCE_BILLING_NOTIFICATIONS: (
        emailing_jobs.initiate_low_balance_billing_notifications
    ),
    t.SEND_LOW_BALANCE_BILLING_NOTIFICATION: emailing_jobs.send_low_balance_billing_notification,
    t.INITIATE_FALLBACK_INBOX_DOCUMENT_NOTIFICATIONS: (
        emailing_jobs.initiate_fallback_inbox_document_notifications
    ),
    t.SEND_INBOX_FALLBACK_DOCUMENT_NOTIFICATIONS: (
        emailing_jobs.send_fallback_inbox_document_notification
    ),
    t.SEND_BLACKBOX_INBOX_DOCUMENTS_NOTIFICATION: (
        emailing_jobs.send_blackbox_inbox_documents_notification
    ),
    t.INITIATE_REMINDERS_FOR_UNSIGNED_DOCUMENTS: (
        emailing_jobs.initiate_reminders_for_unsigned_documents
    ),
    t.SEND_REMINDER_FOR_UNSIGNED_DOCUMENTS: emailing_jobs.send_reminder_for_unsigned_documents,
    t.GATHER_REMINDERS_FOR_UNPAID_BILLS: emailing_jobs.gather_reminders_for_unpaid_bills,
    t.INITIATE_REMINDERS_FOR_UNPAID_BILL: emailing_jobs.initiate_reminders_for_unpaid_bill,
    t.SEND_REMINDER_FOR_UNPAID_BILL: emailing_jobs.send_reminder_for_unpaid_bill,
    t.INITIATE_NEW_REVIEW_REQUEST_EMAILS: emailing_jobs.initiate_emails_for_new_review_request,
    t.SEND_NEW_REVIEW_REQUEST_EMAIL: emailing_jobs.send_email_for_new_review_request,
    t.SEND_NEW_REVIEW_REQUEST_MOBILE_PUSH_NOTIFICATION: (
        emailing_jobs.send_mobile_push_notification_for_new_review_request
    ),
    t.INITIATE_REMINDERS_FOR_ABANDONED_REGISTRATION: (
        emailing_jobs.initiate_notifications_about_abandoned_registration
    ),
    t.SEND_REMINDER_FOR_ABANDONED_REGISTRATION: (
        emailing_jobs.send_notification_about_abandoned_registration
    ),
    t.INITIATE_NEW_DOCUMENT_VERSION_NOTIFICATIONS: (
        emailing_jobs.initiate_new_document_version_notifications
    ),
    t.SEND_NEW_DOCUMENT_VERSION_NOTIFICATION: emailing_jobs.send_new_document_version_notification,
    t.INITIATE_REMINDERS_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE: (
        emailing_jobs.initiate_reminders_for_new_successfully_registered_employee
    ),
    t.SEND_REMINDER_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE: (
        emailing_jobs.send_reminder_for_new_successfully_registered_employee
    ),
    t.INITIATE_REMINDERS_FOR_FAILED_TO_REGISTER_EMPLOYEE: (
        emailing_jobs.initiate_reminders_for_failed_to_register_employee
    ),
    t.SEND_REMINDER_FOR_FAILED_TO_REGISTER_EMPLOYEE: (
        emailing_jobs.send_reminder_for_failed_to_register_employee
    ),
    t.SYNC_USER_ROLES: auth_jobs.sync_user_roles,
}

# CPU bound jobs, better process without concurrency
DOCUMENT_PROCESS_JOBS: dict[str, JobFunction] = {
    # Temp job, also may be in that topic
    t.REMOVE_OLD_S3_FILES: remove_old_s3_files,
}

ALL_JOBS = (
    GENERAL_JOBS
    | DOCUMENT_UPDATE_JOBS
    | ANTIVIRUS_JOBS
    | REPORT_JOBS
    | COMMENT_JOBS
    | DOCUMENT_PROCESS_JOBS
)


def get_topic_jobs() -> dict[str, JobFunction]:
    """
    Returns jobs list accordingly to specific worker
    """

    worker_type_to_jobs_map = {
        WorkerType.all_jobs: ALL_JOBS,
        WorkerType.general: GENERAL_JOBS,
        WorkerType.antivirus: ANTIVIRUS_JOBS,
        WorkerType.reports: REPORT_JOBS,
        WorkerType.document_update: DOCUMENT_UPDATE_JOBS,
        WorkerType.comments: COMMENT_JOBS,
        WorkerType.document_process: DOCUMENT_PROCESS_JOBS,
    }

    if worker_type := os.getenv('WORKER_TYPE'):
        return worker_type_to_jobs_map[WorkerType(worker_type)]
    return ALL_JOBS


TOPIC_JOBS = get_topic_jobs()
