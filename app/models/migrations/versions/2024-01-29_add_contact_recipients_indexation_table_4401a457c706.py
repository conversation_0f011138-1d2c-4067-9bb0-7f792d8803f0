"""Add contact_recipients_indexation table

Revision ID: 4401a457c706
Revises: 2ad4eaec46d0
Create Date: 2024-01-29 11:57:10.942333

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = '4401a457c706'
down_revision = '2ad4eaec46d0'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('CREATE SEQUENCE contact_recipients_indexation_seqnum;')
    op.create_table(
        'contact_recipients_indexation',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('entity_type', SoftEnum(DummyEnum), nullable=False),
        sa.Column(
            'entity_id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column(
            'iteration',
            sa.Integer(),
            server_default=sa.text('0'),
            nullable=False,
        ),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'seqnum',
            sa.BigInteger(),
            server_default=sa.text("nextval('contact_recipients_indexation_seqnum')"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_contact_recipients_indexation_entity_id'),
        'contact_recipients_indexation',
        ['entity_id'],
        unique=False,
    )
    op.create_index(
        op.f('ix_contact_recipients_indexation_seqnum'),
        'contact_recipients_indexation',
        ['seqnum'],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f('ix_contact_recipients_indexation_seqnum'),
        table_name='contact_recipients_indexation',
    )
    op.drop_index(
        op.f('ix_contact_recipients_indexation_entity_id'),
        table_name='contact_recipients_indexation',
    )
    op.drop_table('contact_recipients_indexation')
