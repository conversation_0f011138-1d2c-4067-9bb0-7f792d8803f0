from aiohttp import web

from api import errors
from api.utils import api_response
from app.auth import types as auth_types
from app.i18n import _
from app.lib.validators import validate_json_request
from app.mobile import constants
from app.mobile.auth import decorators as mobile_decorators
from app.mobile.auth import types as mobile_auth_types
from app.mobile.notifications import db, types, utils, validators
from app.services import services


@mobile_decorators.mobile_login_required()
async def list_mobile_notifications(request: web.Request, user: auth_types.User) -> web.Response:
    """
    List mobile push notifications
    """

    options = validators.validate_list_mobile_notifications(request)

    async with services.db.acquire() as conn:
        notifications = await db.select_mobile_notifications_by_role_id(
            conn=conn,
            role_id=user.role_id,
            status=options.status,
            limit=options.limit,
            offset=options.offset,
        )

    response = [notification.to_response() for notification in notifications]
    return api_response(request, response)


@mobile_decorators.mobile_login_required()
async def update_mobile_notification_status(
    request: web.Request, user: auth_types.User
) -> web.Response:
    """
    Update mobile notification status
    """

    notification_id = request.match_info['notification_id']
    data = await validate_json_request(request)

    valid_data = await validators.validate_update_mobile_notification_status(
        notification_id=notification_id, raw_data=data
    )

    async with services.db.acquire() as conn:
        await db.update_mobile_notification_status(
            conn=conn,
            notification_id=notification_id,
            status=valid_data.status,
        )

    return api_response(request=request, data={})


@mobile_decorators.mobile_login_required()
async def mark_all_notifications_as_seen(
    request: web.Request, user: auth_types.User
) -> web.Response:
    """
    Mark all role's push notifications as seen
    """
    async with services.db.acquire() as conn:
        await db.update_mobile_notification_statuses_for_role_to_seen(
            conn=conn,
            role_id=user.role_id,
        )

    return api_response(request=request, data={})


@mobile_decorators.mobile_login_required()
async def count_mobile_notifications(request: web.Request, user: auth_types.User) -> web.Response:
    """
    Count notifications for role with certain status
    """

    status_raw = request.rel_url.query.get('status')
    if not status_raw:
        raise errors.InvalidRequest(reason=_('Статус сповіщення повинен бути переданий'))

    try:
        status = types.MobileNotificationStatus(status_raw)
    except ValueError:
        raise errors.InvalidRequest(reason=_('Невідомий статус сповіщення'))

    async with services.db.acquire() as conn:
        count = await db.count_mobile_notifications_for_role(
            conn=conn,
            role_id=user.role_id,
            status=status,
        )

    return api_response(request=request, data={'status': status, 'count': count})


@mobile_decorators.mobile_base_login_required()
async def set_firebase_id(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    """
    Set firebase id for an instance of mobile app.
    Firebase id stores in mobile_auth_refresh_tokens table.
    """

    raw_data = await validate_json_request(request)
    token: mobile_auth_types.MobileAuthAccessToken = request[constants.AUTH_ACCESS_TOKEN]

    ctx = validators.validate_set_firebase_id(raw_data)

    async with services.db.acquire() as conn:
        await utils.process_set_firebase_id(
            conn=conn,
            token_id=token.auth_refresh_token_id,
            firebase_id=ctx.firebase_id,
        )

    return api_response(request, {})
