"""Delete old companies table.

Revision ID: 9fd82b6968e3
Revises: 51b0493d5bc4
Create Date: 2017-06-07 12:23:46.557994


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9fd82b6968e3'
down_revision = '51b0493d5bc4'
branch_labels = None
depends_on = None

def upgrade():
    op.execute('DROP TABLE IF EXISTS companies')


def downgrade():
    pass
