import React from 'react';

import { t } from 'ttag';

import Button from '../../ui/button/button';

import {
    BASE_PRICE,
    INTEGRATION_PRICE_DISPLAY,
    INTEGRATION_PRICE_YEARLY,
    INTEGRATION_RATE_CONSULTATION_URL,
    INTEGRATION_URL,
    PRO_PRICE,
    PRO_URL,
    START_PRICE,
    START_URL,
} from '../../../lib/constants';
import { formatPrice } from '../../../lib/numbers';
import eventTracking from '../../../services/analytics/eventTracking';

export type RateFeature =
    | 'signIncomeDocuments'
    | 'signOutcomeDocuments'
    | 'comments'
    | 'antivirusCheck'
    | 'cloudDocumentStorage'
    | 'documentsView'
    | 'internalDocuments'
    | 'internalReview'
    | 'employee2FA'
    | 'tags'
    | 'customDocumentParams'
    | 'documentScripts'
    | 'documentRequiredFields'
    | 'keysKEP'
    | 'perDocumentPayment'
    | 'individualEmployeePermissions'
    | 'signOutcomeAndIncomeDocuments';

export type RateFeatureValue = string | boolean;

export type RateDescription = {
    title: string;
    price: React.ReactNode | string;
    priceTime?: string;
    pricePerYear?: string;
    specification?: string;
    subPrice?: string;
    features: Partial<Record<RateFeature, RateFeatureValue>>;
    subtitle: string;
    links: React.ReactNode | React.ReactNode[];
    label?: React.ReactNode;
    isHighlight?: boolean;
    isNew?: boolean;
};

export const featureLabelPopoverMap: Record<string, string> = {
    documentsView: t`Сервіс «Вчасно» зберігає в архіві всі документи вашої компанії. Обмеження впливає лише на кількість доступних для перегляду документів згідно з обраним тарифом.`,
};

export const webFeatureList: RateFeature[] = [
    'signIncomeDocuments',
    'signOutcomeDocuments',
    'comments',
    'antivirusCheck',
    'cloudDocumentStorage',
    'documentsView',
    'individualEmployeePermissions',
    'internalDocuments',
    'internalReview',
    'employee2FA',
    'tags',
    'customDocumentParams',
    'documentScripts',
    'documentRequiredFields',
    'keysKEP',
];

export const integrationFeatureList: RateFeature[] = [
    'signIncomeDocuments',
    'signOutcomeDocuments',
    'comments',
    'perDocumentPayment',
];

export const generateProRates = (
    registrationURL: string,
    isLanding?: boolean,
): RateDescription[] => [
    {
        title: t`Базовий`,
        subtitle: t`1 співробітник`,
        price: `₴ ${BASE_PRICE.toLocaleString()}`,
        features: {
            signIncomeDocuments: t`Без обмежень`,
            signOutcomeDocuments: t`25 на рік`,
            comments: true,
            antivirusCheck: true,
            cloudDocumentStorage: true,
            documentsView: t`50 останніх документів`,
            individualEmployeePermissions: false,
            internalDocuments: false,
            internalReview: false,
            employee2FA: false,
            tags: false,
            customDocumentParams: false,
            documentScripts: false,
            documentRequiredFields: false,
            keysKEP: false,
        },
        links: (
            <a
                href={registrationURL}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() =>
                    eventTracking.sendEvent(
                        'rates',
                        isLanding
                            ? 'get-btn-click-on-landing'
                            : 'get-btn-click-on-rate-page',
                        'base',
                    )
                }
            >
                <Button width="full" theme="blue" typeContour>
                    {t`Почати безкоштовно`}
                </Button>
            </a>
        ),
    },
    {
        title: t`Старт`,
        subtitle: t`до 2 співробітників включно`,
        price: `₴ ${(START_PRICE / 12).toLocaleString()}`,
        priceTime: t`/міс.`,
        pricePerYear: `${t`при оплаті`} ${START_PRICE.toLocaleString()} ${t`грн/рік`}`,
        specification: t`(Вартість з ПДВ)`,
        features: {
            signIncomeDocuments: t`Без обмежень`,
            signOutcomeDocuments: t`250 на рік`,
            comments: true,
            antivirusCheck: true,
            cloudDocumentStorage: true,
            documentsView: t`Без обмежень`,
            individualEmployeePermissions: false,
            internalDocuments: false,
            internalReview: false,
            employee2FA: false,
            tags: false,
            customDocumentParams: false,
            documentScripts: false,
            documentRequiredFields: false,
            keysKEP: false,
        },
        links: (
            <a
                href={START_URL}
                rel="noopener noreferrer"
                target="_blank"
                onClick={() => {
                    eventTracking.sendEvent(
                        'rates',
                        isLanding
                            ? 'get-btn-click-on-landing'
                            : 'get-btn-click-on-rate-page',
                        'start',
                    );
                    eventTracking.sendToGTM({
                        event: 'tarif_click',
                        category: 'tarif_click',
                        action: 'start',
                    });
                }}
            >
                <Button width="full" theme="cta">
                    {t`Купити`}
                </Button>
            </a>
        ),
    },
    {
        title: t`Професійний`,
        subtitle: t`до 3 співробітників включно`,
        price: `₴ ${(PRO_PRICE / 12).toLocaleString()}`,
        priceTime: t`/міс.`,
        isHighlight: true,
        pricePerYear: `${t`при оплаті`} ${PRO_PRICE.toLocaleString()} ${t`грн/рік`}`,
        specification: t`(Вартість з ПДВ)`,
        features: {
            signIncomeDocuments: t`Без обмежень`,
            signOutcomeDocuments: t`2000 на рік`,
            comments: true,
            antivirusCheck: true,
            cloudDocumentStorage: true,
            documentsView: t`Без обмежень`,
            individualEmployeePermissions: true,
            internalDocuments: true,
            internalReview: true,
            employee2FA: true,
            tags: true,
            customDocumentParams: false,
            documentScripts: false,
            documentRequiredFields: false,
            keysKEP: t`2 комплекта ключів`,
        },
        links: (
            <a
                href={PRO_URL}
                rel="noopener noreferrer"
                target="_blank"
                onClick={() => {
                    eventTracking.sendEvent(
                        'rates',
                        isLanding
                            ? 'get-btn-click-on-landing'
                            : 'get-btn-click-on-rate-page',
                        'pro',
                    );
                    eventTracking.sendToGTM({
                        event: 'tarif_click',
                        category: 'tarif_click',
                        action: 'pro',
                    });
                }}
            >
                <Button width="full" theme="cta">
                    Купити
                </Button>
            </a>
        ),
    },
    {
        title: t`Максимальний`,
        subtitle: t`від 3 співробітників`,
        price: <span style={{ fontSize: 24 }}>{t`Договірна`}</span>,
        isNew: true,
        features: {
            signIncomeDocuments: t`Без обмежень`,
            signOutcomeDocuments: t`Без обмежень`,
            comments: true,
            antivirusCheck: true,
            cloudDocumentStorage: true,
            documentsView: t`Без обмежень`,
            individualEmployeePermissions: true,
            internalDocuments: true,
            internalReview: true,
            employee2FA: true,
            tags: true,
            customDocumentParams: true,
            documentScripts: true,
            documentRequiredFields: true,
            keysKEP: t`3 комплекта ключів`,
        },
        links: (
            <a
                href={INTEGRATION_RATE_CONSULTATION_URL}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() =>
                    eventTracking.sendEvent(
                        'rates',
                        isLanding
                            ? 'get-btn-click-on-landing'
                            : 'get-btn-click-on-rate-page',
                        'maximal',
                    )
                }
            >
                <Button width="full" theme="blue" typeContour>
                    {t`Консультація`}
                </Button>
            </a>
        ),
    },
];

export const generateIntegrationRates = (
    isLanding?: boolean,
): RateDescription[] => [
    {
        title: t`Інтеграція`,
        subtitle: t`Робота з вашої облікової системи`,
        price: (
            <>
                ₴ {formatPrice(INTEGRATION_PRICE_YEARLY)}{' '}
                <small style={{ fontSize: 14, fontWeight: 400 }}>
                    - {t`Доступ до АРІ`}
                </small>
                <br />+ {INTEGRATION_PRICE_DISPLAY}
                <small style={{ fontSize: 14, fontWeight: 400 }}>
                    {t`грн/документ`}
                </small>
                <br />
                <div
                    style={{
                        fontSize: 12,
                        fontWeight: 400,
                        textAlign: 'center',
                        color: '#BFCEDE',
                    }}
                >{t`(Вартість з ПДВ)`}</div>
                <div style={{ marginTop: 15 }}>
                    <a
                        href={INTEGRATION_URL}
                        rel="noopener noreferrer"
                        target="_blank"
                        onClick={() =>
                            eventTracking.sendEvent(
                                'rates',
                                isLanding
                                    ? 'get-btn-click-on-landing'
                                    : 'get-btn-click-on-rate-page',
                                'integration',
                            )
                        }
                        key="INTEGRATION_BUY"
                    >
                        <Button width="full" theme="cta">
                            {t`Підключити`}
                        </Button>
                    </a>
                </div>
            </>
        ),
        pricePerYear: ``,
        features: {
            signIncomeDocuments: true,
            signOutcomeDocuments: true,
            perDocumentPayment: true,
            comments: true,
        },
        links: [
            <a
                href={INTEGRATION_RATE_CONSULTATION_URL}
                rel="noopener noreferrer"
                target="_blank"
                onClick={() =>
                    eventTracking.sendEvent(
                        'rates',
                        isLanding
                            ? 'get-btn-click-on-landing'
                            : 'get-btn-click-on-rate-page',
                        'integration-consult',
                    )
                }
                key="INTEGRATION_CRM_FORM"
            >
                <Button width="full" theme="blue" typeContour>
                    {t`Консультація по інтеграції`}
                </Button>
            </a>,
        ],
    },
];
