.block {
    margin-top: 20px;
}

.fieldBlock + .fieldBlock {
    margin-top: 20px;
}

.alertFieldBlock {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background: var(--pink-color);
    border-radius: var(--border-radius);
    font-weight: 700;
}

.inputLine {
    display: flex;
    width: 100%;
    align-items: center;
}

.input {
    width: 100%;
}

.deleteField {
    width: 20px;
    margin-left: 5px;
}

.title {
    margin-bottom: 10px;
    font-weight: bold;
}

.required {
    position: relative;
    top: -3px;
    left: 2px;
    color: var(--red-color);
}

.checkbox {
    margin-top: 10px;
}
