.container {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f3f6fb;
    border-radius: 8px;
    gap: 8px;
}

:global(.vchasno-dark-theme) .container {
    background: var(--dark-3-color);
}

.errorContainer {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border: none;
    background: #f3f6fb;
    border-radius: 8px;
    cursor: pointer;
    gap: 8px;
}

:global(.vchasno-dark-theme) .errorContainer {
    background: var(--dark-3-color);
}

.errorText {
    color: var(--grey-color);
    font-size: 12px;
}

.retryButton {
    padding: 4px 8px;
    background: var(--orange-bg);
    border-radius: 24px;
    color: #fff;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.retryButton:hover {
    background: var(--hover-orange-bg);
}

.loadingText {
    color: var(--grey-color);
    font-size: 12px;
}

.qrContainer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 4px;
}

.staleOverlay {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    border: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    inset: 0;
}

:global(.vchasno-dark-theme) .staleOverlay {
    background: rgba(0, 0, 0, 0.95);
}
