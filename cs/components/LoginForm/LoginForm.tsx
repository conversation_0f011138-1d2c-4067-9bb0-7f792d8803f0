import React, { useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { Link, useHistory } from 'react-router-dom';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox';
import { AUTH_ENTRYPOINT_PATH } from 'components/auth';
import { TOO_MANY_REQUEST_ERROR_CODE } from 'components/auth/constants';
import { loadAuthFormStorageItem } from 'components/auth/utils';
import PageTitle from 'components/pageTitle/pageTitle';
import { REQUEST_LOCK_DELAY } from 'constants/auth';
import { useInterval } from 'lib/reactHelpers/hooks';
import {
    getLocationQuery,
    stringifyLocationQuery,
    updateQueryParamsPath,
} from 'lib/url';
import eventTracking from 'services/analytics/eventTracking';
import auth from 'services/auth';
import { t } from 'ttag';
import { Nullable } from 'types/general';
import Alert from 'ui/Alert/Alert';
import BackButton from 'ui/BackButton/BackButton';
import Button from 'ui/button/button';
import Checkbox from 'ui/checkbox/checkbox';
import OutlinedInput from 'ui/input/OutlinedInput/OutlinedInput';

import { LoginFormFields } from './types';

import { ERROR_MESSAGE, HANDLED_ERRORS_BY_CODES } from './constants';
import { useLoginSuccessEffect } from './useLoginSuccessEffect';
import { loginFormResolver } from './validation';

import css from './LoginForm.css';

const LoginForm: React.FC = () => {
    const [commonErrorMessage, setCommonErrorMessage] = useState<string>('');
    const [requestLockTime, setRequestLockTime] = useState<Nullable<number>>(
        null,
    );

    const history = useHistory();
    const searchQuery = getLocationQuery(history.location);

    const initialFormData = loadAuthFormStorageItem();

    const {
        control,
        handleSubmit,
        formState,
        reset,
    } = useForm<LoginFormFields>({
        resolver: loginFormResolver,
        defaultValues: {
            password: '',
            remember: true,
        },
    });
    const loginSuccessEffect = useLoginSuccessEffect();

    const isDisabledLoginButton = formState.isSubmitting || !!requestLockTime;
    const isRequestLockTimeMoreMinute =
        !!requestLockTime && requestLockTime > 60;
    const requestLockTimeText = isRequestLockTimeMoreMinute
        ? t`годину`
        : t`${requestLockTime} с.`;

    const onSubmit: SubmitHandler<LoginFormFields> = async ({
        password,
        remember,
    }) => {
        if (!initialFormData?.login) {
            onBackHandler();
            return;
        }

        try {
            setCommonErrorMessage('');

            const {
                next_url: nextUrl,
                is_2fa_enabled: is2faEnabled,
            } = await auth.login({
                email: initialFormData?.login,
                password,
                remember,
            });
            const url = updateQueryParamsPath(nextUrl, searchQuery);
            loginSuccessEffect({
                nextUrl: url,
                is2FAEnabled: is2faEnabled,
                method: 'email',
            });
            eventTracking.sendToGTM({
                event: 'funnel_reg_step_2_email',
                action: 'success',
                category: 'login',
            });
        } catch (error) {
            eventTracking.sendToGTM({
                event: 'funnel_reg_step_2_email',
                action: 'fail',
                category: 'login',
            });
            reset();
            const errorMessage = HANDLED_ERRORS_BY_CODES.includes(error.code)
                ? error.reason
                : ERROR_MESSAGE;

            if (error.code === TOO_MANY_REQUEST_ERROR_CODE) {
                setRequestLockTime(REQUEST_LOCK_DELAY);
                return;
            }

            setCommonErrorMessage(errorMessage);
        }
    };

    const onBackHandler = () => {
        history.push({
            pathname: AUTH_ENTRYPOINT_PATH,
            search: stringifyLocationQuery(searchQuery),
        });
    };

    useInterval(
        () => {
            if (!requestLockTime) {
                return;
            }
            setRequestLockTime(requestLockTime - 1);
        },
        requestLockTime ? 1000 : null,
    );

    useEffect(() => {
        if (!initialFormData?.login) {
            onBackHandler();
        }
    }, [initialFormData]);

    return (
        <form id="login" onSubmit={handleSubmit(onSubmit)}>
            <PageTitle>{t`Вхід`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    <FlexBox className={css.header} direction="column" gap={27}>
                        <BackButton
                            className={css.backBtn}
                            onClick={onBackHandler}
                        />
                        <PartnerLogos className={css.partnersLogos} />
                        <h1 className={css.title}>
                            <span
                                role="img"
                                aria-label={t`Раді вас бачити знову!`}
                            >
                                ✌️
                            </span>{' '}
                            {t`Раді вас бачити знову!`}
                        </h1>
                    </FlexBox>
                    <FlexBox direction="column" align="flex-start">
                        <input
                            className={css.hiddenInput}
                            id="email"
                            type="email"
                            value={initialFormData?.login}
                        />
                        <Controller
                            control={control}
                            name="password"
                            render={({ field, fieldState }) => (
                                <OutlinedInput
                                    id="password"
                                    autoComplete="current-password"
                                    type="password"
                                    name="password"
                                    required
                                    label={t`Пароль`}
                                    value={field.value}
                                    onChange={(event) =>
                                        field.onChange(
                                            event.target.value.trim(),
                                        )
                                    }
                                    autoFocus
                                    error={fieldState.error?.message}
                                />
                            )}
                        />
                        <Link
                            className={css.remindPasswordLink}
                            to={{
                                pathname: '/auth/password/remind',
                                search: stringifyLocationQuery(searchQuery),
                            }}
                        >
                            {t`Забули пароль?`}
                        </Link>
                    </FlexBox>
                    {!!requestLockTime && (
                        <Alert theme="error" hideIcon>
                            {t`Ви використали усі спроби на введення паролю. Спробуйте через ${requestLockTimeText}`}
                        </Alert>
                    )}
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <div className={css.submitBtnContainer}>
                        <Button
                            className={css.submitBtn}
                            type="submit"
                            theme="darkGray"
                            isLoading={formState.isSubmitting}
                            disabled={isDisabledLoginButton}
                        >
                            {t`Увійти`}
                        </Button>
                        <Controller
                            control={control}
                            name="remember"
                            render={({ field }) => (
                                <Checkbox
                                    checked={field.value}
                                    onChange={field.onChange}
                                    color="deepSkyBlue"
                                    text={t`Запам’ятати мене`}
                                />
                            )}
                        />
                    </div>
                </div>
            </FlexBox>
        </form>
    );
};

export default LoginForm;
