import React from 'react';

import { t } from 'ttag';
import Icon from 'ui/icon';

import { EmployeesSectionTabConfig } from './types';

import { getDisplayCount } from './utils';

import EmployeesReviewersSection from '../EmployeesReviewersSection';
import EmployeesShareSection from '../EmployeesShareSection';
import EmployeesSignSection from '../EmployeesSignSection';

import AccessSVG from './icons/access.svg';
import AgreementSVG from './icons/ageement.svg';
import SignSVG from './icons/sign.svg';

export const tabItems: ReadonlyArray<EmployeesSectionTabConfig> = [
    {
        key: 'employeesSigners',
        label: (methods) => {
            const count = getDisplayCount(methods, 'employeesSigners');

            return t`Підписання ${count}`;
        },
        Component: EmployeesSignSection,
        icon: <Icon glyph={SignSVG} />,
    },
    {
        key: 'employeesReviewers',
        label: (methods) => {
            const count = getDisplayCount(methods, 'employeesReviewers');
            return t`Погодження ${count}`;
        },
        Component: EmployeesReviewersSection,
        icon: <Icon glyph={AgreementSVG} />,
    },
    {
        key: 'employeesViewers',
        label: (methods) => {
            const count = getDisplayCount(methods, 'employeesViewers');

            return t`Доступ ${count}`;
        },
        Component: EmployeesShareSection,
        icon: <Icon glyph={AccessSVG} />,
    },
];
