import os
import time
import uuid


def uuid7() -> uuid.UUID:
    """
    Naive UUIDv7 implementation.
    Does not fully follow the specs, but is good enough for most use cases.

    Consider using CPython's uuid7 implementation when move to 3.14+

    Python implementation:
    https://github.com/python/cpython/blob/ba11f45dd969dfb039dfb47270de4f8c6a03d241/Lib/uuid.py#L840
    """
    timestamp_ms = int(time.time() * 1000)
    timestamp_bytes = timestamp_ms.to_bytes(6, byteorder='big')

    counter = int(time.time_ns() % 1000) & 0xFFFF
    counter_bytes = counter.to_bytes(2, byteorder='big')
    random_bytes = os.urandom(8)

    # Start with a bytearray instead of bytes to allow modifications
    uuid_bytes = bytearray(timestamp_bytes + counter_bytes + random_bytes)

    # Modify the bytearray directly
    uuid_bytes[6] = (uuid_bytes[6] & 0x0F) | 0x70  # Set version to 7
    uuid_bytes[8] = (uuid_bytes[8] & 0x3F) | 0x80  # Set variant to RFC 4122

    return uuid.UUID(bytes=bytes(uuid_bytes))
