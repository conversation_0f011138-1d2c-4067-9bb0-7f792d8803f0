.root {
    position: relative;
}

:global(.vchasno-dark-theme) .root {
    --grey-color: var(--content-color);
}

.title {
    margin-bottom: 18px;
    font-weight: bold;
}

.icon {
    display: block;
    width: 20px;
    height: 20px;
    margin-left: auto;
    color: inherit;
    cursor: pointer;
}

.icon svg {
    color: var(--grey-color);
    transition: color 0.3s ease-in-out;
}

.icon:hover svg {
    color: var(--primary-cta-color);
}

.content {
    min-width: 260px;
    max-width: 300px;
    box-sizing: border-box;
    padding: 20px;
}

.item + .item {
    margin-top: 15px;
}

.counterBlock {
    padding: 6px 0;
    margin: 18px 0;
    background: var(--corporate-color);
    border-radius: 15px;
    color: #333;
    text-align: center;
}

.count {
    margin-right: 10px;
    font-weight: bold;
}
