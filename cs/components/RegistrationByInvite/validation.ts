import { yupResolver } from '@hookform/resolvers/yup';

import {
    EMAIL_ERROR_MESSAGE,
    REQUIRED_FIELD_ERROR_MESSAGE,
} from 'components/LoginForm/constants';
import { EMAIL_PATTERN } from 'lib/constants';
import { passwordTest } from 'lib/yup/helpers';
import * as yup from 'yup';

import { RegistrationByInviteFormFields } from './types';

const email = yup
    .string()
    .trim()
    .required(REQUIRED_FIELD_ERROR_MESSAGE)
    .matches(EMAIL_PATTERN, EMAIL_ERROR_MESSAGE);

// Password validation depends on the email domain
const password = yup.string().required().test(passwordTest);

const registrationByInviteFormValidationSchema: yup.ObjectSchema<RegistrationByInviteFormFields> = yup
    .object()
    .shape({
        email,
        password,
    })
    .required();

export const registrationByInviteFormResolver = yupResolver(
    registrationByInviteFormValidationSchema,
);
