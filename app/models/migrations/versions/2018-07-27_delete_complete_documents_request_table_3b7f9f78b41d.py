"""Delete complete documents request table

Revision ID: 3b7f9f78b41d
Revises: 78ed7420b536
Create Date: 2018-07-27 09:58:42.182502


Author: <PERSON>
"""
import citext
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3b7f9f78b41d'
down_revision = '78ed7420b536'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('delete_document_requests',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False
        ),
        sa.Column('document_id', sa.String(length=64), nullable=True),
        sa.Column('initiator_role_id', postgresql.UUID(), nullable=False),
        sa.Column('recipient_role_id', postgresql.UUID(), nullable=True),
        sa.Column('recipient_email', citext.CIText(), nullable=True),
        sa.Column(
            'status',
            postgresql.ENUM(
                'new', 'accepted', 'rejected', name='deleterequeststatus'
            ),
            server_default='new',
            nullable=False
        ),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('reject_message', sa.Text(), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False
        ),
        sa.Column('date_accepted', sa.DateTime(timezone=True), nullable=True),
        sa.Column('date_rejected', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ['document_id'], ['documents.id'], ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['initiator_role_id'], ['roles.id'], ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['recipient_role_id'], ['roles.id'], ondelete='CASCADE'
        ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('delete_document_requests')
    op.execute('drop type deleterequeststatus;')
