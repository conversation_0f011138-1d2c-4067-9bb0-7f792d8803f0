import typing as t

from pydantic import BaseModel, BeforeV<PERSON><PERSON><PERSON>, ConfigDict, Field

from api.graph.constants import DEFAULT_LIST_PAGE_LIMIT, DEFAULT_LIST_PAGE_OFFSET
from app.directories.constants import MAX_DIRECTORY_NAME_LENGTH
from app.lib import validators_pydantic as pv
from app.lib.enums import DocumentListOrder, ListDirection
from app.lib.types import DataDict

OffsetInt = t.Annotated[int, BeforeValidator(lambda v: v or DEFAULT_LIST_PAGE_OFFSET)]
LimitInt = t.Annotated[int, BeforeValidator(lambda v: v or DEFAULT_LIST_PAGE_LIMIT)]


class _BaseDirectorySchema(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True)

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class CreateDirectorySchema(_BaseDirectorySchema):
    name: str = Field(..., min_length=1, max_length=MAX_DIRECTORY_NAME_LENGTH)
    parent_id: int | None = None


class UpdateDirectorySchema(_BaseDirectorySchema):
    id: int
    name: str = Field(..., min_length=1, max_length=MAX_DIRECTORY_NAME_LENGTH)


class DeleteDirectoriesSchema(_BaseDirectorySchema):
    directory_ids: list[int]


class AddDocumentsToDirectorySchema(BaseModel):
    parent_id: int | None
    document_ids: list[pv.UUID] = Field(default_factory=list)
    directory_ids: list[int] = Field(default_factory=list)


class DirectoriesListSearchOptions(BaseModel):
    model_config = ConfigDict(extra='allow')

    search: str | None = None
    parent_id: int | None = Field(None, alias='parentDirectoryId')

    # use same enums as for documents list,
    # because we use same options for ordering documents + directories
    order: DocumentListOrder | None = DocumentListOrder.date
    direction: ListDirection | None = ListDirection.desc

    limit: LimitInt
    offset: OffsetInt

    @property
    def has_extra_search_options(self) -> bool:
        """
        Check if some extra search options provided for search through directories.
        It is possible to get some options related to documents only,
        so for that situation we return True.
        """
        allowed_extra_options = {'sortDate', 'isArchived'}
        if self.model_extra is None:
            return False

        return bool(
            {
                k
                for k, v in self.model_extra.items()
                if v is not None and k not in allowed_extra_options
            }
        )
