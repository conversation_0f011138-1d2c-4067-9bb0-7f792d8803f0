import React, {
    ChangeEvent,
    FormEvent,
    ReactNode,
    RefObject,
    useState,
} from 'react';
import ReactDOM from 'react-dom';
import MediaQuery from 'react-responsive';
import { useHistory } from 'react-router-dom';

import { BlackTooltip, Button, Text, TextInput } from '@vchasno/ui-kit';

import { MEDIA_WIDTH } from 'lib/constants';
import { useIsMounted } from 'lib/reactHelpers/hooks';
import { isValidCompanyIdentifyCode } from 'lib/validators';
import io from 'services/io';
import { t } from 'ttag';
import { Nullable } from 'types/general';

import { CheckResult, Status } from './types';

import { trackNewOnBoardingAction } from '../../utils';

import Icon, { Glyph } from '../../../ui/icon/icon';
import PseudoLink from '../../../ui/pseudolink/pseudolink';

import ContractorsUploadPopup from './ContractorsUploadPopup';
import ExistingCompanyTableList from './ExistingCompanyTableList';

import SvgSearch from './images/search.svg';
import SvgSmileError from './images/smile-error.svg';
import SvgSmileNo from './images/smile-no.svg';
import SvgSmileYes from './images/smile-yes.svg';

import css from './ContractorCheck.css';

interface ContractorCheckProps {
    containerRef: RefObject<HTMLDivElement>;
}

const STATUS_TO_SMILE_MAP: Record<Status, Glyph> = {
    success: SvgSmileYes,
    not_registered: SvgSmileNo,
    error: SvgSmileError,
};

const getMessagePropsFromPercentage = (
    percentage: number,
): [Status, string, string?] => {
    if (percentage === 0) {
        return [
            'not_registered',
            '0% ваших контрагентів використовують "Вчасно".',
            'Здається, ви за природою інноватор! Зареєструйтеся та запросіть усіх до нас!',
        ];
    } else if (percentage < 10) {
        return [
            'success',
            `${percentage}% ваших контрагентів використовують "Вчасно".`,
            'Гарний початок!  Зареєструйтеся та запросіть решту!',
        ];
    } else {
        return [
            'success',
            `Ура! ${percentage}% ваших контрагентів вже користуються “Вчасно”`,
        ];
    }
};

const ContractorCheck: React.FC<
    React.PropsWithChildren<ContractorCheckProps>
> = ({ containerRef }) => {
    const history = useHistory();
    const [edrpou, setEdrpou] = useState('');
    const [edrpouValidationError, setValidationError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [status, setStatus] = useState<Nullable<Status>>(null);
    const [message, setMessage] = useState<string | ReactNode>(null);
    const [resultsList, setResultsList] = useState<Array<CheckResult>>([]);
    const [isUploadPopupOpen, setUploadPopupOpen] = useState(false);

    const isMounted = useIsMounted();

    const clearResults = () => {
        if (status) {
            setStatus(null);
            setMessage(null);
            setResultsList([]);
        }
        if (edrpouValidationError) {
            setValidationError('');
        }
    };

    const onInputChange = (evt: ChangeEvent<HTMLInputElement>) => {
        clearResults();
        setEdrpou(evt ? evt.currentTarget.value : '');
    };

    const showMessage = (type: Status, title: string, text?: string) => {
        setStatus(type);
        setMessage(
            <>
                <div className={css.messageTitle}>{title}</div>
                {text && <div className={css.messageText}>{text}</div>}
            </>,
        );
    };

    const showUploadPopup = () => {
        clearResults();
        setUploadPopupOpen(true);
    };

    const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();

        clearResults();

        if (edrpou.trim() === '') {
            setValidationError('Введіть ЄДРПОУ або ІПН компанії в поле пошуку');
            return;
        }

        if (!isValidCompanyIdentifyCode(edrpou.trim())) {
            setValidationError('Невалідний ЄДРПОУ/ІПН');
            return;
        }

        setIsLoading(true);
        try {
            const { is_registered, name } = await io.post(
                '/internal-api/check/company',
                { edrpou },
                true,
            );

            if (!isMounted()) {
                return;
            }

            if (is_registered) {
                trackNewOnBoardingAction('invite-colleague', 'found');
                showMessage('success', 'Ура! Контрагент вже у «Вчасно»');
                setResultsList([{ edrpou, name }]);
            } else {
                trackNewOnBoardingAction('invite-colleague', 'not-found');
                showMessage(
                    'not_registered',
                    'Контрагент ще не користується «Вчасно»',
                );
            }
        } catch (err) {
            showMessage('error', err.message);
        }
        setIsLoading(false);
    };

    const onUploadContactsFile = async (file: File) => {
        setIsLoading(true);
        try {
            const checkResult = await io.postFile(
                '/internal-api/check/company/upload',
                file,
            );

            if (!isMounted()) {
                return;
            }

            const percentage = parseInt(checkResult.percentage, 10);
            showMessage(...getMessagePropsFromPercentage(percentage));

            if (checkResult.companies.length > 0) {
                setResultsList(checkResult.companies);
            }

            trackNewOnBoardingAction('check-contractor-mass');
        } catch (err) {
            showMessage('error', err.message);
        }
        setIsLoading(false);
    };

    const messageBlock = status && (
        <div className={css.message}>
            <div className={css.smileIcon}>
                <Icon glyph={STATUS_TO_SMILE_MAP[status]} />
            </div>
            {message}

            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                {resultsList.length > 1 && (
                    <div className={css.messageButton}>
                        <PseudoLink onClick={() => clearResults()}>
                            {t`Закрити таблицю`}
                        </PseudoLink>
                    </div>
                )}
            </MediaQuery>
        </div>
    );

    return (
        <div className={css.root}>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                {messageBlock}
                {!!resultsList[0] && (
                    <div className={css.singleCompany}>
                        <span className={css.singleCompanyName}>
                            {resultsList[0].name}
                        </span>
                        <span>{resultsList[0].edrpou}</span>
                    </div>
                )}
            </MediaQuery>

            <form className={css.search} onSubmit={handleSubmit}>
                <TextInput
                    startElement={
                        <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                            <Icon
                                style={{ width: 20, height: 20 }}
                                glyph={SvgSearch}
                            />
                        </MediaQuery>
                    }
                    isClearable
                    label={t`ЄДРПОУ або ІПН компанії`}
                    value={edrpou}
                    onChange={onInputChange}
                    error={edrpouValidationError}
                />
                <div className={css.buttonWrapper}>
                    <Button loading={isLoading} type="submit">
                        {t`Перевірити`}
                    </Button>
                </div>
            </form>

            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <div className={css.text}>
                    {t`Щоб перевірити одразу всіх контрагентів,`}{' '}
                    <BlackTooltip
                        title={
                            <>
                                {t`Ви можете завантажити таблицю у форматі`}
                                <br />
                                {t`CSV або XLSX з ЄДРПОУ/ІПН всіх контрагентів`}
                            </>
                        }
                    >
                        <Text type="link" onClick={showUploadPopup}>
                            {t`завантажте перелік контактів`}
                        </Text>
                    </BlackTooltip>
                </div>
                {containerRef.current &&
                    ReactDOM.createPortal(
                        <>
                            {messageBlock}
                            {resultsList.length > 0 && (
                                <ExistingCompanyTableList list={resultsList} />
                            )}
                            {resultsList.length > 10 && (
                                <div hidden>
                                    <PseudoLink
                                        onClick={() => {
                                            trackNewOnBoardingAction(
                                                'redirect-to-company-contacts',
                                            );
                                            history.push(
                                                '/app/settings/contacts',
                                            );
                                        }}
                                    >{t`Перейти в контакти, щоб переглянути весь список?`}</PseudoLink>
                                </div>
                            )}
                        </>,
                        containerRef.current,
                    )}
            </MediaQuery>

            <ContractorsUploadPopup
                isOpen={isUploadPopupOpen}
                isUploadProcess={isLoading}
                onClose={() => setUploadPopupOpen(false)}
                onUploadContactsFile={onUploadContactsFile}
            />
        </div>
    );
};

export default ContractorCheck;
