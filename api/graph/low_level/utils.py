import logging
import re
from collections.abc import Callable
from functools import wraps
from typing import Any

import sqlalchemy as sa
import ujson

from api.graph.types import FieldList
from app.auth.helpers import generate_hash_md5
from app.auth.types import AuthUser, User
from app.documents.enums import DocumentReviewState
from app.documents.tables import listing_table
from app.documents.utils import get_documents_access_filters
from app.documents_fields.db import select_company_documents_fields, select_document_field
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.types import DocumentsField
from app.lib import tracking, validators
from app.lib.constants import TERNARY_STATUS
from app.lib.database import DBConnection
from app.lib.datetime_utils import soft_parse_raw_datetime, soft_to_local_datetime, to_utc_datetime
from app.lib.types import DataDict
from app.lib.validators import int_or_none

DocumentSearchParameter = tuple[DocumentsField, str]
DocumentSearchParameters = list[DocumentSearchParameter]


filter_regex = re.compile(
    r'(?P<folder_id>\d{4})'
    r'(?P<status_id>\d{4})'
    r'((?P<is3p>(0|1|2))'
    r'((?P<is_one_sign>(0|1|2))'
    r'((?P<has_all_signatures>(0|1|2))'
    r'((?P<current_company_flow>(0|1|2))'
    r')?)?)?)?'
)

logger = logging.getLogger(__name__)


def _parse_new_condition(
    condition: str,
) -> tuple[
    int | None,
    int | None,
    bool | None,
    bool | None,
    bool | None,
    bool | None,
]:
    # Try to parse condition filter
    groups = list(filter_regex.finditer(condition))
    if not groups:
        return None, None, None, None, None, None

    parameters = groups.pop().groupdict()
    return (
        int_or_none(parameters.get('folder_id')),
        int_or_none(parameters.get('status_id')),
        TERNARY_STATUS.get(parameters.get('is3p', '2')),
        TERNARY_STATUS.get(parameters.get('is_one_sign', '2')),
        TERNARY_STATUS.get(parameters.get('has_all_signatures', '2')),
        TERNARY_STATUS.get(parameters.get('current_company_flow', '2')),
    )


async def select_document_id_for_graph(
    conn: DBConnection,
    *,
    user: AuthUser | User,
    document_id: str,
) -> str | None:
    """
    This function used to resolve a single document from graph
    """
    query = (
        sa.select([listing_table.c.document_id])
        .select_from(listing_table)
        .where(
            sa.and_(
                listing_table.c.document_id == document_id,
                get_documents_access_filters(user),
            )
        )
        .limit(1)
    )
    return await conn.scalar(query)


def get_options_from_fields(fields: FieldList) -> DataDict:
    return fields[0].options.copy() if fields[0].options else {}


async def get_order_field_for_documents_resolve(
    conn: DBConnection, options: DataDict, user: User | AuthUser
) -> DocumentsField | None:
    # Prepare order field
    order_field_id: str | None = options.get('orderField')
    if not order_field_id:
        return None

    valid_field_id = validators.validate_uuid(order_field_id)
    if not valid_field_id:
        return None

    field = await select_document_field(conn, valid_field_id)
    if not field:
        return None

    if field.company_id != user.company_id:
        return None
    return field


def prepare_review_folder(options: DataDict) -> DocumentReviewState | None:
    value = options.get('reviewFolder', None)
    if not value:
        return None
    try:
        return DocumentReviewState[value]
    except KeyError:
        logger.warning('Unexpected reviewFolder option value', extra={'value': value})
        return None


def parse_search_parameters(queries: list[str]) -> list[tuple[str, str]]:
    """
    Parse list of field id and search value separated via colon symbol -
    {field_id}:{value}
    """
    items = []
    for query in queries:
        parts = query.split('_', maxsplit=1)
        if len(parts) != 2:
            logger.warning(
                'Can not split search parameter query',
                extra={'queries': queries, 'query': query},
            )
            continue

        field_id, value = parts
        if not validators.validate_uuid(field_id):
            logger.warning(
                'Search parameter field id is not valid',
                extra={'queries': queries, 'query': query, 'field_id': field_id},
            )
            continue

        items.append((field_id, value.lower()))

    return items


async def get_search_parameters_for_documents_resolve(
    conn: DBConnection, options: DataDict, user: User | AuthUser
) -> DocumentSearchParameters:
    """Extract, parse and validate search parameters"""
    company_id: str | None = user.company_id

    if not company_id:
        return []

    raw: list[str] | None = options.get('searchParameter')
    if not raw:
        return []

    parsed = parse_search_parameters(raw)
    fields_ids = [field_id for field_id, _ in parsed]

    fields = await select_company_documents_fields(conn, company_id, fields_ids)
    mapping = {field.id_: field for field in fields}

    # Combine field and value
    parameters: DocumentSearchParameters = []
    for field_id, raw_value in parsed:
        field = mapping.get(field_id)
        if not field:
            continue

        # Pay attention that when value is empty string it means that we need
        # to find documents where this field is not set
        value: str = raw_value

        # Parse and normalize value
        if field.type_ == DocumentFieldType.date and raw_value:
            dt = soft_to_local_datetime(soft_parse_raw_datetime(raw_value))
            if dt is not None:
                value = to_utc_datetime(dt).isoformat()
            else:
                continue  # skip parameters that we can't parse

        parameters.append((field, value))

    return parameters


async def get_search_queries(
    conn: DBConnection,
    options: DataDict,
    user: User | AuthUser,
) -> DataDict:
    parameters = await get_search_parameters_for_documents_resolve(conn, options, user=user)
    return {
        'search': options.get('search'),
        'search_titles': options.get('searchTitle') or [],
        'search_numbers': options.get('searchNumber') or [],
        'search_company_names': options.get('searchCompanyName') or [],
        'search_company_edrpous': options.get('searchCompanyEdrpou') or [],
        'search_tags': options.get('searchTag') or [],
        'search_user_emails': options.get('searchUserEmail') or [],
        'search_parameters': parameters,
    }


async def prepare_resolve_es_documents_options(
    conn: DBConnection, options: DataDict, user: User | AuthUser
) -> DataDict:
    options['sign_session_documents_ids'] = (
        user.documents_ids if isinstance(user, AuthUser) else None
    )

    # Prepare order field
    order_field = await get_order_field_for_documents_resolve(conn, options, user)
    review_folder = prepare_review_folder(options)

    search_queries = await get_search_queries(conn, options, user=user)
    return {**options, **search_queries, 'order_field': order_field, 'reviewFolder': review_folder}


def get_should_update_date_delivered_key(role_id: str, options: DataDict) -> str:
    page_fingerprint = generate_hash_md5(ujson.dumps(options, sort_keys=True))
    return f'should_update_date_delivered_{role_id}_{page_fingerprint}'


def track_graph_resolve_time(func: Callable[..., Any]) -> Callable[..., Any]:
    """
    Decorator to track time of graph resolve function.
    """

    @wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Callable[..., Any]:
        with tracking.graphp_resolve_field_duration.time(function=func.__name__):
            return await func(*args, **kwargs)

    return wrapper
