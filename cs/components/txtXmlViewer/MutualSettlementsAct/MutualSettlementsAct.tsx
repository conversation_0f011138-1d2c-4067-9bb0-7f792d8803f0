import React, { FC } from 'react';

import { FlowVarusEdrpouList } from 'components/txtXmlViewer/Comdoc/constants';
import { formatDate } from 'lib/date';
import { formatPrice } from 'lib/numbers';
import { t } from 'ttag';

import { Props } from './types';

import SignLine from '../ui/signLine/signLine';

import uuid from '../../../services/uuid';
import { ROZETKA_EDRPOUS } from '../rozetka/layouts/rozetka/constants';
import { DATE_FORMAT_FULL_WITHOUT_DAY } from './constants';

import css from './MutualSettlementsAct.css';

const MutualSettlementsAct: FC<React.PropsWithChildren<Props>> = (props) => {
    const {
        date,
        number,
        owner,
        partner,
        servicesAct,
        documentType,
        parentNumber,
        startServicesProvisionDate,
        endServicesProvisionDate,
    } = props.data;

    const isVarusOwner = FlowVarusEdrpouList.find(
        (edrpouItem) =>
            edrpouItem === partner?.edrpou || edrpouItem === owner?.edrpou,
    );

    const isRozetkaPartOfDeal = ROZETKA_EDRPOUS.find(
        (edrpouItem) =>
            edrpouItem === partner?.edrpou || edrpouItem === owner?.edrpou,
    );

    const ownerFinalBalanceDebit = formatPrice(
        servicesAct?.ownerFinalBalanceDebit,
    );
    const ownerFinalBalanceCredit = formatPrice(
        servicesAct?.ownerFinalBalanceCredit,
    );

    return (
        <div>
            <div className={css.documentTitleBlock}>
                <h1 className={css.documentTitle}>{documentType}</h1>
                {isRozetkaPartOfDeal && (
                    <>
                        <p>
                            № {number} від {formatDate(date)} р.
                        </p>
                        <br />
                    </>
                )}

                <p>
                    {t`взаємних розрахунків по стану за період:`}{' '}
                    {formatDate(
                        startServicesProvisionDate,
                        DATE_FORMAT_FULL_WITHOUT_DAY,
                    )}{' '}
                    р. -{' '}
                    {formatDate(
                        endServicesProvisionDate,
                        DATE_FORMAT_FULL_WITHOUT_DAY,
                    )}{' '}
                    р.
                </p>
                <p>між {owner?.fullName}</p>
                <p>і {partner?.fullName}</p>
                {parentNumber && (
                    <>
                        <br />
                        <p>за договором №{parentNumber}</p>
                    </>
                )}

                {!isVarusOwner && !isRozetkaPartOfDeal && (
                    <p>
                        за договором № {number} від {formatDate(date)} р.
                    </p>
                )}
            </div>
            <p>
                Ми, що нижче підписалися ___________ {owner?.fullName}{' '}
                ________________________, з одного боку, і ___________{' '}
                {partner?.fullName} ________________________, з іншого боку,
                склали даний акт звірки у тому, що стан взаємних розрахунків за
                даними обліку наступний:
            </p>
            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <td colSpan={4}>За даними {partner?.fullName}, грн.</td>
                        <td colSpan={4}>За даними {owner?.fullName}, грн.</td>
                    </tr>
                    <tr>
                        <th className={css.number}>Дата</th>
                        <th className={css.name}>Документ</th>
                        <th className={css.quantity}>Дебет</th>
                        <th className={css.quantity}>Кредит</th>
                        <th className={css.number}>Дата</th>
                        <th className={css.name}>Документ</th>
                        <th className={css.quantity}>Дебет</th>
                        <th className={css.quantity}>Кредит</th>
                    </tr>
                    <tr>
                        <td colSpan={2}>Сальдо початкове</td>
                        <td>
                            {formatPrice(
                                servicesAct?.partnerInitialDebitBalance,
                            )}
                        </td>
                        <td>
                            {formatPrice(
                                servicesAct?.partnerInitialCreditBalance,
                            )}
                        </td>
                        <td colSpan={2}>Сальдо початкове</td>
                        <td>
                            {formatPrice(servicesAct?.ownerInitialDebitBalance)}
                        </td>
                        <td>
                            {formatPrice(
                                servicesAct?.ownerInitialCreditBalance,
                            )}
                        </td>
                    </tr>
                </thead>
                <tbody>
                    {servicesAct?.items?.map((service) => (
                        <tr key={`product-${uuid()}`}>
                            <td className={css.textCenter}>
                                {formatDate(service.partner?.date)}
                            </td>
                            <td>{service.partner?.name}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.partner?.debit)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.partner?.credit)}
                            </td>
                            <td className={css.textCenter}>
                                {formatDate(service.owner?.date)}
                            </td>
                            <td>{service.owner?.name}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.owner?.debit)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.owner?.credit)}
                            </td>
                        </tr>
                    ))}
                </tbody>
                <tfoot>
                    <tr>
                        <td colSpan={2}>Обороти за період</td>
                        <td>
                            {formatPrice(
                                servicesAct?.partnerTurnoverPeriodDebit,
                            )}
                        </td>
                        <td>
                            {formatPrice(
                                servicesAct?.partnerTurnoverPeriodCredit,
                            )}
                        </td>
                        <td colSpan={2}>Обороти за період</td>
                        <td>
                            {formatPrice(servicesAct?.ownerTurnoverPeriodDebit)}
                        </td>
                        <td>
                            {formatPrice(
                                servicesAct?.ownerTurnoverPeriodCredit,
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td colSpan={2}>Сальдо кінцеве</td>
                        <td>
                            {formatPrice(servicesAct?.partnerFinalBalanceDebit)}
                        </td>
                        <td>
                            {formatPrice(
                                servicesAct?.partnerFinalBalanceCredit,
                            )}
                        </td>
                        <td colSpan={2}>Сальдо кінцеве</td>
                        <td>{ownerFinalBalanceDebit}</td>
                        <td>{ownerFinalBalanceCredit}</td>
                    </tr>
                </tfoot>
            </table>

            <div className={css.resultText}>
                <p>за даними {owner?.fullName}</p>
                <p>
                    <b>
                        на {formatDate(date)} заборгованість на користь{' '}
                        {ownerFinalBalanceCredit > ownerFinalBalanceDebit
                            ? `${partner?.fullName} ${ownerFinalBalanceCredit}`
                            : `${owner?.fullName} ${ownerFinalBalanceDebit}`}
                    </b>
                </p>
            </div>

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від {partner?.fullName}
                            <br />
                        </th>
                        <th>
                            Від {owner?.fullName}
                            <br />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default MutualSettlementsAct;
