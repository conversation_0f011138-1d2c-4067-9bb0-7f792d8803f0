from collections.abc import Sequence

import sqlalchemy as sa

from app.auth import tables as auth_tables
from app.document_categories import tables, types
from app.lib import types as core_types
from app.lib.database import DBConnection
from app.models import exists, select_all, select_one

document_category_company_join = tables.document_categories_table.outerjoin(
    auth_tables.company_table,
    tables.document_categories_table.c.company_id == auth_tables.company_table.c.id,
)


async def insert_document_category(
    conn: DBConnection,
    title: str,
    company_id: str | None = None,
) -> types.DocumentCategory:
    """
    Insert document category in database
    """

    data = {'title': title}

    if company_id is not None:
        data['company_id'] = company_id

    row = await select_one(
        conn=conn,
        query=(
            tables.document_categories_table.insert()
            .values(data)
            .returning(tables.document_categories_table)
        ),
    )

    return types.DocumentCategory.from_db(row=row)


async def update_document_category(
    conn: DBConnection,
    document_category_id: int,
    data: core_types.DataDict,
) -> types.DocumentCategory:
    """
    Update document category in database
    """

    data['date_updated'] = sa.text('now()')

    row = await select_one(
        conn=conn,
        query=(
            tables.document_categories_table.update()
            .values(data)
            .where(tables.document_categories_table.c.id == document_category_id)
            .returning(tables.document_categories_table)
        ),
    )

    return types.DocumentCategory.from_db(row=row)


async def delete_document_category(
    conn: DBConnection,
    document_category_id: int,
) -> types.DocumentCategory:
    """
    Delete document category from db
    """
    data = {
        'date_updated': sa.text('now()'),
        'date_deleted': sa.text('now()'),
    }

    row = await select_one(
        conn=conn,
        query=(
            tables.document_categories_table.update()
            .values(data)
            .where(tables.document_categories_table.c.id == document_category_id)
            .returning(tables.document_categories_table)
        ),
    )

    return types.DocumentCategory.from_db(row=row)


async def select_document_category(
    conn: DBConnection,
    document_category_id: int | None = None,
    title: str | None = None,
    company_id: str | None = None,
    is_active: bool = True,
) -> types.DocumentCategory | None:
    """
    Select document category from database
    """

    where = sa.true()

    if document_category_id is not None:
        where = sa.and_(where, tables.document_categories_table.c.id == document_category_id)

    if company_id is not None:
        where = sa.and_(
            where,
            sa.or_(
                tables.document_categories_table.c.company_id == company_id,
                tables.document_categories_table.c.company_id.is_(None),
            ),
        )
    else:
        where = sa.and_(where, tables.document_categories_table.c.company_id.is_(None))

    if title is not None:
        where = sa.and_(where, tables.document_categories_table.c.title == title)

    if is_active:
        where = sa.and_(where, tables.document_categories_table.c.date_deleted.is_(None))
    else:
        where = sa.and_(where, tables.document_categories_table.c.date_deleted.isnot(None))

    row = await select_one(
        conn=conn,
        query=sa.select([tables.document_categories_table]).where(where),
    )

    if not row:
        return None

    return types.DocumentCategory.from_db(row=row)


async def select_available_document_categories(
    conn: DBConnection,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    ids: Sequence[str] | None = None,
    only_public: bool | None = None,
    only_internal: bool | None = None,
    title: str | None = None,
    limit: int | None = None,
    offset: int | None = None,
) -> list[types.DocumentCategory]:
    """
    Select available document categories
    """

    if (company_id is None and company_edrpou is None) or only_public:
        # Select only public categories
        filters = [
            tables.document_categories_table.c.date_deleted.is_(None),
            tables.document_categories_table.c.company_id.is_(None),
        ]
        from_table = tables.document_categories_table
    elif only_internal:
        assert company_id or company_edrpou, (
            'Company id or edrpou should be provided with only_internal option'
        )
        # Select only internal categories
        filters = [tables.document_categories_table.c.date_deleted.is_(None)]
        company_access_filters = []
        if company_id is not None:
            company_access_filters.append(
                tables.document_categories_table.c.company_id == company_id
            )
        if company_edrpou is not None:
            company_access_filters.append(auth_tables.company_table.c.edrpou == company_edrpou)
        filters.append(sa.or_(*company_access_filters))
        from_table = document_category_company_join
    else:
        # Select public & internal
        filters = [tables.document_categories_table.c.date_deleted.is_(None)]
        company_access_filters = [tables.document_categories_table.c.company_id.is_(None)]
        if company_id is not None:
            company_access_filters.append(
                tables.document_categories_table.c.company_id == company_id
            )
        if company_edrpou is not None:
            company_access_filters.append(auth_tables.company_table.c.edrpou == company_edrpou)
        filters.append(sa.or_(*company_access_filters))
        from_table = document_category_company_join

    if title is not None:
        filters.append(tables.document_categories_table.c.title.ilike(f'%{title}%'))

    if ids is not None:
        filters.append(tables.document_categories_table.c.id.in_(ids))

    query = (
        sa.select([tables.document_categories_table])
        .select_from(from_table)
        .where(sa.and_(*filters))
        .order_by(tables.document_categories_table.c.title)
    )

    if limit is not None:
        query = query.limit(limit)

    if offset is not None:
        query = query.offset(offset)

    rows = await select_all(conn=conn, query=query)

    return [types.DocumentCategory.from_db(row=row) for row in rows]


async def select_document_categories(
    conn: DBConnection,
    *,
    categories_ids: Sequence[str],
) -> list[types.DocumentCategory]:
    """
    Select categories by IDs

    NOTE: this function doesn't have any company filtering, so consider using
    "select_available_document_categories" that have company filtering
    """

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([tables.document_categories_table])
            .select_from(tables.document_categories_table)
            .where(
                sa.and_(
                    tables.document_categories_table.c.date_deleted.is_(None),
                    tables.document_categories_table.c.id.in_(categories_ids),
                )
            )
        ),
    )

    return [types.DocumentCategory.from_db(row=row) for row in rows]


async def exists_document_category(
    *,
    conn: DBConnection,
    document_category_id: int,
    company_id: str | None = None,
) -> bool:
    """
    Check if document category exists
    """

    select_from = tables.document_categories_table
    clause = sa.and_(
        tables.document_categories_table.c.id == document_category_id,
        tables.document_categories_table.c.date_deleted.is_(None),
        sa.or_(
            tables.document_categories_table.c.company_id.is_(None),
            tables.document_categories_table.c.company_id == company_id,
        ),
    )

    return await exists(conn, select_from, clause)
