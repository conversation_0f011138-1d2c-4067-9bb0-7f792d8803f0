.root,
.rootOrange,
.rootGreen,
.rootRed {
    overflow: hidden;
    max-width: max-content;
    color: var(--slate-grey-color);
    text-overflow: ellipsis;
    white-space: nowrap;
}

:global(.vchasno-autumn-theme) .root,
:global(.vchasno-autumn-theme) .rootOrange,
:global(.vchasno-autumn-theme) .rootGreen,
:global(.vchasno-autumn-theme) .rootRed {
    --green-alt-color: var(--autumn-2-color);
}

.rootWrap {
    white-space: normal;
}

.rootOrange {
    color: #f80;
}

.rootGreen {
    color: var(--green-alt-color);
}

.rootRed {
    color: var(--red-color);
}

.root.badge {
    height: 20px;
    box-sizing: border-box;
    padding: 2px 10px;
    border-width: 1px;
    border-color: transparent;
    background: rgba(0, 0, 0, 0.1);
    background: var(--light-blue-bg);
    border-radius: 10px;
    line-height: 14px;
}

.root.badge.rootOrange {
    background-color: rgba(255, 136, 0, 0.3);
}

.root.badge.rootOrange:hover {
    border-color: var(--primary-cta-color);
}

.root.badge.rootGreen {
    background-color: rgba(28, 184, 0, 0.3);
}

:global(.vchasno-autumn-theme) .root.badge.rootGreen {
    background-color: var(--autumn-4-color);
}

.root.badge.rootGreen:hover {
    border-color: var(--green-color);
}

.root.badge.rootRed {
    background-color: rgba(255, 44, 44, 0.3);
}

.root.badge.rootRed:hover {
    border-color: var(--red-color);
}
