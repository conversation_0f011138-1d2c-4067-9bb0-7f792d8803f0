import { yupResolver } from '@hookform/resolvers/yup';

import { passwordTest } from 'lib/yup/helpers';
import * as yup from 'yup';

import { AdditionalInfoFormFields } from './types';

import {
    MAX_USERNAME_LENGTH,
    MAX_USERNAME_LENGTH_ERROR_MESSAGE,
} from './constants';

const defaultUsernameSchema = yup
    .string()
    .trim()
    .default('')
    .max(MAX_USERNAME_LENGTH, MAX_USERNAME_LENGTH_ERROR_MESSAGE);

const additionalInfoFormValidationSchema: yup.ObjectSchema<AdditionalInfoFormFields> = yup
    .object({
        isPhoneNumberRequired: yup.boolean().default(false).required(),
        isUsernameRequired: yup.boolean().default(false).required(),
        isPositionRequired: yup.boolean().default(false).required(),
        isPasswordRequired: yup.boolean().default(false).required(),
        phoneNumber: defaultUsernameSchema.when((_, schema, options) => {
            if (options.parent.isPhoneNumberRequired) {
                return schema.required();
            }
            return schema;
        }),
        firstName: defaultUsernameSchema.when((_, schema, options) => {
            if (options.parent.isUsernameRequired) {
                return schema.required();
            }
            return schema;
        }),
        lastName: defaultUsernameSchema.optional(),
        password: yup
            .string()
            .trim()
            .when((_, schema, options) => {
                if (options.parent.isPasswordRequired) {
                    return schema.test(passwordTest).required();
                }
                return schema;
            }),
        position: yup
            .string()
            .trim()
            .default('')
            .when((_, schema, options) => {
                if (options.parent.isPositionRequired) {
                    return schema.required();
                }
                return schema;
            }),
        // Імейл потрібний тільки для валідації довжини пароля (passwordTest)
        email: yup
            .string()
            .when((_, schema, options) =>
                options.parent.isPasswordRequired
                    ? schema.required()
                    : schema.notRequired(),
            ),
    })
    .required();

export const additionalInfoFormResolver = yupResolver(
    additionalInfoFormValidationSchema,
);
