import React, { ChangeEvent, FC, useEffect, useState } from 'react';

import { t } from 'ttag';

import { Nullable } from '../../types/general';

import { flattenInnerArray } from '../../lib/helpers';

import Autosuggest from '../ui/autosuggest/autosuggest';
import Button from '../ui/button/button';
import Hint from '../ui/hint/hint';
import Icon from '../ui/icon/icon';

import { EDRPOU_PATTERN, EMAIL_PATTERN } from '../../lib/constants';
import { useDebounce } from '../../lib/reactHelpers/hooks';
import { findRecipientEmail } from '../../services/documents/api';
import { getContactPersons, getContacts } from '../../services/user';
import HiddenEmailInput from '../hiddenEmailInput/hiddenEmailInput';
import {
    IContact,
    IContactPerson,
} from '../recipientDataFields/recipientDataFieldsTypes';
import rfConstants from '../recipientForm/constants';

import SvgCheck from './images/check.svg';

import css from './recipientSettings.css';

interface Props {
    isMultilateral: boolean;
    edrpou: string;
    email: string;
    isEmailHidden: boolean;
    onChangeRecipient: (
        edrpou: string,
        email: string,
        isEmailHidden: boolean,
        isValidRecipient: boolean,
    ) => void;
}

const renderEdrpouSuggestion = (suggestion: IContact) => (
    <div title={suggestion.persons.mainRecipient ? t`Основний контакт` : ''}>
        {`${suggestion.edrpou}${
            suggestion.name ? ` (${suggestion.name})` : ''
        }`}
        <div className={css.suggestionSecondPart}>
            {suggestion.persons && (
                <div className={css.suggestionEmail}>
                    {suggestion.persons.mainRecipient && (
                        <div className={css.icon}>
                            <Icon glyph={SvgCheck} />
                        </div>
                    )}
                    {suggestion.persons.email}
                </div>
            )}
        </div>
    </div>
);

const renderEmailSuggestion = (suggestion: IContactPerson) => {
    const { contact } = suggestion;
    return (
        <div title={suggestion.mainRecipient ? t`Основний контакт` : ''}>
            <div className={css.suggestionEmail}>
                {suggestion.mainRecipient && (
                    <div className={css.icon}>
                        <Icon glyph={SvgCheck} />
                    </div>
                )}
                {suggestion.email}
            </div>
            <div className={css.suggestionSecondPart}>
                {contact &&
                    `
                        ${contact.edrpou}${
                        contact.name ? ` (${contact.name})` : ''
                    }
                    `}
            </div>
        </div>
    );
};

const RecipientSettings: FC<React.PropsWithChildren<Props>> = (
    props: Props,
) => {
    const [edrpou, setEdrpou] = useState(props.edrpou);
    const [email, setEmail] = useState(props.email);
    const [isEmailHidden, setEmailHidden] = useState(props.isEmailHidden);
    const [edrpouErrorMessage, setEdrpouErrorMessage] = useState('');
    const [emailErrorMessage, setEmailErrorMessage] = useState('');
    const [suggestionCompanies, setSuggestionCompanies] = useState<IContact[]>(
        [],
    );
    const [suggestionUsers, setSuggestionUsers] = useState([]);

    const debouncedSearchEdrpou = useDebounce(edrpou, 300);
    const debouncedSearchEmail = useDebounce(email, 300);

    const autosuggestByEdrpou = async (search: string) => {
        const { contacts } = await getContacts({ search });
        const suggestions = flattenInnerArray(contacts, 'persons').filter(
            (contact) => contact.persons && !contact.persons.isEmailHidden,
        );
        setSuggestionCompanies(suggestions);
    };

    const autosuggestByEmail = async (search: string) => {
        const { users } = await getContactPersons({ search });
        setSuggestionUsers(users);
    };

    useEffect(() => {
        if (debouncedSearchEdrpou) {
            const isValidEdrpou = EDRPOU_PATTERN.test(debouncedSearchEdrpou);
            if (!isValidEdrpou) {
                setEdrpouErrorMessage(rfConstants.EDRPOU_ERROR_MESSAGE);
                autosuggestByEdrpou(debouncedSearchEdrpou);
                return;
            }
        }
        setEdrpouErrorMessage('');
        setSuggestionCompanies([]);
    }, [debouncedSearchEdrpou]);

    useEffect(() => {
        if (debouncedSearchEmail) {
            let isValidEmail = true;
            for (const e of debouncedSearchEmail.split(',')) {
                if (!EMAIL_PATTERN.test(e.trim()) && !isEmailHidden) {
                    isValidEmail = false;
                }
            }
            if (!isValidEmail) {
                setEmailErrorMessage(rfConstants.EMAIL_ERROR_MESSAGE);
                autosuggestByEmail(debouncedSearchEmail);
                return;
            }
        }
        setEmailErrorMessage('');
        setSuggestionUsers([]);
    }, [debouncedSearchEmail]);

    useEffect(() => {
        const isValidRecipient =
            (debouncedSearchEdrpou === '' && debouncedSearchEmail === '') ||
            (debouncedSearchEdrpou &&
                debouncedSearchEmail === '' &&
                isEmailHidden) ||
            (!edrpouErrorMessage && !emailErrorMessage);
        props.onChangeRecipient(
            debouncedSearchEdrpou,
            debouncedSearchEmail,
            isEmailHidden,
            isValidRecipient,
        );
    }, [
        debouncedSearchEdrpou,
        debouncedSearchEmail,
        edrpouErrorMessage,
        emailErrorMessage,
        isEmailHidden,
    ]);

    const onCloseSuggestions = () => {
        setSuggestionCompanies([]);
        setSuggestionUsers([]);
    };

    const handleEdrpouSuggestionClick = (suggestion: IContact) => {
        setEdrpou(suggestion.edrpou);
        setEmail(suggestion.persons ? suggestion.persons.email : '');
        onCloseSuggestions();
    };

    const handleEmailSuggestionClick = (suggestion: IContactPerson) => {
        setEdrpou(suggestion.contact ? suggestion.contact.edrpou : '');
        setEmail(suggestion.email);
        onCloseSuggestions();
    };

    const handleFillEmailClick = async () => {
        let recipients;
        try {
            recipients = await findRecipientEmail(null, [edrpou]);
        } catch (err) {
            setEdrpouErrorMessage(err.message);
            return;
        }
        if (recipients.length === 0) {
            setEmailErrorMessage(
                t`Ми не знайшли контактного email для відправлення документу.`,
            );
            return;
        }
        if (recipients) {
            const isRecipientEmailHidden = recipients[0].is_hidden;
            const emailRecipient = isRecipientEmailHidden
                ? ''
                : recipients[0].emails && recipients[0].emails.join(',');
            setEmail(emailRecipient);
            setEmailHidden(isRecipientEmailHidden);
        }
    };

    const handleEdrpouChange = (
        evt: Nullable<ChangeEvent<HTMLInputElement>>,
    ) => {
        if (evt === null) {
            setEmailHidden(false);
            setEdrpou('');
            return;
        }

        const edrpouValue = evt.target.value.trim();
        setEdrpou(edrpouValue);

        if (isEmailHidden) {
            setEmailHidden(false);
        }

        if (emailErrorMessage) {
            setEmailErrorMessage('');
        }
    };

    const handleEmailChange = (
        evt: Nullable<ChangeEvent<HTMLInputElement>>,
    ) => {
        if (evt === null) {
            setEmail('');
            return;
        }

        const emailValue = evt.target.value.trim();
        setEmail(emailValue);
    };

    return (
        <>
            <div className={css.input}>
                <div className={css.label}>{t`ЄДРПОУ/ІПН контрагента`}</div>
                <Autosuggest
                    name="edrpou"
                    value={edrpou}
                    error={Boolean(edrpouErrorMessage)}
                    errorMessage={edrpouErrorMessage}
                    showSuggestions={suggestionCompanies.length > 0}
                    suggestionsData={suggestionCompanies}
                    renderSuggestion={renderEdrpouSuggestion}
                    onSuggestionClick={handleEdrpouSuggestionClick}
                    onCloseSuggestion={onCloseSuggestions}
                    onChange={handleEdrpouChange}
                    placeholder={t`Вкажіть ЄДРПОУ/ІПН чи назву контрагента`}
                />
            </div>
            <div className={css.input}>
                <div className={css.label}>{t`Email контрагента`}</div>
                {isEmailHidden ? (
                    <HiddenEmailInput showHint hintPosition="right" />
                ) : (
                    <Autosuggest
                        name="email"
                        value={email}
                        error={Boolean(emailErrorMessage)}
                        errorMessage={emailErrorMessage}
                        showSuggestions={suggestionUsers.length > 0}
                        suggestionsData={suggestionUsers}
                        renderSuggestion={renderEmailSuggestion}
                        onSuggestionClick={handleEmailSuggestionClick}
                        onCloseSuggestion={onCloseSuggestions}
                        onChange={handleEmailChange}
                        placeholder={t`Вкажіть email контрагента`}
                    />
                )}
            </div>
            <div className={css.buttonContainer}>
                <Button
                    typeContour
                    theme="blue"
                    onClick={handleFillEmailClick}
                    dataQa="qa_pick_up_email_button"
                >
                    {t`Підібрати email`}
                </Button>
                <Hint black position="right" tooltipClassName={css.tooltip}>
                    {t`За кодом ЄДРПОУ/ІПН сервіс підбере email-адреси вашого контрагента, якщо така компанія вже зареєстрована у ${config.BRAND_NAME}, або додана до переліку ваших контактів.`}
                </Hint>
            </div>
        </>
    );
};

export default RecipientSettings;
