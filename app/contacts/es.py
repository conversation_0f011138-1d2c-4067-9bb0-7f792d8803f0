import logging
from typing import Any

from elasticmagic import Bool, SearchQuery, Sort
from elasticmagic.actions import Delete, Index
from elasticmagic.expression import QueryExpression
from elasticmagic.ext.asyncio import AsyncSearchQuery

from app.auth.db import (
    select_companies_for_contact_recipient_indexation,
    select_roles_for_contact_recipient_indexation,
)
from app.auth.types import CompanyForIndexation, RoleForIndexation
from app.contacts import db
from app.contacts.types import ContactRecipient as ContactRecipientModel
from app.contacts.types import ContactRecipientIndexationBatch, ContactRecipientIndexationResult
from app.contacts.validators import validate_es_search_window_limit, validate_phone
from app.es.errors import BulkActionError
from app.es.helpers import es_perform_bulk
from app.es.models.contact_recipient import ContactRec<PERSON>ient, ContactRecipientType
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.es import AsyncIndex
from app.lib.helpers import build_full_user_name
from app.services import services

logger = logging.getLogger(__name__)

# Maximum number of documents that can be returned by Elasticsearch in a single query
ELASTIC_MAX_SEARCH_WINDOW_LIMIT = 10_000


def _append_access_filter(
    *,
    query: SearchQuery,
    company_id: str,
) -> SearchQuery:
    """Append access filter to query."""

    return query.filter(
        # Filters combined with logical OR
        Bool.should(
            # Contacts filters (contacts owned by current company)
            Bool.must(
                ContactRecipient.type.in_(
                    [
                        ContactRecipientType.contact,
                        ContactRecipientType.person,
                    ]
                ),
                ContactRecipient.contact_owner_id.term(company_id),
            ),
            # Companies filter (all companies in the system)
            ContactRecipient.type.term(ContactRecipientType.company),
            # Roles filter (roles in a current company)
            Bool.must(
                ContactRecipient.type.term(ContactRecipientType.role),
                ContactRecipient.company_id.term(company_id),
            ),
        )
    )


def _build_search_filters(
    search_query: str,
    filters: list[QueryExpression],
) -> list[QueryExpression]:
    """Build various search filters to ContactRecipient query."""

    if edrpou := pv.EDRPOUOrNoneAdapter.validate_python(search_query):
        # Search exactly by edrpou
        filters.append(
            Bool.must(
                ContactRecipient.company_edrpou.term(edrpou),
            )
        )

    elif maybe_email := pv.EmailOrNoneAdapter.validate_python(search_query):
        # Contact persons with hidden emails are not presented in the index
        filters.append(
            Bool.must(
                ContactRecipient.contact_person_email.lowercase.term(maybe_email),
            )
        )

    elif phone_search_query := validate_phone(search_query):
        filters.append(
            Bool.must(
                ContactRecipient.contact_person_phone.term(phone_search_query),
            )
        )

    elif search_query.isdigit():
        # Search by edrpou prefix
        filters.append(
            Bool.must(
                ContactRecipient.company_edrpou.suggest.match(search_query),
            )
        )

    else:
        # Search by all meta_data fields
        filters.append(
            Bool.should(
                ContactRecipient.meta_data.match(search_query, operator='and'),
                ContactRecipient.meta_data.suggest.match(search_query, operator='and'),
            )
        )

    return filters


async def _build_paginated_query(
    es_query: AsyncSearchQuery,
    limit: int | None,
    offset: int | None,
) -> dict[str, Any]:
    """
    Build contacts paginated query search using aggregation by contact_id
    to avoid duplicates.

    We use this approach because we have two different types of ContactRecipient:
    person and contact in one index and need to search by both.
    """
    base_query = await es_query.to_dict()
    aggregations = {}
    pagination = {}

    if limit is not None:
        pagination['size'] = limit

    if offset is not None:
        pagination['from'] = offset

    if pagination:
        aggregations['aggs'] = {
            'bucket_pagination': {
                'bucket_sort': pagination,
            }
        }

    return {
        **base_query,
        'aggs': {
            # Count total unique contacts
            'total_unique_contacts': {
                'cardinality': {
                    'field': ContactRecipient.contact_id.get_field_name(),
                },
            },
            # Aggregate by EDRPOU + contact_id and sort by EDRPOU
            'unique_contacts': {
                'multi_terms': {
                    'terms': [
                        {'field': ContactRecipient.company_edrpou.get_field_name()},
                        {'field': ContactRecipient.contact_id.get_field_name()},
                    ],
                    'size': ELASTIC_MAX_SEARCH_WINDOW_LIMIT,
                    'order': {'_key': 'asc'},
                },
                **aggregations,
            },
        },
    }


async def search_contacts(
    *,
    index: AsyncIndex,
    current_user_company_id: str,
    current_user_edrpou: str,
    limit: int | None,
    offset: int | None,
    search_query: str | None,
    is_registered: bool | None = None,
) -> tuple[int, tuple[str, ...]]:
    """
    Search in ElasticSearch for contacts using various fields
    of contacts and persons types of contact recipients.
    """
    validate_es_search_window_limit(limit, offset)

    es_query = index.search_query().size(0)
    search_filters: list[QueryExpression] = []

    if search_query:
        search_filters = _build_search_filters(
            search_query=search_query,
            filters=search_filters,
        )

    # Apply is_registered filter
    if is_registered is not None:
        search_filters.append(
            Bool.must(
                ContactRecipient.company_registered.term(is_registered),
            )
        )

    if search_filters:
        es_query = es_query.query(Bool.must(*search_filters))

    # Add contact persons type to support filtering
    # by person's first_name, last_name, etc.
    type_filters: list[QueryExpression] = [
        Bool.should(
            ContactRecipient.type.term(ContactRecipientType.contact),
            ContactRecipient.type.term(ContactRecipientType.person),
        )
    ]

    # Apply type and user filters
    es_query = es_query.filter(
        Bool.must(
            *type_filters,
            ContactRecipient.contact_owner_id.term(current_user_company_id),
            ContactRecipient.company_edrpou != current_user_edrpou,
        )
    )

    # Apply pagination and sorting using aggregations to avoid contact duplicates
    paginated_query = await _build_paginated_query(
        es_query=es_query,
        limit=limit,
        offset=offset,
    )

    client = index.cluster.client
    result = await client.search(index=index.name, body=paginated_query)

    total_count = result['aggregations']['total_unique_contacts']['value']
    contact_ids = tuple(
        bucket['key'][1] for bucket in result['aggregations']['unique_contacts']['buckets']
    )
    return total_count, contact_ids


async def search_contact_persons(
    *,
    index: AsyncIndex,
    current_user_company_id: str,
    current_user_edrpou: str,
    limit: int,
    offset: int,
    search_query: str | None,
) -> tuple[int, tuple[str, ...]]:
    """
    Search in ElasticSearch contact recipients that will be used when a user
    needs to find specific contact persons within their company's contacts.
    """
    validate_es_search_window_limit(limit, offset)

    es_query = index.search_query()
    search_filters: list[QueryExpression] = []

    if search_query:
        search_filters = _build_search_filters(
            search_query=search_query,
            filters=search_filters,
        )

    if search_filters:
        es_query = es_query.query(Bool.must(*search_filters))

    # Apply type and user filters
    es_query = es_query.filter(
        Bool.must(
            ContactRecipient.type.term(ContactRecipientType.person),
            ContactRecipient.contact_owner_id.term(current_user_company_id),
            ContactRecipient.company_edrpou != current_user_edrpou,
        )
    )

    # Apply pagination and sorting
    es_query = es_query.limit(limit).offset(offset)
    es_query = es_query.order_by(
        Sort('_score', 'desc'),
        Sort(ContactRecipient.person_main_recipient, 'desc'),
        Sort(ContactRecipient.contact_person_email, 'asc'),
    )

    results = await es_query.get_result()
    total_count = results.total

    person_ids = tuple(h.person_id for h in results.hits)
    return total_count, person_ids


async def search_contact_recipients(
    *,
    company_id: str,
    search_query: str,
    limit: int,
    offset: int,
) -> list[ContactRecipientModel]:
    """
    Search in ElasticSearch contact recipients that will be used for recipient search when a
    user fills recipient fields on an uploading/editing document.
    """
    es = services.es
    search_query = search_query.strip()

    eq_query = es.contact_recipients.search_query()

    if pv.EDRPOUOrNoneAdapter.validate_python(search_query):
        # search exactly by edrpou
        es_query = eq_query.query(
            Bool.must(
                ContactRecipient.company_edrpou.term(search_query),
            )
        )

    elif maybe_email := pv.EmailOrNoneAdapter.validate_python(search_query):
        # search exactly by email
        search_query = maybe_email
        es_query = eq_query.query(
            Bool.must(
                ContactRecipient.contact_person_email.lowercase.term(search_query),
            )
        )

    elif search_query.isdigit():
        # search by edrpou prefix
        es_query = eq_query.query(
            Bool.must(
                ContactRecipient.company_edrpou.suggest.match(search_query),
            )
        )

    else:
        # broad search by company name, contact person name, edrpou and so on.
        # "meta_data" is a field that combines all searchable fields
        es_query = eq_query.query(
            Bool(
                must=[
                    Bool.should(
                        ContactRecipient.meta_data.match(search_query, operator='and'),
                        ContactRecipient.meta_data.suggest.match(search_query, operator='and'),
                    ),
                ],
                should=[
                    ContactRecipient.company_edrpou.term(search_query),
                    ContactRecipient.contact_person_email.lowercase.term(search_query),
                ],
            )
        )

    es_query = _append_access_filter(
        query=es_query,
        company_id=company_id,
    )

    # Apply pagination params to query
    es_query = es_query.limit(limit).offset(offset)

    # Apply sorting: contacts first, then everything else
    es_query = es_query.order_by(
        # contact should be on top of the list
        # See ContactRecipientType for more details
        Sort(ContactRecipient.type, 'asc'),
        Sort('_score', 'desc'),
        Sort(ContactRecipient.date, 'desc'),
    )

    results = await es_query.get_result()

    recipients = [
        ContactRecipientModel(
            edrpou=hit.company_edrpou,
            name=hit.company_name_extra or hit.company_name_short or hit.company_name,
            email=hit.contact_person_email,
            user_name=hit.contact_person_name,
            main_recipient=hit.person_main_recipient,
        )
        for hit in results.hits
    ]

    return recipients


async def _index_contact_recipients_by_contacts(
    conn: DBConnection,
    *,
    contacts_ids: list[str],
) -> ContactRecipientIndexationResult:
    """
    Index contact recipients by contacts
    """

    result = ContactRecipientIndexationResult()

    contacts = await db.select_contacts_for_indexation(
        conn=conn,
        contacts_ids=contacts_ids,
    )

    actions = []
    indexed_contacts_ids = set()
    for contact in contacts:
        contact_id: str = contact.contact_id
        person_id: str | None = contact.person_id

        # Create contact recipient by contact. We should create it only once
        # for each contact, even if it has multiple persons
        if contact_id not in indexed_contacts_ids:
            item = ContactRecipient.build(
                _id=contact_id,
                company_edrpou=contact.contact_company_edrpou,
                company_registered=contact.company_registered,
                company_name=contact.contact_company_name,
                company_name_short=contact.contact_company_name_short,
                company_name_extra=contact.company_name_extra,
                type_=ContactRecipientType.contact,
                contact_owner_id=contact.contact_owner_id,
                contact_person_email=None,
                contact_person_name=None,
                contact_person_phone=None,
                person_main_recipient=False,
                contact_id=contact.contact_id,
                person_id=None,
                company_id=None,
                role_id=None,
                date=contact.contact_date_created,
            )
            actions.append(Index(item))
            indexed_contacts_ids.add(contact_id)

        # Create contact recipient by contact person
        if person_id:
            # Skip contact person with hidden emails, but mark it as successfull
            # to avoid indexing it again
            if contact.person_is_email_hidden:
                result.success_ids.add(contact.contact_id)
                continue

            person_name = build_full_user_name(
                first=contact.person_first_name,
                second=contact.person_second_name,
                last=contact.person_last_name,
            )

            # Create contact recipient by contact person
            item = ContactRecipient.build(
                _id=person_id,
                company_edrpou=contact.contact_company_edrpou,
                company_registered=contact.company_registered,
                company_name=contact.contact_company_name,
                company_name_short=contact.contact_company_name_short,
                company_name_extra=contact.company_name_extra,
                type_=ContactRecipientType.person,
                contact_owner_id=contact.contact_owner_id,
                contact_person_email=contact.person_email,
                contact_person_name=person_name,
                contact_person_phone=contact.person_phone,
                person_main_recipient=contact.person_main_recipient,
                contact_id=contact.contact_id,
                person_id=contact.person_id,
                company_id=None,
                role_id=None,
                date=contact.person_date_created,
            )
            actions.append(Index(item))

    # Let's mark contacts that were requested to index but not found in a database as
    # successfully indexed to avoid indexing them again
    missing_contacts_ids = set(contacts_ids) - indexed_contacts_ids
    result.success_ids.update(missing_contacts_ids)

    if missing_contacts_ids:
        logger.warning(
            msg='Some contacts were not found in a database for indexing',
            extra={
                'missing_contacts_size': len(missing_contacts_ids),
                'indexed_contacts_size': len(indexed_contacts_ids),
                'missing_contacts_ids': missing_contacts_ids,
                'indexed_contacts_ids': indexed_contacts_ids,
            },
        )

    try:
        await es_perform_bulk(services.es.contact_recipients, actions)
        result.success_ids.update(indexed_contacts_ids)
    except BulkActionError as e:
        logger.exception(
            'Failed to index contact recipients by contacts',
            extra={
                'contacts_size': len(contacts_ids),
                'persons_size': len(contacts),
                'missing_contacts_size': len(missing_contacts_ids),
                'missing_contacts_ids': missing_contacts_ids,
            },
        )
        # e.indexed_ids may also contain IDs of indexed contact persons,
        # but we néed only IDs of indexed contacts
        success_contacts_ids = {id_ for id_ in e.indexed_ids if id_ in indexed_contacts_ids}
        result.success_ids.update(success_contacts_ids)

        failed_contacts_ids = {id_ for _id in e.failed_ids for id_ in indexed_contacts_ids}
        result.failed_ids.update(failed_contacts_ids)

    return result


async def _update_contact_recipients_extra_name(
    index: AsyncIndex,
    company: CompanyForIndexation,
) -> None:
    """
    Update company_name_extra for all contacts that have the same EDRPOU in ElasticSearch index.
    """
    client = index.cluster.client
    query = {
        'bool': {
            'must': [
                {'term': {'company_edrpou': company.edrpou}},
                {'term': {'is_contact': True}},
            ],
        }
    }
    script = {
        'source': 'ctx._source.company_name_extra = params.name',
        'lang': 'painless',
        'params': {'name': company.name},
    }

    await client.update_by_query(
        index=services.es.contact_recipients.name,
        query=query,
        script=script,
        # do not wait for indexing to complete, make sure the task is queued
        # and move on
        wait_for_completion=False,
        # update_by_query will stop when a single doc has conflict and update would not be
        # available for the rest of docs in that index and next indexes. (of course some doc
        # has been updated) if you use conflict=proceed it will not update only the docs
        # that have conflict (just skip that doc not entire index).
        conflicts='proceed',
    )


async def _index_companies(
    companies: list[CompanyForIndexation],
) -> ContactRecipientIndexationResult:
    """
    Send selected companies to ElasticSearch index
    """
    result = ContactRecipientIndexationResult()

    actions = []
    indexed_companies_ids = set()
    for company in companies:
        item = ContactRecipient.build(
            _id=company.id,
            company_registered=None,
            company_edrpou=company.edrpou,
            company_name=company.full_name,
            company_name_short=company.name,
            company_name_extra=None,
            type_=ContactRecipientType.company,
            contact_owner_id=None,
            contact_person_email=None,
            contact_person_name=None,
            contact_person_phone=None,
            person_main_recipient=False,
            contact_id=None,
            person_id=None,
            company_id=company.id,
            role_id=None,
            date=company.date_created,
        )
        actions.append(Index(item))

        indexed_companies_ids.add(company.id)

    try:
        await es_perform_bulk(services.es.contact_recipients, actions)
        result.success_ids.update(indexed_companies_ids)
    except BulkActionError as e:
        logger.exception(
            'Failed to index contact recipients by companies',
            extra={
                'companies_size': len(companies),
                'indexed_companies_size': len(indexed_companies_ids),
                'missing_companies_size': len(companies) - len(indexed_companies_ids),
                'indexed_companies_ids': indexed_companies_ids,
            },
        )
        # e.indexed_ids may also contain IDs of indexed contact persons,
        # but we néed only IDs of indexed contacts
        success_companies_ids = {id_ for id_ in e.indexed_ids if id_ in indexed_companies_ids}
        result.success_ids.update(success_companies_ids)

        failed_companies_ids = {id_ for _id in e.failed_ids for id_ in indexed_companies_ids}
        result.failed_ids.update(failed_companies_ids)

    return result


async def _index_roles(
    roles: list[RoleForIndexation],
) -> ContactRecipientIndexationResult:
    """
    Send selected roles to ElasticSearch index
    """

    result = ContactRecipientIndexationResult()

    actions = []
    indexed_roles_ids = set()
    for role in roles:
        # Remove all non-active roles from the index
        if not role.is_active_role:
            item = ContactRecipient(_id=role.id)
            actions.append(Delete(item))
            indexed_roles_ids.add(role.id)
            continue

        item = ContactRecipient.build(
            _id=role.id,
            company_registered=None,
            company_edrpou=role.company_edrpou,
            company_name=role.company_full_name,
            company_name_short=role.company_name,
            company_name_extra=None,
            type_=ContactRecipientType.role,
            contact_owner_id=None,
            contact_person_email=role.user_email,
            contact_person_name=role.user_name,
            contact_person_phone=None,
            person_main_recipient=False,
            contact_id=None,
            person_id=None,
            company_id=role.company_id,
            role_id=role.id,
            date=role.date_created,
        )
        actions.append(Index(item))

        indexed_roles_ids.add(role.id)

    try:
        await es_perform_bulk(services.es.contact_recipients, actions)
        result.success_ids.update(indexed_roles_ids)
    except BulkActionError as e:
        logger.exception(
            'Failed to index contact recipients by roles',
            extra={
                'roles_size': len(roles),
                'indexed_roles_size': len(indexed_roles_ids),
                'missing_roles_size': len(roles) - len(indexed_roles_ids),
                'indexed_roles_ids': indexed_roles_ids,
            },
        )
        success_roles_ids = {id_ for id_ in e.indexed_ids if id_ in indexed_roles_ids}
        result.success_ids.update(success_roles_ids)

        failed_roles_ids = {id_ for _id in e.failed_ids for id_ in indexed_roles_ids}
        result.failed_ids.update(failed_roles_ids)
    return result


async def _index_contact_recipients_by_roles(
    conn: DBConnection,
    roles_ids: list[str],
) -> ContactRecipientIndexationResult:
    """
    Index roles from the database to ElasticSearch index
    """

    roles = await select_roles_for_contact_recipient_indexation(
        conn=conn,
        roles_ids=roles_ids,
    )

    result = await _index_roles(roles)

    # Let's mark roles that were requested to index but not found in a database as
    # successfully indexed to avoid indexing them again
    missing_roles_ids = set(roles_ids) - result.combined_ids
    result.success_ids.update(missing_roles_ids)

    if missing_roles_ids:
        logger.warning(
            msg='Some roles were not found in a database for indexing',
            extra={
                'missing_roles_size': len(missing_roles_ids),
                'missing_roles_ids': missing_roles_ids,
            },
        )

    return result


async def _index_contact_recipients_by_companies(
    conn: DBConnection,
    *,
    companies_ids: list[str],
) -> ContactRecipientIndexationResult:
    """
    Index contact recipients by companies
    """

    companies = await select_companies_for_contact_recipient_indexation(
        conn=conn,
        companies_ids=companies_ids,
    )

    result = await _index_companies(companies=companies)

    # Let's mark companies that were requested to index but not found in a database as
    # successfully indexed to avoid indexing them again
    missing_companies_ids = set(companies_ids) - result.combined_ids
    result.success_ids.update(missing_companies_ids)
    if missing_companies_ids:
        logger.warning(
            msg='Some companies were not found in a database for indexing',
            extra={
                'missing_companies_size': len(missing_companies_ids),
                'missing_companies_ids': missing_companies_ids,
            },
        )

    # Update company_name_extra for all contacts that have the same EDRPOU.
    # Keep that code commented for now, because it's not clear if we need it at all
    # for company in companies:
    #     await _update_contact_recipients_extra_name(
    #         index=services.es.contact_recipient,
    #         company=company,
    #     )

    return result


async def index_contact_recipients(
    *,
    limit: int,
    cursor: int,
    min_iteration: int,
    max_iteration: int,
) -> ContactRecipientIndexationBatch:
    """
    Index contact recipients by contacts and companies in batches by reading
    items from indexation queue.
    """
    async with services.db.acquire() as conn_main:
        async with conn_main.begin():
            batch = await db.select_contact_recipient_indexation_batch(
                conn=conn_main,
                limit=limit,
                cursor=cursor,
                min_iteration=min_iteration,
                max_iteration=max_iteration,
            )

            # To reduce a load on the main database, we read contacts and companies
            # from the replica database
            async with services.db_readonly.acquire() as conn_replica:
                # Index by contacts
                contacts_result = await _index_contact_recipients_by_contacts(
                    conn=conn_replica,
                    contacts_ids=batch.contacts_ids,
                )
                batch.add_contact_result(contacts_result)

                # Index by companies
                companies_result = await _index_contact_recipients_by_companies(
                    conn=conn_replica,
                    companies_ids=batch.companies_ids,
                )
                batch.add_company_result(companies_result)

                roles_result = await _index_contact_recipients_by_roles(
                    conn=conn_replica,
                    roles_ids=batch.roles_ids,
                )
                batch.add_role_result(roles_result)

            # Remove successfully indexed items from indexation queue
            await db.delete_contact_recipient_indexation(
                conn=conn_main,
                items_ids=batch.success_items_ids,
            )

            # Increase errors count for failed items
            await db.increase_contact_recipient_indexation_iteration(
                conn=conn_main,
                items_ids=batch.failed_items_ids,
            )

            logger.info(
                msg='Indexing contact recipients',
                extra={
                    'batch_size': batch.size,
                    'contacts_size': len(batch.contacts_ids),
                    'companies_size': len(batch.companies_ids),
                    'roles_size': len(batch.roles_ids),
                    'success_size': len(batch.success_items_ids),
                    'failed_size': len(batch.failed_items_ids),
                    'contacts_ids': batch.contacts_ids[:10],
                    'companies_ids': batch.companies_ids[:10],
                    'roles_ids': batch.roles_ids[:10],
                    'success_ids': batch.success_items_ids[:10],
                    'failed_ids': batch.failed_items_ids[:10],
                    'last_seqnum': batch._items[-1].seqnum if batch.size > 0 else None,
                    'first_seqnum': batch._items[0].seqnum if batch.size > 0 else None,
                },
            )

    return batch


async def delete_contacts(contacts_ids: list[str]) -> None:
    """
    Delete contacts from ElasticSearch index.
    """
    if not contacts_ids:
        return

    query = SearchQuery(ContactRecipient.contact_id.in_(contacts_ids))
    await services.es.contact_recipients.delete_by_query(query, conflicts='proceed')


async def delete_companies(companies_ids: list[str]) -> None:
    """
    Delete companies from ElasticSearch index.
    """
    if not companies_ids:
        return

    query = SearchQuery(ContactRecipient.company_id.in_(companies_ids))
    await services.es.contact_recipients.delete_by_query(query, conflicts='proceed')


async def delete_contacts_persons(persons_ids: list[str]) -> None:
    """
    Delete contact persons from ElasticSearch index.
    """

    if not persons_ids:
        return

    query = SearchQuery(ContactRecipient.person_id.in_(persons_ids))
    await services.es.contact_recipients.delete_by_query(query, conflicts='proceed')


async def reindex_contact_recipients_by_contacts(
    *,
    cursor: str,
    limit: int,
    company_id: str | None = None,
    contact_edrpou: str | None = None,
) -> str | None:
    """
    Re-index contact recipients by contacts
    """

    async with services.db.acquire() as conn:
        contacts_ids = await db.select_contacts_ids_for_reindexation(
            conn=conn,
            limit=limit,
            cursor=cursor,
            company_id=company_id,
            contact_edrpou=contact_edrpou,
        )
        if not contacts_ids:
            return None

        await _index_contact_recipients_by_contacts(
            conn=conn,
            contacts_ids=contacts_ids,
        )

    return contacts_ids[-1]


async def reindex_contact_recipients_by_companies(
    *,
    cursor: str,
    limit: int,
    companies_edrpous: list[str] | None,
    companies_ids: list[str] | None,
) -> str | None:
    """
    Re-index contact recipients by companies
    """

    async with services.db.acquire() as conn:
        companies = await select_companies_for_contact_recipient_indexation(
            conn=conn,
            limit=limit,
            cursor=cursor,
            companies_ids=companies_ids,
            companies_edrpous=companies_edrpous,
        )
        if not companies:
            return None

        await _index_companies(companies=companies)

    return companies[-1].id


async def reindex_contact_recipients_by_roles(
    *,
    cursor: str,
    limit: int,
    roles_ids: list[str] | None,
) -> str | None:
    """
    Re-index contact recipients by roles
    """

    async with services.db.acquire() as conn:
        roles = await select_roles_for_contact_recipient_indexation(
            conn=conn,
            limit=limit,
            cursor=cursor,
            roles_ids=roles_ids,
        )
        if not roles:
            return None

        await _index_roles(roles=roles)

    return roles[-1].id
