import { encryptedPost } from 'components/KepSigner/api';

import { CLOUD_SIGNER_URL } from '../constants';

type AcquireSignResponse =
    | { status: 1; operationId: string }
    | { errorCode: number; errorMessage: string };

export const acquireSign = async (payload: {
    clientId: string;
    clientId2?: string;
    descriptions: string[];
    hashes: string[];
    sendNotification?: boolean;
    sendSms?: boolean;
}) => {
    const response = await encryptedPost<AcquireSignResponse>(
        `${CLOUD_SIGNER_URL}/ss/acquire-sign`,
        {
            clientId: payload.clientId,
            ...(payload.clientId2 ? { clientId2: payload.clientId2 } : {}),
            originatorDescription: 'Вчасно.ЕДО',
            operationDescriptions: payload.descriptions,
            hashes: payload.hashes,
            sendNotification: payload.sendNotification ?? true,
            sendSms: payload.sendSms ?? false,
        },
    );

    if ('errorCode' in response) {
        throw new Error(response.errorMessage);
    }

    return response;
};
