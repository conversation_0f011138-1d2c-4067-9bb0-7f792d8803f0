from unittest.mock import AsyncMock

import pytest

from app.document_antivirus import utils
from app.document_antivirus.db import select_document_antivirus_check, select_draft_antivirus_check
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check, add_draft_antivirus_checks
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.drafts.tests.utils import prepare_draft_from_version
from app.tests.common import prepare_client, prepare_document_data


@pytest.mark.parametrize(
    ('check_status',),
    [
        (AntivirusCheckStatus.clean,),
        (AntivirusCheckStatus.infected,),
    ],
)
async def test_antivirus_check_document(aiohttp_client, monkeypatch, check_status):
    """
    Given:
        - New document
    When:
        - Upload document
    Then:
        - Antivirus check is performed and new entity in
          document_antivirus_check_table is created
    """

    # Arrange
    monkeypatch.setattr(utils, 'check_document', AsyncMock(return_value=check_status))

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    # Act
    async with app['db'].acquire() as conn:
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    # Assert
    async with app['db'].acquire() as conn:
        antivirus_check = await select_document_antivirus_check(
            conn=conn,
            document_id=document.id,
        )

    assert antivirus_check.status == check_status


@pytest.mark.parametrize(
    ('check_status',),
    [
        (AntivirusCheckStatus.clean,),
        (AntivirusCheckStatus.infected,),
    ],
)
async def test_antivirus_check_draft(aiohttp_client, monkeypatch, check_status):
    """
    Given:
        - Draft
    When:
        - Draft sent to antivirus check
    Then:
        - Antivirus check is performed and new entity in
          draft_antivirus_check_table is created
    """

    # Arrange
    monkeypatch.setattr(utils, 'check_document', AsyncMock(return_value=check_status))

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    version = await prepare_document_version(
        document_id=document.id,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        type=DocumentVersionType.new_upload,
    )
    draft = await prepare_draft_from_version(
        version_id=version.id,
        role_id=user.role_id,
    )

    # Act
    async with app['db'].acquire() as conn:
        await add_draft_antivirus_checks(
            conn=conn,
            draft_ids=[draft.id],
        )

    # Assert
    async with app['db'].acquire() as conn:
        antivirus_check = await select_draft_antivirus_check(
            conn=conn,
            draft_id=draft.id,
        )

    assert antivirus_check.status == check_status
