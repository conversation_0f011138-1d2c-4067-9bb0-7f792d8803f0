{% extends "email/layout_v2.jinja2" %}
{% import "email/macros.jinja2" as macros %}
{% import "email/blocks/table_blocks.jinja2" as t %}
{% from "email/blocks/header_macros.jinja2" import header %}
{% from "email/blocks/about_v2_macros.jinja2" import about %}

{% block content %}
    {{
        header(
            text=header_title,
            image_link="images/emails/sign_document.png",
            image_style="width: 224px; height: 135px",
            button_link=document_url,
            button_text=_("Перейти до документа")
        )
    }}
    {# Document notifications #}
    <table style="{{ macros.style_table_base() }}; {{ macros.style_content_body() }};">

        {{ t.greeting_row(name=recipient_first_name) }}
        {{ t.spacing_row(height="10px") }}

        {% call t.emoji_row(emoji="😊") %}
            {{ t.header_cell(text=_("Ваш контрагент")) }}
            {{
                t.user_company_cell(
                    email=user_email,
                    name=user_name,
                    company_name=user_company_name,
                    company_edrpou=user_company_edrpou
                )
            }}
        {% endcall %}
        {{ t.spacing_row(height="23px") }}

        {% call t.emoji_row(emoji="📄") %}
            {% if group_name %}
                <div style="font-weight: bold; margin-bottom: 5px;">
                    {% trans %}Надіслав вам{% endtrans %}
                    <span style="{{ macros.style_header() }}">{% trans %}як учаснику групи{% endtrans %} {{ group_name }}</span>
                    {% trans %}документ{% endtrans %}
                </div>
            {% else %}
                <div style="margin-bottom: 5px;">
                    <span style="font-weight: bold;">{% trans %}Надіслав вам документ до компанії{% endtrans %}</span> <span> {{ recipient_company_edrpou }} {{ recipient_company_name }}</span>
                </div>
            {% endif %}
            {{ macros.link(document_url, text=document_title) }}
        {% endcall %}
        {{ t.spacing_row(height="23px") }}

        {% if comment %}
            {% call t.emoji_row(emoji="📮") %}
                {{ t.header_cell(text=_("І додав коментар")) }}
                <p style="{{ macros.style_comment_block() }}">{{ comment }}</p>
            {% endcall %}
        {% endif %}

        {% if is_versioned %}
            {{ t.spacing_row(height="45px") }}
            {{ t.block_info_row(text=_("Для цього документа увімкнена можливість додавати нові версії")) }}
        {% endif %}

        {{ t.spacing_row(height="45px") }}
        {{ t.button_row(url=document_url, text=_("Перейти до документа")) }}
    </table>

{% endblock %}
