import logging
import re

from api.errors import DoesNotExist, InvalidRequest, Object
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.directories.constants import (
    MAX_DIRECTORIES_COUNT,
    MAX_DIRECTORIES_DEPTH,
)
from app.directories.db import (
    count_all_company_directories,
    select_all_subtree_directories,
    select_company_directories_by_ids,
    select_company_directory_by_id,
    select_directories_by_parent_id,
    select_directory_ids_with_documents,
    select_document_ids_by_directories,
    select_parent_children_max_depth,
)
from app.directories.schemas import (
    AddDocumentsToDirectorySchema,
    CreateDirectorySchema,
    DeleteDirectoriesSchema,
    UpdateDirectorySchema,
)
from app.directories.types import (
    AddToDirectoryCtx,
    CreateDirectoryCtx,
    Directory,
    UpdateDirectoryCtx,
)
from app.documents.validators import validate_documents_access
from app.i18n import _
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.lib.validators import validate_pydantic

logger = logging.getLogger(__name__)


async def validate_add_children_to_directory(
    conn: DBConnection, user: User, raw_data: DataDict
) -> AddToDirectoryCtx:
    from app.archive.validators import validate_documents_archived

    valid_data = validate_pydantic(AddDocumentsToDirectorySchema, raw_data)
    document_ids = valid_data.document_ids
    directory_ids = valid_data.directory_ids

    # Allow move document to directories only for users with specific permissions
    if document_ids:
        validate_user_permission(user, {'can_archive_documents'})

    # We allow to change directory tree structure with separate permission rules,
    # even directories have documents
    if directory_ids:
        validate_user_permission(user, {'can_edit_directories'})

    parent_directory = await _validate_parent_directory_exists(
        conn,
        parent_id=valid_data.parent_id,
        company_id=user.company_id,
    )

    # Validate directories to move
    directories = await select_company_directories_by_ids(
        conn=conn, company_id=user.company_id, ids=directory_ids
    )
    if directory_ids and len(directories) != len(directory_ids):
        raise DoesNotExist(
            Object.directory,
            directory_ids=list(set(directory_ids) - {d.id for d in directories}),
        )
    await _validate_directory_names_uniqueness(
        conn=conn,
        parent_id=parent_directory.id if parent_directory else None,
        directory_names=[d.name for d in directories],
        company_id=user.company_id,
    )
    await _validate_directories_parent_change(
        conn=conn, new_parent=parent_directory, directories=directories
    )

    # Validate documents to move
    await validate_documents_archived(
        conn=conn, company_id=user.company_id, document_ids=document_ids
    )
    documents = await validate_documents_access(conn=conn, user=user, doc_ids=document_ids)

    return AddToDirectoryCtx(
        parent_directory=parent_directory,
        documents=documents,
        directories=directories,
    )


async def validate_update_directory(
    conn: DBConnection, user: User, raw_data: DataDict
) -> UpdateDirectoryCtx:
    validate_user_permission(user, {'can_edit_directories'})
    valid_data = validate_pydantic(UpdateDirectorySchema, raw_data)
    _directory_name_validator(valid_data)

    directory = await validate_directory_exists(
        conn,
        id=valid_data.id,
        company_id=user.company_id,
    )

    directory_names = [valid_data.name] if valid_data.name else []
    await _validate_directory_names_uniqueness(
        conn=conn,
        parent_id=directory.parent_id,
        directory_names=directory_names,
        company_id=user.company_id,
    )

    return UpdateDirectoryCtx(
        directory=directory,
        new_name=valid_data.name,
    )


async def validate_delete_directories(
    conn: DBConnection, user: User, raw_data: DataDict
) -> list[Directory]:
    validate_user_permission(user, {'can_edit_directories'})

    valid_data = validate_pydantic(DeleteDirectoriesSchema, raw_data)

    directory_ids = valid_data.directory_ids

    # Validate directories exists
    requested_directories = await select_company_directories_by_ids(
        conn=conn, company_id=user.company_id, ids=directory_ids
    )
    if directory_ids and len(requested_directories) != len(directory_ids):
        raise DoesNotExist(
            Object.directory,
            directory_ids=list(set(directory_ids) - {d.id for d in requested_directories}),
        )

    # Select all directories: from user request and all their children
    directories = await select_all_subtree_directories(
        conn=conn, company_id=user.company_id, root_ids=directory_ids
    )

    document_ids = await select_document_ids_by_directories(
        conn=conn, company_id=user.company_id, directory_ids=[d.id for d in directories]
    )
    if document_ids:
        # Get non-empty directories for error message
        non_empty_directory_ids = await select_directory_ids_with_documents(
            conn=conn, company_id=user.company_id, document_ids=document_ids
        )
        directory_by_id = {d.id: d for d in directories}
        non_empty_directories = [directory_by_id[dir_id] for dir_id in non_empty_directory_ids]

        non_empty_dirs_path = []
        for non_empty_dir in non_empty_directories:
            path_items = []
            for d_id in non_empty_dir.path:
                dir_ = directory_by_id.get(d_id)
                if not dir_:
                    continue
                path_items.append(dir_.name)
            path_items.append(non_empty_dir.name)
            path = '/'.join(path_items)
            non_empty_dirs_path.append(path)
        raise InvalidRequest(
            reason=_('Неможливо видалити папки, оскільки вони містять документи'),
            details={
                'failed_directories': list(non_empty_dirs_path),
            },
        )

    return directories


async def validate_create_directory(
    conn: DBConnection, user: User, raw_data: DataDict
) -> CreateDirectoryCtx:
    validate_user_permission(user, {'can_edit_directories'})
    valid_data = validate_pydantic(CreateDirectorySchema, raw_data)
    _directory_name_validator(valid_data)

    parent_directory = await _validate_parent_directory_exists(
        conn=conn, parent_id=valid_data.parent_id, company_id=user.company_id
    )
    await _validate_directory_names_uniqueness(
        conn=conn,
        parent_id=parent_directory.id if parent_directory else None,
        company_id=user.company_id,
        directory_names=[valid_data.name],
    )

    if (
        parent_directory
        and parent_directory.path
        and len(parent_directory.path) >= (MAX_DIRECTORIES_DEPTH - 1)
    ):
        raise InvalidRequest(
            reason=_('Максимальна вкладеність папок не має перевищувати {max_depth}').bind(
                max_depth=MAX_DIRECTORIES_DEPTH
            )
        )

    directories_count = await count_all_company_directories(conn, company_id=user.company_id)
    if directories_count >= MAX_DIRECTORIES_COUNT:
        raise InvalidRequest(
            reason=_('Максимальна кількість папок не має перевищувати {max_count}').bind(
                max_count=MAX_DIRECTORIES_COUNT
            )
        )

    return CreateDirectoryCtx(
        parent=parent_directory,
        name=valid_data.name,
    )


async def validate_directory_exists(conn: DBConnection, id: int, company_id: str) -> Directory:
    directory = await select_company_directory_by_id(conn, id=id, company_id=company_id)
    if directory is None:
        raise DoesNotExist(Object.directory, directory_id=id, company_id=company_id)
    return directory


async def _validate_parent_directory_exists(
    conn: DBConnection, parent_id: int | None, company_id: str
) -> Directory | None:
    new_parent = None
    if parent_id is not None:
        new_parent = await select_company_directory_by_id(conn, id=parent_id, company_id=company_id)
        if not new_parent:
            raise DoesNotExist(
                obj=Object.directory,
                parent_id=str(parent_id),
            )
    return new_parent


async def _validate_directories_parent_change(
    conn: DBConnection, new_parent: Directory | None, directories: list[Directory]
) -> None:
    """
    Validate changing parent for list of directories:
    - check all directories from list share same parent.
    - check loops: the new_parent is not one of the directories being updated;
                   the new_parent is not in the path of any directory in the list.
    - check directories_tree max depth after changing parent_id for every directory from list
    """

    if not directories:
        return

    # In the future that rule may be discarded, so we need to implement
    # more complex processing:
    # - rewrite cycle validation for provided directories list.
    # - rewrite validation for max tree depth.
    # - rewrite update all children path for directories list.
    parent_ids = {directory.parent_id for directory in directories}
    if len(parent_ids) > 1:
        raise InvalidRequest(reason=_('Усі переміщувані папки мають знаходитись в спільній папці'))

    # No additional check if we are moving directories to root node
    if not new_parent:
        return

    if new_parent.id in {directory.id for directory in directories}:
        raise InvalidRequest(reason=_('Не можливо перемістити папку саму в себе'))

    for dir_id in new_parent.path:
        for d in directories:
            if dir_id == d.id:
                raise InvalidRequest(
                    reason=_(
                        'Не можливо перемістити папку {directory} '
                        'в одну із свої дочірніх папок {child}'
                    ).format(directory=d.name, child=new_parent)
                )

    # Works with first rule, all directories share same parent
    max_depth = await select_parent_children_max_depth(conn=conn, directory_id=directories[0].id)

    max_path_len = len(new_parent.path) + max_depth - 1
    if max_path_len >= (MAX_DIRECTORIES_DEPTH - 1):
        raise InvalidRequest(
            reason=_('Максимальна вкладеність папок не має перевищувати {max_depth}').bind(
                max_depth=MAX_DIRECTORIES_DEPTH
            )
        )


def _directory_name_validator(
    valid_data: UpdateDirectorySchema | CreateDirectorySchema,
) -> None:
    if not _is_valid_folder_name(valid_data.name):
        logger.warning('Unsupported directory name', extra={'input_name': valid_data.name})
        raise InvalidRequest(
            reason=_(
                'Імʼя папки містить заборонені символи, будь-ласка користуйтесь '
                'загальноприйнятими правилами для операційних систем при створенні папок'
            )
        )


async def _validate_directory_names_uniqueness(
    conn: DBConnection,
    parent_id: int | None,
    directory_names: list[str],
    company_id: str,
) -> None:
    """
    Validate name uniqueness across directory_tree level.
    Validate names lowercase, because we want OS(windows) compatibility.
    """
    if not directory_names:
        return

    level_directories = await select_directories_by_parent_id(
        conn=conn,
        parent_id=parent_id,
        company_id=company_id,
    )
    existing_names = {directory.name.lower() for directory in level_directories}
    for name in directory_names:
        if name.lower() in existing_names:
            raise InvalidRequest(
                reason=_('Папка з такою назвою вже існує на цьому рівні вкладеності')
            )


def _is_valid_folder_name(name: str) -> bool:
    """
    Check if a given folder name is valid across popular OS(Unix,Windows).

    A valid folder name must not contain reserved characters, reserved names
    (e.g., "CON", "PRN", "COM1"), or trailing dots.

    Examples:
    >>> _is_valid_folder_name("Documents")
    True
    >>> _is_valid_folder_name("MyFolder")
    True
    >>> _is_valid_folder_name("CON")
    False
    >>> _is_valid_folder_name("COM1")
    False
    >>> _is_valid_folder_name("Folder.")
    False
    >>> _is_valid_folder_name("Invalid/Name")
    False
    >>> _is_valid_folder_name("Another:Name")
    False
    >>> _is_valid_folder_name("Normal Folder")
    True
    """
    # Check for reserved characters
    if re.search(r'[<>:"/\\|?*]', name):
        return False

    # Reserved names for Windows
    if name.upper() in {'CON', 'PRN', 'AUX', 'NUL'} or re.match(
        r'^(COM[1-9]|LPT[1-9])$', name.upper()
    ):
        return False

    # Check for trailing dots
    if name.endswith('.'):
        return False

    return True
