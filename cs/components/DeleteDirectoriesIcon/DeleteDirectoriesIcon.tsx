import React from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip } from '@vchasno/ui-kit';

import { useDeleteDirectoriesPopupContext } from 'components/DeleteDirectoriesPopup/context';
import { getSelectedDirectories } from 'components/documentList/selectors';
import DeleteSvg from 'icons/delete.svg';
import { t } from 'ttag';
import IconButton from 'ui/iconButton/iconButton';

const DeleteDirectoriesIcon: React.FC = () => {
    const { onOpen } = useDeleteDirectoriesPopupContext();
    const selectedDirectories = useSelector(getSelectedDirectories);

    return (
        <BlackTooltip title={t`Видалити`}>
            <span>
                <IconButton
                    svg={DeleteSvg}
                    onClick={() => onOpen(selectedDirectories)}
                />
            </span>
        </BlackTooltip>
    );
};

export default DeleteDirectoriesIcon;
