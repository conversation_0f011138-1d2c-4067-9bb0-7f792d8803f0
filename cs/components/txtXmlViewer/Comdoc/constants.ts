export const DATE_FORMAT_FULL = 'DD MMMM YYYY';
export const DATE_FORMAT_SHORT = 'DD MM YYYY';
export const DATE_FORMAT_SHORT_WITH_DOTS = 'DD.MM.YYYY';

export const AGREEMENT_NAME_QUERY = 'Заголовок > НомерДоговоруПоставки';
export const AGREEMENT_DATE_QUERY = 'Заголовок > ДатаДоговоруПоставки';
export const DATE_QUERY = 'Заголовок > ДатаДокументу';
export const NUMBER_QUERY = 'Заголовок > НомерДокументу';
export const NUMBER_OVERWRITE_QUERY = 'Заголовок > НомерДокументуЗамінений';
export const MANAGER_ORDER_QUERY = 'Заголовок > ВідповідальнийМенеджер';
export const PAYMENT_TERN_ORDER_QUERY = 'Заголовок > ДатаОплати';
export const DOCUMENT_NAME_QUERY = 'Заголовок > ТипДокументу';
export const EXTRA_COMPOSITION_CHECKED_QUERY = 'Заголовок > ПеревіреноСклад';
export const EXTRA_SHIPPED_QUERY = 'Заголовок > Відвантажено';
export const EXTRA_SHIPMENT_COMPOSITION = 'Заголовок > СкладВідвантаження';
export const DOCUMENT_TYPE_QUERY =
    'Заголовок > КодТипуДокументу, КодТипуДокументу';

export const PARENT_DOCUMENT_NUMBER =
    'Заголовок > ДокПідстава > НомерДокументу';
export const PARENT_DOCUMENT_TYPE = 'Заголовок > ДокПідстава > ТипДокументу';
export const PARENT_DOCUMENT_DATE = 'Заголовок > ДокПідстава > ДатаДокументу';
export const PARENT_ORDER_NUMBER_DATE = 'Заголовок > НомерЗамовлення';
export const PARENT_ORDER_DATE = 'Заголовок > ДатаЗамовлення';
export const PARENT_ORDER_DATE_PAYMENT = 'Заголовок > ТермінОплати';
export const PARENT_TAX_NUMBER_PAYMENT = 'Заголовок > НомерПодатковоїНакладної';
export const PARENT_TAX_DATE_PAYMENT = 'Заголовок > ДатаПодатковоїНакладної';

export const OWNER_FULL_NAME_QUERY =
    'Контрагент:first-child > НазваКонтрагента';
export const OWNER_STATUS_QUERY = 'Контрагент:first-child > СтатусКонтрагента';
export const OWNER_EDRPOU_QUERY = 'Контрагент:first-child > КодКонтрагента';
export const OWNER_IPN_QUERY = 'Контрагент:first-child > ІПН';
export const OWNER_VAT_CERTIFICATE_QUERY =
    'Контрагент:first-child > СвідоцтвоПДВ';
export const OWNER_IBAN_QUERY = 'Контрагент:first-child > IBAN';
export const OWNER_PHONE_QUERY = 'Контрагент:first-child > Телефон';
export const OWNER_TYPE_QUERY = 'Контрагент:first-child > ВидОсоби';

export const OWNER_BANK_ACCOUNT_QUERY = 'Контрагент:first-child > ПоточРах';
export const OWNER_BANK_MFO_QUERY = 'Контрагент:first-child > МФО';

export const PARTNER_FULL_NAME_QUERY =
    'Контрагент:last-child > НазваКонтрагента';
export const PARTNER_MALL_QUERY = 'Контрагент:last-child > НомерТЦ';
export const PARTNER_STATUS_QUERY = 'Контрагент:last-child > СтатусКонтрагента';
export const PARTNER_EDRPOU_QUERY = 'Контрагент:last-child > КодКонтрагента';
export const PARTNER_IPN_QUERY = 'Контрагент:last-child > ІПН';
export const PARTNER_TYPE_QUERY = 'Контрагент:last-child > ВидОсоби';
export const PARTNER_VAT_CERTIFICATE_QUERY =
    'Контрагент:last-child > СвідоцтвоПДВ';
export const PARTNER_IBAN_QUERY = 'Контрагент:last-child > IBAN';
export const PARTNER_ADDRESS_CITY_QUERY =
    'Контрагент:last-child > ЮрАдреса > Місто';
export const PARTNER_ADDRESS_QUERY =
    'Контрагент:last-child > ЮрАдреса > Вулиця';
export const PARTNER_ADDRESS_INDEX_QUERY =
    'Контрагент:last-child > ЮрАдреса > Індекс';
export const PARTNER_PHONE_QUERY = 'Контрагент:last-child > Телефон';

export const PARTNER_BANK_ACCOUNT_QUERY = 'Контрагент:last-child > ПоточРах';
export const PARTNER_BANK_MFO_QUERY = 'Контрагент:last-child > МФО';

export const FROM_PERFORMER_QUERY =
    'Параметри > Параметр[назва="Від Виконавця"]';
export const DELIVERY_ADDRESS = 'Параметри > Параметр[назва="Адреса доставки"]';
export const DELIVERY_ADDRESS_POINT =
    'Параметри > Параметр[назва="Точка доставки"]';

export const DOCUMENT_BASIS_ADDRESS =
    'Параметри > Параметр[назва="Номер договору"]';
export const DOCUMENT_BASIS_DATE_ADDRESS =
    'Параметри > Параметр[назва="Дата договору"]';

export const DOCUMENT_PARAM_PARTNER_ADDRESS =
    'Параметри > Параметр[назва="Адреса Покупець"]';
export const DOCUMENT_PARAM_PARTNER_BANK =
    'Параметри > Параметр[назва="Назва банку Покупець"]';
export const DOCUMENT_PARAM_OWNER_ADDRESS =
    'Параметри > Параметр[назва="Адреса Продавець"]';
export const DOCUMENT_PARAM_OWNER_BANK =
    'Параметри > Параметр[назва="Назва банку Продавець"]';

export const DOCUMENT_PARAM_NAME_POWER_OF_ATTORNEY =
    'Параметри > Параметр[назва="Номер довіреності"]';
export const DOCUMENT_PARAM_DATE_POWER_OF_ATTORNEY =
    'Параметри > Параметр[назва="Дата довіреності"]';

export const TOTAL_SUM_QUERY = 'ВсьогоПоДокументу > СумаБезПДВ';
export const TOTAL_TAXES_QUERY = 'ВсьогоПоДокументу > ПДВ';
export const TOTAL_DOCUMENT_SUM_QUERY = 'ВсьогоПоДокументу > Сума';

export const SERVICES_QUERY = 'Таблиця';
export const SERVICE_ITEM_QUERY = 'Рядок';
export const SERVICE_TEXT_QUERY = 'Текст';
export const SERVICE_EAN_QUERY = 'Штрихкод';
export const SERVICE_ITEM_CODE_QUERY = 'АртикулПокупця';
export const SERVICE_ITEM_SELLER_CODE_QUERY = 'АртикулПродавця';
export const SERVICE_ITEM_VENDOR_CODE_QUERY = 'КодПродавця';
export const SERVICE_NATIONAL_CODE_QUERY = 'КодУКТЗЕД';
export const SERVICE_ALT_PRICE_WITHOUT_TAX_QUERY = 'АльтЦінаБезПДВ';
export const SERVICE_ALT_QUANTITY_QUERY = 'КількістьАльтОВ';
export const SERVICE_ALT_MEASURE_QUERY = 'АльтОВ';
export const SERVICE_ALT_SUM_WITHOUT_TAX_QUERY = 'АльтСумаБезПДВ';
export const SERVICE_NUMBER_QUERY = 'НомПоз';
export const SERVICE_NAME_QUERY = 'Найменування';
export const SERVICE_PRICE_QUERY = 'БазоваЦіна';
export const SERVICE_PRICE_WITH_TAX_QUERY = 'Ціна';
export const SERVICE_ITEM_TAX_QUERY = 'ПДВ';
export const SERVICE_DECLARED_QUANTITY_QUERY = 'ЗаявленаКількість';
export const SERVICE_QUANTITY_QUERY =
    'ДоПовернення > Кількість, ПрийнятаКількість';
export const SERVICE_NOT_ACCEPTED_QUANTITY_QUERY = 'Причина > Кількість';
export const SERVICE_NOT_ACCEPTED_REASON = 'Причина > Опис';
export const SERVICE_SUM_QUERY = 'ВсьогоПоРядку > СумаБезПДВ';
export const SERVICE_TAX_QUERY = 'ВсьогоПоРядку > СумаПДВ';
export const SERVICE_SUM_WITH_TAX_QUERY = 'ВсьогоПоРядку > Сума';
export const SERVICE_UNIT_QUERY = 'ОдиницяВиміру';
export const SERVICE_ADDITIONAL_QUANTITY_QUERY = 'КількістьКг';
export const SERVICE_ADDITIONAL_PRICE_QUERY = 'ЦінаКг';

export const LOCATION_QUERY = 'МісцеСкладання';

export enum ComdocTypes {
    INCONSISTENCY_ACT = '005',
    SALES_INVOICE = '006',
    INCOME_INVOICE = '007',
    COMMODITY_SPECIFICATION = '008',
    RECONCILATION_ACT = '015',
    COMDOC_016 = '016',
    SERVICE_DEED = '018',
    MUTUAL_SETTLEMENTS_ACT = '029',
    RETURN_PACKAGING = '031',
    WORK_ACCEPTANCE_CERTIFICATE = '013',
    RETURN_TO_SUPPLIER = '050',
    RETURN_INVOICE = '012',
}

export const FlowFaynoMarketEdrpouList = [
    '31774943',
    '44341366',
    '44411993',
    '99000413',
];
export const FlowVarusEdrpouList = ['30982361'];
export const AsbisEdrpouList = ['25274129'];
