import logging
import typing as t

import pydantic
from aiohttp import web

from api.errors import AccessDenied, AlreadyExists, Object
from app.auth.types import User
from app.auth.utils import get_company_config, has_permission
from app.auth.validators import validate_coworkers_roles_ids, validate_user_permission
from app.billing.enums import CompanyPermission
from app.contacts.validators import validate_contacts_exists
from app.documents.validators import validate_documents_access
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.profile.validators import validate_company_permission
from app.tags.db import (
    build_tags_access_filter,
    select_tags_by_ids,
    select_tags_by_names,
)
from app.tags.schemas import GetTagRolesParams
from app.tags.types import (
    ConnectContactTagOptions,
    ConnectRoleTagOptions,
    CreateTagsCtx,
    CreateTagsForContactsOptions,
    CreateTagsForDocumentOptions,
    CreateTagsForRolesOptions,
    DocumentTagConnectionOptions,
)
from app.tags.utils import get_tags_access_filter

logger = logging.getLogger(__name__)

TagName = t.Annotated[
    str,
    pydantic.StringConstraints(strip_whitespace=True, min_length=1, max_length=255),
]


class CreateTagForDocumentMultipleSchema(pydantic.BaseModel):
    names: pv.UniqueList[TagName] = pydantic.Field(..., min_length=1)
    documents_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


class DocumentTagMultipleSchema(pydantic.BaseModel):
    documents_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)
    tags_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


class RoleTagMultipleSchema(pydantic.BaseModel):
    roles_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)
    tags_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


class ContactTagMultipleSchema(pydantic.BaseModel):
    contacts_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)
    tags_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


class CreateTagForRolesMultipleSchema(pydantic.BaseModel):
    names: pv.UniqueList[TagName] = pydantic.Field(..., min_length=1)
    roles_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


class CreateTagForContactsMultipleSchema(pydantic.BaseModel):
    names: pv.UniqueList[TagName] = pydantic.Field(..., min_length=1)
    contacts_ids: pv.UniqueList[pv.UUID] = pydantic.Field(..., min_length=1)


def _raise_tags_access_error(user: User, tags_ids: list[str], existed_tags: set[str]) -> t.NoReturn:
    missed_tags = list(set(tags_ids) - existed_tags)
    logger.warning(
        'Access to tags is missed',
        extra={
            'missed_tags_ids': missed_tags,
            'user_email': user.email,
            'user_role': user.user_role,
            'user_role_id': user.role_id,
            'user_company_edrpou': user.company_edrpou,
            'user_company_id': user.company_id,
        },
    )
    raise AccessDenied(reason=_('Доступ до одного або декількох ярликів заборонено'))


async def validate_tags_permissions(conn: DBConnection, user: User, count: int) -> None:
    """Validate if user has permission to work with tags"""
    await validate_company_permission(
        conn=conn,
        company_id=user.company_id,
        permission=CompanyPermission.tags_enabled,
        new_entity_count=count,
        company_edrpou=user.company_edrpou,
    )
    validate_user_permission(user, {'can_create_tags'})


async def validate_tags_access(
    conn: DBConnection,
    tags_ids: list[str],
    user: User,
) -> list[DBRow]:
    """
    Strict validate access to given tags IDs.
    It also raises AccessDenied for not existed tags
    """
    if not tags_ids:
        return []

    tags_access_filters = await get_tags_access_filter(conn, user)
    tags = await select_tags_by_ids(conn, tags_ids, filters=[tags_access_filters])

    existed_tags = {tag.id for tag in tags}
    if existed_tags != set(tags_ids):
        _raise_tags_access_error(user, tags_ids, existed_tags)

    return tags


async def validate_roles_tags_access(
    conn: DBConnection, tags_ids: list[str], user: User
) -> list[DBRow]:
    """
    Strict validate access to add/delete tags by user for given tags IDs.

    Validator is pretty similar to validator validate_tags_access, with one exception,
    not admin user can not assign/delete role tags for another users even if
    company has config that allows any coworker to view all company
    tags (soft_access_to_tags).
    """
    tags_access_filters = build_tags_access_filter(
        role_id=user.role_id,
        user_role=user.user_role,
        company_id=user.company_id,
        can_view_document=user.can_view_document,
        can_view_private_document=user.can_view_private_document,
        soft_access_to_tags=False,
    )

    tags = await select_tags_by_ids(conn, tags_ids, filters=[tags_access_filters])

    existed_tags = {tag.id for tag in tags}
    if existed_tags != set(tags_ids):
        _raise_tags_access_error(user, tags_ids, existed_tags)

    return tags


async def validate_tags_for_duplication(
    conn: DBConnection, tags_names: list[str], company_id: str
) -> None:
    """
    Check existence of given tags by names in company,
    used for preventing tags duplication
    """
    tags = await select_tags_by_names(conn, tags_names, company_id=company_id, case_sensitive=False)
    if len(tags) > 0:
        existed_names = [tag.name for tag in tags]
        logger.warning(
            'Some of tags already exists in company',
            extra={
                'company_id': company_id,
                'tags_names': tags_names,
                'existed_names': existed_names,
            },
        )

        existed_names_joined = ', '.join(existed_names)
        reason = _(
            'Неможливо створити ярлики, які вже існують в компанії: '
            '{names}. '
            'Зверніться до адміністратора компанії, щоб він надав доступ до '
            'цих ярликів.'
        ).bind(names=existed_names_joined)
        raise AlreadyExists(Object.tags, reason=reason, names=existed_names)


async def validate_tags_on_document_upload(
    conn: DBConnection,
    tags_ids: list[str],
    tags_names: list[str],
    user: User | None,
) -> CreateTagsCtx:
    """
    Validate the ability to create and connect tags with a currently uploaded document
    """

    if not tags_ids and not tags_names:
        return CreateTagsCtx(tags_ids=[], tags_names=[])

    if not user:
        raise AccessDenied(reason=_('Неможливо створити ярлики без авторизації'))

    create_ctx = await soft_validate_create_new_tags(
        conn=conn,
        tags_names=tags_names,
        user=user,
    )
    tags_ids.extend(create_ctx.tags_ids)
    tags_names = create_ctx.tags_names

    if tags_ids:
        await validate_tags_access(conn=conn, tags_ids=tags_ids, user=user)

    return CreateTagsCtx(
        tags_ids=tags_ids,
        tags_names=tags_names,
    )


async def soft_validate_create_new_tags(
    conn: DBConnection,
    tags_names: list[str],
    user: User,
) -> CreateTagsCtx:
    """
    Check if user can add new tags to the company. If the tag already exists in the company,
    ID of that tag will be returned from this function.

    Existed tags can be passed as names instead of IDs, when user mass edits tags on several
    documents. In this case, each document is updated in a loop with the same payload, and after
    the first document all "new_tags" become created tags.
    """
    if not tags_names:
        return CreateTagsCtx(tags_ids=[], tags_names=[])

    tags = await select_tags_by_names(
        conn, tags_names=tags_names, company_id=user.company_id, case_sensitive=False
    )

    existed_names = {tag.name for tag in tags}
    new_names = set(tags_names) - existed_names

    if new_names:
        await validate_tags_permissions(conn=conn, user=user, count=len(new_names))

    existed_ids = [tag.id for tag in tags]
    return CreateTagsCtx(
        tags_ids=existed_ids,
        tags_names=list(new_names),
    )


async def validate_create_tags_for_documents(
    conn: DBConnection, request: web.Request, user: User
) -> CreateTagsForDocumentOptions:
    """Validate mass creating tags and connect them with document"""

    # Validate format for creating tags
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(CreateTagForDocumentMultipleSchema, raw_data)
    documents_ids = data.documents_ids
    tags_names = data.names

    await validate_tags_permissions(conn=conn, user=user, count=len(tags_names))
    await validate_tags_for_duplication(conn, tags_names, company_id=user.company_id)
    await validate_documents_access(conn=conn, user=user, doc_ids=documents_ids)

    return CreateTagsForDocumentOptions(tags_names=tags_names, document_ids=documents_ids)


async def validate_document_tags(
    conn: DBConnection, request: web.Request, user: User
) -> DocumentTagConnectionOptions:
    """Validate ability to create or disconnect connection between
    documents and tags.
    NOTE: this validator doesn't check existence of connection, for using
    as validator for creating and removing connection .
    """

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(DocumentTagMultipleSchema, raw_data)
    tags_ids = data.tags_ids
    documents_ids = data.documents_ids

    tags = await validate_tags_access(conn, tags_ids=tags_ids, user=user)
    await validate_documents_access(conn=conn, user=user, doc_ids=documents_ids)

    return DocumentTagConnectionOptions(tags=tags, documents_ids=documents_ids)


def validate_permission_to_change_roles_tags(user: User) -> None:
    # todo[DOCUMENTS_PRIVATE_ACCESS]: investigate if "can_view_document" is proper permission here
    if not has_permission(user, {'can_view_document'}):
        raise AccessDenied(
            reason=_(
                'Створювати, видаляти чи призначати ярлики для ролей можуть лише '
                'адміністратори компанії або користувачі з правами переглядати '
                'документи в компанії.'
            )
        )


async def validate_create_tags_for_role(
    conn: DBConnection, request: web.Request, user: User
) -> CreateTagsForRolesOptions:
    """Strict validation of that new tags not exists"""

    data = await validators.validate_json_request(request)
    validate_permission_to_change_roles_tags(user)
    valid_data = validators.validate_pydantic(CreateTagForRolesMultipleSchema, data)

    roles_ids = valid_data.roles_ids
    tags_names = valid_data.names
    company_edrpou = user.company_edrpou

    await validate_tags_permissions(conn=conn, user=user, count=len(tags_names))
    await validate_tags_for_duplication(conn, tags_names, company_id=user.company_id)
    await validate_coworkers_roles_ids(conn, roles_ids, company_edrpou=company_edrpou)

    return CreateTagsForRolesOptions(tags_names=tags_names, roles_ids=roles_ids)


async def validate_create_tags_for_contacts(
    conn: DBConnection, request: web.Request, user: User
) -> CreateTagsForContactsOptions:
    """
    Strict validation of that new tags not exists and all contacts exist
    or user have access to all contacts"""

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(CreateTagForContactsMultipleSchema, data)

    contacts_ids = valid_data.contacts_ids
    tags_names = valid_data.names
    company_id = user.company_id

    await validate_tags_permissions(conn=conn, user=user, count=len(tags_names))
    await validate_tags_for_duplication(conn, tags_names, company_id=user.company_id)
    await validate_contacts_exists(conn=conn, contacts_ids=contacts_ids, company_id=company_id)

    return CreateTagsForContactsOptions(tags_names=tags_names, contacts_ids=contacts_ids)


async def validate_roles_tags(
    conn: DBConnection, request: web.Request, user: User
) -> ConnectRoleTagOptions:
    """Strict validation of tags exists, access for given tags ids"""

    data = await validators.validate_json_request(request)
    validate_permission_to_change_roles_tags(user)
    valid_data = validators.validate_pydantic(RoleTagMultipleSchema, data)

    roles_ids = valid_data.roles_ids
    tags_ids = valid_data.tags_ids
    company_edrpou = user.company_edrpou

    tags = await validate_roles_tags_access(conn, tags_ids=tags_ids, user=user)
    await validate_coworkers_roles_ids(conn, roles_ids, company_edrpou=company_edrpou)

    return ConnectRoleTagOptions(roles_ids=roles_ids, tags=tags)


async def validate_contacts_tags(
    conn: DBConnection, request: web.Request, user: User
) -> ConnectContactTagOptions:
    """Strict validation of tags exists, access for given tags ids"""

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(ContactTagMultipleSchema, data)
    contacts_ids = valid_data.contacts_ids
    tags_ids = valid_data.tags_ids
    config = await get_company_config(conn, company_edrpou=user.company_edrpou)

    # soft_access_to_tags - DOC-3018
    if not has_permission(user, {'can_edit_company_contact'}) and not config.soft_access_to_tags:
        raise AccessDenied(
            reason=_(
                'Створювати, видаляти чи призначати ярлики для компаній можуть лише '
                'адміністратори компанії.'
            )
        )
    tags = await validate_tags_access(conn, tags_ids=tags_ids, user=user)
    await validate_contacts_exists(conn, contacts_ids, company_id=user.company_id)

    return ConnectContactTagOptions(contacts_ids=contacts_ids, tags=tags)


async def validate_get_tag_roles(conn: DBConnection, request: web.Request, user: User) -> str:
    raw_data = {
        'tag_id': request.match_info['tag_id'],
    }
    data = validators.validate_pydantic(schema=GetTagRolesParams, data=raw_data)
    await validate_tags_access(conn, tags_ids=[data.tag_id], user=user)
    return data.tag_id
