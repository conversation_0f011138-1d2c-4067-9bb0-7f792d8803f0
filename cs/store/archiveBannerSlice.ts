import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export type ArchiveBannerState = {
    isArchiveBannerOpen: boolean;
};

const initialState: ArchiveBannerState = {
    isArchiveBannerOpen: false,
};

export const archiveBannerSlice = createSlice({
    name: 'archiveBanner',
    initialState,
    reducers: {
        setArchiveBannerOpen: (
            state,
            action: PayloadAction<{ isArchiveBannerOpen: boolean }>,
        ) => {
            state.isArchiveBannerOpen = action.payload.isArchiveBannerOpen;
        },
    },
});

export const { setArchiveBannerOpen } = archiveBannerSlice.actions;

export default archiveBannerSlice.reducer;
