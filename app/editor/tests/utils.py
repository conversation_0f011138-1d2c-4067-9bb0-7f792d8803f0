from aiohttp.web_app import Application

from app.auth.types import User
from app.document_versions.tests.utils import prepare_document_version
from app.documents.types import Document
from app.drafts.tests.utils import prepare_draft_from_version
from app.drafts.types import Draft
from app.editor.enums import EditSessionType
from app.editor.types import PreparedContext
from app.editor.utils import prepare_initiate
from app.editor.validators import InitiateEditContext
from app.templates.types import Template
from app.tests.common import prepare_document_data


def get_initiate_edit_session_draft_url() -> str:
    return '/editor/'


def get_file_metadata_url(ctx: PreparedContext) -> str:
    return f'/editor/session/{ctx.session_id}?access_token={ctx.access_token}'


def get_file_content_url(ctx: PreparedContext) -> str:
    return f'/editor/session/{ctx.session_id}/contents?access_token={ctx.access_token}'


def get_file_save_url(ctx: PreparedContext) -> str:
    return f'/editor/session/{ctx.session_id}/contents?access_token={ctx.access_token}'


async def initiate_editor_draft_versioned(
    app: Application,
    user: User,
    document: Document | None = None,
    draft: Draft | None = None,
) -> tuple[PreparedContext, Document | None, Draft | None]:
    if not document:
        document = await prepare_document_data(app, user)
        version = await prepare_document_version(
            role_id=user.role_id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            document_id=document.id,
        )
        draft = await prepare_draft_from_version(
            version_id=version.id,
            role_id=user.role_id,
        )

    context = await prepare_initiate(
        ctx=InitiateEditContext(
            type=EditSessionType.draft,
            user=user,
            extension=document.extension,
            title='Test Document',
            entity_id=draft.id if draft else document.id,
        )
    )
    return context, document, draft


async def initiate_editor_template(
    user: User,
    template: Template,
) -> PreparedContext:
    context = await prepare_initiate(
        ctx=InitiateEditContext(
            type=EditSessionType.template,
            user=user,
            extension=template.extension,
            title='Test Document',
            entity_id=template.id,
        )
    )
    return context
