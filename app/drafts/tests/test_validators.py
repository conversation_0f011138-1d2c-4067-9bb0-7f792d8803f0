import pytest

from api.errors import Code, Error
from app.auth.types import User
from app.document_versions.tests.utils import prepare_document_version
from app.drafts.validators import (
    validate_create_draft_from_versioned,
    validate_draft_create_counter,
)
from app.lib.datetime_utils import utc_now
from app.services import services
from app.tests.common import prepare_client, prepare_document_data

TEST_UUID_1 = '3d42cac4-0660-4eb8-bee5-ac14f61f2544'
TEST_UUID_2 = '162db85c-1c15-4152-acec-cd9d649febe1'
TEST_UUID_3 = '6f04a003-ece7-4732-b39e-46e77c548cc7'
TEST_UUID_4 = 'b5a8b0f6-2bd9-419e-958f-4d5b38ebe637'


async def test_validate_draft_create_counter_overdraft(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)

    monkeypatch.setattr('app.drafts.validators.DRAFT_LIMIT', 0)

    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await validate_draft_create_counter(conn=conn, user=User.from_row(user))

    assert e.value.code == Code.overdraft


async def test_validate_create_draft_from_versioned(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        extension='.pdf',
    )

    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await validate_create_draft_from_versioned(
                conn=conn,
                data={
                    'document_id': document.id,
                    'version': version.id,
                },
                user=User.from_row(user),
            )

    assert e.value.code == Code.invalid_file_extension
