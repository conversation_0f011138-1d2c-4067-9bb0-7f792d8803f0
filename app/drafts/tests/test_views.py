from app.auth.types import User
from app.document_antivirus.enums import AntivirusCheckStatus, AntivirusProvider
from app.document_antivirus.utils import (
    add_draft_antivirus_checks,
    set_status_draft_antivirus_check,
)
from app.document_versions.db import select_document_versions
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.documents.db import select_document
from app.documents.enums import DocumentSource
from app.documents.tests.test_documents_views import TEST_RECIPIENT_EDRPOU, TEST_RECIPIENT_EMAIL
from app.drafts.db import select_drafts
from app.drafts.enums import DraftType
from app.drafts.tests.utils import (
    get_convert_draft_to_new_version_url,
    get_create_draft_from_version_url,
    get_delete_draft_url,
    get_draft_content_url,
    get_draft_convert_to_document_url,
    get_draft_create_docx_url,
    get_draft_create_from_template_url,
    prepare_draft_from_version,
    prepare_draft_standalone,
)
from app.events import document_actions
from app.events.document_actions.db import select_document_actions_for
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus, UserRole
from app.services import services
from app.templates.tests.utils import prepare_template
from app.tests.common import (
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_user_data,
    sign_and_send_document,
)

TEST_UUID_1 = '14b7066b-cd16-4af4-88c5-58285c6c8644'
TEST_UUID_2 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
TEST_UUID_3 = '57fabbd0-f085-4c11-890b-74a5892272a6'
TEST_UUID_4 = '2c705883-70ad-4ff2-833f-d72bd06d4ee7'


async def test_add_draft_from_version(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )

    async def act_assert(status=201):
        response = await client.post(
            get_create_draft_from_version_url(
                document_id=document.id,
                version=version.id,
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == status
        response_data = await response.json()
        assert response_data['company_id'] == user.company_id
        assert response_data['document_id'] == document.id
        assert response_data['type'] == DraftType.version
        assert response_data['creator_role_id'] == user.role_id
        assert response_data['id']
        assert response_data['date_created']
        assert response_data['date_updated']
        # copy version to draft
        assert len(s3_emulation.copy_calls) == 1
        async with services.db.acquire() as conn:
            drafts = await select_drafts(conn=conn, company_id=user.company_id)
        assert len(drafts) == 1
        assert drafts[0].document_id == document.id
        assert drafts[0].company_id == user.company_id
        assert drafts[0].type == DraftType.version
        assert drafts[0].creator_role_id == user.role_id

        # Check action about draft creation from a document version
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 1
        assert actions[0].action == document_actions.Action.document_version_draft_create

    await act_assert()
    # Make sure that we can't create 2 drafts for the same document from the same company
    await act_assert(status=200)


async def test_add_draft_for_signed_document(aiohttp_client, s3_emulation):
    """
    We can't create a draft for a signed document
    """
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app,
        user,
        id=TEST_UUID_1,
        status_id=DocumentStatus.signed.value,
    )
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )

    response = await client.post(
        get_create_draft_from_version_url(
            document_id=document.id,
            version=version.id,
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400
    assert await response.json() == {
        'code': 'invalid_document_status',
        'reason': 'Невалідний статус документу',
        'details': None,
    }


async def test_add_draft_from_version_no_access_to_document(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )
    response = await client.post(
        get_create_draft_from_version_url(
            document_id=document.id,
            version=version.id,
        ),
        headers=prepare_auth_headers(coworker),
    )
    assert response.status == 403
    assert await response.json() == {
        'code': 'access_denied',
        'reason': 'Доступ до документу заборонено',
        'details': None,
    }
    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)

    assert len(drafts) == 0
    assert len(s3_emulation.copy_calls) == 0


async def test_add_draft_from_version_doesnt_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    response = await client.post(
        get_create_draft_from_version_url(
            document_id=document.id,
            version=TEST_UUID_2,
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 404
    assert await response.json() == {
        'code': 'object_does_not_exist',
        'reason': 'Версію документу не знайдено у базі даних',
        'details': {
            'id': 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2',
            'type': 'document_version',
            'type_label': 'Версія документу',
        },
    }


async def test_add_draft_from_version_document_doesnt_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    response = await client.post(
        get_create_draft_from_version_url(
            document_id=TEST_UUID_1,
            version=TEST_UUID_2,
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 403
    assert await response.json() == {
        'code': 'access_denied',
        'reason': 'Доступ до документу заборонено',
        'details': None,
    }


async def test_delete_draft(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )

    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    async def act_assert(status=204):
        response = await client.delete(
            get_delete_draft_url(draft_id=draft.id),
            headers=prepare_auth_headers(user),
        )
        assert response.status == status
        async with services.db.acquire() as conn:
            drafts = await select_drafts(conn=conn, company_id=user.company_id)
        assert len(drafts) == 0

        # Check action about draft delete for drafts created from versioned documents
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 2
        delete_action = document_actions.Action.document_version_draft_delete
        action = next((a for a in actions if a.action == delete_action), None)
        assert action is not None

    await act_assert()
    # Make sure that we can't delete draft twice
    await act_assert(status=404)


async def test_delete_draft_no_access_to_doc(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )

    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)
    response = await client.delete(
        get_delete_draft_url(draft_id=draft.id),
        headers=prepare_auth_headers(coworker),
    )
    assert response.status == 403
    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
    assert len(drafts) == 1


async def test_convert_draft_to_new_version(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        document_id=document.id,
        company_id=user.company_id,
        date_created=utc_now(),
        extension='.docx',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    async def act_assert(status=204):
        response = await client.post(
            get_convert_draft_to_new_version_url(draft_id=draft.id),
            headers=prepare_auth_headers(user),
        )
        assert response.status == status
        async with services.db.acquire() as conn:
            drafts = await select_drafts(conn=conn, company_id=user.company_id)
            versions = await select_document_versions(conn=conn, document_ids=[document.id])
        assert len(drafts) == 0
        assert len(versions) == 2
        upload_version = next(
            (v for v in versions if version.type == DocumentVersionType.new_upload), None
        )

        assert upload_version is not None
        assert upload_version.role_id == user.role_id
        assert upload_version.name == 'name'
        assert upload_version.is_sent is False
        assert upload_version.extension == '.docx'

        editor_version = next(
            (v for v in versions if v.type == DocumentVersionType.editor_created), None
        )
        assert editor_version.role_id == user.role_id
        assert editor_version is not None
        assert editor_version.name == '#2'
        assert editor_version.is_sent is False
        assert editor_version.extension == '.docx'

        # Check action about document version creation from draft
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 2
        version_create_action = document_actions.Action.document_version_create_from_draft
        action = next((a for a in actions if a.action == version_create_action), None)
        assert action is not None

    await act_assert()
    # Make sure that draft was deleted
    await act_assert(status=404)


async def test_convert_draft_to_new_version_no_access_to_doc(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    response = await client.post(
        get_convert_draft_to_new_version_url(draft_id=draft.id),
        headers=prepare_auth_headers(coworker),
    )
    assert response.status == 403
    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
        versions = await select_document_versions(conn=conn, document_ids=[document.id])
    assert len(drafts) == 1
    assert len(versions) == 1


async def test_drafts_deleted_on_signature_added_to_document(aiohttp_client):
    """
    On document sign we should delete all drafts for this document
    """
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )
    await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)
    await sign_and_send_document(
        client,
        document_id=document.id,
        signer=user,
        recipient_email=TEST_RECIPIENT_EMAIL,
        recipient_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
        assert len(drafts) == 0


async def test_download_draft_content(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'Hello, World! I am very unique content',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    response = await client.get(
        get_draft_content_url(draft_id=draft.id),
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    assert await response.read() == b'Hello, World! I am very unique content'


async def test_download_draft_content_from_sign_session(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'Hello, World! I am very unique content',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    sign_session = await prepare_sign_session_data(
        app=app,
        document=document,
        email='<EMAIL>',
        edrpou=user.company_edrpou,
        created_by=user.role_id,
    )

    response = await client.get(
        get_draft_content_url(draft_id=draft.id),
        headers=prepare_sign_session_headers(sign_session, client),
    )

    assert response.status == 200
    assert await response.read() == b'Hello, World! I am very unique content'


async def test_download_draft_content_no_auth(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'Hello, World! I am very unique content',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    response = await client.get(get_draft_content_url(draft_id=draft.id))

    assert response.status == 403


async def test_download_draft_content_no_access_to_doc(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
    )

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'Hello, World! I am very unique content',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)

    response = await client.get(
        get_draft_content_url(draft_id=draft.id),
        headers=prepare_auth_headers(coworker),
    )

    assert response.status == 403


async def test_download_draft_content_infected(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, id=TEST_UUID_1)
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'Hello, World! I am very unique content',
    )
    draft = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)
    async with services.db.acquire() as conn:
        await add_draft_antivirus_checks(
            conn=conn,
            draft_ids=[draft.id],
        )
        await set_status_draft_antivirus_check(
            conn=conn,
            draft_id=draft.id,
            status=AntivirusCheckStatus.infected,
            provider=AntivirusProvider.eset,
        )

    response = await client.get(
        get_draft_content_url(draft_id=draft.id),
        headers=prepare_auth_headers(user),
    )

    assert response.status == 403
    assert await response.json() == {
        'code': 'access_denied',
        'reason': 'Доступ до документа забороненно адміністратором. Документ не'
        ' перевірено або містить вірус.',
        'details': None,
    }


async def test_create_draft_docx(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    response = await client.post(
        get_draft_create_docx_url(),
        json={},
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201

    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
    assert len(drafts) == 1

    draft = drafts[0]
    assert draft.company_id == user.company_id
    assert draft.creator_role_id == user.role_id
    assert draft.type == DraftType.standalone
    assert draft.document_id is None
    assert draft.document_version_id is None
    assert draft.template_id is None


async def test_create_draft_from_template(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    template = await prepare_template(User.from_row(user))

    response = await client.post(
        get_draft_create_from_template_url(template.id),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201

    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
    assert len(drafts) == 1

    draft = drafts[0]
    assert draft.company_id == user.company_id
    assert draft.creator_role_id == user.role_id
    assert draft.type == DraftType.template
    assert draft.document_id is None
    assert draft.document_version_id is None
    assert draft.template_id == template.id
    assert draft.extension == '.docx'


async def test_convert_draft_to_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    draft = await prepare_draft_standalone(user=User.from_row(user))

    response = await client.post(
        get_draft_convert_to_document_url(draft.id),
        json={'title': 'Test'},
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201
    document_id = (await response.json())['id']

    async with services.db.acquire() as conn:
        drafts = await select_drafts(conn=conn, company_id=user.company_id)
    assert len(drafts) == 0

    async with services.db.acquire() as conn:
        documents = await select_document(conn=conn, document_id=document_id)

    assert documents is not None
    assert documents.title == 'Test'
    assert documents.source == DocumentSource.vchasno_standalone
