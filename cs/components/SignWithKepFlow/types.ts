import { Document } from 'services/documents/ts/types';

// flow types
export type SignWithKepFlowView =
    | 'init'
    | 'get_kep_ad'
    | 'select_kep'
    | 'sign_with_app'
    | 'sign'
    | 'kep_activation_in_progress'
    | 'download_kep_app'
    | 'success';

export type SignWithKepFlowOrigin = 'document' | 'documentList';

// api types
export type StorageType = 'cloud' | 'file' | 'hardware_key';

export type SignatureType = 'signature' | 'stamp' | 'rro';

export type ValidityTerm = 'one_year' | 'two_years' | 'file_storage_march_2022';

export type CertificateStatus =
    | 'not_registered'
    | 'registered'
    | 'rejected'
    | 'blocked'
    | 'restored';

export type CertificatePasswordType = 'password' | 'pin_code';

export type CloudCertificateInfo = {
    acsk_key_id: string;
    status: CertificateStatus;
    date_started: string;
    date_finished: string;
    storage_type: StorageType;
    signature_type: SignatureType;
    validity_term: ValidityTerm;
    rro: string | null;
    inn: string | null;
    company_edrpou: string | null;
    company_name: string | null;
    phone: string;
    position: string | null;
    name: string;
    surname: string;
    patronymic: string | null;
    password_type: CertificatePasswordType | null;
};

export type SignResult = {
    signature: string;
    serialNumber: string;
    doc: Document;
    signatureType: SignatureType;
};
