"""Document flow - remove uneccessary columns

Revision ID: 1c1d6ab6ccd7
Revises: c53f11118ce8
Create Date: 2018-08-06 15:21:36.795481


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1c1d6ab6ccd7'
down_revision = 'c53f11118ce8'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        'documents_flows',
        'edrpou',
        existing_type=sa.VARCHAR(length=64),
        nullable=False
    )
    op.drop_column('documents_flows', 'emails')
    op.drop_column('documents_flows', 'role_ids')


def downgrade():
    op.add_column(
        'documents_flows',
        sa.Column(
            'role_ids',
            postgresql.ARRAY(sa.VARCHAR()),
            autoincrement=False,
            nullable=True
        )
    )
    op.add_column(
        'documents_flows',
        sa.Column(
            'emails',
            postgresql.ARRAY(sa.VARCHAR()),
            autoincrement=False,
            nullable=True
        )
    )
    op.alter_column(
        'documents_flows',
        'edrpou',
        existing_type=sa.VARCHAR(length=64),
        nullable=True
    )
