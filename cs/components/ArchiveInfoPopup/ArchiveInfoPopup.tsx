import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { Button } from '@vchasno/ui-kit';

import Popup from 'components/ui/popup/popup';
import PopupMobile from 'components/ui/popup/popup';
import { MEDIA_WIDTH } from 'lib/constants';
import { getIsArchiveInfoPopupIsActive } from 'selectors/archiveInfoPopup.selector';
import { setArchiveInfoPopupOpen } from 'store/archiveInfoPopupSlice';
import { t } from 'ttag';

import ArchiveInfoPopupContentItem from './ArchiveInfoPopupContentItem/ArchiveInfoPopupContentItem';
import { ARCHIVE_INFO_POPUP_CONTENT } from './constants';

import css from './ArchiveInfoPopup.css';

const ArchiveInfoPopup: React.FC = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getIsArchiveInfoPopupIsActive);
    const handleClose = () =>
        dispatch(setArchiveInfoPopupOpen({ isArchiveInfoPopupOpen: false }));

    const popUp = (
        <>
            <h2 className={css.title}>{t`Зручний Архів у «﻿Вчасно»!`}</h2>
            <div className={css.subtitle}>
                {t`Перетворіть керування завершеними документами на просту та ефективну задачу за допомогою нашого Архіву`}
            </div>
            <div className={css.contentSection}>
                {ARCHIVE_INFO_POPUP_CONTENT.map((item) => (
                    <div id={item.title} key={item.title}>
                        <ArchiveInfoPopupContentItem item={item} />
                    </div>
                ))}
            </div>
            <Button className={css.button} onClick={handleClose} wide>
                {t`Купити Архів`}
            </Button>
        </>
    );
    return (
        <>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup
                    className={css.root}
                    active={isOpen}
                    onClose={handleClose}
                >
                    {popUp}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile active={isOpen} onClose={handleClose}>
                    {popUp}
                </PopupMobile>
            </MediaQuery>
        </>
    );
};

export default ArchiveInfoPopup;
