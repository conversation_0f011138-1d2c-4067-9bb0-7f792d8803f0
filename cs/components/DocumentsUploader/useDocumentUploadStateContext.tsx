import React, { createContext, useContext, useReducer } from 'react';

import { UploadApiErrorResponseBody } from 'components/uploader/types';

import { DocumentsUploadForm } from './types';

import { UPLOAD_STEP } from './constants';

interface DocumentUploadState {
    step: UPLOAD_STEP; // files | setting | sign
    formSnapshot: DocumentsUploadForm | null;
    error: UploadApiErrorResponseBody | null;
    aiRequest: boolean;
}

type Actions =
    | {
          type: 'error';
          payload: DocumentUploadState['error'];
      }
    | {
          type: 'step';
          payload: UPLOAD_STEP;
      }
    | {
          type: 'formSnapshot';
          payload: DocumentsUploadForm | null;
      }
    | {
          type: 'aiRequest';
          payload: boolean;
      };

const reducer = (
    state: DocumentUploadState,
    action: Actions,
): DocumentUploadState => {
    switch (action.type) {
        case 'step':
            return {
                ...state,
                step: action.payload,
            };
        case 'formSnapshot':
            return {
                ...state,
                formSnapshot: action.payload,
            };
        case 'error':
            return {
                ...state,
                error: action.payload,
            };
        case 'aiRequest':
            return {
                ...state,
                aiRequest: action.payload,
            };
        default:
            return state;
    }
};

const initialState: DocumentUploadState = {
    step: 'files',
    error: null,
    formSnapshot: null,
    aiRequest: false,
};

export const useDocumentUploadState = () => {
    const [state, dispatch] = useReducer(reducer, initialState);

    return [
        state,
        {
            setStep: (step: UPLOAD_STEP) =>
                dispatch({ type: 'step', payload: step }),
            setError: (error: DocumentUploadState['error']) =>
                dispatch({ type: 'error', payload: error }),
            setBackStep: () => {
                switch (state.step) {
                    case 'setting':
                        dispatch({ type: 'step', payload: 'files' });
                        break;
                    case 'sign':
                        dispatch({ type: 'step', payload: 'setting' });
                        break;
                    default:
                        break;
                }
            },
            setFormSnapshot: (formSnapshot: DocumentsUploadForm | null) =>
                dispatch({ type: 'formSnapshot', payload: formSnapshot }),
            setAiRequest: (aiRequest: boolean) => {
                dispatch({ type: 'aiRequest', payload: aiRequest });
            },
        } as const,
    ] as const;
};

const initialActions: ReturnType<typeof useDocumentUploadState>[1] = {
    setStep: () => undefined,
    setBackStep: () => undefined,
    setFormSnapshot: () => undefined,
    setError: () => undefined,
    setAiRequest: () => undefined,
};

const UploadDocumentContext = createContext<
    ReturnType<typeof useDocumentUploadState>
>([initialState, initialActions]);

export const useDocumentUploadStateContext = () =>
    useContext(UploadDocumentContext);

export const DocumentUploadStateProvider = ({
    children,
}: {
    children: React.ReactNode;
}) => {
    return (
        <UploadDocumentContext.Provider value={useDocumentUploadState()}>
            {children}
        </UploadDocumentContext.Provider>
    );
};
