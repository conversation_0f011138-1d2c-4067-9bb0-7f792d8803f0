from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.drafts.tests.utils import prepare_draft_from_version
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    with_elastic,
)

TEST_UUID_1 = '3d42cac4-0660-4eb8-bee5-ac14f61f2544'
TEST_UUID_2 = '162db85c-1c15-4152-acec-cd9d649febe1'
TEST_UUID_3 = '6f04a003-ece7-4732-b39e-46e77c548cc7'
TEST_UUID_4 = 'b5a8b0f6-2bd9-419e-958f-4d5b38ebe637'


async def test_resolve_draft_in_document(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    d1 = await prepare_document_data(app, owner, id=TEST_UUID_1)
    d2 = await prepare_document_data(app, owner, id=TEST_UUID_2)
    d3 = await prepare_document_data(app, owner, id=TEST_UUID_3)
    d4 = await prepare_document_data(app, owner, id=TEST_UUID_4)
    doc_ids = [d1.id, d2.id, d3.id, d4.id]

    version = await prepare_document_version(
        document_id=TEST_UUID_1,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        company_id=owner.company_id,
        type=DocumentVersionType.new_upload,
    )
    draft = await prepare_draft_from_version(
        version_id=version.id,
        role_id=owner.role_id,
    )

    query = '{ allDocuments { documents { id drafts { id } } } }'

    headers = prepare_auth_headers(owner)
    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(client, query, headers)
    res = {item['id']: item['drafts'] for item in data['allDocuments']['documents']}

    assert res == {
        TEST_UUID_1: [{'id': draft.id}],
        TEST_UUID_2: [],
        TEST_UUID_3: [],
        TEST_UUID_4: [],
    }


async def test_resolve_draft_all_drafts(aiohttp_client):
    """
    Different companies are able to see only their drafts
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    await prepare_document_data(app, owner, id=TEST_UUID_1)
    await prepare_document_data(app, recipient, id=TEST_UUID_3)

    version = await prepare_document_version(
        document_id=TEST_UUID_1,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        company_id=owner.company_id,
        type=DocumentVersionType.new_upload,
    )
    draft = await prepare_draft_from_version(
        version_id=version.id,
        role_id=owner.role_id,
    )

    version_recipient = await prepare_document_version(
        document_id=TEST_UUID_3,
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        company_id=recipient.company_id,
        type=DocumentVersionType.new_upload,
    )
    draft_recipient = await prepare_draft_from_version(
        version_id=version_recipient.id,
        role_id=recipient.role_id,
    )

    query = '{ allDrafts { drafts { id antivirusCheck {draftId} } count } }'

    headers = prepare_auth_headers(owner)
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 1
    assert data['allDrafts']['count'] == 1
    assert data['allDrafts']['drafts'][0]['id'] == draft.id
    assert data['allDrafts']['drafts'][0]['antivirusCheck']['draftId'] == draft.id

    # No drafts for coworker
    headers = prepare_auth_headers(coworker)
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 0
    assert data['allDrafts']['count'] == 0

    headers = prepare_auth_headers(recipient)
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 1
    assert data['allDrafts']['count'] == 1
    assert data['allDrafts']['drafts'][0]['id'] == draft_recipient.id
    assert data['allDrafts']['drafts'][0]['antivirusCheck']['draftId'] == draft_recipient.id

    # Test filter with types
    query = '{ allDrafts(types: ["version"])  { drafts { id antivirusCheck {draftId} } count } }'
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 1
    assert data['allDrafts']['count'] == 1
    assert data['allDrafts']['drafts'][0]['id'] == draft_recipient.id
    assert data['allDrafts']['drafts'][0]['antivirusCheck']['draftId'] == draft_recipient.id

    # No standalone drafts for recipient
    query = '{ allDrafts(types: ["standalone"])  { drafts { id antivirusCheck {draftId} } count } }'
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 0
    assert data['allDrafts']['count'] == 0

    # Type doesn't exist
    query = '{ allDrafts(types: ["some_type"])  { drafts { id antivirusCheck {draftId} } count } }'
    data = await fetch_graphql(client, query, headers)
    assert len(data['allDrafts']['drafts']) == 0
    assert data['allDrafts']['count'] == 0
