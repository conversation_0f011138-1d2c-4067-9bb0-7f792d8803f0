import React, { FC, useEffect } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import { AUTH_ENTRYPOINT_PATH } from 'components/auth';
import { STATIC_ERROR_PHRASES } from 'components/auth/constants';
import { loadAuthFormStorageItem } from 'components/auth/utils';
import PageTitle from 'components/pageTitle/pageTitle';
import { useRegistrationRedirectLink } from 'components/registration/hooks/useRegistrationRedirectLink';
import { useRegistrationSource } from 'components/registration/hooks/useRegistrationSource';
import { useRegistrationSuccessEffect } from 'components/registration/hooks/useRegistrationSuccessEffect';
import { useVerifiedInviteTokenData } from 'components/registration/hooks/useVerifiedInviteTokenData';
import { useUrlQueryParam } from 'hooks/useUrlQueryParam';
import { OFFERS_LINK } from 'lib/constants';
import { saveRegistrationSources } from 'lib/helpers';
import { REGISTRATION_CONFIRM_EMAIL_URL } from 'lib/routing/constants';
import {
    getLocationQuery,
    getReferrerQueries,
    stringifyLocationQuery,
} from 'lib/url';
import eventTracking from 'services/analytics/eventTracking';
import auth from 'services/auth';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import BackButton from 'ui/BackButton/BackButton';
import Button from 'ui/button/button';
import OutlinedInput from 'ui/input/OutlinedInput/OutlinedInput';

import { RegistrationFormFields } from './types';

import Panel from '../panel/panel';
import Title from '../title/title';
import { registrationFormResolver } from './validation';

import css from './UserRegistrationForm.css';

const trackEvent = (action: string, label?: string) => {
    eventTracking.sendEvent('form-signup-new-step1', action, label);
};

const UserRegistrationForm: FC = () => {
    const [commonErrorMessage, setCommonErrorMessage] = React.useState<string>(
        '',
    );
    const initialFormData = loadAuthFormStorageItem();

    const history = useHistory();
    const searchQuery = getLocationQuery(history.location);
    const activeProTrial = useUrlQueryParam('activeProTrial');
    const { edrpou, email, token } = useVerifiedInviteTokenData();
    const referrerQueries = getReferrerQueries();
    const sourceLink = useRegistrationSource();
    const redirectLink = useRegistrationRedirectLink();
    const registrationSuccessEffect = useRegistrationSuccessEffect();
    const {
        control,
        handleSubmit,
        formState,
    } = useForm<RegistrationFormFields>({
        resolver: registrationFormResolver,
        shouldUnregister: false,
        defaultValues: {
            email: initialFormData?.login || email || '',
            password: '',
        },
    });

    const onBackHandler = () => {
        if (formState.isSubmitting) {
            return;
        }

        history.push({
            pathname: AUTH_ENTRYPOINT_PATH,
            search: stringifyLocationQuery(searchQuery),
        });
    };

    const onSubmit: SubmitHandler<RegistrationFormFields> = async (values) => {
        try {
            await auth.register({
                email: values.email,
                password: values.password,
                token,
                referrer: searchQuery?.ref,
                redirect: redirectLink,
                source: sourceLink,
                isActiveProTrial: !!activeProTrial,
            });

            eventTracking.sendToGTM({
                event: 'main_reg_event',
                category: 'vchasno_main_reg',
                action: email,
            });

            eventTracking.sendToGTM({
                event: 'funnel_reg_step_3_email',
                action: 'success',
                category: '',
            });

            await registrationSuccessEffect({
                nextUrl: REGISTRATION_CONFIRM_EMAIL_URL,
                login: values.email,
                registrationMethod: 'email',
            });
        } catch (error) {
            eventTracking.sendToGTM({
                event: 'funnel_reg_step_3_email',
                action: 'fail',
                category: '',
            });
            trackEvent('error', error.message);
            setCommonErrorMessage(
                error.reason ||
                    error.message ||
                    STATIC_ERROR_PHRASES.COMMON_ERROR,
            );
        }
    };

    useEffect(() => {
        trackEvent('show');
        saveRegistrationSources({
            ...referrerQueries,
            ...searchQuery,
            page: 'registration',
            referrer: document.referrer,
            ref: searchQuery?.ref,
            registration_source: searchQuery?.token ? 'after_invite' : 'main',
        });
    }, []);

    useEffect(() => {
        if (!initialFormData?.login) {
            onBackHandler();
        }
    }, [initialFormData]);

    if (token && !edrpou) {
        return (
            <Panel>
                <div className={css.root}>
                    <Title className={css.errorTitle}>{t`Помилка`}</Title>
                    <div>
                        {t`Виникла помилка при спробі реєстрації. Неможливо
                            розібрати унікальний токен, необхідний для реєстрації.
                            Спробуйте ще раз перейти за посиланням на реєстрацію з
                            листа, відправленого на вашу електронну пошту.`}
                    </div>
                    <div>
                        {t`Будь ласка, перейдіть на`}{' '}
                        <a href="/auth">{t`головну сторінку`}</a>.
                    </div>
                </div>
            </Panel>
        );
    }

    return (
        <form id="sign-up" onSubmit={handleSubmit(onSubmit)}>
            <PageTitle>{t`Реєстрація`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    <FlexBox className={css.header} direction="column" gap={27}>
                        <BackButton
                            className={css.backBtn}
                            onClick={onBackHandler}
                        />
                        <PartnerLogos className={css.partnersLogos} />
                        <FlexBox direction="column" gap={10}>
                            <h1
                                className={css.title}
                            >{t`Придумайте пароль`}</h1>
                            <h4
                                className={css.description}
                            >{t`Пароль повинен містити щонайменше 8 символів, хоча б одну цифру і один символ ( ! ' # $ % * - / : ; = ? )`}</h4>
                        </FlexBox>
                    </FlexBox>
                    <input
                        className={css.hiddenInput}
                        id="email"
                        type="email"
                        value={initialFormData?.login || email}
                    />
                    <Controller
                        control={control}
                        name="password"
                        render={({ field, fieldState }) => (
                            <OutlinedInput
                                required
                                id="password"
                                autoComplete="new-password"
                                name="password"
                                type="password"
                                label={t`Пароль`}
                                value={field.value}
                                onChange={(event) => {
                                    field.onChange(event.target.value.trim());
                                }}
                                autoFocus
                                error={fieldState.error?.message}
                            />
                        )}
                    />
                    <span className={css.offers}>
                        {t`Реєструючись, я погоджуюсь з`}{' '}
                        <a
                            href={OFFERS_LINK}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            {t`публічними договорами (офертами) та політикою конфіденційності`}
                        </a>
                    </span>
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <Button
                        className={css.submitBtn}
                        type="submit"
                        theme="darkGray"
                        isLoading={formState.isSubmitting}
                    >{t`Зареєструватися`}</Button>
                </div>
            </FlexBox>
        </form>
    );
};

export default UserRegistrationForm;
