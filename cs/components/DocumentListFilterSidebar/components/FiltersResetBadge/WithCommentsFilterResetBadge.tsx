import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'ttag';

import { getHasCommentsFilter } from '../../../../selectors/filter.selectors';
import filtersActionCreators from '../../../filters/filtersActionCreators';

import eventTracking from '../../../../services/analytics/eventTracking';
import FilterResetItem from '../FilterResetItem';

const WithCommentsFilterResetBadge: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const hasComments = useSelector(getHasCommentsFilter);
    const dispatch = useDispatch();

    if (!hasComments) {
        return null;
    }

    const handleClick = () => {
        dispatch(filtersActionCreators.onSetWithCommentsFilter(false));

        eventTracking.sendToGTM({
            category: 'New_filter',
            action: 'Clear_filter',
            label: 'with_comments',
        });
    };

    return (
        <FilterResetItem
            onClick={handleClick}
        >{t`З коментарями`}</FilterResetItem>
    );
};

export default WithCommentsFilterResetBadge;
