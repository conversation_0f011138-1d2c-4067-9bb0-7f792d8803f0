from dataclasses import dataclass

from pydantic import BaseModel

from app.lib.types import DataDict


class KEPCertificate(BaseModel):
    acsk_key_id: str | None
    status: str | None
    password_type: str | None
    date_started: str | None
    date_finished: str | None
    storage_type: str | None
    signature_type: str | None
    validity_term: str | None
    rro: str | None
    inn: str | None
    surname: str | None
    name: str | None
    patronymic: str | None
    company_name: str | None
    company_edrpou: str | None
    phone: str | None
    position: str | None

    def to_dict(self) -> DataDict:
        return self.model_dump()


@dataclass(frozen=True)
class KEPCertificateResponse:
    certificates: list[KEPCertificate]
    is_mobile_logged: bool

    def to_dict(self) -> DataDict:
        return {
            'certificates': [cert.to_dict() for cert in self.certificates],
            'is_mobile_logged': self.is_mobile_logged,
        }
