import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    getCurrentCompanyEdrpou,
    getDocumentsTerminateFreeRate,
    getDocumentsTerminateIntegrationRate,
    getDocumentsTerminatePayedWebRate,
    getExpiresActiveIntegrationRate,
    getExpiresActivePayedWebRate,
    getIsCompanyHasPlannedIntegrationRate,
    getIsCompanyHasPlannedWebRates,
} from 'selectors/app.selectors';
import { INTEGRATION_RATES_SET } from 'services/billing/constants';

import { getRecommendedWebRateActionCreator } from '../../Checkout/checkoutActionCreators';

import ExpiresDate from './ExpiresDate/ExpiresDate';
import ExpiresDocuments from './ExpiresDocuments/ExpiresDocuments';

import activeRatesCss from '../ActiveRates.css';

const ExpiresRateBlock: React.FC = () => {
    const dispatch = useDispatch();
    const edrpou = useSelector(getCurrentCompanyEdrpou);
    const terminateDocumentsWebRate = useSelector(
        getDocumentsTerminatePayedWebRate,
    );
    const terminateDocumentsFreeRate = useSelector(
        getDocumentsTerminateFreeRate,
    );
    const terminateDocumentsIntegrationRate = useSelector(
        getDocumentsTerminateIntegrationRate,
    );
    const expiresWebRate = useSelector(getExpiresActivePayedWebRate);
    const expiresIntegrationRate = useSelector(getExpiresActiveIntegrationRate);
    const isCompanyHasPlannedWebRates = useSelector(
        getIsCompanyHasPlannedWebRates,
    );
    const isCompanyHasPlannedIntegrationRates = useSelector(
        getIsCompanyHasPlannedIntegrationRate,
    );

    useEffect(() => {
        dispatch(getRecommendedWebRateActionCreator());
    }, [edrpou]);

    const terminateRate =
        expiresWebRate ||
        (!terminateDocumentsWebRate &&
            !terminateDocumentsFreeRate &&
            expiresIntegrationRate);

    const terminateDocumentsRate =
        terminateDocumentsWebRate ||
        terminateDocumentsFreeRate ||
        terminateDocumentsIntegrationRate;
    const isIntegrationTerminateRate = INTEGRATION_RATES_SET.has(
        terminateRate?.rate,
    );
    const isIntegrationTerminateDocumentsRate = INTEGRATION_RATES_SET.has(
        terminateDocumentsRate?.rate,
    );

    if (!terminateRate && !terminateDocumentsRate) {
        return null;
    }

    if (
        !expiresWebRate &&
        !terminateDocumentsWebRate &&
        (isIntegrationTerminateRate || isIntegrationTerminateDocumentsRate) &&
        isCompanyHasPlannedIntegrationRates
    ) {
        return null;
    }

    if (
        (!isIntegrationTerminateRate || !isIntegrationTerminateDocumentsRate) &&
        isCompanyHasPlannedWebRates
    ) {
        return null;
    }
    return (
        <div className={activeRatesCss.container}>
            {terminateRate && <ExpiresDate rate={terminateRate} />}
            {!terminateRate && terminateDocumentsRate && (
                <ExpiresDocuments rate={terminateDocumentsRate} />
            )}
        </div>
    );
};

export default ExpiresRateBlock;
