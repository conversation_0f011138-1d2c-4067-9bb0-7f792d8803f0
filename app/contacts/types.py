from __future__ import annotations

from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from typing import (
    NamedTuple,
    TypedDict,
)

from app.auth.types import User
from app.contacts.enums import ContactRecipientIndexationEntity
from app.lib.database import DBRow
from app.lib.helpers import build_full_user_name
from app.lib.types import (
    DataDict,
)


class ContactBase(NamedTuple):
    """
    Base class for creating and updating contact.
    """

    company_id: str
    edrpou: str
    name: str | None = None
    short_name: str | None = None


class UpdateContactPersonOptions(NamedTuple):
    contact_person_id: str
    edrpou: str
    email: str | None
    is_email_hidden: bool
    first_name: str | None = None
    second_name: str | None = None
    last_name: str | None = None
    main_recipient: bool = False
    phone: str | None = None

    def as_db(self) -> DataDict:
        data = {
            'first_name': self.first_name,
            'second_name': self.second_name,
            'last_name': self.last_name,
            'main_recipient': self.main_recipient,
            'is_email_hidden': self.is_email_hidden,
        }
        if self.is_email_hidden is False:
            data['email'] = self.email

        return data


class CreateContactPersonOptions(NamedTuple):
    edrpou: str
    email: str | None
    first_name: str | None = None
    second_name: str | None = None
    last_name: str | None = None
    main_recipient: bool = False
    phone: str | None = None
    is_new_contact_company: bool = False

    def as_db(self) -> DataDict:
        return self._asdict()


class ContactDetails(NamedTuple):
    edrpou: str
    email: str | None
    is_email_hidden: bool
    phone: str | None = None
    first_name: str | None = None
    second_name: str | None = None
    last_name: str | None = None
    company_name: str | None = None


class ContactUploadOptions(NamedTuple):
    # From contact config
    allowed_extensions: set[str]
    max_file_size: int  # MB


def to_contact_details(user: User) -> ContactDetails:
    return ContactDetails(
        edrpou=user.company_edrpou,
        email=user.email,
        phone=user.phone,
        first_name=user.first_name,
        second_name=user.second_name,
        last_name=user.last_name,
        company_name=user.company_name,
        is_email_hidden=False,
    )


class UploadContactCtxDataCompany(TypedDict):
    company_id: str
    edrpou: str
    name: str
    short_name: str


class UploadContactCtxDataEmail(TypedDict):
    edrpou: str
    email: str
    first_name: str
    second_name: str
    last_name: str
    main_recipient: bool


class UploadContactCtxDataPhone(TypedDict):
    edrpou: str
    phone: str
    email: str


class UploadContactCtxData(TypedDict):
    companies: list[UploadContactCtxDataCompany]
    emails: list[UploadContactCtxDataEmail]
    phones: list[UploadContactCtxDataPhone]


class UploadContactCtxCounters(TypedDict):
    rows_total: int
    rows_invalid: int
    invalid_row_numbers: list[int]


class UploadContactCtx(NamedTuple):
    data: UploadContactCtxData
    counters: UploadContactCtxCounters
    contacts_with_main_recipient: set[str]


@dataclass(frozen=True)
class ContactRecipient:
    """
    Model for recipient search. Item can be either contact person or company.
    """

    edrpou: str
    name: str | None
    email: str | None
    user_name: str | None
    main_recipient: bool

    @classmethod
    def from_row(cls, row: DBRow) -> ContactRecipient:
        user_name = build_full_user_name(
            first=row.first_name,
            second=row.second_name,
            last=row.last_name,
        )
        return cls(
            edrpou=row.edrpou,
            name=row.name,
            email=row.email,
            user_name=user_name,
            main_recipient=row.main_recipient or False,
        )


@dataclass(frozen=True)
class Contact:
    id: str
    company_id: str
    edrpou: str
    name: str | None
    short_name: str | None
    full_address: str | None
    zip: str | None
    city: str | None
    street: str | None
    accountant_email: str | None
    date_created: datetime
    date_sync: datetime | None

    @classmethod
    def from_row(cls, row: DBRow) -> Contact:
        return cls(
            id=row.id,
            company_id=row.company_id,
            edrpou=row.edrpou,
            name=row.name,
            short_name=row.short_name,
            full_address=row.full_address,
            zip=row.zip,
            city=row.city,
            street=row.street,
            accountant_email=row.accountant_email,
            date_created=row.date_created,
            date_sync=row.date_sync,
        )


@dataclass(frozen=True)
class SoftCreatedContact(Contact):
    """
    Contact with additional field `is_new` to indicate if contact was created
    or just selected from a database.
    """

    is_new: bool

    @classmethod
    def from_db(cls, row: DBRow, is_new: bool) -> SoftCreatedContact:
        return cls(
            id=row.id,
            company_id=row.company_id,
            edrpou=row.edrpou,
            name=row.name,
            short_name=row.short_name,
            full_address=row.full_address,
            zip=row.zip,
            city=row.city,
            street=row.street,
            accountant_email=row.accountant_email,
            date_created=row.date_created,
            date_sync=row.date_sync,
            is_new=is_new,
        )


@dataclass(frozen=True)
class ContactPerson:
    id: str
    contact_id: str
    email: str | None
    is_email_hidden: bool
    first_name: str | None
    second_name: str | None
    last_name: str | None
    position: str | None
    main_recipient: bool | None
    date_created: datetime
    date_updated: datetime | None

    @classmethod
    def from_row(cls, row: DBRow) -> ContactPerson:
        return cls(
            id=row.id,
            contact_id=row.contact_id,
            email=row.email,
            is_email_hidden=row.is_email_hidden,
            first_name=row.first_name,
            second_name=row.second_name,
            last_name=row.last_name,
            position=row.position,
            main_recipient=row.main_recipient,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass
class UpdatedContactPerson:
    contact: SoftCreatedContact
    person: ContactPerson | None = None


@dataclass
class ContactRecipientIndexationItem:
    id: str
    entity_type: ContactRecipientIndexationEntity
    entity_id: str
    iteration: int
    date_created: datetime
    seqnum: int

    @property
    def is_contact(self) -> bool:
        return self.entity_type == ContactRecipientIndexationEntity.contact

    @property
    def is_company(self) -> bool:
        return self.entity_type == ContactRecipientIndexationEntity.company

    @property
    def is_role(self) -> bool:
        return self.entity_type == ContactRecipientIndexationEntity.role

    @classmethod
    def from_row(cls, row: DBRow) -> ContactRecipientIndexationItem:
        return cls(
            id=row.id,
            entity_type=row.entity_type,
            entity_id=row.entity_id,
            iteration=row.iteration,
            date_created=row.date_created,
            seqnum=row.seqnum,
        )


class ContactRecipientIndexationBatch:
    """
    Batch of contact recipients for indexing.
    """

    def __init__(self, items: list[ContactRecipientIndexationItem]) -> None:
        self._items = items

        # contact_id -> [item_id, item_id, ...]
        self._contacts_mapping: defaultdict[str, list[str]] = defaultdict(list)

        # company_id -> [item_id, item_id, ...]
        self._companies_mapping: defaultdict[str, list[str]] = defaultdict(list)

        # role_id -> [item_id, item_id, ...]
        self._roles_mapping: defaultdict[str, list[str]] = defaultdict(list)

        for item in items:
            if item.is_contact:
                self._contacts_mapping[item.entity_id].append(item.id)
            elif item.is_company:
                self._companies_mapping[item.entity_id].append(item.id)
            elif item.is_role:
                self._roles_mapping[item.entity_id].append(item.id)

        # [company_id, company_id, ...]
        self._success_companies_ids: set[str] = set()
        self._failed_companies_ids: set[str] = set()

        # [contact_id, contact_id, ...]
        self._success_contacts_ids: set[str] = set()
        self._failed_contacts_ids: set[str] = set()

        # [role_id, role_id, ...]
        self._success_roles_ids: set[str] = set()
        self._failed_roles_ids: set[str] = set()

    @property
    def size(self) -> int:
        return len(self._items)

    @property
    def next_cursor(self) -> int:
        return self._items[-1].seqnum

    @property
    def contacts_ids(self) -> list[str]:
        return list(self._contacts_mapping.keys())

    @property
    def companies_ids(self) -> list[str]:
        return list(self._companies_mapping.keys())

    @property
    def roles_ids(self) -> list[str]:
        return list(self._roles_mapping.keys())

    def add_company_result(self, result: ContactRecipientIndexationResult) -> None:
        self._success_companies_ids.update(result.success_ids)
        self._failed_companies_ids.update(result.failed_ids)

    def add_contact_result(self, result: ContactRecipientIndexationResult) -> None:
        self._success_contacts_ids.update(result.success_ids)
        self._failed_contacts_ids.update(result.failed_ids)

    def add_role_result(self, result: ContactRecipientIndexationResult) -> None:
        self._success_roles_ids.update(result.success_ids)
        self._failed_roles_ids.update(result.failed_ids)

    @property
    def success_items_ids(self) -> list[str]:
        """
        Return list of contact recipients ids that were successfully indexed (or skipped).
        """

        # [item_id, item_id, ...]
        items_ids = []

        for contact_id in self._success_contacts_ids:
            items_ids.extend(self._contacts_mapping[contact_id])

        for company_id in self._success_companies_ids:
            items_ids.extend(self._companies_mapping[company_id])

        for role_id in self._success_roles_ids:
            items_ids.extend(self._roles_mapping[role_id])

        return items_ids

    @property
    def failed_items_ids(self) -> list[str]:
        """
        Return list of contact recipients ids that failed to index.
        """

        # [item_id, item_id, ...]
        items_ids = []

        for contact_id in self._failed_contacts_ids:
            items_ids.extend(self._contacts_mapping[contact_id])

        for company_id in self._failed_companies_ids:
            items_ids.extend(self._companies_mapping[company_id])

        for role_id in self._failed_roles_ids:
            items_ids.extend(self._roles_mapping[role_id])

        return items_ids


@dataclass
class ContactRecipientIndexationResult:
    success_ids: set[str] = field(default_factory=set)
    failed_ids: set[str] = field(default_factory=set)

    @property
    def combined_ids(self) -> set[str]:
        return self.success_ids | self.failed_ids


@dataclass
class ContactForIndexation:
    """
    Contact + ContactPerson + Company for contact recipients indexation
    """

    contact_id: str
    contact_company_edrpou: str
    contact_company_name: str | None
    contact_company_name_short: str | None
    contact_owner_id: str
    contact_date_created: datetime

    # Optional because we are doing LEFT JOIN of companies to contacts
    company_name_extra: str | None
    company_registered: bool | None

    # All persons attributes can be None, because we are doing
    # LEFT JOIN of persons to contacts
    person_id: str | None
    person_email: str | None
    person_first_name: str | None
    person_second_name: str | None
    person_last_name: str | None
    person_phone: str | None
    person_is_email_hidden: bool | None
    person_date_created: datetime | None
    person_main_recipient: bool

    @classmethod
    def from_row(cls, row: DBRow) -> ContactForIndexation:
        return cls(
            contact_id=row.contact_id,
            company_registered=row.company_registered,
            contact_company_edrpou=row.contact_company_edrpou,
            contact_company_name=row.contact_company_name,
            contact_company_name_short=row.contact_company_name_short,
            contact_owner_id=row.contact_owner_id,
            contact_date_created=row.contact_date_created,
            company_name_extra=row.company_name_extra,
            person_id=row.person_id,
            person_email=row.person_email,
            person_first_name=row.person_first_name,
            person_second_name=row.person_second_name,
            person_last_name=row.person_last_name,
            person_phone=row.person_phone,
            person_is_email_hidden=row.person_is_email_hidden,
            person_date_created=row.person_date_created,
            person_main_recipient=row.person_main_recipient,
        )
