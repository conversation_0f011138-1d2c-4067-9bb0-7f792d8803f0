import datetime
import io
from http import HTTPStatus
from pathlib import Path

import yarl

from api.downloads import pdf
from app.document_versions.tests.utils import prepare_document_version
from app.documents import db as documents_db
from app.lib import eusign_utils
from app.lib.datetime_utils import utc_now
from app.mobile import constants
from app.mobile.tests import common as mobile_tests_common
from app.services import services
from app.sign_sessions.enums import SignSessionStatus, SignSessionType, SignSessionVendor
from app.sign_sessions.tests.utils import get_sign_session
from app.signatures import enums as signature_enums
from app.tests import common

DATA_PATH = Path(__file__).parent / 'data'
DOC_PATH = DATA_PATH / 'doc.txt'

TEST_UUID_1 = '00000000-0000-0000-0000-000000000001'
TEST_UUID_2 = '00000000-0000-0000-0000-000000000002'
TEST_UUID_3 = '00000000-0000-0000-0000-000000000003'


async def test_view_document(aiohttp_client, monkeypatch, s3_emulation):
    """
    Given an authenticated user
    When executing a request to view document
    Expected successful response
    """
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    user = user_data['user']
    app = user_data['app']

    document = await common.prepare_document_data(app, user, extension='.pdf')
    file_content = b'test_mocked_content'
    monkeypatch.setattr(pdf, 'generate_signed_file', lambda *ar, **kw: io.BytesIO(file_content))
    s3_emulation.upload_document_content(document_id=document.id, content=file_content)

    response = await client.get(
        f'/mobile-api/v1/viewer/{document.id}/view',
        headers={
            constants.MOBILE_AUTH_HEADER: user_data['access_token'],
            constants.MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert response.status == HTTPStatus.OK
    response_data = await response.json()
    viwer_url = yarl.URL(response_data['url'])

    # Example:
    #  - http://localhost:8000/pdf-viewer?file=/downloads/...&expand=1
    assert viwer_url.host is not None
    assert viwer_url.path == '/pdf-viewer'
    assert viwer_url.query['expand'] == '1'
    assert viwer_url.query['file'] is not None

    # Example:
    #  - http://localhost:8000/downloads/00000000-0000-0000-0000-000000000000
    #       ?ssid=11111111-1111-1111-1111-111111111111
    file_url = yarl.URL(viwer_url.query['file'])
    assert file_url.host is None
    assert file_url.path == f'/downloads/{document.id}'
    assert file_url.query['ssid'] is not None
    session_id = file_url.query['ssid']

    sign_session = await get_sign_session(sign_session_id=session_id)
    assert sign_session is not None
    assert sign_session.document_id == document.id
    assert sign_session.role_id == user.role_id
    assert sign_session.email == user.email
    assert sign_session.edrpou == user.company_edrpou
    assert sign_session.vendor == SignSessionVendor.vchasno
    assert sign_session.type == SignSessionType.view_session
    assert sign_session.status == SignSessionStatus.created
    assert sign_session.company_id == user.company_id
    assert sign_session.created_by == user.role_id

    # Check that we can open viewer
    viewer_url_without_host = str(viwer_url).removeprefix('http://localhost:8000')
    response = await client.get(viewer_url_without_host)
    assert response.status == HTTPStatus.OK

    # Check that we can download document
    response = await client.get(file_url)
    assert response.status == HTTPStatus.OK
    returned_content = await response.read()
    assert len(returned_content) > 0
    assert returned_content == file_content


async def test_view_document_versioned(aiohttp_client, monkeypatch, s3_emulation):
    """
    Given an authenticated user
    When executing a request to view document
    Expected successful response
    """
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    user = user_data['user']
    app = user_data['app']

    document = await common.prepare_document_data(app, user, extension='.pdf')
    s3_emulation.upload_document_content(document_id=document.id, content=b'content-2')

    await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        content=b'content-2',
    )
    await prepare_document_version(
        id=TEST_UUID_3,
        document_id=document.id,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        date_created=utc_now() + datetime.timedelta(minutes=1),
        content=b'content-3',
    )

    response = await client.get(
        f'/mobile-api/v1/viewer/{document.id}/view',
        headers={
            constants.MOBILE_AUTH_HEADER: user_data['access_token'],
            constants.MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert response.status == HTTPStatus.OK
    response_data = await response.json()
    viewer_url = yarl.URL(response_data['url'])

    # Example:
    #  - http://localhost:8000/pdf-viewer?file=/downloads/...&expand=1
    assert viewer_url.host is not None
    assert viewer_url.path == '/pdf-viewer'
    assert viewer_url.query['expand'] == '1'
    assert viewer_url.query['file'] is not None

    # Example:
    #  - http://localhost:8000/downloads/00000000-0000-0000-0000-000000000000
    #       ?ssid=11111111-1111-1111-1111-111111111111
    #       &version=*************-2222-2222-************
    file_url = yarl.URL(viewer_url.query['file'])
    assert file_url.host is None
    assert file_url.path == f'/downloads/{document.id}'
    assert file_url.query['ssid'] is not None
    assert file_url.query['version'] == TEST_UUID_3

    session_id = file_url.query['ssid']
    sign_session = await get_sign_session(sign_session_id=session_id)
    assert sign_session is not None
    assert sign_session.document_id == document.id
    assert sign_session.role_id == user.role_id
    assert sign_session.email == user.email
    assert sign_session.edrpou == user.company_edrpou
    assert sign_session.vendor == SignSessionVendor.vchasno
    assert sign_session.type == SignSessionType.view_session
    assert sign_session.status == SignSessionStatus.created
    assert sign_session.company_id == user.company_id
    assert sign_session.created_by == user.role_id

    # Check that we can open viewer
    viewer_url_without_host = str(viewer_url).removeprefix('http://localhost:8000')
    response = await client.get(viewer_url_without_host)
    assert response.status == HTTPStatus.OK

    # Check that we can download document
    response = await client.get(file_url)
    assert response.status == HTTPStatus.OK
    returned_content = await response.read()
    assert returned_content == b'content-3'


async def test_upload_document(aiohttp_client, monkeypatch, s3_emulation):
    """
    Given an authenticated user
    When calling request to upload document
    Expected document to be uploaded successfully
    """
    # Arrange user
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    user = user_data['user']

    # Mock signature verification
    async def fake_verify(*_, **__):
        return common.prepare_signature_info(
            signature_enums.SignatureType.signature,
            user.company_edrpou,
        )

    monkeypatch.setattr(eusign_utils, 'verify', fake_verify)

    # Act - upload document
    response = await client.post(
        '/mobile-api/v1/documents',
        data=common.prepare_form_data(file=DOC_PATH),
        headers={
            constants.MOBILE_AUTH_HEADER: user_data['access_token'],
            constants.MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    response_json = await response.json()

    # Assert upload status is valid
    assert response.status == HTTPStatus.CREATED, response_json

    # Assert document uploaded successfully
    async with services.db.acquire() as conn:
        documents = await documents_db.select_documents_by_owner_edrpou(
            conn=conn,
            edrpou=user.company_edrpou,
        )
        assert len(documents) == 1
    assert response_json == {'documents': [{'id': documents[0].id}]}

    # Act - verify indexation status
    resp = await client.post(
        '/mobile-api/v1/indexation-status',
        json={'document_ids': [response_json['documents'][0]['id']]},
        headers={
            constants.MOBILE_AUTH_HEADER: user_data['access_token'],
            constants.MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    # Assert indexation status is completed
    assert resp.status == HTTPStatus.OK
