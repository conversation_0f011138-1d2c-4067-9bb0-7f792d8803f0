import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { Button, FlexBox, Text, Title } from '@vchasno/ui-kit';

import CloseButton from 'components/ui/closeButton/closeButton';
import { setLocalStorageItem } from 'lib/webStorage';
import {
    getCompanyConfigSettingsMaxArchiveDocumentsCount,
    getCompanyStartProRatesCreatedBefore04092024,
    getIsAnyProRateExists,
    getIsAnyUnlimitRateExists,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { AccountRate } from 'services/enums';
import { setArchiveBannerOpen } from 'store/archiveBannerSlice';
import { t } from 'ttag';

import ArchiveDocumentsInfoBox from './ArchiveDocumentsInfoBox';
import {
    ARCHIVE_BIG_NUMBER_OF_DOCUMENTS,
    ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS,
    MAX_ES_DOCUMENTS_COUNT,
    billGenerationArchiveBigUrl,
    billGenerationProRateUrl,
} from './constants';

import documentsPng from './images/archive-documents.png';
import lockPng from './images/archive-lock.png';

import css from './ArchiveBanner.css';

interface ArchiveBannerProps {
    usedDocumentCount: number;
    maxVisibleDocumentsCount: number;
}

const TEN_K_PLUS = `10 000 +`;

export const ArchiveBanner: React.FC<ArchiveBannerProps> = ({
    usedDocumentCount,
    maxVisibleDocumentsCount,
}) => {
    const history = useHistory();
    const dispatch = useDispatch();
    const [buttonLoading, setButtonLoading] = React.useState(false);

    const archivedDocumentsCount = useSelector(
        getCompanyConfigSettingsMaxArchiveDocumentsCount,
    );
    const companyStartProRatesCreatedBefore04092024 = useSelector(
        getCompanyStartProRatesCreatedBefore04092024,
    );
    const isAnyProRatesExist = useSelector(getIsAnyProRateExists);
    const isAnyUltimateRateExist = useSelector(getIsAnyUnlimitRateExists);

    const isExistArchiveBig =
        archivedDocumentsCount === ARCHIVE_BIG_NUMBER_OF_DOCUMENTS;

    if (
        !usedDocumentCount ||
        isAnyUltimateRateExist ||
        usedDocumentCount < maxVisibleDocumentsCount
    ) {
        return null;
    }

    const blockedDocuments =
        usedDocumentCount - maxVisibleDocumentsCount > MAX_ES_DOCUMENTS_COUNT
            ? MAX_ES_DOCUMENTS_COUNT
            : usedDocumentCount - maxVisibleDocumentsCount;

    const sendToGTM = (onlyArchive: boolean) => {
        return eventTracking.sendToGTMV4({
            event: onlyArchive
                ? 'ec_buy_archive_banner'
                : 'ec_buy_tariff_archive_banner',
        });
    };

    const handleChooseRate = () => {
        if (isExistArchiveBig && !isAnyProRatesExist) {
            setButtonLoading(true);
            history.push(billGenerationProRateUrl);
            sendToGTM(false);
        }

        if (isExistArchiveBig && isAnyProRatesExist) {
            setButtonLoading(true);
            history.push(`/app/checkout?rate=${AccountRate.ULTIMATE_2022_12}`);
            sendToGTM(false);
        }

        if (
            !isExistArchiveBig &&
            isAnyProRatesExist &&
            blockedDocuments < ARCHIVE_BIG_NUMBER_OF_DOCUMENTS
        ) {
            setButtonLoading(true);
            history.push(billGenerationArchiveBigUrl);
            sendToGTM(true);
        }

        if (
            isAnyProRatesExist &&
            blockedDocuments > ARCHIVE_BIG_NUMBER_OF_DOCUMENTS
        ) {
            setButtonLoading(true);
            history.push(`/app/checkout?rate=${AccountRate.ULTIMATE_2022_12}`);
            sendToGTM(false);
        }

        if (
            blockedDocuments < ARCHIVE_BIG_NUMBER_OF_DOCUMENTS &&
            !isExistArchiveBig
        ) {
            setButtonLoading(true);
            history.push(billGenerationArchiveBigUrl);
            sendToGTM(true);
        }

        if (
            blockedDocuments >= ARCHIVE_BIG_NUMBER_OF_DOCUMENTS &&
            blockedDocuments < ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS &&
            !isAnyProRatesExist
        ) {
            setButtonLoading(true);
            history.push(billGenerationProRateUrl);
            sendToGTM(false);
        }

        if (blockedDocuments >= ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS) {
            setButtonLoading(true);
            history.push(`/app/checkout?rate=${AccountRate.ULTIMATE_2022_12}`);
            sendToGTM(false);
        }
    };

    const handleClose = () => {
        dispatch(setArchiveBannerOpen({ isArchiveBannerOpen: false }));
        setLocalStorageItem(`archiveBannerDateClosed`, new Date());
    };

    if (companyStartProRatesCreatedBefore04092024.length > 0) {
        return null;
    }

    return (
        <FlexBox
            justify="space-between"
            wrap="wrap"
            align="center"
            className={css.root}
        >
            <CloseButton
                position="absolute"
                className={css.closeButton}
                withHover
                onClose={handleClose}
            />
            <FlexBox shrink={0} direction="column" gap={3}>
                <Title level={4}>{t`Архів`}</Title>
                <Text
                    type="secondary"
                    style={{ fontSize: 14, lineHeight: '20px' }}
                >{t`Переміщуйте опрацьовані документи в Архів`}</Text>
            </FlexBox>
            <FlexBox shrink={0} gap={40} align="center">
                <ArchiveDocumentsInfoBox
                    type="all_documents"
                    iconSrc={documentsPng}
                    className={css.withRSeparator}
                    value={
                        usedDocumentCount >= MAX_ES_DOCUMENTS_COUNT
                            ? TEN_K_PLUS
                            : String(usedDocumentCount)
                    }
                />
                <ArchiveDocumentsInfoBox
                    type="blocked_documents"
                    iconSrc={lockPng}
                    value={
                        blockedDocuments >= MAX_ES_DOCUMENTS_COUNT
                            ? TEN_K_PLUS
                            : String(blockedDocuments)
                    }
                />
            </FlexBox>
            <FlexBox align="center" justify="flex-end">
                <Button
                    style={{ height: 40 }}
                    theme="secondary"
                    onClick={handleChooseRate}
                    loading={buttonLoading}
                >
                    {blockedDocuments > ARCHIVE_BIG_NUMBER_OF_DOCUMENTS ||
                    isExistArchiveBig
                        ? t`Обрати тариф`
                        : t`Купити архів`}
                </Button>
            </FlexBox>
        </FlexBox>
    );
};
