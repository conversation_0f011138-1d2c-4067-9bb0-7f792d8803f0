{"dynamic": false, "date_detection": false, "properties": {"company_edrpou": {"type": "keyword", "fields": {"suggest": {"type": "text", "analyzer": "ngram_analyzer", "search_analyzer": "standard", "norms": false, "index_options": "docs"}}, "copy_to": "meta_data"}, "company_registered": {"type": "boolean"}, "company_name": {"type": "text", "norms": false, "copy_to": "meta_data"}, "company_name_short": {"type": "text", "norms": false, "copy_to": "meta_data"}, "company_name_extra": {"type": "text", "norms": false, "copy_to": "meta_data"}, "is_contact": {"type": "boolean"}, "type": {"type": "integer"}, "contact_owner_id": {"type": "keyword"}, "contact_person_email": {"type": "keyword", "fields": {"lowercase": {"type": "keyword", "normalizer": "lowercase_normalizer"}}, "copy_to": "meta_data"}, "contact_person_name": {"type": "text", "norms": false, "copy_to": "meta_data"}, "contact_person_phone": {"type": "keyword"}, "person_main_recipient": {"type": "boolean"}, "contact_id": {"type": "keyword"}, "person_id": {"type": "keyword"}, "company_id": {"type": "keyword"}, "role_id": {"type": "keyword"}, "date": {"type": "date"}, "meta_data": {"type": "text", "fields": {"suggest": {"type": "text", "analyzer": "ngram_analyzer", "search_analyzer": "standard", "norms": false, "index_options": "docs"}}, "norms": false, "index_options": "docs"}}}