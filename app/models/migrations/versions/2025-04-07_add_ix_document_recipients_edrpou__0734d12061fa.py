"""Add ix_document_recipients_edrpou_document_id_not_delivered

Revision ID: 0734d12061fa
Revises: e7231a51389a
Create Date: 2025-04-07 18:22:19.973660

"""

from alembic import op
import sqlalchemy as sa

from app.config import is_prod_environ

# revision identifiers, used by Alembic.
revision = '0734d12061fa'
down_revision = 'e7231a51389a'
branch_labels = None
depends_on = None


def upgrade():
    # on production, execute this migration manually
    if is_prod_environ():
        return

    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX CONCURRENTLY
        IF NOT EXISTS ix_document_recipients_edrpou_document_id_not_delivered
        ON document_recipients (edrpou, document_id)
        WHERE date_sent IS NOT NULL AND date_delivered IS NULL;
        """
    )


def downgrade():
    op.drop_index(
        'ix_document_recipients_edrpou_document_id_not_delivered',
        table_name='document_recipients',
        postgresql_where=sa.text('date_sent is not null and date_delivered is null'),
        postgresql_concurrently=True,
    )
