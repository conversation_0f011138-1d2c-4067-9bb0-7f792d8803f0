import logging

import sqlalchemy as sa
from aiohttp import web

from app.auth import db as auth_db
from app.auth.db import USER_COLUMNS
from app.auth.tables import (
    company_table,
    is_active_filter,
    role_table,
    user_role_company_strict_join,
    user_table,
)
from app.auth.types import User
from app.i18n import _
from app.lib import emailing
from app.lib import types as core_types
from app.lib.database import DBConnection
from app.models import select_all
from app.services import services
from worker import topics

# 2 weeks in seconds
SYNC_ERROR_SEND_TO_ESPUTIK_TTL = 60 * 60 * 24


async def _select_users_for_notification(conn: DBConnection, edrpou: str) -> list[User]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([*USER_COLUMNS])
            .select_from(user_role_company_strict_join)
            .where(
                sa.and_(
                    company_table.c.edrpou == edrpou,
                    user_table.c.is_placeholder.isnot(True),
                    user_table.c.email.isnot(None),
                    is_active_filter,
                    role_table.c.can_receive_new_roles.is_(True),
                )
            )
        ),
    )
    return [User.from_row(row) for row in rows]


async def initiate_reminders_for_new_successfully_registered_employee(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Gather recipients and send reminder about new successfully registered employee
    """

    role_id: str = data['role_id']

    async with services.db_readonly.acquire() as conn:
        role = await auth_db.select_role_by_id(conn=conn, role_id=role_id)
        if not role:
            logger.info(
                'Role not found for new employee notification',
                extra={'role_id': role_id},
            )
            return

        recipients = await _select_users_for_notification(
            conn=conn,
            edrpou=role.company_edrpou,
        )
        recipients = [r for r in recipients if r.role_id != role_id]

        if not recipients:
            logger.info(
                'Recipients not found for new employee notification',
                extra={'role_id': role_id},
            )
            return

    await services.kafka.send_records(
        topic=topics.SEND_REMINDER_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE,
        values=[
            {
                'new_role_id': role_id,
                'recipient_role_id': recipient.role_id,
            }
            for recipient in recipients
        ],
    )


async def send_reminder_for_new_successfully_registered_employee(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Send reminder to company admin about new successfully registered employee
    """

    new_role_id: str = data['new_role_id']
    recipient_role_id: str = data['recipient_role_id']

    async with services.db_readonly.acquire() as conn:
        new_role = await auth_db.select_role_by_id(conn, new_role_id)
        if not new_role:
            logger.info(
                'Role not found for new employee notification',
                extra={'role_id': new_role_id},
            )
            return

        recipient = await auth_db.select_user(conn, role_id=recipient_role_id)
        if not recipient:
            logger.info(
                'Recipient role not found for new employee notification',
                extra={'role_id': recipient_role_id},
            )
            return

        if not recipient.email:
            logger.info(
                'User email is not set for new employee notification',
                extra={'user_id': recipient.id, 'role_id': recipient.role_id},
            )
            return

    subject = _('Співробітник вашої компанії зареєструвався у «Вчасно»!')
    context = {
        'recipient_first_name': recipient.first_name,
        'new_role_email': new_role.user_email,
        'change_settings_url': (
            f'{services.config.app.domain}/app/settings/companies/{recipient.role_id}/employees/{new_role.id}'
        ),
    }
    await emailing.send_email(
        recipient_mixed=recipient.email,
        subject=subject,
        template_name='new_employee_successfull_registration',
        context=context,
    )


async def initiate_reminders_for_failed_to_register_employee(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Gather recipients and send reminders about employee which failed to register
    """

    company_id: str = data['company_id']
    employee_user_id: str = data['employee_user_id']

    # Verify there wasn't similar message for this employee recently (last 2 weeks)
    redis_key = f'role-sync-error-{employee_user_id}-{company_id}'
    was_notification_recently_sent = await services.redis.get(redis_key)
    if was_notification_recently_sent:
        logger.info(
            'Notification for failed to register employee has already been sent recently.',
            extra={
                'company_id': company_id,
                'employee_user_id': employee_user_id,
            },
        )
        return

    async with services.db_readonly.acquire() as conn:
        employee = await auth_db.select_base_user(conn, user_id=employee_user_id)
        if not employee:
            logger.info(
                'User not found for failed to register notification',
                extra={'user_id': employee_user_id},
            )
            return

        company = await auth_db.select_company_by_id(conn, company_id)
        if not company:
            logger.info(
                'Company not found for failed to register notification',
                extra={'company_id': company_id},
            )
            return

        recipients = await _select_users_for_notification(
            conn=conn,
            edrpou=company.edrpou,
        )
        if not recipients:
            logger.info(
                'Recipients not found for failed to register employee notification',
                extra={'company_id': company_id},
            )
            return

    await services.kafka.send_records(
        topic=topics.SEND_REMINDER_FOR_FAILED_TO_REGISTER_EMPLOYEE,
        values=[
            {
                'employee_user_id': employee_user_id,
                'recipient_role_id': recipient.role_id,
            }
            for recipient in recipients
        ],
    )

    # Set delay not to spam admins about this reason
    await services.redis.setex(redis_key, value='1', time=SYNC_ERROR_SEND_TO_ESPUTIK_TTL)


async def send_reminder_for_failed_to_register_employee(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Send reminder to company admin about employee which failed to register
    """

    employee_user_id: str = data['employee_user_id']
    recipient_role_id: str = data['recipient_role_id']

    async with services.db_readonly.acquire() as conn:
        employee = await auth_db.select_base_user(conn=conn, user_id=employee_user_id)
        if not employee:
            logger.info(
                'User not found for failed to register notification',
                extra={'user_id': employee_user_id},
            )
            return

        recipient = await auth_db.select_user(conn, role_id=recipient_role_id)
        if not recipient:
            logger.info(
                'Recipient role not found for new employee notification',
                extra={'role_id': recipient_role_id},
            )
            return
        if not recipient.email:
            logger.info('User email is not set', extra={'user_id': recipient.id})
            return

    subject = _('Співробітник вашої компанії не зміг зареєструватися у «Вчасно»')
    context = {
        'recipient_first_name': recipient.first_name,
        'recipient_company_edrpou': recipient.company_edrpou,
        'recipient_company_name': recipient.company_name,
        'employee_email': employee.email,
        'choose_tariff_url': f'{services.config.app.domain}/app/checkout-rates/web',
    }
    await emailing.send_email(
        recipient_mixed=recipient.email,
        subject=subject,
        template_name='new_employee_failed_registration',
        context=context,
    )
