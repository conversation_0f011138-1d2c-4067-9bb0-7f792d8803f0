import datetime
import itertools
import logging

from aiohttp import web

from app.auth.db import (
    select_company_by_edrpou,
    select_user,
)
from app.documents.db import select_documents, select_private_documents_ids
from app.es.utils import send_to_indexator
from app.events import document_actions
from app.lib.types import DataDict
from app.services import services
from app.tags.db import (
    select_tags_by_contact,
    select_tags_by_ids,
)
from app.tags.schemas import CreateDocumentAccessOnRoleTagJobData
from app.tags.utils import (
    create_access_by_document_tag,
    create_document_access_on_role_tag,
    create_document_tags,
    schedule_create_document_access_on_document_tag_job,
)
from worker import topics
from worker.documents.db import select_documents_ids_for_insert
from worker.utils import retry_config

# Each iteration of the job will check and open access up to 1000 documents
# Example: for 2 million documents it will take 2000 iterations to process all documents
CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG = 1000


async def create_documents_tags_by_contact(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    A job that selects tags by contacts, then assign these tags to provided documents,
    and also opens access to these documents for users that has the same tags in
    company role profile.

    :param data:
        - contact_edrpou: str - edrpou of contact for searching tags
        - company_edrpou: str - edrpou of company in which contact must be found
        - documents_ids: str - documents for creating connections

    """
    contact_edrpou: str = data['contact_edrpou']
    company_edrpou: str = data['company_edrpou']
    documents_ids: list[str] = data['documents_ids']

    # just ignore cases when document was send by coworker to other coworker
    if contact_edrpou == company_edrpou:
        return

    async with app['db'].acquire() as conn:
        company = await select_company_by_edrpou(conn, company_edrpou)
        if not company:
            logger.info('Company does not yet registered', extra={**data})
            return

        tags = await select_tags_by_contact(
            conn=conn, contact_edrpou=contact_edrpou, company_id=company.id
        )
        tags_ids = [tag.id for tag in tags]

        if not tags_ids:
            logger.info('No tags associated with contact', extra={**data})
            return

        logger.info(
            msg='Assign tags to documents by contact',
            extra={**data, 'tags_ids': tags_ids},
        )
        async with conn.begin():
            documents_ids = await select_documents_ids_for_insert(conn, documents_ids)
            await create_document_tags(
                conn=conn,
                tags_ids=tags_ids,
                documents_ids=documents_ids,
                company_id=company.id,
            )

    await send_to_indexator(app['redis'], documents_ids, to_slow_queue=True)
    await schedule_create_document_access_on_document_tag_job(
        tags_ids=tags_ids,
        documents_ids=documents_ids,
        company_edrpou=company_edrpou,
        assigner_role_id=None,
        source=None,
    )


@retry_config(max_attempts=500, max_age=datetime.timedelta(days=2), delay_minutes=1)
async def create_document_access_on_role_tag_job(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    When someone attaches a tag to the role, we need to find all documents that tagged with
    this tag and open tag access for a given role to these documents.

    The tag might be attached to more than 2 million documents, so this job might take a while
    to process all documents. Also because of this, we open access in batches to not overload
    the database
    """
    _data = CreateDocumentAccessOnRoleTagJobData.model_validate(data)
    async with services.db.acquire() as conn:
        next_cursor = await create_document_access_on_role_tag(
            conn=conn,
            tag_id=_data.tag_id,
            role_id=_data.role_id,
            company_edrpou=_data.company_edrpou,
            cursor=_data.cursor,
            limit=CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG,
        )
        if not next_cursor:
            logger.info('Create document access on role tag job was finished')
            return

        next_iteration = _data.iteration + 1

        # make a 2-minute delay every 10 iterations to give a database some time to breathe
        delay_min: int | None = 2 if next_iteration % 10 == 0 else None

        await services.kafka.add_task(
            conn=conn,
            topic=topics.CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG,
            delay_min=delay_min,
            data=CreateDocumentAccessOnRoleTagJobData(
                role_id=_data.role_id,
                tag_id=_data.tag_id,
                company_edrpou=_data.company_edrpou,
                iteration=next_iteration,
                cursor=next_cursor,
            ).to_dict(),
        )


@retry_config(max_attempts=500, max_age=datetime.timedelta(days=2))
async def create_document_access_on_document_tag__part1(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    When someone attaches a tag to the document, we need to find all roles that have this tag
    in their profile and open tag access for this document.

    Usually each tag has a connection to a small number of roles (less than 100), so usually
    this operation is quite fast
    """

    tags_ids: list[str] = data['tags_ids']
    documents_ids: list[str] = data['documents_ids']
    if not tags_ids or not documents_ids:
        return

    # Assigner role ID can be none in cases when the tag was attached to the document
    # automatically, for example, by contact tag
    assigner_role_id: str | None = data.get('role_id')
    company_edrpou: str = data['company_edrpou']

    async with services.db.acquire() as conn:
        documents = await select_documents(conn, documents_ids=documents_ids)
        documents_ids = [d.id for d in documents]

        # Filter out documents that are private within the company. The main reason we skip
        # private documents is that some users may not have permission to view them, but they
        # might still have permission to assign tags to coworkers or themselves. Without this
        # logic, such users could potentially gain access to private documents if they know
        # which tags are associated with those documents.
        private_ids = await select_private_documents_ids(
            conn=conn,
            documents_ids=documents_ids,
            company_edrpou=company_edrpou,
        )
        documents = [d for d in documents if d.id not in private_ids]
        documents_ids = [d.id for d in documents]

        await services.kafka.send_records(
            topic=topics.CREATE_DOCUMENT_ACCESS_ON_DOCUMENT_TAG_PART_2,
            values=[
                {
                    'tag_id': tag_id,
                    'document_id': document_id,
                    'company_edrpou': company_edrpou,
                }
                for tag_id, document_id in itertools.product(tags_ids, documents_ids)
            ],
        )

        if not assigner_role_id:
            return

        tags = await select_tags_by_ids(conn, tags_ids=tags_ids)
        user = await select_user(conn, role_id=assigner_role_id)
        if not user:
            logger.error('User not found', extra={'role_id': assigner_role_id})
            return

    tags_names = sorted(tag.name for tag in tags)
    await document_actions.add_document_actions(
        document_actions=[
            document_actions.DocumentAction(
                action=document_actions.Action.tag_attach,
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                role_id=assigner_role_id,
                email=user.email,
                extra={'tags_names': tags_names},
            )
            for document in documents
        ]
    )


@retry_config(max_attempts=500, max_age=datetime.timedelta(days=2))
async def create_document_access_on_document_tag__part2(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Open tag access to document to all roles that have given tag in their profile.

    WARNING: don't use this function directly, use "_part1" of this function instead.
    It contains additional logic that should be called first
    """
    tag_id: str = data['tag_id']
    document_id: str = data['document_id']
    company_edrpou: str = data['company_edrpou']

    async with services.db.acquire() as conn:
        await create_access_by_document_tag(
            conn=conn,
            tag_id=tag_id,
            document_id=document_id,
            company_edrpou=company_edrpou,
        )

    await send_to_indexator(app['redis'], [document_id], to_slow_queue=True)
