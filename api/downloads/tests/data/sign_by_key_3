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
