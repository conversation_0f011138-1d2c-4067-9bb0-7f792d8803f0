import {
    ReviewDocumentVersion,
    ReviewHistoryForTable,
} from './reviewHistoryPopupTypes';

type GroupReviewHistory = [
    version: ReviewDocumentVersion,
    reviewsHistory: ReviewHistoryForTable[],
];

export const groupReviewHistoryByVersion = (
    reviewHistory: ReviewHistoryForTable[],
) =>
    reviewHistory.reduce((groups, review) => {
        const documentVersion = review.documentVersion;

        const versionIdIndex = groups.findIndex(
            (groupsItem: GroupReviewHistory) =>
                groupsItem[0]?.id === documentVersion.id,
        );

        if (versionIdIndex !== -1) {
            groups[versionIdIndex][1]?.push(review);
        } else {
            groups.push([documentVersion, [review]]);
        }

        return groups;
    }, [] as Array<GroupReviewHistory>);
