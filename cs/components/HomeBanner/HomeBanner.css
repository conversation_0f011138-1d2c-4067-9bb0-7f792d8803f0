.root {
    padding: 15px 15px 0;
}

.rootSideBar {
    padding: 0;
}

.wrapper {
    position: relative;
}

.link {
    display: block;
    overflow: hidden;
    background-color: transparent;
    border-radius: 8px;
    line-height: 0;
}

.img {
    width: 100%;
}

.cross {
    position: absolute;
    z-index: 3;
    top: 10px;
    right: 10px;
    display: flex;
    width: 30px;
    height: 30px;
    align-items: center;
    justify-content: center;
    background-color: #fff0;
    border-radius: 8px;
    color: #fff;
    cursor: pointer;
    transition: background-color .3s;
}

.cross:hover {
    background-color: #ffffff4d;
}

.cross > svg {
    display: block;
    width: 20px;
    height: 20px;
}

@media (max-width: 768px) {
    .root {
        padding: 0;
    }
}
