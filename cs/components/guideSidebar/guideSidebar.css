.icon,
.closeIcon {
    position: relative;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.sidebar,
.sidebarAnimated {
    position: fixed;
    z-index: 101;
    top: 0;
    right: -468px;
    bottom: 0;
    display: flex;
    width: 468px;
    flex-direction: column;
    background-color: var(--white-bg);
    box-shadow: -2px 0 2px rgba(28, 54, 73, 0.11);
    font-size: 14px;
    line-height: 20px;
    overflow-x: auto;
    transition: transform 0.3s ease-out;
}

.background {
    position: fixed;
    z-index: 100;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #6e809a;
    opacity: 30%;
}

.sidebarAnimated {
    transform: translateX(-468px);
}

.header {
    padding: 32px 40px;
    border-bottom: 1px solid var(--default-border);
    font-weight: 500;
}

.closeIcon {
    position: absolute;
    top: 20px;
    right: 20px;
    color: var(--content-color);
}

.block {
    height: 74px;
}

.block:hover .subText {
    display: none;
}

.block:hover .buttons {
    display: block;
}

.image {
    width: 60px;
    flex: 0 0 60px;
}

.blockTitle {
    margin-bottom: 8px;
    font-weight: 500;
}

.subText {
    color: var(--content-secondary-color);
}

.subText,
.buttons {
    height: 42px;
}

.buttons {
    display: none;
    margin-top: 10px;
}

.videoLink {
    background-color: var(--corporate-color);
    color: #333;
}

.bottomLink {
    border-bottom: 1px dotted;
    color: var(--link-color);
    text-decoration: none;
}

.bottomLink:hover,
.bottomLink:active {
    border-color: transparent;
    text-decoration: none;
}

.items {
    padding: 0 12px 112px;
}

@media all and (max-width: 580px) {
    .sidebarAnimated {
        right: 0;
        width: 100%;
        transform: translateX(0);
    }
}
