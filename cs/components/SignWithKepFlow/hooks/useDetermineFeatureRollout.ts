import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { isFopCompany as isFopCompanyFn } from 'lib/company';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import { INTEGRATION_RATES_SET } from 'services/billing/constants';
import { AccountRate } from 'services/enums';

import { composeRolloutLsKey } from '../utils';

import { signWithKepFlowSelectors } from '../signWithKepFlowSlice';

// Deterministic selection based on company ID for percentage-based rollout
const isCompanyInRolloutBucket = (
    edrpou: string,
    bucketSizeInPercents: number,
) => {
    // Create a deterministic value based on company ID only
    const hash = Array.from(edrpou).reduce(
        (acc, char) => char.charCodeAt(0) + acc,
        0,
    );

    // Determine if company is in the selected percentage
    return hash % 100 <= bucketSizeInPercents;
};

export const useDetermineFeatureRollout = () => {
    const currentUserInfo = useSelector(
        signWithKepFlowSelectors.selectCurrentUserInfo,
    );

    useEffect(() => {
        if (!currentUserInfo) return;
        if (!currentUserInfo.companyEdrpou) return;

        const companyEdrpou = currentUserInfo.companyEdrpou;

        // Check if feature is already enabled for this company
        const lsKey = composeRolloutLsKey(companyEdrpou);
        if (getLocalStorageItem(lsKey)) {
            return; // Feature is already enabled for this company
        }

        const isFopCompany = companyEdrpou
            ? isFopCompanyFn({ edrpou: companyEdrpou })
            : true;
        const isLegalCompany = !isFopCompany;
        const isPaidCompany = currentUserInfo.activeRates.some(
            (rate: AccountRate) =>
                rate !== AccountRate.FREE && !INTEGRATION_RATES_SET.has(rate),
        );
        const isUnpaidCompany = !isPaidCompany;

        const STAGE = 5; // Set the current rollout stage
        const STAGE_TO_CONDITION_MAP: Record<number, () => boolean> = {
            1: () => {
                // Stage 1: 10% of "free" fop companies
                return (
                    isFopCompany &&
                    isUnpaidCompany &&
                    isCompanyInRolloutBucket(companyEdrpou, 10)
                );
            },
            2: () => {
                // Stage 2: 100% of "free" fop companies
                return isFopCompany && isUnpaidCompany;
            },
            3: () => {
                // Stage 3: 100% of all fop companies
                return isFopCompany;
            },
            4: () => {
                // Stage 4: 100% of all fop companies + "free" legal companies
                return isFopCompany || (isLegalCompany && isUnpaidCompany);
            },
            5: () => {
                // Stage 5: 100% of all companies
                return true;
            },
        };

        // Run the condition check for current stage
        if (STAGE_TO_CONDITION_MAP[STAGE]) {
            const isEligible = STAGE_TO_CONDITION_MAP[STAGE]();

            if (isEligible) {
                setLocalStorageItem(lsKey, isEligible);
            }
        }
    }, [currentUserInfo]);
};
