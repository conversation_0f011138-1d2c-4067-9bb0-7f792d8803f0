import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import documentActions from 'components/document/documentActionCreators';
import {
    getCurrentDocument,
    getDocumentVersionByLocationSelector,
} from 'selectors/document.selectors';
import { t } from 'ttag';
import Button from 'ui/button/button';

const EditDraftDocumentButton: React.FC = () => {
    const doc = useSelector(getCurrentDocument);
    const dispatch = useDispatch();
    const [loading, setLoading] = useState<boolean>(false);

    const documentVersion = useSelector(getDocumentVersionByLocationSelector);

    const documentVersionDraft = doc.drafts.find(
        (draft) => draft.documentVersionId === documentVersion?.id,
    );

    const handleClick = async () => {
        if (!documentVersionDraft && documentVersion) {
            setLoading(true);
            await dispatch(
                documentActions.onCreateVersionDraft(doc, documentVersion.id),
            );
            setLoading(false);
        }

        await dispatch(documentActions.setEditMode(true));
    };

    return (
        <Button
            width="full"
            onClick={handleClick}
            theme="blue"
            isLoading={loading}
        >
            {t`Редагувати чернетку`}
        </Button>
    );
};

export default EditDraftDocumentButton;
