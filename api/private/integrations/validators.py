import logging
from datetime import datetime
from typing import Annotated

import pydantic
from aiohttp import web

from api.errors import DoesNotExist, InvalidRequest, Object
from api.private.integrations.schemas import CreateVchasnoUserSchema
from app.auth.constants import MAX_USERS_NAME_PART_LENGTH
from app.auth.db import select_company_by_edrpou
from app.auth.phone_auth.validators import (
    validate_process_phone_auth_base,
    validate_send_phone_auth_base,
)
from app.documents.validators import validate_document_exists
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.types import DataDict, StrDict
from app.profile.validators import validate_custom_password_rules, validate_email_domain_rules
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.registration.types import KasaRegistrationCtx
from app.registration.validators import (
    validate_user_not_registered,
)
from app.services import services
from app.signatures.db import select_signatures
from app.signatures.types import Signature

logger = logging.getLogger(__name__)


# User name part is a first name, second name or last name
UserNamePart = Annotated[str, pydantic.Field(min_length=1, max_length=MAX_USERS_NAME_PART_LENGTH)]


class InviteUserSchema(pydantic.BaseModel):
    email: pv.Email
    first_name: UserNamePart | None = None
    second_name: UserNamePart | None = None
    last_name: UserNamePart | None = None
    contact_phone: pv.Phone | None = None


class CompanyEDRPOUSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU


class GetCompaniesSchema(pydantic.BaseModel):
    date_from: pv.LeftDatetime
    date_to: pv.RightDatetime


class GetActiveRolesSchema(pydantic.BaseModel):
    vchasno_id: pv.UUID | None = None

    # deprecated field, use vchasno_id instead
    email: pv.Email | None = None


class GetDocumentSignaturesSchema(pydantic.BaseModel):
    ids: list[pv.UUID] | None
    document_id: pv.UUID


class SendPhoneAuthCodeSchema(pydantic.BaseModel):
    phone: pv.Phone
    client_ip: str | None = None


class ProcessPhoneAuth(pydantic.BaseModel):
    phone: pv.Phone
    code: str = pydantic.Field(min_length=6, max_length=6)


class KasaRegistrationValidator(pydantic.BaseModel):
    email: pv.Email
    password: pv.Password

    # unlike web registration, Kasa mobile app requires name and phone
    # to be provided by the user during registration
    name: str = pydantic.Field(min_length=1, max_length=64)
    phone: pv.Phone

    source: RegistrationSource | None = pydantic.Field(default=None)


async def validate_send_phone_auth(
    conn: DBConnection,
    request: web.Request,
) -> SendPhoneAuthCodeSchema:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SendPhoneAuthCodeSchema, raw_data)

    await validate_send_phone_auth_base(
        conn=conn,
        auth_phone=data.phone,
        # We expects that private integration will get client IP from the request headers,
        # similarly to how it is done in the "get_client_ip" function in our codebase.
        client_ip=data.client_ip,
        # For private integration when phone is not used for authentication, we will create
        # a brand new user with this phone, even if someone in the system is already using this
        # phone for other purposes, like 2FA, etc. We do this because a phone app that consumes
        # this endpoint is not aware of such cases, so for now just skip this check to give a
        # user a chance to authenticate with their phone number.
        reject_used_phone_registration=False,
    )

    return data


async def validate_process_phone_auth(conn: DBConnection, request: web.Request) -> ProcessPhoneAuth:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(ProcessPhoneAuth, raw_data)

    await validate_process_phone_auth_base(
        conn=conn,
        auth_phone=data.phone,
        # see comment in `validate_send_phone_auth`
        reject_used_phone_registration=False,
    )

    return data


def validate_get_companies(request: web.Request) -> tuple[datetime, datetime]:
    data = validators.validate_pydantic(GetCompaniesSchema, dict(request.rel_url.query))
    date_from, date_to = data.date_from, data.date_to
    if date_to < date_from:
        raise InvalidRequest(reason=_('Параметр "date_from" має бути менший ніж "date_to"'))

    return date_from, date_to


async def validate_get_company_billing_accounts(conn: DBConnection, raw_data: DataDict) -> str:
    data = validators.validate_pydantic(CompanyEDRPOUSchema, raw_data)
    company = await select_company_by_edrpou(conn, edrpou=data.edrpou)
    if company is None:
        raise DoesNotExist(Object.company, edrpou=data.edrpou)
    return company.id


async def validate_get_document_signatures(request: web.Request) -> list[Signature]:
    data = validators.validate_pydantic(
        GetDocumentSignaturesSchema,
        {
            'ids': request.rel_url.query.getall('ids', None),
            'document_id': request.match_info['document_id'],
        },
    )
    async with services.db.acquire() as conn:
        document = await validate_document_exists(conn, {'document_id': data.document_id})
        signs = await select_signatures(
            conn,
            document_ids=[document.id],
            signatures_ids=data.ids,
        )

    return signs


async def validate_registration_kasa(
    conn: DBConnection,
    raw_data: StrDict,
) -> KasaRegistrationCtx:
    """Validation for registration from Kasa mobile app"""

    data = validators.validate_pydantic(KasaRegistrationValidator, raw_data)

    email = data.email
    password = data.password

    validate_email_domain_rules(email)
    validate_custom_password_rules(email, password=password)
    await validate_user_not_registered(conn, email=email)

    return KasaRegistrationCtx(
        email=email,
        password=password,
        phone=data.phone,
        name=data.name,
        source=data.source,
        registration_method=RegistrationMethod.main,
    )


async def validate_create_vchasno_user_common(request: web.Request) -> CreateVchasnoUserSchema:
    return await validate_json_request(request, CreateVchasnoUserSchema)


async def validate_json_request[JsonSchemaType: pydantic.BaseModel](
    request: web.Request,
    schema: type[JsonSchemaType],
) -> JsonSchemaType:
    raw_data = await validators.validate_json_request(request)
    return validators.validate_pydantic(schema, raw_data)
