import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Checkbox, FlexBox, Paragraph, Text } from '@vchasno/ui-kit';

import companyCardActionCreators from 'components/companyCard/companyCardActionCreators';
import {
    getCurrentCompanyArchiveSettings,
    isAdminSelector,
} from 'selectors/app.selectors';
import { t } from 'ttag';

import { ArchiveSettings } from '../../../../types/user';

const CompanyArchiveSettings: React.FC = () => {
    const dispatch = useDispatch();
    const isAdmin = useSelector(isAdminSelector);
    const archiveSettings = useSelector(getCurrentCompanyArchiveSettings);

    const onChangeConfig = (
        configName: keyof ArchiveSettings,
        value: boolean,
    ) =>
        dispatch(
            companyCardActionCreators.onChangeAdditionalConfig(
                {
                    archive_settings: {
                        [configName]: value,
                    },
                },
                {
                    archive_settings: {
                        ...archiveSettings,
                        [configName]: value,
                    },
                },
            ),
        );

    return (
        <FlexBox direction="column" gap={20}>
            <Paragraph>
                <Text type="secondary">
                    {t`Активуйте ті статуси обробки документів, при котрих ваші співробітники зможуть переносити документи в архів`}
                </Text>
            </Paragraph>
            <Checkbox
                disabled={!isAdmin}
                checked={archiveSettings.allow_uploaded_documents}
                label={t`Завантажені`}
                onChange={(event) => {
                    onChangeConfig(
                        'allow_uploaded_documents',
                        event.target.checked,
                    );
                }}
            />
            <Checkbox
                disabled={!isAdmin}
                checked={archiveSettings.allow_partially_signed_documents}
                label={t`Частково підписані`}
                onChange={(event) => {
                    onChangeConfig(
                        'allow_partially_signed_documents',
                        event.target.checked,
                    );
                }}
            />
            <Checkbox
                disabled={!isAdmin}
                checked={archiveSettings.allow_fully_signed_documents}
                label={t`Підписані`}
                onChange={(event) => {
                    onChangeConfig(
                        'allow_fully_signed_documents',
                        event.target.checked,
                    );
                }}
            />
            <Checkbox
                disabled={!isAdmin}
                checked={archiveSettings.allow_rejected_documents}
                label={t`Відхилені`}
                onChange={(event) => {
                    onChangeConfig(
                        'allow_rejected_documents',
                        event.target.checked,
                    );
                }}
            />
        </FlexBox>
    );
};

export default CompanyArchiveSettings;
