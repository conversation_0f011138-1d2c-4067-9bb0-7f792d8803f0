import React, { ChangeEvent, FC, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    <PERSON><PERSON>,
    Button,
    Datepicker,
    Input,
    Select,
    SelectOption,
    TextInput,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import { RATES_PAGE_LINK } from 'lib/routing/constants';
import {
    getRequiredFieldsDocAmount,
    getRequiredFieldsDocCategory,
    getRequiredFieldsDocDate,
    getRequiredFieldsDocNumber,
} from 'selectors/requiredFieldsResolver.selectors';
import { DOCUMENT_CATEGORY_OPTIONS_FOR_SIGN_POPUP } from 'services/documents/ts/constants';
import { DocumentCategory } from 'services/enums';
import { t } from 'ttag';

import { Nullable } from '../../types/general';
import {
    NeedFields,
    WithoutRequiredFieldsDoc,
} from '../DocumentsRequiredFieldsError/types';

import requiredFieldsResolverActionCreators from '../RequiredFieldsResolver/requiredFieldsResolverActionCreators';

import eventTracking from '../../services/analytics/eventTracking';
import DocumentAmount from '../DocumentAmount/DocumentAmount';

import css from './DocumentRequiredFieldsForm.css';

interface DocumentRequiredFieldsFormProps {
    withoutRequiredFieldsDoc: WithoutRequiredFieldsDoc;
}

const normalizeAmount = (amount?: Nullable<string>): string => amount ?? '';

const DocumentRequiredFieldsForm: FC<DocumentRequiredFieldsFormProps> = ({
    withoutRequiredFieldsDoc,
}) => {
    const dispatch = useDispatch();
    const docDate = useSelector(getRequiredFieldsDocDate);
    const docNumber = useSelector(getRequiredFieldsDocNumber);
    const docCategory = useSelector(getRequiredFieldsDocCategory);
    const docAmount = useSelector(getRequiredFieldsDocAmount);

    const doc = withoutRequiredFieldsDoc.doc;
    const needFields = withoutRequiredFieldsDoc.needFields;

    const emptyRequiredFields = {
        isCategoryRequired: needFields.includes(NeedFields.IS_TYPE_REQUIRED),
        isNumberRequired: needFields.includes(NeedFields.IS_NUMBER_REQUIRED),
        isDateRequired: needFields.includes(NeedFields.IS_DATE_REQUIRED),
        isAmountRequired: needFields.includes(NeedFields.IS_AMOUNT_REQUIRED),
    };
    const needFieldsForEventTracking = needFields.join(', ');

    const sendEventTracking = () =>
        eventTracking.sendEvent(
            'form_doc_sign_step_1_1',
            'fields',
            needFieldsForEventTracking,
        );

    const onChangeField = (evt: ChangeEvent) =>
        dispatch(requiredFieldsResolverActionCreators.onChangeNumber(evt));
    const onChangeDate = (date: Date) =>
        dispatch(requiredFieldsResolverActionCreators.onChangeDate(date));
    const onChangeCategory = (option: SelectOption) =>
        dispatch(
            requiredFieldsResolverActionCreators.onChangeCategory(option.value),
        );
    const onChangeAmount = (value: string) =>
        dispatch(requiredFieldsResolverActionCreators.onChangeAmount(value));
    const onSubmit = () => {
        sendEventTracking();
        dispatch(requiredFieldsResolverActionCreators.onUpdateDocument());
    };

    useEffect(() => {
        if (emptyRequiredFields.isCategoryRequired && !docCategory) {
            onChangeCategory(
                DOCUMENT_CATEGORY_OPTIONS_FOR_SIGN_POPUP.find(
                    (option) => option.value === DocumentCategory.ACT,
                )!,
            );
        }
    }, [emptyRequiredFields]);

    const isDisabledButton =
        (emptyRequiredFields?.isNumberRequired && !docNumber) ||
        (emptyRequiredFields?.isDateRequired && !docDate) ||
        (emptyRequiredFields?.isCategoryRequired && !docCategory) ||
        (emptyRequiredFields?.isAmountRequired && !docAmount);

    return (
        <div className={css.container}>
            <p className={cn(css.informationText, css.desktopOnly)}>
                <a
                    href={`/app/documents/${doc.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    {t`Відкрийте документ`}{' '}
                </a>
                {t`у новій вкладці для зручності заповнення`}
            </p>
            <Alert type="info" className={css.informationPanel}>
                {t`Документи для обраного контрагента мають містити поля, що вказані нижче. Хочете також вказувати обов’язкові поля для вхідних?`}
                <br />
                <a
                    className={css.informationPanelLink}
                    href={RATES_PAGE_LINK}
                    target="_blank"
                    rel="noopener noreferrer"
                >{t`Оберіть новий тариф.`}</a>
            </Alert>
            <div className={cn(css.row, css.rowNameDate)}>
                {emptyRequiredFields?.isNumberRequired && (
                    <div className={css.rowItem}>
                        <TextInput
                            wide
                            value={docNumber || ''}
                            name="docNumber"
                            id="number"
                            label={t`Номер документа`}
                            type="text"
                            onChange={onChangeField}
                        />
                    </div>
                )}
                {emptyRequiredFields?.isDateRequired && (
                    <div className={css.rowItem}>
                        <Datepicker
                            wide
                            popperPlacement="bottom-end"
                            selected={docDate}
                            label={t`Дата формування`}
                            onChange={onChangeDate}
                        />
                    </div>
                )}
            </div>
            {emptyRequiredFields?.isCategoryRequired && (
                <div className={css.row}>
                    <Select
                        wide
                        label={t`Тип документа`}
                        value={
                            DOCUMENT_CATEGORY_OPTIONS_FOR_SIGN_POPUP.find(
                                (option) => option.value === docCategory,
                            ) || null
                        }
                        options={DOCUMENT_CATEGORY_OPTIONS_FOR_SIGN_POPUP}
                        onChange={onChangeCategory}
                    />
                </div>
            )}
            {emptyRequiredFields?.isAmountRequired && (
                <div className={css.row}>
                    <Input
                        hideEmptyMeta
                        className="document-amount-input"
                        label={t`Сума документа (грн.)`}
                        hint={t`Сума документа з урахуванням податків`}
                    >
                        <DocumentAmount
                            placeholder=" "
                            id="amount"
                            hintText={t`Введіть параметр цифрами (100, 100.99)`}
                            value={normalizeAmount(docAmount)}
                            onValueChange={onChangeAmount}
                        />
                    </Input>
                </div>
            )}
            <Button
                theme="primary"
                wide
                onClick={onSubmit}
                disabled={isDisabledButton}
                className={css.button}
                dataQa="qa_submit_form"
            >
                {t`Зберегти і продовжити`}
            </Button>
        </div>
    );
};

export default DocumentRequiredFieldsForm;
