import React, { FC } from 'react';

import { ParticipantElement } from '../types';

import css from './DeletePopupWarningList.css';

interface SignReviewWarningListProps {
    signReviewList: ParticipantElement[];
}

const SignReviewWarningList: FC<SignReviewWarningListProps> = ({
    signReviewList,
}) => (
    <ul className={css.listRoot}>
        {signReviewList.map((item) => (
            <li key={item.id}>
                <a
                    target="_blank"
                    href={`/app/documents/${item.id}`}
                    rel="noopener noreferrer"
                >
                    {item.title}
                </a>
            </li>
        ))}
    </ul>
);

export default SignReviewWarningList;
