.root {
    position: relative;
    padding-right: 32px;
}

.container {
    position: relative;
    display: flex;
    overflow: hidden;
    text-overflow: ellipsis;
}

.signer {
    display: flex;
    align-items: center;
    padding-top: 14px;
}

.signerInline {
    display: flex;
    align-items: center;
}

.icon,
.hintIcon {
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
}

.hintContent {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.hintIcon {
    position: absolute;
    top: 0;
    right: 8px;
    bottom: 0;
    margin: auto;
    color: var(--pigeon-color);
}

.hintIcon:hover {
    color: var(--primary-cta-color);
}

.greyCrossIcon {
    color: var(--grey-color);
}
