import React, { Fragment, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { bindActionCreators } from 'redux';

import { StoreState } from '../../types/store';

import * as actionCreators from './tagsEditPopupActionCreators';

import PopupMobile from '../ui/popup/mobile/popup';
import Popup from '../ui/popup/popup';

import { MEDIA_WIDTH } from '../../lib/constants';
import TagsEditForm from '../tagsEditForm/tagsEditForm';

// styles
import css from './tagsEditPopup.css';

function mapStateToProps(state: StoreState) {
    return state.tagsEditPopup;
}

const TagsEditPopup = () => {
    const state = useSelector(mapStateToProps);

    const dispatch = useDispatch();

    const actions = useMemo(
        () => bindActionCreators(actionCreators, dispatch),
        [dispatch],
    );

    const renderContent = () => {
        return (
            <div className={css.root}>
                <TagsEditForm
                    isLoading={state.isLoading}
                    isSuggestionsShown={state.isSuggestionsShown}
                    errorMessage={state.errorMessage}
                    newTags={state.newTags}
                    selectedTags={state.selectedTags}
                    suggestedTags={state.suggestedTags}
                    tags={state.tags}
                    onAddNewTag={actions.onAddNewTag}
                    onAutosuggestTags={actions.onAutosuggestTags}
                    onClose={actions.onClose}
                    onCloseSuggestions={actions.onCloseSuggestions}
                    onDeleteTagFromList={actions.onDeleteTagFromList}
                    onSubmit={actions.onSubmit}
                    onSuggestionClick={actions.onSuggestionClick}
                />
            </div>
        );
    };
    return (
        <Fragment>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup
                    active={state.isActive}
                    onClose={actions.onClose}
                    title={state.title}
                >
                    {renderContent()}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile
                    active={state.isActive}
                    onClose={actions.onClose}
                    title={state.title}
                >
                    {renderContent()}
                </PopupMobile>
            </MediaQuery>
        </Fragment>
    );
};

export default TagsEditPopup;
