"""remove user_actions indexes from code

Revision ID: 549f53ec7e51
Revises: 9e21d1b962ed
Create Date: 2022-04-18 10:30:21.650737


Author: b.do<PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '549f53ec7e51'
down_revision = '9e21d1b962ed'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS ix_user_actions_edrpou;
        """
    )
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS ix_user_actions_role_id;
        """
    )

def downgrade():
    op.create_index('ix_user_actions_role_id', 'user_actions', ['role_id'], unique=False)
    op.create_index('ix_user_actions_edrpou', 'user_actions', ['edrpou'], unique=False)
