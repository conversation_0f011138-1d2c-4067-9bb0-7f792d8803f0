.root {
    height: 50px;
}

.head {
    box-sizing: border-box;
    padding: 0 30px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.headFixed {
    position: fixed;
    z-index: 1;
    top: 0;
    right: 10px;
    left: calc(260px + 10px);
    box-shadow: 0 2px 2px 0 rgba(28, 54, 72, 0.11);
}

.headerContent {
    display: table;
    width: 100%;
    height: 50px;
    table-layout: fixed;
}

.titles {
    display: table;
    width: 100%;
    height: 50px;
    table-layout: fixed;
}

.title {
    display: table-cell;
    padding: 7px 10px 7px 0;
    font-weight: bold;
    vertical-align: middle;
}

.title:last-child {
    padding-right: 0;
}

.list {
    padding: 0 30px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    line-height: 1.2;
    word-wrap: break-word;
}

.list.fullWidth {
    padding: 0;
}

.counter + .list {
    margin-top: 5px;
}

.item {
    display: table;
    table-layout: fixed;
}

.item.fullWidth {
    border-top: 1px solid var(--default-border);
}

.item + .item {
    border-top: 1px solid var(--default-border);
}

.itemClickable {
    cursor: pointer;
}

.row {
    display: table;
    width: 100%;
    box-sizing: border-box;
    table-layout: fixed;
}

.row.fullWidth {
    min-height: 40px;
    padding: 0 30px;
}

a.row.fullWidth {
    color: var(--content-color);
}

.row:hover {
    text-decoration: none;
}

.row.fullWidth:hover {
    background-color: var(--grey-bg);
    text-decoration: none;
}

.cell {
    display: table-cell;
    padding: 7px 10px 7px 0;
    vertical-align: middle;
}

.cell:last-child {
    padding-right: 0;
}

.header {
    display: flex;
    height: 100%;
    align-items: center;
}

.tools {
    display: table-cell;
    width: 100%;
    vertical-align: middle;
}

.checkbox {
    display: table-cell;
    width: 30px;
    vertical-align: middle;
}

.counter {
    margin-top: 5px;
}

.itemAlignTop .cell {
    vertical-align: top;
}

@media all and (max-width: 768px) {
    .list {
        padding: 0 16px;
    }

    .row {
        display: block;
        padding: 16px 0;
    }

    .cell {
        display: block;
        min-width: 100%;
        padding: 0;
    }

    .cell + .cell {
        margin-top: 8px;
    }

    .cell:empty {
        margin-top: 0;
    }
}

@media all and (min-width: 769px) {
    .itemWithHiddenChild .cell:last-child {
        visibility: hidden;
    }

    .itemWithHiddenChild:hover .cell:last-child {
        visibility: visible;
    }
}

@media all and (max-width: 1024px) {
    .headFixed {
        right: 10px;
        left: 10px;
    }

    .item {
        display: block;
    }
}

@media all and (max-width: 1200px) {
    .head {
        border-right-width: 10px;
        border-left-width: 10px;
    }
}
