/* global describe, it */
import { expect } from 'chai';

import io from '../../io';

const TEST_REL_URL = '/app';
const TEST_REL_URL_SEARCH_PARAMS = { q: 'Query' };
const TEST_REL_URL_WITH_SEARCH_PARAMS = '/app?q=Query';

const TEST_REL_URL_NEW_SEARCH_PARAMS = { q: 'New Query', lang: 'uk-UA' };
const TEST_REL_URL_WITH_NEW_SEARCH_PARAMS = '/app?q=New%20Query&lang=uk-UA';

const TEST_URL = 'https://www.google.com/';
const TEST_URL_SEARCH_PARAMS = { q: 'Query' };
const TEST_URL_WITH_SEARCH_PARAMS = 'https://www.google.com/?q=Query';

const TEST_URL_NEW_SEARCH_PARAMS = { q: 'New Query', lang: 'uk-UA' };
const TEST_URL_WITH_NEW_SEARCH_PARAMS =
    'https://www.google.com/?q=New%20Query&lang=uk-UA';

const TEST_REL_URL_ARRAY_PARAMS = {
    q: 'New Query',
    lang: 'uk-UA',
    to: ['me', 'you'],
};
const TEST_REL_URL_WITH_ARRAY_PARAMS =
    '/app?q=New%20Query&lang=uk-UA&to=me&to=you';

describe('services/io', () => {
    describe('buildUrl', () => {
        it('absolute URL without search params', () => {
            expect(io.buildUrl(TEST_URL)).to.equal(TEST_URL);
        });

        it('absolute URL with empty search params', () => {
            expect(io.buildUrl(TEST_URL, undefined)).to.equal(TEST_URL);
            expect(io.buildUrl(TEST_URL, {})).to.equal(TEST_URL);
        });

        it('absolute URL with search params', () => {
            expect(io.buildUrl(TEST_URL, TEST_URL_SEARCH_PARAMS)).to.equal(
                TEST_URL_WITH_SEARCH_PARAMS,
            );
        });

        it('absolute URL with search params in original url', () => {
            expect(
                io.buildUrl(
                    TEST_URL_WITH_SEARCH_PARAMS,
                    TEST_URL_NEW_SEARCH_PARAMS,
                ),
            ).to.equal(TEST_URL_WITH_NEW_SEARCH_PARAMS);
        });

        it('relative URL without search params', () => {
            expect(io.buildUrl(TEST_REL_URL)).to.equal(TEST_REL_URL);
        });

        it('relative URL with empty search params', () => {
            expect(io.buildUrl(TEST_REL_URL, undefined)).to.equal(TEST_REL_URL);
            expect(io.buildUrl(TEST_REL_URL, {})).to.equal(TEST_REL_URL);
        });

        it('relative URL with search params', () => {
            expect(
                io.buildUrl(TEST_REL_URL, TEST_REL_URL_SEARCH_PARAMS),
            ).to.equal(TEST_REL_URL_WITH_SEARCH_PARAMS);
        });

        it('relative URL with search params in original url', () => {
            expect(
                io.buildUrl(
                    TEST_REL_URL_WITH_SEARCH_PARAMS,
                    TEST_REL_URL_NEW_SEARCH_PARAMS,
                ),
            ).to.equal(TEST_REL_URL_WITH_NEW_SEARCH_PARAMS);
        });

        it('relative URL with array params', () => {
            expect(
                io.buildUrl(TEST_REL_URL, TEST_REL_URL_ARRAY_PARAMS, {
                    arrayFormat: 'repeat',
                }),
            ).to.equal(TEST_REL_URL_WITH_ARRAY_PARAMS);
        });
    });
});
