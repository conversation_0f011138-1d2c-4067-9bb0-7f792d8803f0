import React from 'react';

import { NovusEdrpouList } from 'components/txtXmlViewer/Comdoc/Novus/constants';
import { getIsCompanyXmlOwnerByEdrpou } from 'components/txtXmlViewer/Comdoc/helper';
import { FlowMetroEdrpouList } from 'components/txtXmlViewer/metro/constants';
import { FlowPepsiCoEdrpouList } from 'components/txtXmlViewer/pepsico/constants';
import ReturnToSupplier from 'components/txtXmlViewer/rozetka/ReturnToSupplier/ReturnToSupplier';
import Comdoc016 from 'components/txtXmlViewer/varus/Comdoc016/Comdoc016';
import { ROZETKA_EDRPOUS } from 'lib/constants';
import { parseXml } from 'services/xml';

import { XmlData, XmlDocument } from '../../../types/xmlDocuments';

import { textContent } from '../utils';

import MutualSettlementsAct from '../MutualSettlementsAct/MutualSettlementsAct';
import MutualSettlementsActParser from '../MutualSettlementsAct/parser';
import ReturnPackaging from '../ReturnPackaging/ReturnPackaging';
import returnPackagingParser from '../ReturnPackaging/parser';
import ServiceDeed from '../ServiceDeed/ServiceDeed';
import ServiceDeedParser from '../ServiceDeed/parser';
import FoxtrotComdoc from '../foxtrot/comdoc';
import foxtrotParser from '../foxtrot/parser';
import MetroComdoc from '../metro/Comdoc/Comdoc';
import metroParser from '../metro/Comdoc/parser';
import PepsiCoComdoc from '../pepsico/comdoc';
import RozetkaComdoc018 from '../rozetka/Comdoc018/Comdoc018';
import RozetkaComdoc016 from '../rozetka/Comdoc16/Comdoc16';
import RukavychkaComdoc from '../rukavychka/comdoc';
import rukavychkaParser from '../rukavychka/parser';
import SLogistikaDeed from '../sLogistika/deed';
import sLogistikaParser from '../sLogistika/parser';
import ServiceDeedVarus from '../varus/ServiceDeed/ServiceDeed';
import VarusComdoc from '../varus/comdoc';
import varusParser from '../varus/parser';
import VKComdoc from '../vk/comdoc';
import { FlowVKEdrpouList } from '../vk/constants';
import AsbisComdoc006 from './Asbis/Comdoc006';
import ComdocDefault from './Comdoc';
import Comdoc006 from './Comdoc006/Comdoc';
import Comdoc007 from './Comdoc007/Comdoc';
import Comdoc012 from './Comdoc012/Comdoc';
import CommoditySpecification from './CommoditySpecification/CommoditySpecification';
import commoditySpecificationParser from './CommoditySpecification/parser';
import inconsistencyParcer from './InconsistencyComdoc/inconsistencyParcer';
import koloParser from './Kolo/parser';
import { NovusComdoc007 } from './Novus/index';
import WorksAcceptanceCertificate from './WorksAcceptanceCertificate/WorksAcceptanceCertificate';
import * as constants from './constants';
import {
    AsbisEdrpouList,
    ComdocTypes,
    FlowFaynoMarketEdrpouList,
    FlowVarusEdrpouList,
} from './constants';
import defaultParser from './parser';

interface Data {
    edrpou: string;
    recipientEdrpou: string;
    content: XmlData;
}

interface Props {
    doc: XmlDocument;
    data: Data;
}

// eslint-disable-next-line complexity
export const comdocResolver = (props: Props) => {
    const { data } = props;
    const { edrpou } = data;
    const xml = parseXml(data.content);
    const documentTypeCode = textContent(xml, constants.DOCUMENT_TYPE_QUERY);

    const parsed = defaultParser(xml);
    const isVKOwner = FlowVKEdrpouList.find((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
    );
    const isPepsiCoOwner = FlowPepsiCoEdrpouList.find(
        (edrpouItem) => edrpouItem === parsed.partner?.edrpou,
    );
    const isFaynoMarketOwner = FlowFaynoMarketEdrpouList.find((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
    );
    const isVarusOwner = FlowVarusEdrpouList.find((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
    );
    const isFoxtrotEdrpou = data.recipientEdrpou === '35625082';
    const isMetroOwner = FlowMetroEdrpouList.some(
        (edrpouItem) => edrpouItem === parsed.partner?.edrpou,
    );
    const isRozetka = ROZETKA_EDRPOUS.find((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
    );
    const isNovus = NovusEdrpouList.some((edrpouItem) =>
        getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
    );

    if (documentTypeCode === ComdocTypes.RECONCILATION_ACT) {
        //TODO review name for data.edrpou, because in parser XML it's name like OwnerEdrpou
        const isVarusEdrpou =
            data.recipientEdrpou === '30982361' || data.edrpou === '30982361';

        if (isVarusEdrpou) {
            return <VarusComdoc {...props} data={varusParser(xml)} />;
        }

        return (
            <RukavychkaComdoc
                {...props}
                data={
                    isFoxtrotEdrpou ? foxtrotParser(xml) : rukavychkaParser(xml)
                }
            />
        );
    } else if (edrpou === '40141636' || edrpou === '33584049') {
        return (
            <SLogistikaDeed
                {...props}
                data={sLogistikaParser(xml)}
                renderSignatures={false}
                renderReviews={false}
            />
        );
    } else if (documentTypeCode === ComdocTypes.SERVICE_DEED) {
        if (isRozetka) {
            return (
                <RozetkaComdoc018
                    renderReviews
                    renderSignatures
                    {...props}
                    data={ServiceDeedParser(xml)}
                />
            );
        }

        return (
            <ServiceDeed
                renderReviews
                renderSignatures
                {...props}
                data={ServiceDeedParser(xml)}
            />
        );
    } else if (documentTypeCode === ComdocTypes.MUTUAL_SETTLEMENTS_ACT) {
        return (
            <MutualSettlementsAct
                {...props}
                data={MutualSettlementsActParser(xml)}
            />
        );
    } else if (documentTypeCode === ComdocTypes.INCONSISTENCY_ACT) {
        const parsedInconsistency = inconsistencyParcer(xml);
        const isKoloOwner = parsedInconsistency.owner?.edrpou === '41135005';
        const isKievhlebOwner =
            parsedInconsistency.owner?.edrpou === '40758305';

        return isKoloOwner ? (
            <ComdocDefault
                notAcceptedQuantity
                {...props}
                data={koloParser(xml)}
                quantityColumnName={'Прийнята кількість'}
            />
        ) : (
            <ComdocDefault
                disagreementName={isKievhlebOwner}
                inconsistencyAct
                {...props}
                data={parsedInconsistency}
                quantityColumnName={'Прийнята кількість'}
            />
        );
    } else if (documentTypeCode === ComdocTypes.INCOME_INVOICE) {
        const isKoloOwner = parsed.owner?.edrpou === '41135005';

        if (isNovus) {
            return <NovusComdoc007 {...props} data={parsed} />;
        }

        if (isKoloOwner) {
            return <ComdocDefault {...props} data={koloParser(xml)} />;
        }

        return <Comdoc007 {...props} data={parsed} />;
    } else if (documentTypeCode === ComdocTypes.COMMODITY_SPECIFICATION) {
        return (
            <CommoditySpecification
                {...props}
                data={commoditySpecificationParser(xml)}
            />
        );
    } else if (documentTypeCode === ComdocTypes.RETURN_PACKAGING) {
        return (
            <ReturnPackaging
                renderReviews={false}
                renderSignatures={false}
                {...props}
                data={returnPackagingParser(xml)}
            />
        );
    } else if (
        documentTypeCode === ComdocTypes.WORK_ACCEPTANCE_CERTIFICATE &&
        !isVKOwner &&
        !isFaynoMarketOwner
    ) {
        if (isVarusOwner) {
            return (
                <ServiceDeedVarus
                    renderReviews
                    renderSignatures
                    {...props}
                    data={ServiceDeedParser(xml)}
                />
            );
        }

        return (
            <WorksAcceptanceCertificate {...props} data={defaultParser(xml)} />
        );
    } else if (
        documentTypeCode === ComdocTypes.RETURN_TO_SUPPLIER &&
        isRozetka
    ) {
        return <ReturnToSupplier {...props} data={parsed} />;
    } else if (
        documentTypeCode === ComdocTypes.SALES_INVOICE &&
        !isVKOwner &&
        !isPepsiCoOwner &&
        !isMetroOwner &&
        !isFoxtrotEdrpou
    ) {
        const isAsbis = AsbisEdrpouList.find((edrpouItem) =>
            getIsCompanyXmlOwnerByEdrpou(edrpouItem, parsed),
        );

        if (isAsbis) {
            return <AsbisComdoc006 {...props} data={parsed} />;
        }

        return <Comdoc006 {...props} data={parsed} />;
    } else if (
        documentTypeCode === ComdocTypes.COMDOC_016 &&
        (isVarusOwner || isRozetka)
    ) {
        if (isRozetka) {
            return <RozetkaComdoc016 {...props} data={parsed} />;
        }

        if (isVarusOwner) {
            return <Comdoc016 {...props} data={parsed} />;
        }
    } else if (
        documentTypeCode === ComdocTypes.RETURN_INVOICE &&
        !isVKOwner &&
        !isPepsiCoOwner &&
        !isMetroOwner &&
        !isFoxtrotEdrpou
    ) {
        return <Comdoc012 {...props} data={parsed} />;
    } else {
        if (isVKOwner) {
            return <VKComdoc {...props} data={parsed} />;
        }

        // add specifier fix for invoice(Comdoc 006)
        if (isPepsiCoOwner) {
            return <PepsiCoComdoc {...props} data={parsed} />;
        }

        if (isMetroOwner) {
            const metroParsedData = metroParser(xml);

            return <MetroComdoc {...props} data={metroParsedData} />;
        }

        if (isFoxtrotEdrpou) {
            return <FoxtrotComdoc {...props} data={parsed} />;
        }

        return <ComdocDefault {...props} data={parsed} />;
    }
};
