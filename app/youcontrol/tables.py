import sqlalchemy as sa

from app.models import columns, metadata

# Data synchronised from YouControl API
youcontrol_company_table = sa.Table(
    'youcontrol_companies',
    metadata,
    columns.ForeignKey(
        'company_id',
        'companies.id',
        ondelete='CASCADE',
        nullable=False,
        primary_key=True,
    ),
    # Data extracted from responses
    columns.Boolean('is_company_active', nullable=False),
    columns.Text('contractor_type', nullable=True),
    # Full response from YouControl API
    columns.JSONB('response_edr', nullable=True),
    columns.DateTime('date_sync_edr'),
)
