import React from 'react';
import ReactPhoneInput, { PhoneInputProps } from 'react-phone-input-2';

import { Input, InputProps } from '@vchasno/ui-kit';

import cn from 'classnames';
import { t } from 'ttag';

import css from './PhoneInput.css';

interface PhoneInputCustomProps extends PhoneInputProps {
    className?: string;
    label?: InputProps['label'];
    error?: InputProps['error'];
    disabled?: InputProps['disabled'];
    required?: InputProps['required'];
    hideEmptyMeta?: boolean;
    wide?: boolean;
}

const PhoneInput: React.FC<React.PropsWithChildren<PhoneInputCustomProps>> = ({
    className,
    label,
    error,
    disabled,
    required,
    wide,
    hideEmptyMeta,
    inputProps = {},
    ...phoneInputProps
}) => {
    return (
        <Input
            wide={wide}
            hideEmptyMeta={hideEmptyMeta}
            className={cn(css.inputWrapper, {
                [css.withValue]: phoneInputProps.value,
            })}
            required={required}
            disabled={disabled}
            label={label}
            error={error}
            labelProps={{
                required,
            }}
        >
            <ReactPhoneInput
                disabled={disabled}
                enableSearch
                country="ua"
                countryCodeEditable={false}
                specialLabel={''}
                searchPlaceholder={t`Пошук`}
                searchNotFound={t`Нічого не знайдено`}
                {...phoneInputProps}
                inputProps={{
                    ...inputProps,
                    className: cn(css.input, className, inputProps?.className),
                }}
            />
        </Input>
    );
};

export default PhoneInput;
