import { useMutation, useQueryClient } from '@tanstack/react-query';

import { GET_USER_CLOUD_CERTIFICATES } from 'lib/queriesConstants';

import { updateFirebaseStatus } from '../api';

export function useUpdateFirebaseStatusMutation() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: updateFirebaseStatus,
        onSuccess: () => {
            queryClient.invalidateQueries([GET_USER_CLOUD_CERTIFICATES]);
        },
    });
}
