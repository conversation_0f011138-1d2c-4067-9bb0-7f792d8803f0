from __future__ import annotations

import io
import logging
import typing as t
from stat import S_IFREG

from stream_zip import ZIP_32, AsyncMemberFile, async_stream_zip

from api.downloads.archives import (
    ArchiveFilesExtractor,
    MultiArchiveFilePathPreparer,
    generate_documents_archive,
    prepare_archive_files,
)
from api.downloads.db import insert_download_documents_archive, update_download_documents_archive
from api.downloads.types import (
    ArchiveConfig,
    ArchiveOptions,
    Document,
    GeneratedArchiveCtx,
    to_document,
)
from api.downloads.utils import get_documents_archive_s3_key
from app.auth.db import select_company_by_role_id, select_user
from app.auth.types import AuthUser, User
from app.billing.utils import is_company_has_pro_or_higher_rate
from app.directories.db import select_company_directories_by_ids, select_documents_directories
from app.directories.types import Directory
from app.document_versions.utils import get_latest_document_versions_available_for_company
from app.documents.db import select_documents_by_ids_with_company_info
from app.documents.emailing import send_archive_link
from app.events import document_actions
from app.lib import s3_utils
from app.lib.chunks import iter_bytes_by_chunks
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import local_now
from app.lib.s3_utils import upload_iterable
from app.lib.types import DataDict
from app.services import services
from app.uploads.constants import MB

logger = logging.getLogger(__name__)


async def upload_archive_to_s3(
    archive_id: str,
    content_stream: t.AsyncIterable[bytes],
    company_edrpou: str,
) -> int:
    """
    Uploads archive to S3 and returns its size in bytes
    """
    s3_key = get_documents_archive_s3_key(archive_id=archive_id)
    upload_file = s3_utils.UploadFileIterable(
        key=s3_key,
        body=content_stream,
    )
    return await upload_iterable(upload_file)


async def download_and_generate_archive(
    file_group: list[str],
    user: User,
    archive_name: str,
    archive_config: ArchiveConfig,
) -> GeneratedArchiveCtx | None:
    max_archive_size_bytes = services.config.consts.max_files_size * MB

    async with services.db.acquire() as conn:
        documents_db = await select_documents_by_ids_with_company_info(conn, file_group)

        if not documents_db:
            logger.info(
                'Cant find documents for documents archive',
                extra={'file_group': file_group},
            )
            return None

        document_versions = await get_latest_document_versions_available_for_company(
            conn,
            document_ids=file_group,
            company_edrpou=user.company_edrpou,
        )

    document_versions_map = {
        document_version.document_id: document_version for document_version in document_versions
    }

    documents = [
        to_document(documents_db, version=document_versions_map.get(documents_db.id))
        for documents_db in documents_db
    ]

    buffer = io.BytesIO()

    archive_options: list[ArchiveOptions] = []
    total_size_bytes = 0
    for document in documents:
        archive_option = await prepare_archive_files(
            user=user,
            document=document,
            with_original=True,
            with_signatures_preview=True,
            with_xml_original=archive_config.with_xml_original,
            with_xml_preview=archive_config.with_xml_preview,
            with_signatures=archive_config.with_signatures,
            signature_format=archive_config.signature_format,
            with_revoke_original=archive_config.with_xml_original,
            with_revoke_signatures=archive_config.with_signatures,
        )
        total_size_bytes += archive_option.archive_size
        archive_options.append(archive_option)

        if total_size_bytes >= max_archive_size_bytes:
            break

    # Generate an archive containing up to 50 files in the production environment.
    # If the combined size of files reaches the 100 MB limit, the archive may contain fewer files.
    files_in_archive_count = len(archive_options)

    output = await generate_documents_archive(
        archive_filename=archive_name,
        archives=archive_options,
        in_one_folder=archive_config.in_one_folder,
        with_instruction=archive_config.with_instruction,
        output_buffer=buffer,
        filenames_mode=archive_config.filenames_mode,
        filenames_max_length=archive_config.filenames_max_length,
    )

    output.buffer.seek(0)
    archive_size: int = output.buffer.getbuffer().nbytes  # in bytes

    async with services.db.acquire() as conn:
        async with conn.begin():
            archive_row = await insert_download_documents_archive(
                conn=conn,
                data={
                    'role_id': user.role_id,
                    'edrpou': user.company_edrpou,
                    'documents_ids': file_group,
                    'filename': archive_name,
                    'size': archive_size,
                },
            )
            archive_id = archive_row.id

            output.buffer.seek(0)
            content = output.buffer.read()
            await upload_archive_to_s3(
                archive_id=archive_id,
                content_stream=iter_bytes_by_chunks(content),
                company_edrpou=user.company_edrpou,
            )

    return GeneratedArchiveCtx(
        archive_id=archive_id,
        files_count=files_in_archive_count,
        documents=documents,
    )


async def generate_archive(
    role_id: str,
    document_ids: list[str],
    directory_ids: list[int],
    archive_name: str,
    archive_config: ArchiveConfig,
) -> str | None:
    """
    Iteratively download documents, put into archive and upload to S3.
    Do not keep in memory all data, process in stream mode.
    """
    async with services.db_readonly.acquire() as conn:
        user = await select_user(conn=conn, role_id=role_id)

        if not user:
            logger.warning(
                'Cant find user for archived-documents archive', extra={'role_id': 'role_id'}
            )
            return None

        documents_db = await select_documents_by_ids_with_company_info(conn, document_ids)
        if not documents_db:
            logger.info(
                'Cant find documents for documents archive',
                extra={'document_ids': document_ids},
            )
            return None

        document_versions = await get_latest_document_versions_available_for_company(
            conn,
            document_ids=document_ids,
            company_edrpou=user.company_edrpou,
        )
        document_versions_map = {
            document_version.document_id: document_version for document_version in document_versions
        }
        documents = [
            to_document(documents_db, version=document_versions_map.get(documents_db.id))
            for documents_db in documents_db
        ]

        # Directories from which we get documents for archive
        directories = await select_company_directories_by_ids(
            conn=conn,
            company_id=user.company_id,
            ids=directory_ids,
        )

        # Get additional directories that needed to prepare full path to
        # document from directories tree root
        additional_directories_ids = set()
        for _dir in directories:
            for parent_id in _dir.path:
                additional_directories_ids.add(parent_id)
        additional_directories_ids = additional_directories_ids - set(directory_ids)
        if additional_directories_ids:
            additional_directories = await select_company_directories_by_ids(
                conn=conn,
                company_id=user.company_id,
                ids=list(additional_directories_ids),
            )
            directories.extend(additional_directories)

    async with services.db.acquire() as conn:
        async with conn.begin():
            archive_row = await insert_download_documents_archive(
                conn=conn,
                data={
                    'role_id': user.role_id,
                    'edrpou': user.company_edrpou,
                    'documents_ids': document_ids,
                    'filename': archive_name,
                    'size': 0,
                },
            )
            archive_id = archive_row.id

            archive_stream = async_stream_zip(
                _iter_prepared_documents(
                    conn=conn,
                    archive_name=archive_name,
                    archive_config=archive_config,
                    documents=documents,
                    directories=directories,
                    user=user,
                )
            )
            archive_size = await upload_archive_to_s3(
                archive_id=archive_id,
                content_stream=archive_stream,
                company_edrpou=user.company_edrpou,
            )

            await update_download_documents_archive(
                conn=conn,
                archive_id=archive_id,
                data={'size': archive_size},
            )

    await _save_document_actions(
        documents=documents,
        archive_id=archive_id,
        user=user,
    )

    return archive_id


async def _iter_prepared_documents(
    conn: DBConnection,
    archive_name: str,
    archive_config: ArchiveConfig,
    documents: list[Document],
    directories: list[Directory],
    user: AuthUser | User,
) -> t.AsyncIterable[AsyncMemberFile]:
    """Iteratively produce files for archive creation"""

    assert user.company_id, 'User.company_id must be present'

    directory_by_id = {d.id: d for d in directories}
    document_directory_relations = await select_documents_directories(
        conn=conn,
        document_ids=[d.id_ for d in documents],
        company_id=user.company_id,
    )
    document_to_directory = {
        row.document_id: row.directory_id for row in document_directory_relations
    }
    preparer = MultiArchiveFilePathPreparer(
        archive_filename=archive_name,
        filenames_mode=archive_config.filenames_mode,
        filenames_max_length=archive_config.filenames_max_length,
    )
    for document in documents:
        directory_id = document_to_directory.get(document.id_)
        directory = directory_by_id.get(directory_id) if directory_id else None
        path_name = ''
        if directory:
            path_name = '/'.join(
                [directory_by_id[d_id].name for d_id in directory.path] + [directory.name]
            )
        extractor = ArchiveFilesExtractor(
            conn=conn,
            user=user,
            document=document,
            with_xml_original=archive_config.with_xml_original,
            with_xml_preview=archive_config.with_xml_preview,
            with_original=True,
            with_signatures=archive_config.with_signatures,
            with_signatures_preview=False,
            signature_format=archive_config.signature_format,
            with_revoke_original=archive_config.with_xml_original,
            with_revoke_signatures=archive_config.with_signatures,
        )
        archive_options = await extractor.extract_simple()
        directory_name = '/'.join(filter(None, [path_name, archive_options.document_filename_base]))
        directory_name = preparer.prepare_directory_filename(directory_name)

        preparer.reset_directory_files()
        for file_ in archive_options.files:
            filename = preparer.prepare_filename(
                name=file_.file_name,
                directory=directory_name,
            )
            # yields tuple described in AsyncMemberFile
            yield (
                f'{directory_name}/{filename}',
                local_now(),
                S_IFREG | 0o600,
                ZIP_32,
                iter_bytes_by_chunks(file_.content),
            )


async def send_documents_archive_email(
    role_id: str,
    archive_name: str,
    request_data: DataDict,
    archives_ids: list[str],
) -> None:
    async with services.db.acquire() as conn:
        user = await select_user(conn=conn, role_id=role_id)
        if not user:
            logger.warning('Cant find user for documents archive', {'role_id': role_id})
            return

        company: DBRow = await select_company_by_role_id(conn=conn, role_id=role_id)  # type: ignore[assignment]
        company_has_pro = await is_company_has_pro_or_higher_rate(conn, company.id)

    request_data.update({'arch_name': archive_name.replace('.zip', '')})
    await send_archive_link(
        arch_ids=archives_ids,
        user=user,
        company=company,
        company_has_pro=company_has_pro,
        request_data=request_data,
    )


async def create_archive(
    role_id: str,
    archive_name: str,
    archive_config: ArchiveConfig,
    file_group: list[str],
) -> GeneratedArchiveCtx | None:
    """
    Download documents, create archive, upload to S3
    """
    async with services.db.acquire() as conn:
        user = await select_user(conn=conn, role_id=role_id)

    if not user:
        logger.warning('Cant find user for documents archive', {'role_id': 'role_id'})
        return None

    ctx = await download_and_generate_archive(
        user=user,
        file_group=list(file_group),
        archive_name=archive_name,
        archive_config=archive_config,
    )
    if ctx is not None:
        await _save_document_actions(documents=ctx.documents, archive_id=ctx.archive_id, user=user)

    return ctx


async def _save_document_actions(
    documents: list[Document],
    archive_id: str,
    user: User,
) -> None:
    """
    Add document actions after create milti-archive with documents
    """
    try:
        await document_actions.add_document_actions(
            document_actions=[
                document_actions.DocumentAction(
                    action=document_actions.Action.document_download_archived,
                    document_id=document.id_,
                    document_edrpou_owner=document.owned_by_edrpou,
                    document_title=document.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                    extra={'archive_id': archive_id},
                )
                for document in documents
            ]
        )
    except Exception:
        logger.info(
            'Non-critical error during archive creation',
            exc_info=True,
            extra={'archive_id': archive_id},
        )
