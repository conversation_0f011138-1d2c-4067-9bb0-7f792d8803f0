"""remove views

Revision ID: c260dbe1a195
Revises: da42e3070ef3
Create Date: 2024-10-02 22:57:18.957364

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c260dbe1a195'
down_revision = 'f9b84d586d3f'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("DROP VIEW IF EXISTS document_templates")
    op.execute("DROP VIEW IF EXISTS document_automation")
    op.execute("DROP VIEW IF EXISTS assigned_document_templates")

    op.execute(
       """
       UPDATE companies_config
       SET admin_config = admin_config - 'max_templates_count'
       WHERE admin_config ? 'max_templates_count'
       """
    )
    op.execute(
       """
       UPDATE billing_companies
       SET config = config - 'max_templates_count'
       WHERE config ? 'max_templates_count'
       """
    )


def downgrade():
    pass
