import asyncio
import logging
from http import HTTPStatus

from aiohttp import ClientTimeout

# TODO: Update google-auth library once new version is released
from google.auth.transport._aiohttp_requests import Request as GoogleRequest
from google.oauth2 import _service_account_async as service_account_async

from app.config.schemas import FCMConfig
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n.types import LazyI18nString
from app.lib import types as core_types
from app.lib.datetime_utils import naive_local_now
from app.mobile.fcm import utils
from app.mobile.notifications import db as mobile_db
from app.mobile.notifications.types import MobileNotificationStatus
from app.services import services

logger = logging.getLogger(__name__)

SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']
FCM_SEND_MESSAGE_URL = 'https://fcm.googleapis.com/v1/projects/{project_id}/messages:send'
FCM_REQUEST_TIMEOUT = 5


class FCMClient:
    """
    Firebase Cloud Messaging client

    Used mainly for sending push notifications to the mobile application.
    """

    def __init__(self, *, config: FCMConfig | None) -> None:
        if not config or not config.is_enabled:
            return

        self.send_message_url = FCM_SEND_MESSAGE_URL.format(project_id=config.project_id)
        self.credentials = service_account_async.Credentials.from_service_account_file(
            filename=config.config_path, scopes=SCOPES
        )

    @property
    async def access_token(self) -> str:
        """
        Generate access token for request
        """
        if not self.credentials.valid or (
            self.credentials.expiry and self.credentials.expiry < naive_local_now()
        ):
            # Refresh access token
            google_request = GoogleRequest()
            await self.credentials.refresh(google_request)
            await google_request.session.close()

        return self.credentials.token

    @property
    async def headers(self) -> core_types.DataDict:
        """
        Build headers for request
        """
        return {
            'Authorization': f'Bearer {await self.access_token}',
            'Content-Type': 'application/json; UTF-8',
        }

    async def __send_message(self, url: str, body: core_types.DataDict) -> None:
        headers = await self.headers
        async with services.http_client.post(
            url=url,
            json=body,
            headers=headers,
            timeout=ClientTimeout(total=FCM_REQUEST_TIMEOUT),
        ) as response:
            data = await response.json()
            if response.status != HTTPStatus.OK:
                logger.info(
                    'Request ended with an error',
                    extra={'status_code': response.status, 'response_message': data.get('message')},
                )
            else:
                logger.info(
                    'Push notification sent successfully',
                    extra={'message_id': data['name'].split('/')[-1]},
                )

    async def send_push_notification(
        self,
        *,
        notification_id: str,
        firebase_id: str,
        title: str,
        description: str,
        payload: core_types.DataDict | None = None,
    ) -> None:
        """
        Send single mobile push notification using Firebase cloud messaging client

        Warning - direct usage is not recommended because
            only single mobile app instance would receive push notification.
        Consider using `send_push_notifications` method below.
        """

        if not getattr(self, 'credentials', None):
            logger.info('Firebase client is not initialised. Skip sending notification')
            return

        if not get_flag(flag=FeatureFlags.ENABLE_MOBILE_PUSH_NOTIFICATIONS):
            logger.info('Push notifications are disabled')
            return

        body = utils.build_request_body(
            notification_id=notification_id,
            title=title,
            description=description,
            firebase_id=firebase_id,
            payload=payload,
        )
        if not body:
            return

        logger.info(
            'Try to send push notification',
            extra={
                'title': title,
                'description': description,
                'firebase_id': firebase_id,
                'payload': payload,
            },
        )
        await self.__send_message(url=self.send_message_url, body=body)

    async def send_push_notifications(
        self,
        *,
        role_id: str,
        firebase_ids: list[str],
        title: LazyI18nString | str,
        description: LazyI18nString | str,
        payload: core_types.DataDict | None = None,
    ) -> None:
        """
        Send multiple mobile push notifications using Firebase cloud messaging client
        """

        notification_title = title.value if isinstance(title, LazyI18nString) else title
        notification_description = (
            description.value if isinstance(description, LazyI18nString) else description
        )

        async with services.db.acquire() as conn:
            notification = await mobile_db.insert_mobile_notification(
                conn=conn,
                role_id=role_id,
                title=notification_title,
                description=notification_description,
                payload=payload,
                status=MobileNotificationStatus.new,
            )

        await asyncio.gather(
            *[
                self.send_push_notification(
                    notification_id=notification.id,
                    firebase_id=firebase_id,
                    title=notification_title,
                    description=notification_description,
                    payload=payload,
                )
                # Remove duplicate firebase_ids in order to
                # prevent multiple push notifications
                for firebase_id in set(firebase_ids)
            ]
        )
