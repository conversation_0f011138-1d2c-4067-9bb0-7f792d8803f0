#!/usr/bin/env bash

# This tells bash that it should exit the script if any statement returns a
# non-true return value. The benefit of using -e is that it prevents errors
# snowballing into serious issues when they could have been caught earlier.
set -euf -o pipefail

source ./scripts/init-tests.sh

# Init test env with 1 parallel workers
init 1

pytest \
-m slow \
--maxfail=1 \
./api ./app ./cron ./worker ./indexator
