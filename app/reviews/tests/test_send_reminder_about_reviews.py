import pytest
from yarl import URL

from app.document_versions.tests.utils import prepare_document_version
from app.flags import FeatureFlags
from app.groups.db import insert_group, insert_group_member
from app.lib.enums import DocumentStatus
from app.reviews.emailing import PendingReviewReminder
from app.reviews.enums import ReviewType
from app.reviews.tests.utils import (
    prepare_review_db,
    prepare_review_request_db,
    prepare_review_settings_db,
    prepare_review_status_db,
)
from app.services import services
from app.tests.common import datetime_test, prepare_client, prepare_document_data, prepare_user_data

TEST_EMAIL_0 = '<EMAIL>'
TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_EMAIL_3 = '<EMAIL>'
TEST_EMAIL_4 = '<EMAIL>'
TEST_EMAIL_5 = '<EMAIL>'

ROLE_ID_0 = '30000000-0000-0000-0000-000000000000'
ROLE_ID_1 = '30000000-0000-0000-0000-000000000001'
ROLE_ID_2 = '30000000-0000-0000-0000-000000000002'
ROLE_ID_3 = '30000000-0000-0000-0000-000000000003'
ROLE_ID_4 = '30000000-0000-0000-0000-000000000004'
ROLE_ID_5 = '30000000-0000-0000-0000-000000000005'

GROUP_ID_1 = '20000000-0000-0000-0000-000000000000'
GROUP_ID_2 = '20000000-0000-0000-0000-000000000001'
GROUP_ID_3 = '20000000-0000-0000-0000-000000000002'


DOCUMENT_ID_0 = '10000000-0000-0000-0000-000000000000'
DOCUMENT_ID_1 = '10000000-0000-0000-0000-000000000001'
DOCUMENT_ID_2 = '10000000-0000-0000-0000-000000000002'
DOCUMENT_ID_3 = '10000000-0000-0000-0000-000000000003'
DOCUMENT_ID_4 = '10000000-0000-0000-0000-000000000004'
DOCUMENT_ID_5 = '10000000-0000-0000-0000-000000000005'
DOCUMENT_ID_6 = '10000000-0000-0000-0000-000000000006'
DOCUMENT_ID_7 = '10000000-0000-0000-0000-000000000007'
DOCUMENT_ID_8 = '10000000-0000-0000-0000-000000000008'
DOCUMENT_ID_9 = '10000000-0000-0000-0000-000000000009'
DOCUMENT_ID_10 = '10000000-0000-0000-0000-000000000010'
DOCUMENT_ID_11 = '10000000-0000-0000-0000-000000000011'
DOCUMENT_ID_12 = '10000000-0000-0000-0000-000000000012'
DOCUMENT_ID_13 = '10000000-0000-0000-0000-000000000013'
DOCUMENT_ID_14 = '10000000-0000-0000-0000-000000000014'
DOCUMENT_ID_15 = '10000000-0000-0000-0000-000000000015'


@pytest.mark.parametrize(
    'now_datetime, expected_date_from, expected_date_to',
    [
        pytest.param(
            datetime_test('2020-06-26 12:00:00'),  # Fri
            datetime_test('2020-06-19 00:00:00'),  # -7 days
            datetime_test('2020-06-26 00:00:00'),
            id='friday',
        ),
        pytest.param(
            datetime_test('2020-06-23 12:00:00'),  # Tue
            datetime_test('2020-06-16 00:00:00'),
            datetime_test('2020-06-23 00:00:00'),
            id='tuesday',
        ),
        pytest.param(
            datetime_test('2020-06-24 12:00:00'),  # Wed
            datetime_test('2020-06-17 00:00:00'),
            datetime_test('2020-06-24 00:00:00'),
            id='any_other_day',
        ),
    ],
)
def test_calc_date_ranges(now_datetime, expected_date_from, expected_date_to):
    date_from, date_to = PendingReviewReminder.calc_date_ranges(now_datetime)
    assert date_from == expected_date_from
    assert date_to == expected_date_to


async def test_pending_review_start_sending(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_0,
        role_id=ROLE_ID_0,
    )

    date_to = datetime_test('2020-06-25 00:00:00')
    date_from = datetime_test('2020-06-20 00:00:00')

    document1 = await prepare_document_data(app=app, owner=user)

    await prepare_user_data(app, email=TEST_EMAIL_1, role_id=ROLE_ID_1)
    await prepare_user_data(app, email=TEST_EMAIL_2, role_id=ROLE_ID_2)
    await prepare_user_data(app, email=TEST_EMAIL_3, role_id=ROLE_ID_3)
    await prepare_user_data(app, email=TEST_EMAIL_4, role_id=ROLE_ID_4)
    await prepare_user_data(app, email=TEST_EMAIL_5, role_id=ROLE_ID_5)

    async with services.db.acquire() as conn:

        async def add_group(*, id: str, name: str):
            await insert_group(
                conn=conn,
                name=name,
                id=id,
                created_by=user.role_id,
                company_id=user.company_id,
            )

        async def add_group_member(*, group_id: str, role_id: str):
            await insert_group_member(conn=conn, group_id=group_id, role_id=role_id)

        await add_group(id=GROUP_ID_1, name='group1')
        await add_group(id=GROUP_ID_2, name='group2')
        await add_group(id=GROUP_ID_3, name='group3')

        await add_group_member(group_id=GROUP_ID_1, role_id=ROLE_ID_3)
        await add_group_member(group_id=GROUP_ID_1, role_id=ROLE_ID_4)
        await add_group_member(group_id=GROUP_ID_2, role_id=ROLE_ID_1)
        await add_group_member(group_id=GROUP_ID_3, role_id=ROLE_ID_5)

        # +1 in date range
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=ROLE_ID_0,
            to_role_id=ROLE_ID_1,
            date_created='2020-06-21',
        )
        # +1 in date range (all members of the group will be selected)
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=ROLE_ID_0,
            to_group_id=GROUP_ID_1,  # members: ROLE_ID_3, ROLE_ID_4
            date_created='2020-06-21',
        )
        # +1 in date range (a duplicated role will be ignored)
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=ROLE_ID_0,
            to_group_id=GROUP_ID_2,  # members: ROLE_ID_1
            date_created='2020-06-21',
        )
        # request to a role is out of date range
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=ROLE_ID_0,
            to_role_id=ROLE_ID_2,
            date_created='2020-06-15',
        )
        # request to a group is out of date range
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=ROLE_ID_0,
            to_group_id=GROUP_ID_3,  # members: ROLE_ID_5
            date_created='2020-06-15',
        )

    async with services.db.acquire() as conn:
        generator = await PendingReviewReminder.get_roles_with_reviews(
            conn=conn,
            date_to=date_to,
            date_from=date_from,
        )
        rows = []
        async for chunk in generator:
            rows.extend(chunk)

    result_role_ids = [row.role_id for row in rows]
    expected_role_ids = [ROLE_ID_1, ROLE_ID_3, ROLE_ID_4]
    assert len(result_role_ids) == len(expected_role_ids)
    assert set(result_role_ids) == set(expected_role_ids)


async def test_select_documents_with_pending_requests(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_0,
        role_id=ROLE_ID_0,
    )

    target = await prepare_user_data(app, email=TEST_EMAIL_1, role_id=ROLE_ID_1)  # target role
    await prepare_user_data(app, email=TEST_EMAIL_2, role_id=ROLE_ID_2)
    await prepare_user_data(app, email=TEST_EMAIL_3, role_id=ROLE_ID_3)

    date_to = datetime_test('2020-06-25 00:00:00')
    date_from = datetime_test('2020-06-20 00:00:00')

    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_0)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_1)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_2)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_3)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_4)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_5)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_6)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_7)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_8)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_9)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_10)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_11)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_12)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_13)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_14)
    await prepare_document_data(
        app=app, owner=user, id=DOCUMENT_ID_15, status_id=DocumentStatus.reject.value
    )

    async with services.db.acquire() as conn:

        async def add_group(*, id: str, name: str):
            await insert_group(
                conn=conn,
                name=name,
                id=id,
                created_by=user.role_id,
                company_id=user.company_id,
            )

        async def add_group_member(*, group_id: str, role_id: str):
            await insert_group_member(conn=conn, group_id=group_id, role_id=role_id)

        async def _add_request(
            *,
            document_id: str,
            from_role_id: str = ROLE_ID_0,
            to_role_id: str | None = None,
            to_group_id: str | None = None,
            date_created: str = '2020-06-21',
            order: int | None = None,
        ):
            if not to_group_id and not to_role_id:
                to_role_id = target.role_id

            await prepare_review_request_db(
                conn=conn,
                document_id=document_id,
                from_role_id=from_role_id,
                to_role_id=to_role_id,
                to_group_id=to_group_id,
                date_created=date_created,
                order=order,
            )

        async def _add_version(*, document_id: str):
            return await prepare_document_version(
                document_id=document_id,
                role_id=user.role_id,
                company_edrpou=user.company_edrpou,
                company_id=user.company_id,
            )

        async def _add_review(
            *,
            document_id: str,
            role_id: str = ROLE_ID_1,
            type_: ReviewType = ReviewType.approve,
            document_version_id: str | None = None,
            group_id: str | None = None,
        ):
            return await prepare_review_db(
                conn=conn,
                document_id=document_id,
                role_id=role_id,
                type_=type_,
                document_version_id=document_version_id,
                group_id=group_id,
            )

        async def _update_status(*, document_id: str):
            await prepare_review_status_db(conn, user, document_id=document_id)

        async def _add_settings(*, document_id: str, is_ordered: bool):
            await prepare_review_settings_db(
                conn=conn,
                document_id=document_id,
                user=user,
                is_parallel=not is_ordered,
            )

        # target user is a member of the group
        await add_group(id=GROUP_ID_1, name='group1')
        await add_group_member(group_id=GROUP_ID_1, role_id=target.role_id)
        await add_group_member(group_id=GROUP_ID_1, role_id=ROLE_ID_2)

        # target user is not a member of the group
        await add_group(id=GROUP_ID_2, name='group2')
        await add_group_member(group_id=GROUP_ID_2, role_id=ROLE_ID_3)

        # group without members
        await add_group(id=GROUP_ID_3, name='group3')

        expected = []

        # 0. - a document is not requested for review by role
        # not expected in the result

        # 1. - the document is not reviewed by the target role
        await _add_request(document_id=DOCUMENT_ID_1)
        await _update_status(document_id=DOCUMENT_ID_1)
        expected.append(DOCUMENT_ID_1)

        # 2. - the document has several versions with pending review requests
        await _add_request(document_id=DOCUMENT_ID_2)
        version1 = await _add_version(document_id=DOCUMENT_ID_2)
        await _add_review(document_id=DOCUMENT_ID_2, document_version_id=version1.id)
        await _update_status(document_id=DOCUMENT_ID_2)
        # the second version of the document keeps the existing review request state
        await _add_version(document_id=DOCUMENT_ID_2)
        await _update_status(document_id=DOCUMENT_ID_2)

        # 3. - the document has several versions with finished review process
        await _add_request(document_id=DOCUMENT_ID_3)
        await _add_version(document_id=DOCUMENT_ID_3)
        await _update_status(document_id=DOCUMENT_ID_3)
        version2 = await _add_version(document_id=DOCUMENT_ID_3)
        await _add_review(document_id=DOCUMENT_ID_3, document_version_id=version2.id)
        await _update_status(document_id=DOCUMENT_ID_3)  # the last status is finished
        # not expected in the result

        # 4. - user have request out of date range
        await _add_request(document_id=DOCUMENT_ID_4, date_created='2020-06-15')
        await _update_status(document_id=DOCUMENT_ID_4)
        # not expected in the result

        # 5. - group have request out of date range
        await _add_request(
            document_id=DOCUMENT_ID_5,
            date_created='2020-06-15',
            to_group_id=GROUP_ID_1,
        )
        await _update_status(document_id=DOCUMENT_ID_5)
        # not expected in the result

        # 6. - group have request in date range
        await _add_request(document_id=DOCUMENT_ID_6, to_group_id=GROUP_ID_1)
        await _update_status(document_id=DOCUMENT_ID_6)
        expected.append(DOCUMENT_ID_6)

        # 7. - review request is reviewed by target role
        await _add_request(document_id=DOCUMENT_ID_7, to_role_id=target.role_id)
        await _add_request(document_id=DOCUMENT_ID_7, to_role_id=ROLE_ID_3)
        await _add_review(document_id=DOCUMENT_ID_7, role_id=target.role_id)
        await _update_status(document_id=DOCUMENT_ID_7)
        # not expected in the result

        # 8. - 1 of 2 review requests is reviewed, not target role
        await _add_request(document_id=DOCUMENT_ID_8, to_role_id=target.role_id)
        await _add_request(document_id=DOCUMENT_ID_8, to_role_id=ROLE_ID_3)
        await _add_review(document_id=DOCUMENT_ID_8, role_id=ROLE_ID_3)
        await _update_status(document_id=DOCUMENT_ID_8)
        expected.append(DOCUMENT_ID_8)

        # 9. - has review request as a group member and as a role
        await _add_request(document_id=DOCUMENT_ID_9, to_role_id=target.role_id)
        await _add_request(document_id=DOCUMENT_ID_9, to_group_id=GROUP_ID_1)
        await _update_status(document_id=DOCUMENT_ID_9)
        expected.append(DOCUMENT_ID_9)

        # 10. - target user is not the next signer
        await _add_settings(document_id=DOCUMENT_ID_10, is_ordered=True)
        await _add_request(document_id=DOCUMENT_ID_10, to_role_id=ROLE_ID_2, order=0)
        await _add_request(document_id=DOCUMENT_ID_10, to_role_id=target.role_id, order=1)
        await _update_status(document_id=DOCUMENT_ID_10)
        # not expected in the result

        # 11. - target user is the next signer
        await _add_settings(document_id=DOCUMENT_ID_11, is_ordered=True)
        await _add_request(document_id=DOCUMENT_ID_11, to_role_id=ROLE_ID_2, order=0)
        await _add_request(document_id=DOCUMENT_ID_11, to_role_id=target.role_id, order=1)
        await _add_review(document_id=DOCUMENT_ID_11, role_id=ROLE_ID_2)
        await _update_status(document_id=DOCUMENT_ID_11)
        expected.append(DOCUMENT_ID_11)

        # 12. — overall review process is rejected
        await _add_request(document_id=DOCUMENT_ID_12, to_role_id=target.role_id)
        await _add_request(document_id=DOCUMENT_ID_12, to_role_id=ROLE_ID_3)
        await _add_review(document_id=DOCUMENT_ID_12, role_id=ROLE_ID_3, type_=ReviewType.reject)
        await _update_status(document_id=DOCUMENT_ID_12)
        # not expected in the result

        # 13 - next signer is a group with a target user
        await _add_settings(document_id=DOCUMENT_ID_13, is_ordered=True)
        await _add_request(document_id=DOCUMENT_ID_13, to_role_id=ROLE_ID_2, order=0)
        await _add_request(document_id=DOCUMENT_ID_13, to_group_id=GROUP_ID_1, order=1)
        await _add_review(document_id=DOCUMENT_ID_13, role_id=ROLE_ID_2)
        await _update_status(document_id=DOCUMENT_ID_13)
        expected.append(DOCUMENT_ID_13)

        # 14 - someone from the group has reviewed the document
        await _add_request(document_id=DOCUMENT_ID_14, to_group_id=GROUP_ID_1)
        await _add_request(document_id=DOCUMENT_ID_14, to_role_id=ROLE_ID_3)
        await _add_review(document_id=DOCUMENT_ID_14, role_id=ROLE_ID_2, group_id=GROUP_ID_1)
        await _update_status(document_id=DOCUMENT_ID_14)
        # not expected in the result

        # 15 - document was rejected before review process finished
        await _add_request(document_id=DOCUMENT_ID_15, to_role_id=target.role_id)
        await _update_status(document_id=DOCUMENT_ID_15)
        # not expected in the result

    reminder = PendingReviewReminder()
    async with services.db.acquire() as conn:
        documents_ids = await reminder.select_documents_with_pending_requests(
            conn=conn,
            role_id=target.role_id,
            company_edrpou=target.company_edrpou,
            date_from=date_from,
            date_to=date_to,
            limit=10000,
        )

    assert set(documents_ids) == set(expected)
    assert len(documents_ids) == len(expected)


@pytest.mark.parametrize(
    'user_data, expected_emails',
    [
        pytest.param({'can_receive_notifications': True}, 1, id='enabled_notification'),
        pytest.param({'can_receive_notifications': False}, 0, id='disabled_notification'),
    ],
)
async def test_pending_review_send_reminder(
    aiohttp_client,
    mailbox,
    send_email_mock,
    test_flags,
    user_data: dict,
    expected_emails: int,
):
    """
    Test that we can build email to target user with a proper rendering context
    """

    test_flags[FeatureFlags.ENABLE_REVIEW_REMINDER.value] = True

    date_to = datetime_test('2020-06-25 00:00:00')
    date_from = datetime_test('2020-06-20 00:00:00')

    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_0,
        role_id=ROLE_ID_0,
        company_name='Testova Compan',
        company_edrpou='12345678',
    )

    target = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        first_name='First',
        last_name='Last',
        company_edrpou='12345678',
        **user_data,
    )

    # Prepare 5 documents with 5 review requests to target user
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_0)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_1)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_2)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_3)
    await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID_4)

    async with services.db.acquire() as conn:

        async def _add_request(*, document_id: str):
            # create simplest form of pending review request
            await prepare_review_request_db(
                conn=conn,
                document_id=document_id,
                from_role_id=user.role_id,
                to_role_id=target.role_id,
                to_group_id=None,
                date_created='2020-06-21',
            )
            await prepare_review_status_db(
                conn=conn,
                user=user,
                document_id=document_id,
            )

        await _add_request(document_id=DOCUMENT_ID_0)
        await _add_request(document_id=DOCUMENT_ID_1)
        await _add_request(document_id=DOCUMENT_ID_2)
        await _add_request(document_id=DOCUMENT_ID_3)
        await _add_request(document_id=DOCUMENT_ID_4)

    reminder = PendingReviewReminder()
    async with services.db.acquire() as conn:
        await reminder.send_reminder(
            conn=conn,
            role_id=target.role_id,
            date_from=date_from,
            date_to=date_to,
        )

    if expected_emails == 0:
        assert len(mailbox) == 0
        return

    assert len(mailbox) == 1
    email = mailbox[0]
    assert email['To'] == target.email
    assert email['Subject'] == 'Не забудьте погодити документи в компанії Testova Compan, 12345678'

    assert send_email_mock.call_count == 1
    actual_context = send_email_mock.call_args.kwargs['context']
    assert actual_context['first_name'] == 'First'
    assert actual_context['company_label'] == 'Testova Compan, 12345678'

    main_link = URL(actual_context['main_link'])
    assert main_link.path == '/app/documents'
    assert main_link.query.get('review_folder') == 'wait_my_review'
    assert set(main_link.query.getall('folder_id')) == {'6007', '6008'}
    assert main_link.query.get('cid') == target.company_id

    assert actual_context['has_more_documents'] is True

    assert len(actual_context['documents']) == 3
    for document in actual_context['documents']:
        assert document['title'] == 'Test Document'
        assert document['recipient_label'] == ''
        assert document['number'] == 'Test Number'
        link = URL(document['link'])
        assert link.path.startswith('/app/documents/')
        assert link.query.get('cid') == target.company_id


async def test_pending_review_whole_process(aiohttp_client, mailbox, test_flags):
    """
    Test happy path of the whole process
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        can_receive_notifications=True,
    )

    test_flags[FeatureFlags.ENABLE_REVIEW_REMINDER.value] = True

    target1 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_name='Testova Compan',
        company_edrpou='00000001',
        can_receive_notifications=True,
    )
    target2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_name='Insha Compan',
        company_edrpou='00000002',
        can_receive_notifications=True,
    )

    document1 = await prepare_document_data(app=app, owner=target1)
    document2 = await prepare_document_data(app=app, owner=target2)

    async with services.db.acquire() as conn:
        await prepare_review_request_db(
            conn=conn,
            document_id=document1.id,
            from_role_id=target1.role_id,
            to_role_id=target1.role_id,
            date_created='2020-06-24',
        )
        await prepare_review_status_db(
            conn=conn,
            user=target1,
            document_id=document1.id,
        )

        await prepare_review_request_db(
            conn=conn,
            document_id=document2.id,
            from_role_id=target2.role_id,
            to_role_id=target2.role_id,
            date_created='2020-06-24',
        )
        await prepare_review_status_db(
            conn=conn,
            user=target2,
            document_id=document2.id,
        )

        reminder = PendingReviewReminder()
        await reminder.start_sending(
            conn=conn,
            # 2020-06-26 is friday. range: [2020-06-23, 2020-06-26)
            now_datetime=datetime_test('2020-06-26 12:00:00'),
        )

    assert len(mailbox) == 2
    assert {email['To'] for email in mailbox} == {target1.email, target2.email}
    assert {email['Subject'] for email in mailbox} == {
        'Не забудьте погодити документи в компанії Testova Compan, 00000001',
        'Не забудьте погодити документи в компанії Insha Compan, 00000002',
    }
