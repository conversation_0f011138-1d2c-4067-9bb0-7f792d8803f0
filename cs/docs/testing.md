# Тестування фронтенд коду

Стислий опис процесу тестування фронтенд коду в проекті.

## Інструменти та бібліотеки

- Mocha
- Chai
- jsdom (для емуляції DOM)

## Структура тестів

Тести розміщуються в окремих файлах з розширенням `.ts` у відповідних директоріях. Рекомендується
використовувати структуру, яка відображає структуру коду:

- `src/<some-module-path>/tests/unit/[filename].ts`
- `src/services/tests/unit/[filename].test.ts`

## Що покриваємо тестами

Покриваємо тести лише критичні частини коду, які можуть зламатися або потребують перевірки.

Тести повинні бути зрозумілими та читабельними, з чітким описом того, що вони перевіряють.

- Утиліти (наприклад, функції для роботи з формами, обробки даних).
- Бізнес-логіку (наприклад, функції обробки даних, фільтрації, сортування).
- Сервіси (наприклад, роботу з API, обробку помилок).

## Що не покриваємо тестами

- UI-компоненти (наприклад, форми, таблиці, попапи, складні кнопки).
- enum
- constants
- стилі (CSS)
- Інтеграції між модулями (наприклад, як один модуль використовує інший).

## Приклади тестів

```typescript
import { expect } from 'chai';
import { composePayloadFromDocumentsUploadForm } from '../../uploadUtils';
describe('cs/components/uploader/uploadUtils.ts', () => {
  describe('#composePayloadFromDocumentsUploadForm', () => {
    it('should send template_id param if it exists', () => {
      expect(
        composePayloadFromDocumentsUploadForm(getFormData({ templateId: '123' })),
      ).to.deep.include({ template_id: '123' });
      expect(
        composePayloadFromDocumentsUploadForm(getFormData({ templateId: null })),
      ).to.not.has.property('template_id');
    });
  });
});
```

Якщо необхідно пропустити тест, то можна використовувати `xdescribe` або `xit`:

## Запуск тестів

```bash
yarn test
```

## Best Practice

- Пиши тести лише для логіки, яка може зламатися.

- Пиши короткі та зрозумілі тести.

- Використовуй describe/it для групування та читабельності.

- Слідкуй за покриттям критичних моментів, але не женися за 100% покриттям.

## TODO

- Запланувати й оновити бібліотеки тестування (28.07.2025).
