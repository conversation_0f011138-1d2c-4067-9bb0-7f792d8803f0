import logging
from http import HTTPStatus

from aiohttp import web

from app.auth.enums import AuthF<PERSON>, AuthFlow
from app.auth.phone_auth import utils
from app.auth.phone_auth.validators import validate_process_phone_auth, validate_send_phone_auth
from app.auth.types import AuthUserResponse
from app.auth.utils import login_user, login_user_registration
from app.lib.urls import build_url
from app.registration.utils import send_registration_jobs
from app.services import services

logger = logging.getLogger(__name__)


async def send_phone_auth_code(request: web.Request) -> web.Response:
    """
    Send one time password (OTP) to the phone number for the user to log in or register
    with it.

    It can be also used for resending the OTP code.
    """

    async with services.db.acquire() as conn:
        data = await validate_send_phone_auth(conn, request)

    await utils.send_phone_auth_code(phone=data.phone)
    return web.json_response(status=HTTPStatus.OK)


async def process_phone_auth_code(request: web.Request) -> web.Response:
    """
    Verify phone OTP code and register a new user if it is not registered yet. It can be
    used for both login and registration by our Vchasno products
    """

    async with services.db.acquire() as conn:
        data = await validate_process_phone_auth(conn, request)

        output = await utils.process_phone_auth(
            conn=conn,
            auth_phone=data.phone,
            code=data.code,
        )

        if output.is_created:
            await login_user_registration(
                request=request,
                conn=conn,
                user=output.user,
                first_factor=AuthFactor.phone,
            )
            await send_registration_jobs(
                user=output.user,
                cookies=request.cookies,
            )

            next_url = build_url('app', absolute=False, tail='')
            response = AuthUserResponse(
                email=output.user.email,
                flow=AuthFlow.registration,
                next_url=next_url,
                is_2fa_enabled=False,
            )
        else:
            response = await login_user(
                request=request,
                conn=conn,
                user=output.user,
                first_factor=AuthFactor.phone,
                ask_second_factor=True,
                remember=False,  # not supported for phone auth yet, to be implemented later
            )

    return web.json_response(response.to_dict(), status=HTTPStatus.OK)
