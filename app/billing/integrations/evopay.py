from __future__ import annotations

import base64
import hmac
import json
import logging
import uuid
from hashlib import sha256
from time import time
from typing import NamedTuple

from aiohttp import ClientError

from api.errors import Code, DoesNotExist, EvoPayRequestException, InvalidRequest, Object, Timeout
from app.i18n import _
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


ENCODING = 'UTF-8'
APPLICATION_JOSE_JSON_CONTENT_TYPE = 'application/jose+json'
EVOPAY_PAYMENT_ALREADY_PROCEED_STATUS_CODE = 110
EVOPAY_RESULT_URL = '/app/settings/companies/{role_id}/rates?bill_id={bill_id}'
EVOPAY_WEBHOOK_URL = '/api/evopay/webhook'


class EvopayClient:
    def __init__(self) -> None:
        config = services.config.evopay
        if not config:
            raise DoesNotExist(
                Object.config,
                reason=_('Конфіг для оплати карткою не знайдено.'),
            )

        self.base_url = config.host
        self.public_key = config.public_key
        self.private_key = config.private_key.encode(ENCODING)
        self.client = services.http_client

    @staticmethod
    def urlsafe_b64encode(s: bytes) -> bytes:
        return base64.urlsafe_b64encode(s).rstrip(b'=')

    def _build_signature(self, payload: str) -> str:
        """Builds a signature for evopay request"""

        protected = self.urlsafe_b64encode(
            json.dumps(
                {
                    'alg': 'HS256',
                    'crit': ['jti', 'exp'],
                    'kid': self.public_key,
                    'jti': uuid.uuid4().hex,
                    'exp': int(time()) + 3600,
                },
            ).encode(ENCODING),
        )

        signature = self.urlsafe_b64encode(
            hmac.new(
                self.private_key,
                protected + b'.' + base64.urlsafe_b64encode(payload.encode(ENCODING)).rstrip(b'='),
                sha256,
            ).digest(),
        )

        return (protected + b'..' + signature).decode(ENCODING)

    def get_body_signature(
        self,
        data: bytes | None,
    ) -> tuple[str, bytes]:
        """
        Generates tuple of signature, content_type, payload for evopay request
        """

        payload = b'' if data is None else data
        signature = self._build_signature(payload.decode())

        jws_header, _, jws_signature = signature.split('.')

        payload = json.dumps(
            {
                'payload': self.urlsafe_b64encode(payload).decode(),
                'protected': jws_header,
                'signature': jws_signature,
            },
        ).encode(ENCODING)

        return (
            signature,
            payload,
        )

    async def _request(
        self,
        url: str,
        data: bytes,
        signature: str,
    ) -> DataDict:
        try:
            async with self.client.post(
                url=url,
                data=data,
                headers={
                    'X-jws-signature': signature,
                    'X-signature-version': '3',
                    'Content-Type': APPLICATION_JOSE_JSON_CONTENT_TYPE,
                },
            ) as response:
                resp = await response.json()
                return resp
        except TimeoutError:
            logger.error(
                'Timeout during send request to EvoPay',
            )
            raise Timeout()
        except ClientError:
            logger.exception(
                msg='Exception during send request to EvoPay',
                extra={
                    'status_code': response.status,
                    'body': response.text,
                },
            )
            raise EvoPayRequestException(
                code=Code.external_resource_error,
                reason=_('Сталася помилка під час відправлення запиту в EvoPay'),
            )

    async def initiate_payment_process(self, order: EvopayCheckoutOrder) -> str:
        """Returns order object and initiates payment process"""

        data_raw = json.dumps(order.to_evopay_request(), separators=(',', ':'))

        signature, data = self.get_body_signature(data_raw.encode(ENCODING))

        resp = await self._request(
            url=f'{self.base_url}/api/order/v3/checkout/create',
            data=data,
            signature=signature,
        )

        status_code = resp.get('code')
        if status_code and status_code == EVOPAY_PAYMENT_ALREADY_PROCEED_STATUS_CODE:
            raise InvalidRequest(
                obj=Object.bill,
                reason=_('По цьому рахунку вже була проведена успішна оплата.'),
            )

        if not resp.get('data', {}).get('location'):
            logger.warning('Unexpected evopay response', extra={'resp_data': str(resp)})

        return resp['data']['location']

    @staticmethod
    def generate_evopay_result_url(role_id: str, bill_id: str) -> str:
        """
        This is the URL to which EvoPay will redirect after payment is done
        """
        config = services.config.app

        base_url = config.domain

        return base_url + EVOPAY_RESULT_URL.format(role_id=role_id, bill_id=bill_id)

    @staticmethod
    def generate_evopay_webhook_url() -> str:
        """
        This is the URL to which EvoPay will send webhook request after payment is done
        """
        config = services.config

        base_url = config.app.domain

        # By default, on local environment, webhook will not work because EvoPay will not be able
        # to send a request to your localhost. To make it work, you need to expose your local server
        # to the internet. You can use "ngrok" or "localtunnel" for this purpose and then set
        # "evopay_webhook_domain" in your "config.override.yaml" to the URL provided by
        # "ngrok" or "localtunnel". For example,
        # ```
        # evopay:
        #   evopay_webhook_domain: https://your-url.ngrok.io
        # ```
        if config.evopay and config.evopay.evopay_webhook_domain:
            base_url = config.evopay.evopay_webhook_domain

        return base_url + EVOPAY_WEBHOOK_URL


class EvopayCheckoutOrder(NamedTuple):
    bill_id: str
    role_id: str
    price: int
    currency: str
    result_url: str
    webhook_url: str
    description: str

    def to_evopay_request(self) -> DataDict:
        """Converts class to evopay request"""

        config = services.config.evopay
        if not config:
            raise ValueError('Evopay config is not set')

        return {
            'data': {
                'project_order_id': self.bill_id,
                'project_id': config.project_id,
                'description': self.description,
                'amount': {
                    'value': str(self.price / 100),
                    'currency': self.currency,
                },
                'result_url': self.result_url,
                'webhook_url': self.webhook_url,
                'customer': {
                    'project_user_id': self.role_id,
                },
                'payload': config.evopay_webhook_key,
                'purpose': config.purpose,
            }
        }
