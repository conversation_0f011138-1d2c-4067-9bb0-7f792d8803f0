"""Add allowed IPs field.

Revision ID: 975ac2da2a0e
Revises: 2de620b7a79b
Create Date: 2017-04-13 12:36:36.863727


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '975ac2da2a0e'
down_revision = '2de620b7a79b'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'access_to_doc', 'users', ['user_id'], ['id'], ondelete='NO ACTION')
    op.alter_column('call_responses', 'results',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False,
               existing_server_default=sa.text("'[]'::json"))
    op.alter_column('conversions', 'data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False,
               existing_server_default=sa.text("'[]'::json"))
    op.add_column('roles', sa.Column('allowed_ips', postgresql.JSON(astext_type=sa.Text()), server_default=sa.text("'[]'"), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('roles', 'allowed_ips')
    op.alter_column('conversions', 'data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True,
               existing_server_default=sa.text("'[]'::json"))
    op.alter_column('call_responses', 'results',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True,
               existing_server_default=sa.text("'[]'::json"))
    op.drop_constraint(None, 'access_to_doc', type_='foreignkey')
    # ### end Alembic commands ###
