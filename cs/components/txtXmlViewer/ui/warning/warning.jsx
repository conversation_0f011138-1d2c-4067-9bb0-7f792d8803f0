import React from 'react';

import PropTypes from 'prop-types';

import css from './warning.css';

const Warning = ({ url }) => {
    const buildUrl = (downloadUrl) => {
        const documentIdWithParams = downloadUrl.split('/').pop();
        const documentId = documentIdWithParams.split('?')[0];
        return `${location.origin}/app/documents/${documentId}`;
    };

    return (
        <div className={css.root}>
            Ця копія є відображенням документа: {buildUrl(url)}
        </div>
    );
};

Warning.propTypes = {
    url: PropTypes.string.isRequired,
};

export default Warning;
