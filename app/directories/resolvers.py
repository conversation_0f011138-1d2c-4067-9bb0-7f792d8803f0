import logging
from collections import defaultdict

from hiku.engine import (
    Context,
    pass_context,
)
from hiku.graph import Nothing

from api.graph.constants import (
    DB_ENGINE_KEY,
    DB_READONLY_KEY,
)
from api.graph.utils import get_graph_user
from app.auth.types import User
from app.directories.db import (
    count_company_directories_by_parent,
    count_list_directories,
    select_company_directories_by_ids,
    select_company_directory_by_id,
    select_directories_for_graph,
    select_documents_directories,
    select_list_directory_ids_for_graph,
)
from app.directories.schemas import DirectoriesListSearchOptions
from app.directories.types import DirectoriesList, SimpleDirectory
from app.directories.utils import get_directories_db_order
from app.lib.types import (
    DataDict,
    StrList,
)

logger = logging.getLogger(__name__)


@pass_context
async def resolve_documents_directories(ctx: Context, ids: StrList) -> list[int | Nothing]:
    db = ctx[DB_ENGINE_KEY]
    user = get_graph_user(ctx)
    if not user:
        return [Nothing for _ in ids]

    async with db.acquire() as conn:
        rows = await select_documents_directories(
            conn=conn,
            company_id=user.company_id,
            document_ids=ids,
        )

    mapping = defaultdict(int)
    for row in rows:
        mapping[row.document_id] = row.directory_id

    return [mapping.get(doc_id, Nothing) for doc_id in ids]


@pass_context
async def resolve_directories(ctx: Context, options: DataDict) -> DirectoriesList:
    parent_id: int | None = options['parentId']
    search: str | None = options['search']
    limit: int = options['limit']
    offset: int = options['offset']

    user = get_graph_user(ctx)
    if not user:
        return DirectoriesList(count=0, directory_ids=())

    db = ctx[DB_ENGINE_KEY]

    async with db.acquire() as conn:
        directory_ids = await select_directories_for_graph(
            conn=conn,
            parent_id=parent_id,
            company_id=user.company_id,
            search=search,
            offset=offset,
            limit=limit,
        )
        total_level_count = await count_company_directories_by_parent(
            conn=conn, parent_id=parent_id, company_id=user.company_id, search=search
        )

    return DirectoriesList(
        count=total_level_count,
        directory_ids=directory_ids,
    )


async def resolve_directories_list(
    ctx: Context, raw_options: DataDict, user: User
) -> tuple[int, StrList]:
    db = ctx[DB_READONLY_KEY]

    options = DirectoriesListSearchOptions(**raw_options)

    # Do not search directories if extra search options provided
    if options.has_extra_search_options:
        return 0, []

    async with db.acquire() as conn:
        directories_count = await count_list_directories(
            conn=conn,
            options=options,
            company_id=user.company_id,
        )
        directory_ids = []
        if directories_count > options.offset:
            order, direction = get_directories_db_order(options)
            directory_ids = await select_list_directory_ids_for_graph(
                conn=conn,
                company_id=user.company_id,
                order=order,
                direction=direction,
                options=options,
            )

    return directories_count, directory_ids


@pass_context
async def resolve_directory(ctx: Context, options: DataDict) -> int | Nothing:
    user = get_graph_user(ctx)
    if not user:
        return Nothing

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        directory = await select_company_directory_by_id(
            conn=conn,
            company_id=user.company_id,
            id=options['id'],
        )

    if not directory:
        return Nothing

    return directory.id


@pass_context
async def resolve_directory_parents_list(
    ctx: Context, directory_ids: list[int]
) -> list[list[SimpleDirectory]]:
    """
    Resolve directory parents list for every id from directory_ids
    """
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in directory_ids]

    # We assume that resolver will always be used for directory_ids with 1 item
    if len(directory_ids) > 1:
        logger.warning('Trying to resolve path for multiple directories')
        return [[]]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        directories = await select_company_directories_by_ids(
            conn=conn,
            ids=directory_ids,
            company_id=user.company_id,
        )

        parent_ids = {parent_id for directory in directories for parent_id in directory.path}
        parent_directories = await select_company_directories_by_ids(
            conn=conn,
            ids=list(parent_ids),
            company_id=user.company_id,
        )
        parent_directory_by_id = {d.id: d for d in parent_directories}

        # Map each directory to its list of parent directories
        directory_to_parents = defaultdict(list)
        for directory in directories:
            for parent_id in directory.path:
                directory_to_parents[directory.id].append(parent_directory_by_id[parent_id])

    return [
        [SimpleDirectory(id=parent.id, name=parent.name) for parent in directory_to_parents[d.id]]
        for d in directories
    ]
