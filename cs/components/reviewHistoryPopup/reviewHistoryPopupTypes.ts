import { GroupMembers } from '../../types/user';
import { DocumentListDocumentItem } from '../documentList/types';

export interface IReviewHistory {
    date: Date;
    userName: string;
    userEmail: string;
    actionName: string;
    color: string;
    group: PartialGroup | null;
    recipients?: IRecipient[];
    documentVersion: ReviewDocumentVersion;
}

export interface State {
    isActive: boolean;
    isLoading: boolean;
    errorMessage: string;
    docName: string;
    docId: string;
    reviewHistory: IReviewHistory[];
    document: DocumentListDocumentItem;
}

export interface ActionsTypes {
    onClose: () => void;
}

export interface IRecipient {
    name: string;
    email: string;
}

export type PartialRole = {
    position: string;
    user: {
        email: string;
        firstName: string;
        secondName: Nullable<string>;
        lastName: Nullable<string>;
    };
};

export type PartialGroup = {
    id: string;
    name: string;
    members: GroupMembers[];
};

export interface ReviewDocumentVersion {
    id: Nullable<string>;
    name: Nullable<string>;
}

export interface IReview {
    id: string;
    roleId: string;
    groupId: string;
    documentId: string;
    documentVersion: ReviewDocumentVersion;
    type: 'approve' | 'reject' | 'request' | null; // null - означає було скасовано вибір
    dateCreated: Date;
    userEmail: string;
    role: PartialRole;
    group: PartialGroup | null;
}

export interface IReviewRequest {
    type?: 'active' | 'deleted';
    id: string;
    documentId: string;
    documentVersion: ReviewDocumentVersion;
    fromRoleId: string;
    toRoleId: string;
    toGroupId: Nullable<string>;
    status: 'approve' | 'reject' | 'active';
    dateCreated: ISODate;
    dateUpdated: ISODate;
    toRole: PartialRole;
    toGroup: PartialGroup | null;
    fromRole?: PartialRole;
    order: Nullable<number>;
}

export interface ReviewRequestStatuses {
    deleted: string;
    active: string;

    [key: string]: string;
}

export interface ReviewsActions {
    approve: {
        color: string;
        text: string;
    };
    reject: {
        color: string;
        text: string;
    };
    default: {
        color: string;
        text: string;
    };
}

export interface ReviewHistoryForTable {
    items: {
        userData: JSX.Element;
        action: JSX.Element;
        date: any;
    };
    documentVersion: ReviewDocumentVersion;
}
