import React from 'react';

import { Alert } from '@vchasno/ui-kit';

import { t } from 'ttag';

import DocumentDraftList from './DocumentDraftList';
import { useDraftFetch } from './DraftFetchProvider';
import EmptyBox from './EmptyBox';
import ListPagination from './ListPagination';
import PageLayout from './PageLayout';

const DocumentDrafts: React.FC = () => {
    const { isNoData, error } = useDraftFetch();

    if (error) {
        return (
            <PageLayout title={t`Чернетки`}>
                <Alert
                    wide
                    type="error"
                >{t`Помилка завантаження чернеток`}</Alert>
            </PageLayout>
        );
    }

    if (isNoData) {
        return (
            <PageLayout title={t`Чернетки`}>
                <EmptyBox />
            </PageLayout>
        );
    }

    return (
        <PageLayout
            title={t`Чернетки`}
            footer={<ListPagination />}
            subtitle={t`Тут ви можете  продовжити редагування чернетки та створити документ. Чернетка буде автоматично видалена через 30 днів.`}
        >
            <DocumentDraftList />
        </PageLayout>
    );
};

export default DocumentDrafts;
