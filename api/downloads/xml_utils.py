import io
from collections.abc import Iterator

import ujson
from lxml import etree

from api.downloads import constants
from api.downloads.types import XmlToJsonOptions
from api.downloads.utils import download_file_as_bytes
from api.uploads.xml_utils import clear_element
from app.lib.helpers import nested_defaultict, run_sync
from app.lib.types import DataDict

StreamItemTuple = tuple[str, tuple[str, ...], etree._Element]


async def count_rows_xml_to_json(options: XmlToJsonOptions) -> int:
    content = await download_file_as_bytes(options.original_options)
    return await run_sync(func=sync_count_rows_xml_to_json, content=content)


async def generate_xml_to_json(options: XmlToJsonOptions) -> io.BytesIO:
    content = await download_file_as_bytes(options.original_options)
    return await run_sync(
        func=sync_generate_xml_to_json,
        content=content,
        options=options,
    )


def is_row_number_tag(raw_path: tuple[str, ...]) -> bool:
    if raw_path[-1] != constants.XML_BODY_ROW_TAG:
        return False
    sliced_path = raw_path[:-1]
    return (
        constants.XML_BODY_GOODS_TAG in sliced_path
        or constants.XML_BODY_SERVICES_TAG in sliced_path
    )


def stream_xml(content: bytes) -> Iterator[StreamItemTuple]:
    path = []

    for event, elem in etree.iterparse(io.BytesIO(content), events=('start', 'end')):
        if event == 'start':
            path.append(elem.tag)

        yield event, tuple(path), elem

        if event == 'end':
            path.pop()
            clear_element(elem)


def sync_count_rows_xml_to_json(content: bytes) -> int:
    return sum(
        1 for item in stream_xml(content) if item[0] == 'start' and is_row_number_tag(item[1])
    )


def sync_generate_xml_to_json(content: bytes, options: XmlToJsonOptions) -> io.BytesIO:
    body_group: DataDict | None = None
    body_rows: set[int] = set()
    next_body_row_path: tuple[str, ...] = ()
    processed_body_rows = set()
    data = nested_defaultict()

    for event, raw_path, elem in stream_xml(content):
        data_item = None
        is_body_group = (
            constants.XML_BODY_GOODS_TAG in raw_path[:-1]
            or constants.XML_BODY_SERVICES_TAG in raw_path[:-1]
        )

        if is_body_group and raw_path[-1] == constants.XML_BODY_ROW_TAG and event == 'start':
            row = len(body_rows) + 1
            body_rows.add(row)
            if body_group is None and options.first_row > len(body_rows):
                continue

            processed_body_rows.add(row)
            if options.limit_rows and len(processed_body_rows) > options.limit_rows:
                next_body_row_path = raw_path
                break

            body_group = {
                'number': row,
                'parent': raw_path[-2],
                'tag': '{}_{}'.format(raw_path[-2].split('_')[-1], row),
            }

        if is_body_group and body_group is None:
            continue

        path = list(raw_path)
        if body_group and body_group['parent'] in path:
            idx = path.index(body_group['parent']) + 1
            path.insert(idx, body_group['tag'])

        for item in path:
            data_item = (data_item or data)[item]

            if body_group and item == body_group['tag']:
                data_item['$'] = None
                data_item[f'@{constants.XML_ROW_NUMBER_TAG}'] = body_group['number']

        if data_item is not None:
            data_item['$'] = elem.text
            for key, value in elem.items():
                data_item[f'@{key}'] = value

    data_item = None
    last_idx = len(next_body_row_path) - 1
    for idx, item in enumerate(next_body_row_path):
        if idx == last_idx:
            data_item['@СледующаяСтрока'] = 'true'  # type: ignore
        else:
            data_item = (data_item or data)[item]

    return io.BytesIO(ujson.dumps(data).encode('utf-8'))
