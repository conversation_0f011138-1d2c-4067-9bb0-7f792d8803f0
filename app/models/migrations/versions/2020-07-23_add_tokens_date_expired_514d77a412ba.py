"""add_tokens_date_expired

Revision ID: 514d77a412ba
Revises: 5ed95ebc7eb1
Create Date: 2020-07-23 12:50:55.041890


Author: <PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '514d77a412ba'
down_revision = '5ed95ebc7eb1'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('tokens', sa.Column('date_expired', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    op.drop_column('tokens', 'date_expired')
