import asyncio
from http import HTTPStatus

from aiohttp import web

from api.utils import api_response
from app.archive import utils
from app.archive.validators import (
    validate_archive_documents,
    validate_unarchive_documents,
    validate_upload_histored_document,
)
from app.auth.types import User
from app.directories.db import insert_directory_documents
from app.documents.enums import DocumentSource
from app.es.utils import send_to_indexator
from app.lib import tracking, validators
from app.services import services
from app.uploads.utils import process_web_upload


async def archive_documents(
    request: web.Request,
    user: User,
) -> web.Response:
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        archive_ctx = await validate_archive_documents(
            conn=conn,
            user=user,
            data=data,
        )
        await utils.archive_documents(
            conn=conn,
            ctx=archive_ctx,
            user=user,
        )

    return api_response(request, {}, status=200)


async def unarchive_documents(
    request: web.Request,
    user: User,
) -> web.Response:
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        documents = await validate_unarchive_documents(
            conn=conn,
            user=user,
            data=data,
        )
        await utils.unarchive_documents(
            conn=conn,
            documents=documents,
            user=user,
        )

    return api_response(request, {}, status=200)


async def upload_histored_document(request: web.Request, user: User) -> web.Response:
    """
    Upload `histored` document to archive.
    The histored document has historical reference only.
    No further processing or updates allowed.

    It is stored solely for informational purposes.
    """

    async with services.db_readonly.acquire() as conn:
        valid_data = await validate_upload_histored_document(
            conn=conn,
            user=user,
            data=request.rel_url.query,
        )

    async with asyncio.timeout(services.config.app.upload_timeout):
        documents, _ = await process_web_upload(
            request=request,
            user=user,
            document_source=DocumentSource.archive_histored,
        )

    if valid_data.directory is not None:
        async with services.db.acquire() as conn:
            await insert_directory_documents(
                conn=conn,
                document_ids=[document.id for document in documents],
                directory_id=valid_data.directory.id,
            )

    await send_to_indexator(
        redis=services.redis,
        document_ids=[document.id for document in documents],
        to_slow_queue=False,
    )

    # Trigger prometheus tracking
    tracking.documents_count_web.inc(len(documents))

    return web.json_response(
        data={'documents': [doc.to_api() for doc in documents]},
        status=HTTPStatus.CREATED,
    )
