from __future__ import annotations

import contextvars as c
import logging
from collections.abc import Iterator
from contextlib import contextmanager
from typing import Any

from babel.support import NullTranslations, Translations

from app.i18n.config import config
from app.i18n.locale import Locale
from app.i18n.types import <PERSON>zyI18nString
from app.lib.enums import Language

type TranslationsObject = Translations | NullTranslations

logger = logging.getLogger(__name__)


DEFAULT_LOCALE = Locale.get('uk')

ctx_locale: c.ContextVar[Locale] = c.ContextVar('ctx_locale', default=DEFAULT_LOCALE)


def init_locale_context() -> None:
    """Initialize locale context to default locale"""

    code = config.default_locale
    if not code:
        raise ValueError('Set "i18n.config.default_locale" before initializing locale context')

    locale = Locale.get(code=code)
    ctx_locale.set(locale)


def get_current_locale_code() -> str | None:
    """
    Returns current locale code from context
    Example: "uk", "en", ...
    """
    locale = ctx_locale.get()
    if not locale:
        return None
    return locale.language


@contextmanager
def set_locale_context(code: str | Language | None) -> Iterator[None]:
    """Set locale in context"""

    # if not code provided default locale will be used
    if not code:
        yield
        return

    if isinstance(code, Language):
        code = code.value

    locale: Locale | None = Locale.safe_get(code)
    if not locale:
        yield
        return

    # temporary set locale ContextVar
    token = ctx_locale.set(locale)
    try:
        yield
    finally:
        ctx_locale.reset(token)


def lazy_gettext(message: str, **kwargs: Any) -> LazyI18nString:
    """
    Lazy gettext function that will return LazyI18nString object that can be translated later
    """

    assert not isinstance(
        message,  # type: ignore
        tuple,
    ), 'Message can not be tuple, check for comma in gettext call'

    if isinstance(message, LazyI18nString):
        return message

    def _lookup_func() -> str:
        _locale = ctx_locale.get()
        return _locale.translate(message, **kwargs)

    return LazyI18nString(_lookup_func)


_ = lazy_gettext


def _lazy_ngettext(
    msg1: str,
    msg2: str,
    msg3: str,
    *,
    n: int,
    **kwargs: Any,
) -> LazyI18nString:
    """
    Lazy ngettext function that will return LazyI18nString object
    that can be translated later
    """

    def _lookup_func() -> str:
        _locale = ctx_locale.get()
        return _locale.translate_plural(
            msg1=msg1,
            msg2=msg2,
            msg3=msg3,
            n=n,
            **kwargs,
        )

    return LazyI18nString(_lookup_func)


ngettext = _lazy_ngettext
