.headerTable {
    width: 100%;
    margin-bottom: 1.5cm;
}

.headerTable td {
    width: 50%;
    padding-right: 0.25cm;
    font-size: 80%;
    vertical-align: top;
}

.documentTitle {
    font-size: 110%;
    font-weight: bold;
}

.documentNumber {
    padding-bottom: 0.05cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
    font-size: 110%;
    font-weight: bold;
    text-indent: 0.1cm;
}

.documentTrivia,
.documentTriviaBorder,
.documentTriviaIndent,
.documentTriviaMargin {
    width: 85%;
    font-size: 85%;
    line-height: 110%;
}

.compilationPlace {
    margin-top: 0.3cm;
    margin-bottom: 0.1cm;
    font-size: 85%;
    line-height: 110%;
}

.documentTriviaBorder {
    width: 100%;
    padding-bottom: 0.2cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-top: 0.2cm;
}

.documentTriviaIndent {
    text-indent: 0.2cm;
}

.documentTriviaMargin {
    margin: 0.4cm 0;
}

.documentDetailsTable {
    width: 100%;
}

.documentDetailsTable td {
    padding-top: 0.1cm;
    padding-bottom: 0.2cm;
    font-size: 80%;
}

.documentDetailsTable .detailName {
    width: 25%;
    padding-right: 1cm;
    text-align: right;
}

.footerTable {
    width: 100%;
    margin-top: 0.4cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }

    .headerTable td {
        padding-right: 1.25cm;
    }
}

.footerDate {
    font-size: 110%;
}

.digitalSignaturesWrapper {
    position: relative;
}

.positions td {
    padding-top: 6px;
}

.table table thead th {
    background-color: unset;
}

.additionTextIndent {
    margin-top: 20px;
}
