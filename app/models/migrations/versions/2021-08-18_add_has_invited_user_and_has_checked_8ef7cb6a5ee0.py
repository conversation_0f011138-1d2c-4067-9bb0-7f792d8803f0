"""Add has_invited_user and has_checked_companies flags to user meta table

Revision ID: 8ef7cb6a5ee0
Revises: 49c733dfd4f7
Create Date: 2021-08-18 21:02:21.797317


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '8ef7cb6a5ee0'
down_revision = '49c733dfd4f7'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('user_meta', sa.<PERSON>umn('has_checked_companies', sa.<PERSON><PERSON>(), server_default='0', nullable=False))
    op.add_column('user_meta', sa.Column('has_invited_user', sa.<PERSON>(), server_default='0', nullable=False))


def downgrade():
    op.drop_column('user_meta', 'has_invited_user')
    op.drop_column('user_meta', 'has_checked_companies')
