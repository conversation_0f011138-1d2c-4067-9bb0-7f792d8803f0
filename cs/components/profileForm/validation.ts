import { yupResolver } from '@hookform/resolvers/yup';

import { STATIC_ERROR_PHRASES } from 'components/auth/constants';
import { EMAIL_MAX_LENGTH, EMAIL_PATTERN } from 'lib/constants';
import * as yup from 'yup';

import { ProfileFormFields } from './types';

const profileFormValidationSchema: yup.ObjectSchema<ProfileFormFields> = yup
    .object({
        formEmail: yup
            .string()
            .default('')
            .trim()
            .required()
            .max(EMAIL_MAX_LENGTH, STATIC_ERROR_PHRASES.LOGIN_MAX_LENGTH_ERROR)
            .matches(EMAIL_PATTERN, STATIC_ERROR_PHRASES.EMAIL_ERROR),
        formFirstName: yup.string().required(),
        formLastName: yup.string().required(),
        formSecondName: yup.string().default(''),
    })
    .required();

export const profileFormResolver = yupResolver(profileFormValidationSchema);
