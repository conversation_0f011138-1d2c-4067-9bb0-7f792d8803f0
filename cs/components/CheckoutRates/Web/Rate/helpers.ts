import { t } from 'ttag';

import { RateInformation } from '../../../Checkout/types';

import CheckSvg from './images/check.svg';
import CrossSvg from './images/cross.svg';

const getIsIncludedFunctional = (
    functional: Nullable<string | boolean | number>,
) => functional === null || !!functional;
const getFunctionalStatus = (
    functionalValue: Nullable<string | boolean | number>,
    functionalName?: keyof RateInformation,
) => {
    const isIncludedFunctional = getIsIncludedFunctional(functionalValue);

    if (functionalName === 'signing_incoming_documents') {
        return {
            isIncluded: isIncludedFunctional,
            status: t`Без обмежень`,
            statusIcon: null,
        };
    }

    return {
        isIncluded: isIncludedFunctional,
        status: null,
        statusIcon: isIncludedFunctional ? CheckSvg : CrossSvg,
    };
};

export const getAdditionalInformation = (rateInformation: RateInformation) => [
    {
        label: t`Підписання вхідних документів`,
        ...getFunctionalStatus(
            rateInformation.signing_incoming_documents,
            'signing_incoming_documents',
        ),
    },
    {
        label: t`Індивідуальні права для кожного співробітника`,
        ...getFunctionalStatus(rateInformation.manage_employees_access),
    },
    {
        label: t`Двофакторна автентифікація для співробітників`,
        ...getFunctionalStatus(rateInformation.enforce_2fa),
    },
    {
        label: t`Сценарії документів`,
        ...getFunctionalStatus(rateInformation.templates),
    },
    {
        label: t`Коментарі зі співробітниками та контрагентами`,
        ...getFunctionalStatus(rateInformation.comments),
    },
    {
        label: t`Внутрішні документи`,
        ...getFunctionalStatus(rateInformation.internal_documents),
    },
    {
        label: t`Ярлики`,
        ...getFunctionalStatus(rateInformation.tags),
    },
    {
        label: t`Обовʼязкові поля`,
        ...getFunctionalStatus(rateInformation.required_fields),
    },
    {
        label: t`Перевірка документів антивірусом`,
        ...getFunctionalStatus(rateInformation.antivirus),
    },
    {
        label: t`Внутрішнє погодження`,
        ...getFunctionalStatus(rateInformation.reviews),
    },
    {
        label: t`Додаткові параметри документів`,
        ...getFunctionalStatus(rateInformation.additional_fields),
    },
    {
        label: t`Зберігання документів у хмарному архіві`,
        ...getFunctionalStatus(rateInformation.cloud_storage),
    },
];
