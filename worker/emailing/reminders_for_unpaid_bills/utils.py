from dateutil.relativedelta import relativedelta

from app.auth import db as auth_db
from app.billing import db as billing_db
from app.billing import enums as billing_enums
from app.billing import types as billing_types
from app.billing import utils as billing_utils
from app.lib import datetime_utils as dt_utils
from app.lib.database.types import DBConnection
from worker.emailing.reminders_for_unpaid_bills.types import MAX_DAYS_FOR_BILL_REMINDER


class ArchiveVerifications:
    @staticmethod
    def company_has_bill_for_larger_single_archive(
        original_bill: billing_types.Bill,
        recent_company_bills: list[billing_types.Bill],
    ) -> bool:
        """
        Company has bill for larger single (not combined) archive
        """

        # Expand this list and add if conditions when new versions of archive are introduced
        larger_archive_rates_set = {
            billing_enums.AccountRate.archive_big,
        }

        for bill in recent_company_bills:
            if (
                bill.services_type == billing_enums.BillServicesType.rate
                and bill.rate_services[0].rate in larger_archive_rates_set  # Large archive
                and bill.id_ != original_bill.id_  # Verify bills are not duplicated (edge case)
            ):
                return True
        return False

    @staticmethod
    def company_has_similar_active_archive_rate(
        archive_rate: billing_types.AccountRate,
        active_company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company has similar archive (>30 days expiry) archive rate
        """
        active_archive_rates = [
            company_rate.rate
            for company_rate in active_company_rates
            if company_rate.rate.is_archive
            and company_rate.date_expired
            and company_rate.date_expired >= dt_utils.local_now() + relativedelta(days=30)
        ]
        return archive_rate in active_archive_rates

    @staticmethod
    def company_has_similar_planned_archive_rate(
        archive_rate: billing_types.AccountRate,
        planned_company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company already has similar planned web rate
        """
        planned_archive_rates = [
            company_rate.rate
            for company_rate in planned_company_rates
            if company_rate.rate.is_archive
        ]
        return archive_rate in planned_archive_rates

    @staticmethod
    def company_has_higher_archive_rate(
        archive_rate: billing_types.AccountRate,
        company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company already has higher archive rate
        """

        if archive_rate == billing_types.AccountRate.archive_small:
            return billing_types.AccountRate.archive_big in [
                company_rate.rate for company_rate in company_rates if company_rate.rate.is_archive
            ]
        return False


class WebVerifications:
    @staticmethod
    def company_has_similar_active_web_rate(
        web_rate: billing_types.AccountRate,
        active_company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company already has similar active (>30 days expiry) web rate
        """
        active_web_rates = [
            company_rate.rate
            for company_rate in active_company_rates
            if company_rate.rate.is_web
            and company_rate.date_expired
            and company_rate.date_expired >= dt_utils.local_now() + relativedelta(days=30)
        ]
        return web_rate in active_web_rates

    @staticmethod
    def company_has_similar_planned_web_rate(
        web_rate: billing_types.AccountRate,
        planned_company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company already has similar planned web rate
        """
        planned_web_rates = [
            company_rate.rate for company_rate in planned_company_rates if company_rate.rate.is_web
        ]
        return web_rate in planned_web_rates

    @staticmethod
    def company_has_higher_web_rate(
        web_rate: billing_types.AccountRate,
        company_rates: list[billing_types.CompanyRate],
    ) -> bool:
        """
        Company already has higher web rate
        """
        highest_web_rate = billing_utils.get_highest_web_rate(company_rates)
        if not highest_web_rate:
            return False

        return highest_web_rate.rate.priority_rank > web_rate.priority_rank

    @staticmethod
    def company_has_bill_for_higher_web_rate(
        web_rate: billing_types.AccountRate,
        recent_company_bills: list[billing_types.Bill],
    ) -> bool:
        """
        Company has requested higher web rate
        """
        for bill in recent_company_bills:
            if not bill.rate_services:
                continue

            for service in bill.rate_services:
                if service.rate.priority_rank > web_rate.priority_rank:
                    return True

        return False


class CombinedVerifications:
    @staticmethod
    def company_has_newer_combined_bill(
        original_bill: billing_types.Bill, recent_company_bills: list[billing_types.Bill]
    ) -> bool:
        """
        Company has bill which was created after the original one,
        and it's combined (web + archive)
        """
        for bill in recent_company_bills:
            if len(bill.rate_services) == 2 and bill.date_created >= original_bill.date_created:
                return True

        return False


def _should_remind_about_archive_bill(
    bill: billing_types.Bill,
    recent_company_bills: list[billing_types.Bill],
    active_company_rates: list[billing_types.CompanyRate],
    planned_company_rates: list[billing_types.CompanyRate],
) -> tuple[str | None, bool]:
    """
    Company shouldn't ... :
        - ... have newer combined bill.
        - ... have bill for larger single archive.
        - ... have similar active archive (>30 days expiry).
        - ... have similar planned archive.
        - ... have higher active archive.
        - ... have higher planned archive.
    """
    archive_rate = bill.rate_services[0].rate

    # If company has recent combined bill motivate to pay for combined and skip archive.
    if CombinedVerifications.company_has_newer_combined_bill(
        original_bill=bill,
        recent_company_bills=recent_company_bills,
    ):
        return 'Company has newer combined bill', False

    # If company has bill for larger archive motivate to pay for larger and skip smaller.
    if ArchiveVerifications.company_has_bill_for_larger_single_archive(
        original_bill=bill,
        recent_company_bills=recent_company_bills,
    ):
        return 'Company has bill for larger single archive', False

    # Don't remind if company has similar active archive rate
    if ArchiveVerifications.company_has_similar_active_archive_rate(
        archive_rate=archive_rate,
        active_company_rates=active_company_rates,
    ):
        return 'Company has similar active archive rate', False

    # Don't remind if company has similar planned archive rate
    if ArchiveVerifications.company_has_similar_planned_archive_rate(
        archive_rate=archive_rate,
        planned_company_rates=planned_company_rates,
    ):
        return 'Company has similar planned archive rate', False

    # Don't remind if company has higher active archive rate
    if ArchiveVerifications.company_has_higher_archive_rate(
        archive_rate=archive_rate,
        company_rates=active_company_rates,
    ):
        return 'Company has higher active archive rate', False

    # Don't remind if company has higher planned archive rate
    if ArchiveVerifications.company_has_higher_archive_rate(
        archive_rate=archive_rate,
        company_rates=planned_company_rates,
    ):
        return 'Company has higher planned archive rate', False

    return None, True


def _should_remind_about_web_bill(
    bill: billing_types.Bill,
    recent_company_bills: list[billing_types.Bill],
    active_company_rates: list[billing_types.CompanyRate],
    planned_company_rates: list[billing_types.CompanyRate],
) -> tuple[str | None, bool]:
    """
    Company shouldn't ... :
        - ... have similar active web rate (>30 days expiry)
        - ... have similar planned web rate
        - ... have higher active web rate
        - ... have higher planned web rate
        - ... have bill for higher web rate (no matter single or combined)
    """
    web_rate = bill.rate_services[0].rate

    # Don't remind if company has similar active web rate, expiring in more than 30 days
    if WebVerifications.company_has_similar_active_web_rate(
        web_rate=web_rate,
        active_company_rates=active_company_rates,
    ):
        return 'Company has similar active web rate', False

    # Don't remind if company has similar planned web rate
    if WebVerifications.company_has_similar_planned_web_rate(
        web_rate=web_rate,
        planned_company_rates=planned_company_rates,
    ):
        return 'Company has similar planned web rate', False

    # Don't remind if company has higher active web rate
    if WebVerifications.company_has_higher_web_rate(
        web_rate=web_rate,
        company_rates=active_company_rates,
    ):
        return 'Company has higher active web rate', False

    # Don't remind if company has higher planned web rate
    if WebVerifications.company_has_higher_web_rate(
        web_rate=web_rate,
        company_rates=planned_company_rates,
    ):
        return 'Company has higher planned web rate', False

    # Don't remind if company has bill for higher web rate
    if WebVerifications.company_has_bill_for_higher_web_rate(
        web_rate=web_rate,
        recent_company_bills=recent_company_bills,
    ):
        return 'Company has bill for higher web rate', False

    # Remind if all conditions passed
    return None, True


def _should_remind_about_combined_bill(
    bill: billing_types.Bill,
    recent_company_bills: list[billing_types.Bill],
    active_company_rates: list[billing_types.CompanyRate],
    planned_company_rates: list[billing_types.CompanyRate],
) -> tuple[str | None, bool]:
    """
    Company shouldn't ... :
        - ... have similar active web and archive rates (>30 days expiry)
        - ... have similar planned web and archive rates
        - ... have higher active web rate
        - ... have higher planned web rate
        - ... have bill for higher web rate (no matter single or combined)
    """
    web_rate = [s.rate for s in bill.rate_services if s.rate.is_web][0]
    archive_rate = [s.rate for s in bill.rate_services if s.rate.is_archive][0]

    # Don't remind if company already has similar active web and archive
    if WebVerifications.company_has_similar_active_web_rate(
        web_rate=web_rate, active_company_rates=active_company_rates
    ):
        if ArchiveVerifications.company_has_similar_active_archive_rate(
            archive_rate=archive_rate,
            active_company_rates=active_company_rates,
        ):
            return 'Company has similar active web and archive rate', False
        return 'Company has similar active web rate', False

    # Don't remind if company already has similar planned web and archive
    if WebVerifications.company_has_similar_planned_web_rate(
        web_rate=web_rate,
        planned_company_rates=planned_company_rates,
    ) and ArchiveVerifications.company_has_similar_planned_archive_rate(
        archive_rate=archive_rate,
        planned_company_rates=planned_company_rates,
    ):
        return 'Company has similar planned web and archive rate', False

    # Don't remind if company has higher active web rate
    if WebVerifications.company_has_higher_web_rate(
        web_rate=web_rate,
        company_rates=active_company_rates,
    ):
        return 'Company has higher active web rate', False

    # Don't remind if company has higher planned web rate
    if WebVerifications.company_has_higher_web_rate(
        web_rate=web_rate,
        company_rates=planned_company_rates,
    ):
        return 'Company has higher planned web rate', False

    # Don't remind if company has bill for higher web rate
    if WebVerifications.company_has_bill_for_higher_web_rate(
        web_rate=web_rate,
        recent_company_bills=recent_company_bills,
    ):
        return 'Company has bill for higher web rate', False

    # Remind if all conditions passed
    return None, True


async def should_remind_about_unpaid_bill(
    conn: DBConnection, bill: billing_types.Bill
) -> tuple[str | None, bool]:
    """
    Decide whether we need to remind about unpaid bill.
    https://tabula-rasa.atlassian.net/browse/DOC-6935
    """
    if not bill.rate_services:
        # As for now, we remind only about rate bills
        return 'Bill without rate services', False

    company = await auth_db.select_company(conn, edrpou=bill.edrpou)
    if not company:
        return 'Bill was created by unregistered company', False

    # Select recent company bills within last 13 days
    recent_company_bills = await billing_db.select_bills_by_edrpous(
        conn=conn,
        edrpous=[bill.edrpou],
        date_created_from=dt_utils.midnight() - relativedelta(days=MAX_DAYS_FOR_BILL_REMINDER),
        exclude_deleted=True,
    )

    # Select company active and planned company rates
    active_company_rates = await billing_db.select_active_company_rates(conn, company.id)
    planned_company_rates = await billing_db.select_planned_company_rates(conn, company.id)

    # Process single archive or web rate bill
    if bill.services_type == billing_enums.BillServicesType.rate:
        # Process single archive rate bill
        if bill.rate_services[0].rate.is_archive:
            return _should_remind_about_archive_bill(
                bill=bill,
                recent_company_bills=recent_company_bills,
                active_company_rates=active_company_rates,
                planned_company_rates=planned_company_rates,
            )

        # Process single web rate bill
        if bill.rate_services[0].rate.is_web:
            return _should_remind_about_web_bill(
                bill=bill,
                recent_company_bills=recent_company_bills,
                active_company_rates=active_company_rates,
                planned_company_rates=planned_company_rates,
            )

        # Don't remind if bill is not for archive and not for web (should never happen)
        return 'Bill is single rate but not archive and not web', False

    # Process combined web and archive rate bill
    if bill.services_type == billing_enums.BillServicesType.web_and_archive:
        return _should_remind_about_combined_bill(
            bill=bill,
            recent_company_bills=recent_company_bills,
            active_company_rates=active_company_rates,
            planned_company_rates=planned_company_rates,
        )

    if bill.services_type in (
        billing_enums.BillServicesType.integration_and_documents,
        billing_enums.BillServicesType.documents,
        billing_enums.BillServicesType.add_employee,
    ):
        return 'Unsupported services type', False

    # If no condition met (rarely), try to remind anyway
    return None, True
