import actions from './employeeActions';

import { EmployeeRole } from '../../records/user';
import { ADDITIONAL_FIELDS_TAG, TAGS_TAG } from '../tagsAccessPopup/constants';

const initTagsState = {
    isSuggestionsShown: false,
    allTags: [],
    tags: [],
    suggestedTags: [],
    errorMessage: '',
};

const initState = {
    isDeleteRolePopupOpened: false,
    isLabelSavedShown: false,
    currentEmployeeRole: new EmployeeRole(),
    newIps: '',
    newApiIps: '',
    ipErrorMessage: '',
    apiIpErrorMessage: '',
    currentUserId: '',
    tagsState: initTagsState,
    additionalFields: initTagsState,
    newToken: '',
    tokenExpireDays: 365,
    employeeCardEdited: false,
    editedSection: '',
};

const employeeTagsReducer = (state = initTagsState, action) => {
    switch (action.type) {
        case actions.EMPLOYEE__SHOW_SUGGESTIONS_TAGS:
            return {
                ...state,
                isSuggestionsShown: true,
                suggestedTags: action.suggestedTags,
                allTags: action.allTags || state.allTags,
            };
        case actions.EMPLOYEE__CLOSE_SUGGESTIONS_TAGS:
            return {
                ...state,
                isSuggestionsShown: false,
                suggestedTags: [],
            };
        case actions.EMPLOYEE__SHOW_ERROR_MESSAGE:
            return {
                ...state,
                errorMessage: action.errorMessage,
            };
        default:
            return state;
    }
};

const generateEmployeeTagsReducer = (name) => (
    state = initTagsState,
    action,
) => {
    if (action.tag !== name) {
        return state;
    } else {
        return employeeTagsReducer(state, action);
    }
};

const tagsReducer = generateEmployeeTagsReducer(TAGS_TAG);
const additionalFieldsReducer = generateEmployeeTagsReducer(
    ADDITIONAL_FIELDS_TAG,
);

const employeeReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.EMPLOYEE__SET_ROLE_DATA:
            return {
                ...initState,
                currentEmployeeRole: action.currentEmployeeRole,
                currentUserId: action.currentUserId,
            };
        case actions.EMPLOYEE__SET_NEW_IPS:
            return {
                ...state,
                newIps: action.newIps,
                ipErrorMessage: '',
            };
        case actions.EMPLOYEE__SET_NEW_API_IPS:
            return {
                ...state,
                newApiIps: action.newApiIps,
                apiIpErrorMessage: '',
            };
        case actions.EMPLOYEE__SHOW_IPS_ERROR:
            return {
                ...state,
                ipErrorMessage: action.errorMessage,
            };
        case actions.EMPLOYEE__SHOW_API_IPS_ERROR:
            return {
                ...state,
                apiIpErrorMessage: action.errorMessage,
            };
        case actions.EMPLOYEE__OPEN_DELETE_ROLE_POPUP:
            return {
                ...state,
                isDeleteRolePopupOpened: true,
            };
        case actions.EMPLOYEE__CLOSE_DELETE_ROLE_POPUP:
            return {
                ...state,
                isDeleteRolePopupOpened: false,
            };
        case actions.EMPLOYEE__SHOW_LABEL_SAVED:
            return {
                ...state,
                isLabelSavedShown: action.isLabelSavedShown,
                editedSection: action.editedSection,
            };
        case actions.EMPLOYEE__SHOW_ERROR_MESSAGE:
        case actions.EMPLOYEE__SHOW_SUGGESTIONS_TAGS:
        case actions.EMPLOYEE__CLOSE_SUGGESTIONS_TAGS:
            return {
                ...state,
                tagsState: tagsReducer(state.tagsState, action),
                additionalFields: additionalFieldsReducer(
                    state.additionalFields,
                    action,
                ),
            };
        case actions.EMPLOYEE__CREATE_TOKEN:
            return {
                ...state,
                newToken: action.newToken,
            };
        case actions.EMPLOYEE__CREATE_TOKEN_EXPIRE_DAYS:
            return {
                ...state,
                tokenExpireDays: action.tokenExpireDays,
            };
        case actions.EMPLOYEE__EDIT:
            return {
                ...state,
                employeeCardEdited: action.edit,
            };
        case actions.EMPLOYEE__RESET_CURRENT_EMPLOYEE:
            return {
                ...state,
                currentEmployeeRole: initState.currentEmployeeRole,
            };
        default:
            return state;
    }
};

export default employeeReducer;
