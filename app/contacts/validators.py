import csv
import logging
from collections.abc import Callable, Iterable
from typing import Any, cast

import charset_normalizer
import trafaret as t
from aiohttp import web
from aiohttp.web_request import FileField
from trafaret_validator import TrafaretValidator

from api.errors import (
    AccessDenied,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    Object,
)
from api.graph.exceptions import GraphQLValidationError
from app.auth.types import User
from app.config.schemas import UploadsConfig
from app.contacts.constants import ACCOUNTANT_EMAIL_KEY
from app.contacts.db import (
    select_contact,
    select_contact_person,
    select_contact_person_by_id,
    select_contacts_by_ids,
)
from app.contacts.types import (
    Contact,
    ContactUploadOptions,
    CreateContactPersonOptions,
    UpdateContactPersonOptions,
    UploadContactCtx,
    UploadContactCtxDataCompany,
    UploadContactCtxDataEmail,
    UploadContactCtxDataPhone,
)
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.helpers import get_file_extension
from app.lib.types import (
    DataDict,
    DataMapping,
)
from app.lib.validators import is_valid_email
from app.lib.xlsx import (
    XLSXReader,
    cell_to_string,
)
from app.uploads.constants import MB

logger = logging.getLogger(__name__)


class UploadContactValidator(TrafaretValidator):
    edrpou = validators.EDRPOU()
    email = validators.email() | t.String(allow_blank=True, max_length=0)
    company_name = t.String(allow_blank=True)
    company_short_name = t.String(allow_blank=True)
    phone = t.String(allow_blank=True)
    first_name = t.String(allow_blank=True)
    second_name = t.String(allow_blank=True)
    last_name = t.String(allow_blank=True)
    main_recipient = t.Bool()


class BaseContactPersonValidator(TrafaretValidator):
    edrpou = validators.EDRPOU()
    first_name = t.String(allow_blank=True) | t.Null()
    second_name = t.String(allow_blank=True) | t.Null()
    last_name = t.String(allow_blank=True) | t.Null()
    main_recipient = validators.boolean(nullable=True)
    phone = validators.phone(allow_blank=True)


class CreateContactPersonValidator(BaseContactPersonValidator):
    email = validators.email(nullable=True)


class UpdateContactPersonValidator(BaseContactPersonValidator):
    contact_person_id = validators.UUID()
    email = validators.email() | t.Null()
    is_email_hidden = t.Bool()


class ContactValidator(TrafaretValidator):
    company_name = t.String(allow_blank=True) | t.Null()
    company_short_name = t.String(allow_blank=True) | t.Null()


class DownloadYouControlValidator(TrafaretValidator):
    code = validators.EDRPOU() | validators.IPN() | validators.PassportCode()


def validate_api_contact(contact: Any) -> DataDict | None:
    if not isinstance(contact, dict):
        return None

    edrpou = str(contact.get('edrpou', ''))
    if not validators.validate_edrpou(edrpou):
        return None

    email_trafaret = validators.email()
    try:
        email_trafaret.check(contact.get(ACCOUNTANT_EMAIL_KEY))
    except t.DataError:
        contact[ACCOUNTANT_EMAIL_KEY] = None

    valid_users = []
    for item in contact['users']:
        try:
            validators.email().check(item['email'])
        except t.DataError:
            pass
        else:
            valid_users.append(item)

    if not valid_users:
        return None

    # Ensure EDRPOU value is valid and type is string
    contact['edrpou'] = edrpou
    contact['users'] = valid_users
    return contact


def contact_read_xlsx(valid_file: FileField, user: User) -> Iterable[list[str]]:
    file_content = valid_file.file.read()

    reader = XLSXReader(file=file_content)
    return [[cell_to_string(cell.value) for cell in row] for row in reader]


def contact_read_csv(valid_file: FileField, user: User) -> Iterable[list[str]]:
    file_content = valid_file.file.read()

    encoding = charset_normalizer.detect(file_content)['encoding']
    encoding = cast(str, encoding)
    if not encoding:
        logger.warning(
            'Unhandled contacts CSV file encoding',
            extra={
                'email': user.email,
                'edrpou': user.company_edrpou,
                'file_name': valid_file.filename,
                'file_size_bytes': len(file_content),
                'count_rows': len(file_content.splitlines()),
            },
        )
        raise Error(Code.unknown_encoding)

    # Define delimiter
    data = file_content.decode(encoding)
    delimiter = ','
    if data.count(';') > data.count(','):
        delimiter = ';'

    csv_reader = csv.reader(data.splitlines(), delimiter=delimiter)
    return csv_reader


def validate_contacts_file(
    valid_file: FileField,
    user: User,
    reader: Callable[[FileField, User], Iterable[list[str]]],
) -> UploadContactCtx:
    user_company_id = user.company_id

    # Counters for feedback
    count_rows = 0
    count_invalid_rows = 0
    empty_rows = 0
    invalid_row_numbers = []
    has_header = False

    # Valid data
    companies: dict[str, UploadContactCtxDataCompany] = {}
    emails: dict[tuple[str, str], UploadContactCtxDataEmail] = {}
    phones: dict[tuple[str, str, str], UploadContactCtxDataPhone] = {}

    contacts_with_main_recipient = set()

    for count_rows, row in enumerate(reader(valid_file, user), 1):
        # Ignore empty lines, generated on Mac during Excel -> CSV conversion.
        if not any(row):
            empty_rows += 1
            continue

        if len(row) < 2:
            count_invalid_rows += 1
            invalid_row_numbers.append(count_rows)
            continue

        # skip first row if edrpou value is header
        if count_rows == 1 and validators.int_or_none(row[0]) is None:
            has_header = True
            continue

        row_dict = dict(enumerate(row))
        validator = UploadContactValidator(
            **{
                'edrpou': row_dict[0],
                'email': row_dict.get(1, ''),
                'company_name': row_dict.get(2, ''),
                'company_short_name': row_dict.get(3, ''),
                'phone': row_dict.get(4, ''),
                'first_name': row_dict.get(5, ''),
                'last_name': row_dict.get(6, ''),
                'second_name': row_dict.get(7, ''),
                'main_recipient': row_dict.get(8) == '1',
            }
        )
        if not validator.validate():
            count_invalid_rows += 1
            invalid_row_numbers.append(count_rows)
            continue

        valid_contact = validator.data
        edrpou = valid_contact['edrpou'].lower()
        email = valid_contact['email'].lower()
        phone = valid_contact['phone']
        if not is_valid_email(email):
            email = ''

        # Ignore duplicates in file
        if edrpou not in companies:
            companies[edrpou] = {
                'company_id': user_company_id,
                'edrpou': edrpou,
                'name': valid_contact['company_name'],
                'short_name': valid_contact['company_short_name'],
            }

        main_recipient = valid_contact['main_recipient']
        if main_recipient:
            contacts_with_main_recipient.add(edrpou)

        email_key = (edrpou, email)
        if email and email_key not in emails:
            emails[email_key] = {
                'edrpou': edrpou,
                'email': email,
                'first_name': valid_contact['first_name'],
                'second_name': valid_contact['second_name'],
                'last_name': valid_contact['last_name'],
                'main_recipient': main_recipient,
            }

        phone_key = (edrpou, email, phone)
        if email and phone_key not in phones:
            phones[phone_key] = {'phone': phone, 'email': email, 'edrpou': edrpou}

    if not companies:
        raise Error(Code.empty_upload_csv)

    return UploadContactCtx(
        data={
            'companies': list(companies.values()),
            'emails': list(emails.values()),
            'phones': list(phones.values()),
        },
        counters={
            'rows_total': count_rows - has_header - empty_rows,
            'rows_invalid': count_invalid_rows,
            'invalid_row_numbers': invalid_row_numbers,
        },
        contacts_with_main_recipient=contacts_with_main_recipient,
    )


def validate_upload(
    data: DataMapping,
    config: UploadsConfig,
    content_length: int,
    user: User,
) -> UploadContactCtx:
    """Ensure input data is a file with allowed extension and size."""
    options = ContactUploadOptions(set(config.allowed_extensions), config.max_file_size)
    file_item = data.get('file')

    if not (
        isinstance(file_item, FileField)
        and get_file_extension(file_item.filename) in options.allowed_extensions
    ):
        raise Error(
            Code.invalid_file_extension,
            details={'allowed_extensions': ', '.join(options.allowed_extensions)},
        )

    if content_length / MB > options.max_file_size:
        raise Error(Code.max_file_size, details={'max_file_size': options.max_file_size})

    extension = get_file_extension(file_item.filename)
    if extension == '.xlsx':
        reader = contact_read_xlsx
    elif extension == '.csv':
        reader = contact_read_csv
    else:
        raise NotImplementedError

    return validate_contacts_file(file_item, user, reader)


async def validate_edit_contact_person(
    conn: DBConnection, raw_data: DataDict, user: User
) -> UpdateContactPersonOptions:
    """
    Validate contact person data. If email is hidden, than email can be
    None and can't be update to this value.
    """
    data = validators.validate(UpdateContactPersonValidator, raw_data)

    if data['is_email_hidden'] is False and not data['email']:
        raise InvalidRequest(reason=_('Поле email не може бути пустим.'))

    contact_person_id = data['contact_person_id']
    contact_person = await select_contact_person_by_id(
        conn=conn, contact_person_id=contact_person_id, with_contact=True
    )
    if not contact_person:
        raise DoesNotExist(Object.contact_person, id=contact_person_id)

    if contact_person.company_id != user.company_id:
        raise AccessDenied(reason=_('Контактна особа не належить поточній компанії'))

    return UpdateContactPersonOptions(
        contact_person_id=data['contact_person_id'],
        edrpou=data['edrpou'],
        email=data['email'],
        is_email_hidden=data['is_email_hidden'],
        first_name=data['first_name'],
        second_name=data['second_name'],
        last_name=data['last_name'],
        main_recipient=data['main_recipient'],
        phone=data['phone'],
    )


async def validate_create_contact_person(
    conn: DBConnection, user: User, raw_data: DataDict
) -> CreateContactPersonOptions:
    data = validators.validate(CreateContactPersonValidator, raw_data)
    edrpou: str = data['edrpou']

    if data['email']:
        contact_person = await select_contact_person(
            conn=conn,
            email=data['email'],
            edrpou=data['edrpou'],
            company_id=user.company_id,
        )
        if contact_person:
            reason = _('Контакт з таким email і ЄДРПОУ вже існує у Вас у контактах.')
            raise InvalidRequest(reason=reason)

    contact = await select_contact(conn, contact_edrpou=edrpou, company_id=user.company_id)

    return CreateContactPersonOptions(
        edrpou=data['edrpou'],
        email=data['email'],
        first_name=data['first_name'],
        second_name=data['second_name'],
        last_name=data['last_name'],
        main_recipient=data['main_recipient'],
        phone=data['phone'],
        is_new_contact_company=contact is None,
    )


async def validate_contacts_exists(
    conn: DBConnection, contacts_ids: list[str], company_id: str
) -> list[Contact]:
    contacts = await select_contacts_by_ids(
        conn=conn, contacts_ids=contacts_ids, company_id=company_id
    )
    selected_contacts_ids = {contact.id for contact in contacts}
    expected_contacts_ids = set(contacts_ids)

    if expected_contacts_ids != selected_contacts_ids:
        # return with error which contacts ids not exists or user not have access
        missing_contacts_ids = expected_contacts_ids - selected_contacts_ids
        raise DoesNotExist(Object.contact, contacts_ids=list(missing_contacts_ids))

    return contacts


async def validate_get_youcontrol_contact_url(request: web.Request, user: User) -> str:
    raw_data = {'code': request.rel_url.query.get('code')}
    data = validators.validate(DownloadYouControlValidator, raw_data)

    return data['code']


def validate_phone(value: str) -> str | None:
    """
    Ensure that the given value is a valid phone number with or without the "+" prefix.
    This is not an actual validator as it does not raise an exception but suppresses it.
    """
    for val in (value, f'+{value}'):
        if phone := pv.PhoneOrNoneAdapter.validate_python(val):
            return phone
    return None


def validate_es_search_window_limit(
    limit: int | None,
    offset: int | None,
) -> None:
    """Validate that the given limit and offset are within the allowed range."""
    from app.contacts.es import ELASTIC_MAX_SEARCH_WINDOW_LIMIT

    # limit 0 causes exception in ES query (bucket_sort "size" field)
    if limit is not None and limit <= 0:
        raise GraphQLValidationError(
            errors=['Query limit must be greater than 0'],
        )

    if offset is not None and offset < 0:
        raise GraphQLValidationError(
            errors=['Query offset must be greater than or equal to 0'],
        )

    if (
        limit is not None
        and offset is not None
        and limit + offset > ELASTIC_MAX_SEARCH_WINDOW_LIMIT
    ):
        raise GraphQLValidationError(
            errors=[f'Query offset + limit must be less than {ELASTIC_MAX_SEARCH_WINDOW_LIMIT}'],
        )
