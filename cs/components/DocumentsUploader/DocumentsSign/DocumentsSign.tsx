import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FlexBox, Spinner, Text, Title } from '@vchasno/ui-kit';

import KepSignButton from 'components/KepSigner/KepSignButton';
import { MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT } from 'components/KepSigner/constants';
import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import actionCreators from 'components/document/documentActionCreators';
import actionListCreators from 'components/documentList/documentListActionCreators';
import signPopupActions from 'components/signPopup/signPopupActions';
import { isTovCompany } from 'lib/helpers';
import {
    getCurrentCompanyEdrpou,
    getCurrentUser,
} from 'selectors/app.selectors';
import { getSignAfterUploadDocumentIdList } from 'selectors/uploader.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { getDocuments } from 'services/documents/api';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import Button from 'ui/button/button';

const DocumentsSign: React.FC = () => {
    const dispatch = useDispatch();
    const documentIdListForSign = useSelector(getSignAfterUploadDocumentIdList);
    const edrpou = useSelector(getCurrentCompanyEdrpou);
    const currentUser = useSelector(getCurrentUser);
    const isNewKepFlowEnabled = useSelector(
        signWithKepFlowSelectors.selectIsFlowEnabled,
    );

    const isMultiSign = documentIdListForSign.length > 1;
    const totalDocumentsCount = documentIdListForSign?.length;
    const isDisallowToSignOverflowMaxLimit =
        totalDocumentsCount > MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT;

    const handleSignDIIA = async () => {
        await dispatch(
            actionCreators.onLoadDocument(documentIdListForSign[0], false),
        );
        await dispatch(
            actionCreators.onSign({
                isDiia: true,
            }),
        );
    };

    const handleSignDocuments = async (
        payload: { isKep?: boolean; isKepNew?: boolean } = {
            isKep: false,
            isKepNew: false,
        },
    ) => {
        const { documents } = await getDocuments(
            {
                ids: documentIdListForSign,
            },
            currentUser,
        );

        if (payload.isKepNew) {
            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.SIGN_AFTER_UPLOAD,
            });
        }

        await dispatch(
            actionListCreators.onSignDocuments(documents, {
                isKep: payload.isKep,
                isKepNew: payload.isKepNew,
            }),
        );
        // приховуємо попап про підтвердження підписання версійного документу
        // припускаємо що користувач обрав версійний внутрішній докумиент і хоче його одразу підписати
        dispatch({
            type: signPopupActions.SIGN_POPUP__HIDE_VERSIONS_POPUPS,
        });
    };

    if (documentIdListForSign && documentIdListForSign.length === 0) {
        return (
            <FlexBox justify="center" wrap="wrap" align="center">
                <Spinner width={50} color="var(--blue-border)" />
                <Text>{t`Документи у процесі завантаження...`}</Text>
            </FlexBox>
        );
    }

    if (isNewKepFlowEnabled) {
        return (
            <FlexBox direction="column" gap={30}>
                <Alert theme="success">{t`Документи успішно завантажені!`}</Alert>
                <Title level={2}>{t`Оберіть спосіб підписання`}</Title>
                <FlexBox direction="column">
                    <KepSignButton
                        width="full"
                        onSign={() => handleSignDocuments({ isKepNew: true })}
                        buttonTitle={t`Підписати Вчасно.КЕП`}
                    />
                    <Button
                        onClick={() => handleSignDocuments()}
                        theme="blue"
                        typeContour
                        disabled={isDisallowToSignOverflowMaxLimit}
                    >{t`Інший спосіб підписання`}</Button>
                </FlexBox>
            </FlexBox>
        );
    }

    return (
        <FlexBox direction="column" gap={30}>
            <Alert theme="success">{t`Документи успішно завантажені!`}</Alert>
            {isDisallowToSignOverflowMaxLimit && (
                <Alert theme="error">{t`При завантаженні можна підписувати не більше 250 документів!`}</Alert>
            )}
            <Title level={2}>{t`Оберіть спосіб підписання`}</Title>
            <FlexBox direction="column">
                {!isDisallowToSignOverflowMaxLimit && (
                    <KepSignButton
                        width="full"
                        onSign={() => handleSignDocuments({ isKep: true })}
                        buttonTitle={t`Підписати Вчасно.КЕП`}
                    />
                )}
                {!isTovCompany(edrpou) && !isMultiSign && (
                    <Button
                        onClick={handleSignDIIA}
                        theme="darkGray"
                    >{t`Дія.Підпис`}</Button>
                )}

                <Button
                    onClick={() => handleSignDocuments()}
                    theme="blue"
                    typeContour
                    disabled={isDisallowToSignOverflowMaxLimit}
                >{t`Інший спосіб підписання`}</Button>
            </FlexBox>
        </FlexBox>
    );
};

export default DocumentsSign;
