import { expect } from 'chai';

import { Actor, DocumentsUploadForm } from '../../../DocumentsUploader/types';

import { composePayloadFromDocumentsUploadForm } from '../../uploadUtils';

const getFormData = (
    options: Partial<DocumentsUploadForm> = {},
): DocumentsUploadForm => ({
    counterparties: [],
    employeesSigners: [],
    employeesReviewers: [],
    employeesViewers: [],
    isInternal: false,
    isParallel: false,
    title: '',
    amount: null,
    date: null,
    category: '',
    number: '',
    comment: '',
    isVersioned: false,
    templateId: null,
    selectedTags: [],
    files: [],
    employeesSignIsOrdered: false,
    employeesReviewersIsOrdered: false,
    employeesIsSignAfterReview: false,
    companyEdrpou: '',
    isPayedRate: false,
    documentParameters: [],
    maxTotalCount: 10,
    maxFileSize: 10,
    maxTotalSize: 10,
    dataAI: null,
    accessLevel: 'extended',
    ...options,
});

const getCounterparty = (
    options: Partial<DocumentsUploadForm['counterparties'][0]> = {},
): DocumentsUploadForm['counterparties'][0] => ({
    edrpou: '12345678',
    email: '<EMAIL>',
    name: 'Test',
    shouldSign: true,
    isEmailHidden: false,
    ...options,
});
describe('cs/components/uploader/uploadUtils.ts', () => {
    describe('#composePayloadFromDocumentsUploadForm', () => {
        it('should return default payload', () => {
            expect(composePayloadFromDocumentsUploadForm(getFormData())).to.eql(
                {
                    access_settings: {
                        level: 'extended',
                    },
                },
            );
        });

        it('should send template_id param if it exists', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        templateId: '123',
                    }),
                ),
            ).to.deep.include({
                template_id: '123',
            });

            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        templateId: null,
                    }),
                ),
            ).to.not.has.property('template_id');
        });

        it('should return title payload', () => {
            const title = 'test title';
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        title,
                    }),
                ),
            ).to.deep.include({ title });
        });

        it('should return category payload', () => {
            const category = '1';
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        category,
                    }),
                ),
            ).to.deep.include({ category });
        });

        it('should return doc_number payload', () => {
            const number = '123';
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        number,
                    }),
                ),
            ).to.deep.include({ doc_number: number });
        });

        it('should return date_document payload', () => {
            const date = new Date();
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        date,
                    }),
                ),
            ).to.deep.include({ date_document: date.toISOString() });
        });

        it('should return amount payload', () => {
            const amount = 123;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        amount,
                    }),
                ),
            ).to.deep.include({ amount: '12300' });
        });

        it('should return amount payload with 0', () => {
            const amount = 0;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        amount,
                    }),
                ),
            ).to.deep.include({ amount: '0' });
        });

        it('should return float amount payload', () => {
            const amount = 123.455;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        amount,
                    }),
                ),
            ).to.deep.include({ amount: '12346' });
        });

        it('should add viewer recipient correct', () => {
            const counterparty = getCounterparty({
                shouldSign: false,
            });
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        counterparties: [counterparty],
                    }),
                ),
            ).to.deep.include({
                recipients: {
                    is_ordered: true,
                    recipients: [
                        {
                            role: 'viewer',
                            edrpou: counterparty.edrpou,
                            emails: [counterparty.email],
                            is_email_hidden: counterparty.isEmailHidden,
                        },
                    ],
                },
            });
        });

        it('should add recipient with hidden email', () => {
            const counterparty = getCounterparty({
                isEmailHidden: true,
            });
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        counterparties: [counterparty],
                    }),
                ),
            ).to.deep.include({
                recipients: {
                    is_ordered: true,
                    recipients: [
                        {
                            role: 'signer',
                            edrpou: counterparty.edrpou,
                            emails: null,
                            is_email_hidden: counterparty.isEmailHidden,
                        },
                    ],
                },
            });
        });

        it('should add signer recipient correct', () => {
            const counterparty = getCounterparty({
                edrpou: '12345678',
            });
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        companyEdrpou: counterparty.edrpou,
                        counterparties: [counterparty],
                    }),
                ),
            ).to.deep.include({
                recipients: {
                    is_ordered: true,
                    recipients: [
                        {
                            role: 'signer',
                            edrpou: counterparty.edrpou,
                            emails: [counterparty.email],
                            is_email_hidden: counterparty.isEmailHidden,
                        },
                    ],
                },
            });
        });

        it('should return recipients payload with is_ordered = true, when less then 2 counterparties', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isParallel: true,
                        counterparties: [getCounterparty()],
                    }),
                ).recipients?.is_ordered,
            ).to.eql(true);
        });

        it('should return recipients payload with is_ordered = false', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isParallel: true,
                        counterparties: [
                            getCounterparty(),
                            getCounterparty(),
                            getCounterparty(),
                        ],
                    }),
                ).recipients?.is_ordered,
            ).to.eql(false);
        });

        it('should return signers with order mapped payload', () => {
            const signer = {
                id: '123',
                type: 'group',
            } as Actor;
            const assertData = composePayloadFromDocumentsUploadForm(
                getFormData({
                    employeesSigners: [signer],
                }),
            );
            expect(assertData).to.deep.include({
                signer_parameters: {
                    signers: [{ value: signer.id, signer_type: signer.type }],
                },
            });

            expect(assertData).to.not.has.property('parallel_signing');
        });

        it('should return signers with parallel_signing mapped payload', () => {
            const signer = {
                id: '123',
                type: 'role',
            } as Actor;

            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesSigners: [signer, signer],
                        employeesSignIsOrdered: true,
                    }),
                ),
            ).to.deep.include({
                signer_parameters: {
                    signers: [
                        { value: signer.id, signer_type: signer.type },
                        { value: signer.id, signer_type: signer.type },
                    ],
                },
                parallel_signing: false,
            });
        });

        it('should return reviewers mapped payload', () => {
            const reviewer = {
                id: '123',
                type: 'group',
            } as Actor;
            const assertData = composePayloadFromDocumentsUploadForm(
                getFormData({
                    employeesReviewers: [reviewer],
                }),
            );
            expect(assertData).to.deep.include({
                reviewers: [{ id: reviewer.id, type: reviewer.type }],
            });
            expect(assertData).to.not.has.property('parallel_review');
        });

        it('should return reviewers with parallel_review mapped payload', () => {
            const reviewer = {
                id: '123',
                type: 'role',
            } as Actor;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesReviewers: [reviewer, reviewer],
                        employeesReviewersIsOrdered: true,
                    }),
                ),
            ).to.deep.include({
                reviewers: [
                    { id: reviewer.id, type: reviewer.type },
                    { id: reviewer.id, type: reviewer.type },
                ],
                parallel_review: false,
            });
        });

        it('should return viewers mapped payload - only role actors', () => {
            const viewer = {
                id: '123',
                type: 'role',
            } as Actor;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesViewers: [viewer],
                    }),
                ),
            ).to.deep.include({
                share_to: [viewer.id],
            });
        });

        it('should return viewers mapped payload - only group actors', () => {
            const viewer = {
                id: '123',
                type: 'group',
            } as Actor;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesViewers: [viewer],
                    }),
                ),
            ).to.deep.include({
                share_to_groups: [viewer.id],
            });
        });

        it('should return viewers mapped payload - group and role actors', () => {
            const roleViewer = {
                id: '122',
                type: 'role',
            } as Actor;
            const groupViewer = {
                id: '123',
                type: 'group',
            } as Actor;
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesViewers: [roleViewer, groupViewer],
                    }),
                ),
            ).to.deep.include({
                share_to: [roleViewer.id],
                share_to_groups: [groupViewer.id],
            });
        });

        it('should return comment mapped payload', () => {
            const comment = 'test comment';
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        comment,
                    }),
                ),
            ).to.deep.include({
                comment: {
                    text: comment,
                },
            });
        });

        it('should prepare tags payload correct', () => {
            const tags = [
                { id: '1', isNew: false },
                { id: '', isNew: true, name: 'new tag' },
            ] as DocumentsUploadForm['selectedTags'];
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        selectedTags: tags,
                    }),
                ),
            ).to.deep.include({
                tags: ['1'],
                new_tags: ['new tag'],
            });
        });

        it('should return is_required_review payload', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesReviewers: [
                            { id: '1', type: 'role' } as Actor,
                        ],
                        employeesSigners: [{ id: '2', type: 'role' } as Actor],
                        employeesIsSignAfterReview: true,
                    }),
                ),
            ).to.deep.include({
                is_required_review: true,
            });
        });

        it('should avoid is_required_review if no reviewers passed to payload', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesSigners: [{ id: '2', type: 'role' } as Actor],
                        employeesIsSignAfterReview: true,
                    }),
                ),
            ).to.not.has.property('is_required_review');
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        employeesReviewers: [
                            { id: '2', type: 'role' } as Actor,
                        ],
                        employeesIsSignAfterReview: true,
                    }),
                ),
            ).to.has.property('is_required_review');
        });

        it('should avoid isVersioned payload if there no two recipients', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isVersioned: true,
                    }),
                ),
            ).to.not.has.property('is_versioned');
        });

        it('should return isVersioned payload if exists 2 counterparties', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isVersioned: true,
                        counterparties: [getCounterparty(), getCounterparty()],
                    }),
                ),
            ).to.deep.include({
                is_versioned: true,
            });
        });

        it('should return isVersioned payload if it is internal documents', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isVersioned: true,
                        isInternal: true,
                    }),
                ),
            ).to.deep.include({
                is_versioned: true,
            });
        });

        it('should return isInternal payload', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isInternal: true,
                    }),
                ),
            ).to.deep.include({
                is_internal: true,
            });
        });

        it('should not send recipients if is_internal', () => {
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        isInternal: true,
                        counterparties: [getCounterparty()],
                    }),
                ),
            ).to.not.has.property('recipients');
        });

        it('should return document params (text field) to payload', () => {
            const additionalFields = {
                id: '123',
                type: 'text',
                value: 'test',
                isRequired: true,
            } as DocumentsUploadForm['documentParameters'][number];
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        documentParameters: [additionalFields],
                    }),
                ),
            ).to.deep.include({
                parameters: {
                    parameters: [
                        {
                            field_id: additionalFields.id,
                            value: additionalFields.value,
                            is_required: additionalFields.isRequired,
                        },
                    ],
                },
            });
        });

        it('should return document params (number field) to payload', () => {
            const additionalFields = {
                id: '123',
                type: 'number',
                value: 123,
                isRequired: true,
            } as DocumentsUploadForm['documentParameters'][number];
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        documentParameters: [additionalFields],
                    }),
                ),
            ).to.deep.include({
                parameters: {
                    parameters: [
                        {
                            field_id: additionalFields.id,
                            value: '123',
                            is_required: additionalFields.isRequired,
                        },
                    ],
                },
            });
        });

        it('should return document params (date field) to payload', () => {
            const value = new Date();
            const additionalFields = {
                id: '123',
                type: 'date',
                value,
                isRequired: true,
            } as DocumentsUploadForm['documentParameters'][number];
            expect(
                composePayloadFromDocumentsUploadForm(
                    getFormData({
                        documentParameters: [additionalFields],
                    }),
                ),
            ).to.deep.include({
                parameters: {
                    parameters: [
                        {
                            field_id: additionalFields.id,
                            value: value.toISOString(),
                            is_required: additionalFields.isRequired,
                        },
                    ],
                },
            });
        });
    });
});
