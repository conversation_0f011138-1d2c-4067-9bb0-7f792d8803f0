import { useState } from 'react';
import { useSelector } from 'react-redux';

import { UUID_PATTERN } from 'lib/constants';
import { getCurrentUser } from 'selectors/app.selectors';
import { addLinkGroup, getDocument } from 'services/documents/api';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import { Dispatch } from '../../types';
import { Nullable } from '../../types/general';
import { IUser } from '../../types/user';

import loggerService from '../../services/logger';

const UUID_PATTERN_STRING = UUID_PATTERN.source.slice(1, -1);
const DOCUMENT_URL_PATTERN = new RegExp(
    `.*documents/(${UUID_PATTERN_STRING}).*`,
);
const DOCUMENT_NOT_FOUND_ERROR = t`Документ не знайдено. Перевірте правильність написання лінку або id документа`;

export const useDocumentsState = (documents?: Document[]) => {
    return useState<Document[]>(documents || []);
};

export const useParentDocState = () => {
    return useState<Document | undefined>();
};

export const composeErrorMetaMessage = (error: object): string => {
    if ('reason' in error) {
        if (error.reason === 'Доступ до деяких документів заборонено') {
            return t`Один із обраних документів ймовірно видалений. Оновіть сторінку та повторіть повʼязування заново.`;
        }

        return error.reason as string;
    }

    return t`Сталася невідома помилка. Оновіть сторінку та повторіть повʼязування заново.`;
};

export const useAddDocumentChildrenRequest = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    const submit = async (parent: Document, documents: Document[]) => {
        if (!parent) {
            setError(t`Вкажіть головний документ`);
            throw Error('No parent document');
        }
        setError('');

        // Remove parent document from list of child documents
        const docs = documents.filter((doc) => doc.id !== parent.id);
        const childrenIds: string[] = [];
        docs.forEach((item) => {
            childrenIds.push(item.id);
        });

        setIsLoading(true);
        try {
            await addLinkGroup(parent.id, childrenIds);
        } catch (e) {
            setError(composeErrorMetaMessage(e));
            setIsLoading(false);
            throw e;
        }
        setIsLoading(false);
    };

    return { isLoading, error, submit };
};

export const getDocumentIdFromUrl = (value: string): Nullable<string> => {
    // simplest case when provided string is already valid ID
    if (UUID_PATTERN.test(value)) {
        return value;
    }
    const found = value.match(DOCUMENT_URL_PATTERN);
    if (found && found.length === 2) {
        return found[1];
    }
    return null;
};

export const getLinkedDocumentFromURL = async (
    documentUrl: string,
    currentUser: IUser,
): Promise<Nullable<Document>> => {
    const docId = getDocumentIdFromUrl(documentUrl);
    if (!docId) {
        loggerService.log('Can not find document id from url', { documentUrl });
        return null;
    }
    const doc: Document | undefined = await getDocument(docId, currentUser);
    if (!doc) {
        loggerService.log('Can not find document by id from url', {
            documentUrl,
            docId,
        });
        return null;
    }
    return {
        ...doc,
    };
};

export const useGetDocumentByURLRequest = (setError: Dispatch<string>) => {
    const [isLoading, setIsLoading] = useState(false);
    const currentUser = useSelector(getCurrentUser);

    const getDoc = async (value: string): Promise<Document | undefined> => {
        setIsLoading(true);
        let doc: Nullable<Document> = null;
        try {
            doc = await getLinkedDocumentFromURL(value, currentUser);
        } catch (e) {
            loggerService.log('Unhandled error on getting document from url', {
                inputValue: value,
            });
        }
        setIsLoading(false);
        if (!doc) {
            setError(DOCUMENT_NOT_FOUND_ERROR);
            return undefined;
        }
        return doc;
    };

    return { isLoading, getDoc };
};
