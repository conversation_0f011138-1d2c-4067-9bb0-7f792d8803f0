import React, { FC, useEffect } from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import CompanyArchiveSettings from 'components/companyCard/components/CompanyArchiveSettings';
import CompanyVersionSettings from 'components/companyCard/components/CompanyVersionSettings';
import { useBindActionCreators } from 'hooks/useBindActionCreators';
import {
    getCurrentCompany,
    getCurrentCompanyDefaultRolePermissionKey,
    getCurrentUser,
    getCurrentUserRole,
} from 'selectors/app.selectors';
import { t } from 'ttag';

import appActionCreators from '../../../app/appActionCreators';
import { fetchRequiredDocuments } from '../../../documentSettings/documentSettingsActionCreators';
import companyCardActionCreators from '../../companyCardActionCreators';

import Card from '../../../ui/card/card';

import AntivirusConfig from '../../../AntivirusConfig/AntivirusConfig';
import CompanyHistoryCard from '../../../CompanyHistoryCard/CompanyHistoryCard';
import UnregisteredDocumentViewConfig from '../../../UnregisteredDocumentViewConfig/UnregisteredDocumentViewConfig';
import CompanyAdditionalConfig from '../../../companyAdditionalConfig/companyAdditionalConfig';
import CompanyGeneralInfoForm from '../../../companyGeneralInfoForm/CompanyGeneralInfoForm';
import CompanyRolePermission from '../../../companyRolePermission/companyRolePermission';
import DefaultRecipient from '../../../defaultRecipient/defaultRecipient';
import RenderReviewConfig from '../../../renderReviewConfig/renderReviewConfig';
import RenderSignatureConfig from '../../../renderSignatureConfig/renderSignatureConfig';

import css from '../../companyCard.css';

const CompanyMainSettings: FC = () => {
    const currentCompany = useSelector(getCurrentCompany);
    const currentUser = useSelector(getCurrentUser);
    const currentCompanyRole = useSelector(getCurrentUserRole);
    const companyRolePermissions = useSelector(
        getCurrentCompanyDefaultRolePermissionKey,
    );

    const canEditCompany =
        currentCompanyRole &&
        (currentCompanyRole.isAdmin || currentCompanyRole.canEditCompany);

    const ACTIONS = {
        onAlertPopupShow: appActionCreators.onAlertPopupShow,
        onConfirmPopupShow: appActionCreators.onConfirmPopupShow,
        onFetchCompanyDocumentFields:
            appActionCreators.onFetchCompanyDocumentFields,
        onRefreshCurrentUser: appActionCreators.onRefreshCurrentUser,
        onChangeAdditionalConfig:
            companyCardActionCreators.onChangeAdditionalConfig,
        onChangeConfig: companyCardActionCreators.onChangeConfig,
        onSubmitGeneralInfo: companyCardActionCreators.onSubmitGeneralInfo,
        onChangeRolePermissions:
            companyCardActionCreators.onChangeRolePermissions,
        onMount: fetchRequiredDocuments,
    };
    const actions = useBindActionCreators<typeof ACTIONS>(ACTIONS);

    const companyConfig = {
        renderSignatureInInterface: currentCompany.renderSignatureInInterface,
        renderSignatureAtPage: currentCompany.config?.render_signature_at_page,
        renderSignatureOnPrintDocument:
            currentCompany.renderSignatureOnPrintDocument,
        renderReviewInInterface:
            currentCompany.config?.render_review_in_interface,
        renderReviewOnPrintDocument:
            currentCompany.config?.render_review_on_print_document,
        allowUnregisteredDocumentView:
            currentCompany.config?.allow_unregistered_document_view,
        renderReviewConfig: {
            renderFullName:
                currentCompany.config?.review_render_config?.render_full_name ??
                false,
            renderEmail:
                currentCompany.config?.review_render_config?.render_email ??
                false,
            renderPosition:
                currentCompany.config?.review_render_config?.render_position ??
                false,
        },
    };

    const onSubmitGeneralInfoHandler = (data: { phone: string }) => {
        actions.onSubmitGeneralInfo(data);
    };

    useEffect(() => {
        actions.onMount();
    }, []);

    return (
        <>
            <Card
                title={t`Загальна інформація`}
                titleStyles={css.cardTitle}
                dataQa="qa_general_information"
                wideContent
            >
                <CompanyGeneralInfoForm
                    edrpou={currentCompany.edrpou}
                    name={currentCompany.name}
                    phone={currentCompany.phone}
                    onSubmit={onSubmitGeneralInfoHandler}
                />
            </Card>
            {companyRolePermissions && (
                <Card
                    title={t`Налаштування прав для нових співробітників`}
                    titleStyles={css.cardTitle}
                    dataQa="qa_rights_new_employees"
                    wideContent
                >
                    <CompanyRolePermission
                        permissions={companyRolePermissions}
                        onChangePermissions={actions.onChangeRolePermissions}
                        disabled={!canEditCompany}
                    />
                </Card>
            )}
            <Card
                title={t`Додаткові налаштування`}
                titleStyles={css.cardTitle}
                dataQa="qa_advanced_settings"
                wideContent
            >
                <CompanyAdditionalConfig
                    additionalConfigs={currentCompany.config}
                    onChangeAdditionalConfig={actions.onChangeAdditionalConfig}
                    disabled={!canEditCompany}
                />
            </Card>
            {currentUser.currentRole.isAdmin && (
                <>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування прав для перегляду документів без реєстрації`}
                    >
                        <UnregisteredDocumentViewConfig
                            config={companyConfig}
                            onChangeConfig={actions.onChangeAdditionalConfig}
                        />
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Співробітник, який отримує документи за замовчуванням`}
                    >
                        <DefaultRecipient
                            companyId={currentCompany.id}
                            className={cn(css.cardContent, css.hintText)}
                        />
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування відображення графічних печаток на PDF документах`}
                    >
                        <div
                            className={cn(css.cardContent, css.cardContentWide)}
                        >
                            <RenderSignatureConfig
                                config={companyConfig}
                                onChangeConfig={actions.onChangeConfig}
                            />
                        </div>
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування відображення графічних відміток погодження на документах`}
                    >
                        <div className={css.cardContent}>
                            <RenderReviewConfig
                                config={companyConfig}
                                onChangeConfig={
                                    actions.onChangeAdditionalConfig
                                }
                            />
                        </div>
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування завантаження документів`}
                    >
                        <div className={css.cardContent}>
                            <AntivirusConfig />
                        </div>
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування роботи з версіями`}
                    >
                        <div className={css.cardContent}>
                            <CompanyVersionSettings />
                        </div>
                    </Card>
                    <Card
                        titleStyles={css.cardTitle}
                        wideContent
                        title={t`Налаштування архіву`}
                    >
                        <div className={css.cardContent}>
                            <CompanyArchiveSettings />
                        </div>
                    </Card>
                </>
            )}
            <CompanyHistoryCard />
        </>
    );
};

export default CompanyMainSettings;
