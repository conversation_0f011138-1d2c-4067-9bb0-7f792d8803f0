# Модуль Auth

- [Логи статусу відправки SMS](https://kibana-os-prd.vchasno.com.ua/goto/3188ff3dd532f63fd8260297ce9ff555)
  - [Список можливих статусів](https://developers.gms.net/cpaas/statuses/)

# 2FA

Для автентифікації користувача для web і mobile у нас є такі фактори:

1. Імейл — це може бути імейл + пароль або auth провайдер (Google, Facebook, Apple)
2. Телефон — телефон + OTP код (одноразовий пароль) з Viber/SMS.

Якщо у користувача увімкнено 2FA, то ми пропонуємо йому ввести додатковий додатковий фактор, за<PERSON><PERSON><PERSON><PERSON><PERSON>
від того, який перший фактор він обрав. Якщо перший фактор — це імейл, то ми пропонуємо ввести OTP
код з Viber/SMS. Якщо перший фактор — це телефон, то ми пропонуємо ввести імейл + пароль (в
майбутньому також і auth провайдери).

Нотатка: auth провайдери хоч і виглядають як окремий фактор, але насправді вони є альтернативною
опцією першого фактора (імейлу). Якщо в зловмисника є доступ до Gmail акаунту, то зловмисник не
повинен мати змогу використати активний Google акаунт для входу в систему.

## Як реалізовано 2FA

### Web

Для web API коли користувач пройшов перший фактор автентифікації, ми створюємо сесію в Redis, куку,
яка посилається на цю сесію і поєднуємо сесію з користувачем. Якщо в користувача увімкнено 2FA, то
ми в цьому ж запиті ставимо відмітку `session[VERIFY_2FA_KEY] = true` в сесії користувача. Далі
майже на кожен запит, який потребує автентифікації ми в декораторі `@login_required` перевіряємо чи
немає в сесії користувача цієї відмітки. Якщо відмітка є, то ми не пропускаємо запит далі і
повертаємо помилку.

Якщо користувач натиснув "Запам'ятати мене" при вході, то після другого фактора ми створюємо
додатково куку з підписом `TRUST_2FA_KEY`, яку зберігаємо в браузері користувача. Ця кука при
повторному вході автоматично підтверджує другий фактор автентифікації.

### Mobile

Для мобільного API схема виглядає схожим чином, що і для web, тільки замість сесії користувача ми
зберігаємо відмітку про те, що чи потрібно ще пройти 2FA чи ні у базі в таблиці з refresh токенами і
в redis поряд з access токенами. Для refresh токена це статус `pending` з
`MobileAuthRefreshTokenStatus`, а для access токена це статус `pending` з
`MobileAuthAccessTokenStatus`

### Блокування ролей для 2FA

Для великих компаній у нас є можливість включити в налаштуваннях компанії вимогу 2FA для всіх
співробітників у цій компанії. Якщо в користувача немає телефону, або телефон не підтверджений, або
невалідний, то ми блокуємо роботу з цією компанією і просимо вказати і підтвердити телефон.

Історично склалося, що ми в різних ситуація по різному блокуємо роботу з цією компанією:

- якщо користувача є невалідний телефон, то ми при логіні ставимо статус RoleStatus.blocked_2fa
  замість RoleStatus.active. Оскільки статус не active, то на бекенді ця роль буде
  відфільтровуватися всюди, де ми вимагаємо активну роль.
- якщо користувача немає телефону, то ми не блокуємо роль, а на фронтенді показуємо попап, який не
  можна закрити, з проханням вказати і підтвердити телефон. Після того, як користувач вказав і
  підтвердив телефон, ми пускаємо його далі. Оскільки це блокування тільки на фронтенді, то технічно
  можливо обійти це блокування https://vchasno-group.atlassian.net/browse/DOC-7581
