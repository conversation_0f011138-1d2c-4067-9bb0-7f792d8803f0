import typing as t
from contextlib import asynccontextmanager

from aiopg import Connection as AiopgConnection
from aiopg.sa import Engine, SAConnection, create_engine
from aiopg.sa.result import RowProxy
from aiopg.sa.transaction import Transaction
from aiopg.utils import _ContextManager

DBRow = RowProxy


class DBConnection:
    """
    Custom class to define database connection object within the project.

    Avoid direct driver import, use this class instead.
    """

    def __init__(self, conn: SAConnection) -> None:
        self._conn = conn

    async def execute(self, *args: t.Any, **kwargs: t.Any) -> RowProxy:
        return await self._conn.execute(*args, **kwargs)  # type: ignore

    async def scalar(self, *args: t.Any, **kwargs: t.Any) -> t.Any:
        return await self._conn.scalar(*args, **kwargs)  # type: ignore

    async def close(self) -> None:
        await self._conn.close()  # type: ignore

    @property
    def closed(self) -> None:
        return self._conn.closed

    @property
    def in_transaction(self) -> bool:
        return self._conn.in_transaction

    @asynccontextmanager
    async def begin(self) -> t.AsyncGenerator[Transaction, None]:
        """
        Begin database transaction block.

        This method returns a transaction based on the current state of the connection.

        If the connection is already in a transaction, it returns active transaction object.
        Otherwise, it starts a new standard transaction.

        Returns:
            Transaction: The initiated/existing transaction.

        Example:
            async with conn.begin():
                # Perform database operations within the transaction

            if you want to operate with a transaction object, use following approach
            async with conn.begin() as db_transaction:
                ...
                db_transaction.commit()
                ...
        """

        if self.in_transaction:
            yield self._conn._transaction  # type: ignore
        else:
            async with self._conn.begin() as transaction:  # type: ignore
                yield transaction

    @asynccontextmanager
    async def begin_nested(self) -> t.AsyncGenerator[Transaction, None]:
        """
        Begin database nested transaction block.
        Be aware, rollback of a nested transaction doesn't rollback the partent transaction.

        This method initiates a nested transaction based on the current state of the connection.

        If the connection is already in a transaction, it
            begins a nested transaction using a savepoint.
        Otherwise, it starts a new standard transaction.

        Returns:
            Transaction: The initiated transaction or nested transaction.

        Example:
            async with conn.begin_nested():
                # Perform database operations within the transaction

            if you want to operate with a transaction object, use following approach
            async with conn.begin_nested() as nested_db_transaction:
                ...
                nested_db_transaction.commit()
                ...
        """

        async with self._conn.begin_nested() as transaction:  # type: ignore
            yield transaction


class AsyncDBEngine:
    """
    This class represents an engine object for database operations

    As of 12.08.24, we are still using aiopg connection pool.

    Refactor this class when migrating to sqlalchemy 1.4,
        so __init__ method will initialize sqlalchemy.ext.asyncio.AsyncEngine object instead
    """

    def __init__(self, engine: Engine) -> None:
        self._engine = engine

    def acquire(self) -> _ContextManager[DBConnection]:
        """
        Open new database connection
        """

        async def __acquire() -> DBConnection:
            raw = await self._engine._pool.acquire()
            return DBConnection(SAConnection(raw, self._engine))  # type: ignore

        async def __close_connection(conn: SAConnection) -> None:
            await conn.close()  # type: ignore[no-untyped-call]

        return _ContextManager[DBConnection](__acquire(), __close_connection)  # type: ignore

    async def dispose(self) -> None:
        """
        Dispose engine. Close and wait until closed.
        """
        self._engine.close()  # type: ignore
        await self._engine.wait_closed()  # type: ignore


async def create_database_engine(
    dsn: str,
    timeout: float,
    minsize: int,
    maxsize: int,
    ssl_mode: str | None = None,
    ssl_root_cert: str | None = None,
) -> AsyncDBEngine:
    """
    Initialize database engine from provided parameters.
    """
    statement_timeout = int(timeout) * 1000

    async def on_connect(conn: AiopgConnection) -> None:
        async with conn.cursor() as cursor:
            await cursor.execute(f'set statement_timeout to {statement_timeout};')

    engine = await create_engine(  # type: ignore
        dsn=dsn,
        minsize=minsize,
        maxsize=maxsize,
        timeout=timeout,
        on_connect=on_connect,
        sslmode=ssl_mode,
        sslrootcert=ssl_root_cert,
    )
    return AsyncDBEngine(engine)
