"""Update user_meta table

Revision ID: 48965f051123
Revises: 25ddd53d4853
Create Date: 2023-02-07 19:58:54.318304

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '48965f051123'
down_revision = '25ddd53d4853'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'user_meta',
        sa.Column(
            'has_invited_recipient',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
    )
    op.drop_column('user_meta', 'has_signed_outgoing_document')
    op.drop_column('user_meta', 'has_signed_incoming_document')


def downgrade():
    op.add_column(
        'user_meta',
        sa.Column(
            'has_signed_incoming_document',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'user_meta',
        sa.Column(
            'has_signed_outgoing_document',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_column('user_meta', 'has_invited_recipient')
