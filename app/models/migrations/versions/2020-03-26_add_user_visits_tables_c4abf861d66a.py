"""Add user_visits tables

Revision ID: c4abf861d66a
Revises: 0f7006b7bcb8
Create Date: 2020-03-26 12:51:24.006125


Author: <PERSON><PERSON><PERSON><PERSON>
"""
import citext
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.actions.enums import ActionPage
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = 'c4abf861d66a'
down_revision = '0f7006b7bcb8'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'user_visits',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False,
        ),
        sa.Column('email', citext.CIText(), nullable=False),
        sa.Column('edrpou', sa.String(length=64), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('page', SoftEnum(ActionPage), nullable=False),
        sa.PrimaryKeyConstraint('id'),
    )


def downgrade():
    op.drop_table('user_visits')
