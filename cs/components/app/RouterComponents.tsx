import React, { useEffect } from 'react';
import {
    Redirect,
    Route,
    Switch,
    useHistory,
    useLocation,
} from 'react-router-dom';

import {
    BusinessPaperlessBannerProvider,
    DeminingBannerContext,
} from '@vchasno/shared-components';
import DonateRatelMBannerProvider from '@vchasno/shared-components/dist/components/DonateRatelMBanner/DonateRatelMBannerContext';

import { ArchiveNotBuyLandingPage } from 'components/ArchiveNotBuyLandingPage';
import { queryParamsBusinessPaperlessBanner } from 'components/BusinessPaperlessBanner/constants';
import { deminingBannerQueryParams } from 'components/DeminingBanner';
import DetectedInTimeBannerV2Provider from 'components/DetectedInTimeBannerV2/context';
import DocumentCreationCompanyTemplates from 'components/DocumentCreationCompanyTemplates';
import DocumentCreationFavoriteTemplates from 'components/DocumentCreationFavoriteTemplates';
import DocumentDrafts from 'components/DocumentDrafts';
import DocumentRecognition from 'components/DocumentRecognition';
import DocumentVchasnoTemplateGallery from 'components/DocumentVchasnoTemplateGallery';
import { FeatureShow } from 'components/FeatureDisplay';
import { HomeBannerProvider } from 'components/HomeBanner/HomeBanner.context';
import MFPage from 'components/MFPage';
import TypeFormSurvey from 'components/TypeFormSurvey/TypeFormSurvey';
import DocumentProcessesProvider from 'contexts/documentProcesses';
import { REGISTRATION_CONFIRM_EMAIL_URL } from 'lib/routing/constants';
import { SessionRecorder, appRoutesToSessionRecord } from 'services/posthog';

import eventTracking from '../../services/analytics/eventTracking';
import AuthLayout from '../AuthLayout/AuthLayout';
import Checkout from '../Checkout/Checkout';
import CheckoutRates from '../CheckoutRates/CheckoutRates';
import DocumentContainer from '../document/documentContainer';
import DocumentListContainer from '../documentList/documentListContainer';
import Documents from '../documents/documents';
import ChooseProjectForm from '../registration/components/chooseProjectForm/chooseProjectForm';
import EmailConfirmation from '../registration/components/emailConfirmation';
import TriggerNotificationPage from '../triggerNotificationPage/triggerNotificationPage';
import SettingsComponent from './SettingsComponent';
// Containers
import AppContainer from './appContainer';
// Others
import AppError from './appError';

const AppComponent = () => {
    const location = useLocation();

    return (
        <Switch>
            <Route exact path={REGISTRATION_CONFIRM_EMAIL_URL}>
                <AuthLayout>
                    <EmailConfirmation />
                </AuthLayout>
            </Route>
            <Route path="/app">
                <TypeFormSurvey />
                <AppContainer>
                    <Switch>
                        {config.DEBUG && (
                            <Route path="/app/mf" exact>
                                <MFPage />
                            </Route>
                        )}
                        <Route path="/app/document-recognition" exact>
                            <FeatureShow feature="ENABLE_DOCUMENT_STRUCTURED_TEST_PAGE">
                                <DocumentRecognition />
                            </FeatureShow>
                        </Route>
                        <Redirect
                            exact
                            from="/app"
                            to={{
                                pathname: '/app/documents',
                                search: location.search,
                            }}
                        />
                        <Route
                            path="/app/registration/choose-project"
                            component={ChooseProjectForm}
                        />
                        <Route path="/app/checkout" component={Checkout} />
                        <Route
                            path="/app/checkout-rates"
                            component={CheckoutRates}
                        />
                        <Route
                            exact
                            path={[
                                '/app/documents',
                                '/app/archive',
                                '/app/archive/:directoryId',
                            ]}
                        >
                            <DonateRatelMBannerProvider>
                                <DetectedInTimeBannerV2Provider>
                                    <Documents>
                                        <DocumentListContainer />
                                    </Documents>
                                </DetectedInTimeBannerV2Provider>
                            </DonateRatelMBannerProvider>
                        </Route>
                        <Route
                            exact
                            path={[
                                '/app/documents/:docId',
                                '/app/documents/:docId/versions/:versionId',
                                '/app/documents/:docId/drafts/:draftId',
                            ]}
                            render={({ match }) => {
                                return (
                                    <Documents>
                                        <DocumentContainer
                                            key={match.url}
                                            match={match}
                                        />
                                    </Documents>
                                );
                            }}
                        />
                        <Route
                            exact
                            path="/app/notifications"
                            component={TriggerNotificationPage}
                        />
                        <Route
                            exact
                            path="/app/drafts"
                            component={DocumentDrafts}
                        />
                        <Route
                            exact
                            path="/app/archive-preview"
                            component={ArchiveNotBuyLandingPage}
                        />
                        <Route
                            exact
                            path="/app/document-creation-templates/company"
                            component={DocumentCreationCompanyTemplates}
                        />
                        <Route
                            exact
                            path="/app/document-creation-templates/favorite"
                            component={DocumentCreationFavoriteTemplates}
                        />
                        <Route
                            exact
                            path="/app/document-creation-templates/gallery"
                            component={DocumentVchasnoTemplateGallery}
                        />
                        <Redirect
                            from="/app/document-creation-templates"
                            to="/app/document-creation-templates/company"
                        />
                        <Route
                            exact
                            path="/app/not-found"
                            component={AppError}
                        />
                        <Route
                            path="/app/settings"
                            component={SettingsComponent}
                        />
                        <Redirect
                            from="/app/doc/:docId"
                            to="/app/documents/:docId"
                        />
                        <Redirect
                            from="/app/doc/:docId/versions/:versionId"
                            to="/app/documents/:docId/versions/:versionId"
                        />
                        <Redirect from="/app/admin" to="/app/settings" />
                        <Redirect from="/app/admin/**" to="/app/settings/**" />
                        <Redirect from="/app/profile" to="/app/settings" />
                        <Redirect
                            from="/app/profile/**"
                            to="/app/settings/**"
                        />
                        <Redirect from="/app/profile" to="/app/settings" />
                        <Redirect
                            from="/app/profile/settings"
                            to="/app/settings"
                        />
                        <Redirect
                            from="/app/statistics"
                            to="/app/settings/statistics"
                        />
                        <Redirect
                            from="/app/statistics/**"
                            to="/app/settings/statistics/**"
                        />
                        <Redirect to="/app/not-found" />
                    </Switch>
                    <Switch>
                        <Route
                            path={appRoutesToSessionRecord}
                            component={SessionRecorder}
                        />
                    </Switch>
                </AppContainer>
            </Route>
        </Switch>
    );
};

const RootComponent = () => {
    const history = useHistory();

    const { pathname, search } = useLocation();

    useEffect(() => {
        window.scrollTo(0, 0);
    }, [pathname, search]);

    useEffect(() => {
        const unregister = history.listen((location) => {
            if (location) {
                eventTracking.pageview('', location.pathname + location.search);
            }
        });

        return unregister;
    }, [history]);

    return (
        <Switch>
            <Route path="/app">
                <DocumentProcessesProvider>
                    <HomeBannerProvider>
                        <BusinessPaperlessBannerProvider
                            queryParams={queryParamsBusinessPaperlessBanner}
                        >
                            <DeminingBannerContext
                                queryParams={deminingBannerQueryParams}
                            >
                                <AppComponent />
                            </DeminingBannerContext>
                        </BusinessPaperlessBannerProvider>
                    </HomeBannerProvider>
                </DocumentProcessesProvider>
            </Route>
            <Route
                exact
                path="/sign-sessions/:signSessionId"
                component={AppContainer}
            />
            <Route
                exact
                path={[
                    '/sign-sessions/:signSessionId/:docId',
                    '/sign-sessions/:signSessionId/:docId/versions/:versionId',
                    '/shared-document-view/:signSessionId/:docId',
                ]}
                render={({ match }) => (
                    <AppContainer>
                        <DocumentContainer match={match} />
                    </AppContainer>
                )}
            />
            <Redirect to="/app/not-found" />
        </Switch>
    );
};

export default RootComponent;
