import I from 'immutable';
import moment from 'moment';

import { boolContent, parseTableData, textContent } from '../utils';

import { sum } from '../../../lib/numbers';
import {
    XmlBank,
    XmlCompany,
    XmlData,
    XmlProduct,
    XmlProducts,
} from '../../../records/xml';
import { parseXml as domParseXml } from '../../../services/xml';
import constants from './constants';

const XmlExtra = new I.Record({
    invoiceNumber: null,
    ownerRepresentativeFullname: null,
    bankAccounts: null,
});

export const parseXmlOwnerBank = (xml) => {
    const account = textContent(xml, constants.OWNER_BANK_ACCOUNT_QUERY, false);
    const mfo = textContent(xml, constants.OWNER_BANK_MFO_QUERY, false);
    const name = textContent(xml, constants.OWNER_BANK_NAME_QUERY, false);

    return new XmlBank({ account, mfo, name });
};

export const parseXnlOwnerBankAccouts = (xml) => {
    const account = textContent(
        xml,
        constants.OWNER_BANK_ACCOUNT_FALLBACK_QUERY,
        false,
    );
    const mfo = textContent(
        xml,
        constants.OWNER_BANK_MFO_FALLBACK_QUERY,
        false,
    );
    const name = textContent(
        xml,
        constants.OWNER_BANK_NAME_FALLBACK_QUERY,
        false,
    );

    return new XmlBank({ account, mfo, name });
};

export const parseXmlOwner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.OWNER_ADDRESS_QUERY),
        bank: parseXmlOwnerBank(xml),
        edrpou: textContent(xml, constants.OWNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.OWNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.OWNER_IPN_QUERY),
        phone: textContent(xml, constants.OWNER_PHONE_QUERY),
        representative:
            textContent(
                xml,
                constants.OWNER_REPRESENTATIVE_FALLBACK_QUERY,
                false,
            ) || textContent(xml, constants.OWNER_REPRESENTATIVE_QUERY, false),
        representativePosition: textContent(
            xml,
            constants.OWNER_REPRESENTATIVE_POSITION_QUERY,
            false,
        ),
    });
};

export const parseXmlPartnerBank = (xml) => {
    const account = textContent(
        xml,
        constants.PARTNER_BANK_ACCOUNT_QUERY,
        false,
    );
    const mfo = textContent(xml, constants.PARTNER_BANK_MFO_QUERY, false);
    const name = textContent(xml, constants.PARTNER_BANK_NAME_QUERY, false);

    return new XmlBank({ account, mfo, name });
};

export const parseXmlPartner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.PARTNER_ADDRESS_QUERY),
        bank: parseXmlPartnerBank(xml),
        edrpou: textContent(xml, constants.PARTNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.PARTNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.PARTNER_IPN_QUERY),
        phone: textContent(xml, constants.PARTNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.PARTNER_REPRESENTATIVE_QUERY,
            false,
        ),
        representativePosition: textContent(
            xml,
            constants.PARTNER_REPRESENTATIVE_POSITION_QUERY,
            false,
        ),
    });
};

export const parseXmlService = (serviceXml) => {
    return new XmlProduct({
        item: textContent(serviceXml, constants.SERVICE_ITEM_QUERY),
        name: textContent(serviceXml, constants.SERVICE_NAME_QUERY),
        fullName: textContent(
            serviceXml,
            constants.SERVICE_FULL_NAME_QUERY,
            false,
        ),
        number: textContent(serviceXml, constants.SERVICE_NUMBER_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.SERVICE_PRICE_QUERY),
        ),
        quantity: textContent(serviceXml, constants.SERVICE_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.SERVICE_SUM_QUERY)),
        taxes: parseFloat(
            textContent(serviceXml, constants.SERVICE_TAXES_QUERY),
        ),
        type: constants.ProductType.SERVICE,
    });
};

export const parseXmlGoods = (serviceXml) => {
    return new XmlProduct({
        item: textContent(serviceXml, constants.GOODS_ITEM_QUERY),
        name: textContent(serviceXml, constants.GOODS_NAME_QUERY),
        fullName: textContent(
            serviceXml,
            constants.GOODS_FULL_NAME_QUERY,
            false,
        ),
        number: textContent(serviceXml, constants.GOODS_NUMBER_QUERY),
        price: parseFloat(textContent(serviceXml, constants.GOODS_PRICE_QUERY)),
        quantity: textContent(serviceXml, constants.GOODS_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.GOODS_SUM_QUERY)),
        taxes: parseFloat(textContent(serviceXml, constants.GOODS_TAXES_QUERY)),
        type: constants.ProductType.GOOD,
    });
};

export const parseXmlServices = (xml) => {
    const serviceXmls = xml.querySelector(constants.SERVICES_QUERY)
        ? parseTableData(
              xml,
              constants.SERVICES_QUERY,
              constants.SERVICE_NUMBER_QUERY,
          )
        : [];
    const goodsXmls = xml.querySelector(constants.GOODS_QUERY)
        ? parseTableData(
              xml,
              constants.GOODS_QUERY,
              constants.GOODS_NUMBER_QUERY,
          )
        : [];
    const items = new I.List([
        ...goodsXmls.map(parseXmlGoods),
        ...serviceXmls.map(parseXmlService),
    ]);

    return new XmlProducts({
        currency: textContent(xml, constants.CURRENCY_QUERY),
        isTaxesIncluded: boolContent(xml, constants.IS_TAXES_INCLUDED_QUERY),
        items,
        totalDocumentSum: parseFloat(
            textContent(xml, constants.TOTAL_DOCUMENT_SUM_QUERY),
        ),
        totalPrice: sum(items, 'price'),
        totalPriceStr: textContent(xml, constants.TOTAL_PRICE_STR_QUERY),
        totalSum: sum(items, 'sum'),
        totalSumStr:
            textContent(xml, constants.TOTAL_SUM_STR_QUERY, false) ||
            textContent(xml, constants.TOTAL_SUM_STR_FALLBACK_QUERY),
        totalTaxes: sum(items, 'taxes'),
        totalTaxesStr: textContent(xml, constants.TOTAL_TAXES_STR_QUERY),
    });
};

export const parseXmlExtra = (xml) => {
    const bankXmls = xml.querySelector(constants.BANK_ACCOUNT_QUERY)
        ? parseTableData(
              xml,
              constants.HEADER_QUERY,
              constants.BANK_ACCOUNT_QUERY,
              'Вид',
              'БанковскиеСчета',
          )
        : [];
    const bankAccounts = new I.List(bankXmls.map(parseXnlOwnerBankAccouts));

    return new XmlExtra({
        invoiceNumber: textContent(xml, constants.EXTRA_INVOICE_NUMBER_QUERY),
        ownerRepresentativeFullname: textContent(
            xml,
            constants.EXTRA_OWNER_REPRESENTATIVE_FULLNAME_QUERY,
        ),
        bankAccounts,
    });
};

export const parseXml = (content) => {
    const xml = domParseXml(content);
    const date = textContent(xml, constants.DATE_QUERY).trim();
    return new XmlData({
        date: date && moment(date),
        documentType: textContent(xml, constants.DOCUMENT_TYPE_QUERY).trim(),
        number: textContent(xml, constants.NUMBER_QUERY).trim(),

        agreementName: textContent(xml, constants.AGREEMENT_NAME_QUERY),
        agreementPlace: textContent(xml, constants.AGREEMENT_PLACE_QUERY),
        dealName: textContent(xml, constants.DEAL_NAME_QUERY, false),

        owner: parseXmlOwner(xml),
        partner: parseXmlPartner(xml),
        services: parseXmlServices(xml),

        extra: parseXmlExtra(xml),
    });
};

export default (data) => parseXml(data.content);
