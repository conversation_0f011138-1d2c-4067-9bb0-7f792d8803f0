.root,
.rootColorWhite,
.rootSmall {
    width: 26px;
    height: 26px;
    margin: auto;
    border-radius: 50%;
    box-shadow: inset #eee 0 0 0 5px;
}

.rootColorWhite {
    box-shadow: inset #d3d3d3 0 0 0 3px;
}

.rootColorWhite::after {
    animation: animate2White 1.5s ease-in-out infinite;
}

.rootSmall {
    width: 20px;
    height: 20px;
    box-shadow: inset #eee 0 0 0 3px;
}

.rootSmall.rootColorWhite {
    box-shadow: inset #d3d3d3 0 0 0 3px;
}

.rootSmall.rootColorWhite .slider::after {
    animation: animate2smallWhite 1.5s ease-in-out infinite;
}

.rootSmall .slider {
    width: 20px;
    height: 20px;
    clip: rect(0, 20px, 20px, 10px);
}

.rootSmall .slider::after {
    width: 20px;
    height: 20px;
    animation: animate2small 1.5s ease-in-out infinite;
    clip: rect(0, 20px, 20px, 13px);
}

.slider {
    position: absolute;
    width: 26px;
    height: 26px;
    animation: animate 1.5s linear infinite;
    clip: rect(0, 26px, 26px, 13px);
    transform: translateZ(0);
}

.slider::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 26px;
    height: 26px;
    animation: animate2 1.5s ease-in-out infinite;
    border-radius: 50%;
    clip: rect(0, 26px, 26px, 13px);
    content: '';
    transform: translateZ(0);
}

@keyframes animate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(220deg);
    }
}

@keyframes animate2 {
    0% {
        box-shadow: inset var(--dark-pigeon-color) 0 0 0 5px;
        transform: rotate(-140deg);
    }

    100% {
        box-shadow: inset var(--dark-pigeon-color) 0 0 0 5px;
        transform: rotate(140deg);
    }
}

@keyframes animate2White {
    0% {
        box-shadow: inset #fff 0 0 0 5px;
        transform: rotate(-140deg);
    }

    100% {
        box-shadow: inset #fff 0 0 0 5px;
        transform: rotate(140deg);
    }
}

@keyframes animate2small {
    0% {
        box-shadow: inset var(--dark-pigeon-color) 0 0 0 3px;
        transform: rotate(-140deg);
    }

    100% {
        box-shadow: inset var(--dark-pigeon-color) 0 0 0 3px;
        transform: rotate(140deg);
    }
}

@keyframes animate2smallWhite {
    0% {
        box-shadow: inset #fff 0 0 0 3px;
        transform: rotate(-140deg);
    }

    100% {
        box-shadow: inset #fff 0 0 0 3px;
        transform: rotate(140deg);
    }
}
