import datetime
import io
import pathlib
import zipfile
from http import HTT<PERSON>tatus
from pathlib import Path
from unittest.mock import ANY

import openpyxl
import pytest
import ujson

from api.downloads import pdf
from api.downloads.constants import NTFS_REAL_MAX_PATH_LENGTH
from api.downloads.db import select_download_document_archive
from api.downloads.enums import SignatureArchiveFormat
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.documents.utils import get_xml_to_pdf_key
from app.events.document_actions.db import select_document_actions_for
from app.flags import FeatureFlags
from app.lib import s3_utils
from app.lib.datetime_utils import naive_local_now, utc_now
from app.lib.enums import SignatureFormat, UserRole
from app.lib.helpers import decode_base64_str, translit
from app.lib.types import DataDict
from app.reviews.enums import ReviewType
from app.services import services
from app.sign_sessions.enums import SignSessionType
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    assert_list_any_order,
    cleanup_on_teardown,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_referer_headers,
    prepare_review,
    prepare_review_db,
    prepare_review_requests,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_signature_data,
    prepare_signature_row,
    prepare_user_data,
    wait_streaming_response_eof,
)

DATA_PATH = Path(__file__).parents[2] / 'public' / 'tests' / 'data'

TEST_CONTENT = b'Test Content'
TEST_CONTENT_XZ = (
    b'\xfd7zXZ\x00\x00\x04\xe6\xd6\xb4F\x02\x00!\x01\x16\x00\x00\x00t/\xe5'
    b'\xa3\x01\x00\x0bTest content\x00\xd2\x87c\x8d\xeb\xa4\x7f\x83\x00\x01'
    b'$\x0c\xa6\x18\xd8\xd8\x1f\xb6\xf3}\x01\x00\x00\x00\x00\x04YZ'
)
TEST_CONTENT_XZ_UNCOMPRESSED = b'Test content'
TEST_CONTENT_XML = b'<root key="value">text</root>'
TEST_CONTENT_XML_TO_JSON = {'root': {'$': 'text', '@key': 'value'}}
TEST_CONTENT_XML_XZ = (
    b'\xfd7zXZ\x00\x00\x04\xe6\xd6\xb4F\x02\x00!\x01\x16\x00\x00\x00t/\xe5\xa3'
    b'\x01\x00\x1c<root key="value">text</root>\x00\x00\x00\x00\xf9\xd6ws9'
    b'\x10M\xb0\x00\x015\x1dD\x1b\xb1\xe1\x1f\xb6\xf3}\x01\x00\x00\x00\x00'
    b'\x04YZ'
)
TEST_NP_PATH = DATA_PATH / 'nova_poshta_file.xml'
TEST_NP_CONTENT_XML = TEST_NP_PATH.read_bytes()
TEST_USER_EMAIL = '<EMAIL>'

TEST_UUID_1 = '41a2d812-d8d5-4f5e-bff7-ca9e79a653a7'
TEST_UUID_2 = 'bfd97067-83a7-4895-a494-5baf4b86292a'
TEST_UUID_3 = 'ea077bb8-d3c1-4897-970c-014d950432a1'
TEST_UUID_4 = 'f5b5c9c1-5b5a-4b1f-9c1c-5b5a4b1f9c1c'
TEST_UUID_5 = '82a91ff4-57c2-4026-811a-c62781e1e106'

TEST_DOCUMENT_ID_1 = '84043588-f8fb-4163-a068-e7fc8fead83f'
TEST_DOCUMENT_ID_2 = 'f6eeaf17-d95f-4c5d-82a9-1efe176f770b'

# 323 characters. If you think it's too long, you are underestimating our users that sometimes
# uploads files 200+ characters long.
TEST_LONG_FILENAME_BASE_1 = 'як_умру_то_поховайте_мене_на_могилі_серед_степу_широкого_на_Вкраїні_милій_щоб_лани_широкополі_і_Дніпро_і_кручі_було_видно_було_чути_як_реве_ревучий_як_понесе_з_України_у_синєє_море_кров_ворожу_отойді_я_і_лани_і_гори_все_покину_і_полину_до_самого_Бога_молитися_а_до_того_я_не_знаю_Бога_поховайте_та_вставайте_кайдани_порвіте'  # noqa: E501
TEST_LONG_FILENAME_TRANSLIT_BASE_1 = 'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bulo_vydno_bulo_chuty_iak_reve_revuchyi_iak_ponese_z_Ukrainy_u_synieie_more_krov_vorozhu_otoidi_ia_i_lany_i_hory_vse_pokynu_i_polynu_do_samoho_Boha_molytysia_a_do_toho_ia_ne_znaiu_Boha_pokhovaite_ta_vstavaite_kaidany_porvite'  # noqa: E501

# 493 characters
TEST_LONG_FILENAME_BASE_2 = 'любіть_Україну_як_сонце_любіть_як_вітер_і_трави_і_води_в_годину_щасливу_і_в_радості_мить_любіть_у_годину_негоди_любіть_Україну_у_сні_й_наяву_вишневу_свою_Україну_красу_її_вічно_живу_і_нову_і_мову_її_соловїну_без_неї_ніщо_ми_як_порох_і_дим_розвіяний_в_полі_вітрами_любіть_Україну_всім_серцем_своїм_і_всіми_своїми_ділами_для_нас_вона_в_світі_єдина_одна_як_очі_її_ніжно_карі_вона_у_зірках_і_у_вербах_вона_і_в_кожному_серця_ударі_у_квітці_в_пташині_в_кривеньких_тинах'  # noqa: E501
TEST_LONG_FILENAME_TRANSLIT_BASE_2 = 'liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liubit_Ukrainu_u_sni_i_naiavu_vyshnevu_svoiu_Ukrainu_krasu_ii_vichno_zhyvu_i_novu_i_movu_ii_solovinu_bez_nei_nishcho_my_iak_porokh_i_dym_rozviianyi_v_poli_vitramy_liubit_Ukrainu_vsim_sertsem_svoim_i_vsimy_svoimy_dilamy_dlia_nas_vona_v_sviti_iedyna_odna_iak_ochi_ii_nizhno_kari_vona_u_zirkakh_i_u_verbakh_vona_i_v_kozhnomu_sertsia_udari_u_kvittsi_v_ptashyni_v_kryvenkykh_tynakh'  # noqa: E501

DOWNLOAD_REVIEW_HISTORY_XLSX_URL = '/downloads/documents/{document_id}/reviews/history.xlsx'
DOWNLOAD_P7S_URL = '/downloads/{document_id}/p7s'
DOWNLOAD_P7S_API_URL = '/api/v2/documents/{document_id}/p7s'

DOWNLOAD_ARCHIVE_URL = '/downloads/{document_id}/archive'
DOWNLOAD_ARCHIVE_API_URL = '/api/v2/documents/{document_id}/archive'
DATA_FILES_PATH = Path(__file__).parent / 'data'


async def test_create_xml_to_pdf_has_s3_key(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        title='pryvit',
        extension='.xml',
        s3_xml_to_pdf_key='pryvit.pdf',
        content=TEST_CONTENT,
    )

    s3_pdf_key = get_xml_to_pdf_key(
        document_id=document.id,
        s3_xml_to_pdf_key=document.s3_xml_to_pdf_key,
    )
    s3_emulation[s3_pdf_key] = s3_utils.UploadFile(
        key=s3_pdf_key,
        body=b'converted-content',
    )

    response = await client.post(
        f'/internal-api/documents/{document.id}/xml-to-pdf',
        headers=prepare_auth_headers(user),
    )
    await wait_streaming_response_eof(response)
    assert response.status == 200
    assert response.headers['Content-Disposition'] == 'Attachment; filename="pryvit.pdf"'

    data = await response.read()
    assert data == b'converted-content'


@pytest.mark.parametrize(
    'permissions, action, ext, expected',
    [
        ({}, 'archive', '.pdf', 200),
        ({}, 'archive', '.xml', 200),
        ({}, 'original', '.pdf', 200),
        ({}, 'original', '.xml', 200),
        ({}, 'print', '.pdf', 200),
        ({}, 'print', '.xml', 200),
        ({}, 'signed', '.pdf', 200),
        ({}, 'signed', '.xml', 200),
        ({'can_download_document': False}, 'archive', '.pdf', 403),
        ({'can_download_document': False}, 'archive', '.xml', 403),
        ({'can_download_document': False}, 'original', '.pdf', 200),
        ({'can_download_document': False}, 'original', '.xml', 200),
        ({'can_download_document': False}, 'print', '.pdf', 200),
        ({'can_download_document': False}, 'print', '.xml', 200),
        ({'can_download_document': False}, 'signed', '.pdf', 200),
        ({'can_download_document': False}, 'signed', '.xml', 200),
        ({'can_print_document': False}, 'archive', '.pdf', 200),
        ({'can_print_document': False}, 'archive', '.xml', 200),
        ({'can_print_document': False}, 'original', '.pdf', 200),
        ({'can_print_document': False}, 'original', '.xml', 200),
        ({'can_print_document': False}, 'print', '.pdf', 403),
        ({'can_print_document': False}, 'print', '.xml', 403),
        ({'can_print_document': False}, 'signed', '.pdf', 200),
        ({'can_print_document': False}, 'signed', '.xml', 200),
    ],
)
async def test_download(aiohttp_client, permissions, action, ext, expected, monkeypatch):
    app, client, owner = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner, extension=ext, content=TEST_CONTENT)
    monkeypatch.setattr(pdf, 'generate_signed_file', lambda *ar, **kw: io.BytesIO(TEST_CONTENT))
    monkeypatch.setattr(pdf, 'generate_print_file', lambda *ar, **kw: io.BytesIO(TEST_CONTENT))

    user = await prepare_user_data(
        app,
        email=TEST_USER_EMAIL,
        company_edrpou=owner.company_edrpou,
        user_role=UserRole.user.value,
        **permissions,
    )

    url = f'/downloads/{document.id}'
    if action == 'archive':
        url = f'{url}/archive'
    if action == 'original':
        url = f'{url}/original'
    if action == 'print':
        url = f'{url}/print'

    response = await client.get(url, headers=prepare_auth_headers(user))
    assert response.status == expected

    # Check if we provide charset in response.content-type for xml files
    if ext == '.xml' and action == 'original':
        assert 'charset=' in response.headers['Content-Type']

    # As downloads return a stream-response, we need to wait before the test app
    #  will consume response content
    if expected == 200:
        await wait_streaming_response_eof(response)


async def test_download_xml_with_signatures_and_reviews(aiohttp_client):
    """
    Check happy path for downloading JSON for XML documents with signatures
    and reviews
    """

    app, client, owner = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(
        app=app,
        owner=owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        title='hello-world',
        extension='.xml',
        content=TEST_CONTENT,
    )
    signature1 = await prepare_signature_data(
        app=app,
        owner=owner,
        document=document,
        date_created='2020-01-01T00:00:00Z',
        key_timemark='2020-02-01T00:00:00Z',
        stamp_timemark='2020-03-01T00:00:00Z',
        key_serial_number='1234567890',
        stamp_serial_number='0987654321',
    )
    signature2 = await prepare_signature_data(
        app=app,
        owner=owner,
        document=document,
        date_created='2021-01-01T00:00:00Z',
        key_timemark='2021-02-01T00:00:00Z',
        stamp_timemark='2021-03-01T00:00:00Z',
        key_serial_number='1234567890',
        stamp_serial_number='0987654321',
    )
    await prepare_review_db(
        document_id=document.id,
        user=owner,
        type=ReviewType.approve,
        date_created='2020-01-01T00:00:00Z',
    )
    await prepare_review_db(
        document_id=document.id,
        user=coworker,
        type=ReviewType.reject,
        date_created='2021-01-01T00:00:00Z',
    )

    url = f'/downloads/{document.id}'
    response = await client.get(url, headers=prepare_auth_headers(owner))
    response_json = await response.json()
    assert response.status == HTTPStatus.OK, response_json
    assert response_json == {
        'id': document.id,
        'content_url': f'http://localhost:8000/downloads/{document.id}/original',
        'content_type': 'application/xml',
        'date_delivered': None,
        'date_rejected': None,
        'date_sent': '2020-01-01T02:00:00+02:00',
        'edrpou': '11111111',
        'recipient_edrpou': '00000001',
        'ext': '.xml',
        'title': 'hello-world.xml',
        'is_3p': False,
        # Signature should be sorted by date_created, stamp and key should be
        # separated into different items
        'signatures': [
            {
                'id': f'{signature1.id}-signature',
                'is_owner': True,
                'type': 'signature',
                'company_name': 'Test Company',
                'edrpou': '11111111',
                'name': 'Test User',
                'position': None,
                'serial_number': '1234567890',
                'is_legal': True,
                'time_mark': '2020-02-01T02:00:00+02:00',
                'signature_power_type': None,
                'certificate_power_type': None,
            },
            {
                'id': f'{signature1.id}-stamp',
                'is_owner': True,
                'type': 'stamp',
                'company_name': 'Test Company',
                'edrpou': '11111111',
                'name': 'Test User',
                'position': None,
                'serial_number': '0987654321',
                'is_legal': True,
                'time_mark': '2020-03-01T02:00:00+02:00',
                'signature_power_type': None,
                'certificate_power_type': None,
            },
            {
                'id': f'{signature2.id}-signature',
                'is_owner': True,
                'type': 'signature',
                'company_name': 'Test Company',
                'edrpou': '11111111',
                'name': 'Test User',
                'position': None,
                'serial_number': '1234567890',
                'is_legal': True,
                'time_mark': '2021-02-01T02:00:00+02:00',
                'signature_power_type': None,
                'certificate_power_type': None,
            },
            {
                'id': f'{signature2.id}-stamp',
                'is_owner': True,
                'type': 'stamp',
                'company_name': 'Test Company',
                'edrpou': '11111111',
                'name': 'Test User',
                'position': None,
                'serial_number': '0987654321',
                'is_legal': True,
                'time_mark': '2021-03-01T02:00:00+02:00',
                'signature_power_type': None,
                'certificate_power_type': None,
            },
        ],
        'reviews': [
            {
                'type': 'reject',
                'email': '<EMAIL>',
                'first_name': None,
                'second_name': None,
                'last_name': None,
                'position': None,
            },
            {
                'type': 'approve',
                'email': '<EMAIL>',
                'first_name': None,
                'second_name': None,
                'last_name': None,
                'position': None,
            },
        ],
        'review_render_config': {
            'render_email': True,
            'render_full_name': False,
            'render_position': True,
        },
    }


@pytest.mark.parametrize(
    'type_, url_template, use_header, expirable',
    [
        (SignSessionType.sign_session, '/downloads/{0}', False, False),
        (SignSessionType.view_session, '/downloads/{0}', False, False),
        (SignSessionType.sign_session, '/downloads/{0}', True, False),
        (SignSessionType.view_session, '/downloads/{0}', True, False),
        (SignSessionType.sign_session, '/downloads/{0}/archive', False, False),
        (SignSessionType.view_session, '/downloads/{0}/archive', False, False),
        (SignSessionType.sign_session, '/downloads/{0}/archive', True, False),
        (SignSessionType.view_session, '/downloads/{0}/archive', True, False),
        (SignSessionType.sign_session, '/downloads/{0}/original', False, False),
        (SignSessionType.view_session, '/downloads/{0}/original', False, False),
        (SignSessionType.sign_session, '/downloads/{0}/original', True, False),
        (SignSessionType.view_session, '/downloads/{0}/original', True, False),
        (SignSessionType.sign_session, '/downloads/{0}/print', False, False),
        (SignSessionType.view_session, '/downloads/{0}/print', False, False),
        (SignSessionType.sign_session, '/downloads/{0}/print', True, False),
        (SignSessionType.view_session, '/downloads/{0}/print', True, False),
        (SignSessionType.view_session, '/downloads/{0}', False, True),
    ],
)
async def test_download_from_sign_session(
    aiohttp_client,
    type_,
    url_template,
    use_header,
    expirable,
    test_flags,
):
    test_flags[FeatureFlags.USE_EXPIRABLE_URLS_FOR_MS_VIEWER.name] = True

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        title='hello-world',
        extension='.txt',
        content=TEST_CONTENT,
    )
    sign_session = await prepare_sign_session_data(
        app=app,
        user=user,
        document=document,
        type=type_,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    url = url_template.format(document.id)
    if use_header:
        headers = prepare_sign_session_headers(sign_session, client)
        url += '?'
    else:
        headers = prepare_referer_headers(client)
        url += f'?ssid={sign_session.id}'
        url += '&'

    if expirable:
        url += 'expirable=true'

    try:
        response = await client.get(url, headers=headers)
        await wait_streaming_response_eof(response)
        assert response.status == 200

        if expirable:
            response2 = await client.get(url, headers=headers)
            await wait_streaming_response_eof(response2)
            assert response2.status == 403
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'extension, content, expected_status, expected_json',
    [
        ('.pdf', TEST_CONTENT, 400, None),
        ('.pdf.xz', TEST_CONTENT, 400, None),
        ('.xml', TEST_CONTENT_XML, 200, TEST_CONTENT_XML_TO_JSON),
        ('.xml.xz', TEST_CONTENT_XML_XZ, 200, TEST_CONTENT_XML_TO_JSON),
    ],
)
async def test_xml_to_json(aiohttp_client, extension, content, expected_status, expected_json):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, extension=extension, content=content)

    try:
        response = await client.get(
            f'/downloads/{document.id}/xml-to-json', headers=prepare_auth_headers(user)
        )
        await wait_streaming_response_eof(response)
        assert response.status == expected_status

        if expected_json:
            assert await response.json() == expected_json
    finally:
        await cleanup_on_teardown(app)


async def test_xml_to_json_count_rows(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, extension='.xml', content=TEST_NP_CONTENT_XML)

    try:
        response = await client.get(
            f'/downloads/{document.id}/xml-to-json/rows',
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        assert await response.json() == {'rows': 2005}
    finally:
        await cleanup_on_teardown(app)


async def test_xml_to_json_options_first_and_limit(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, extension='.xml', content=TEST_NP_CONTENT_XML)

    try:
        response = await client.get(
            f'/downloads/{document.id}/xml-to-json?first=100&limit=1',
            headers=prepare_auth_headers(user),
        )
        await wait_streaming_response_eof(response)

        assert response.status == 200

        data = await response.json()
        body = data['Документ.РеализацияТоваровУслуг']['РеквизитыТабличнойЧасти_Услуги']
        assert 'Услуги_100' in body
        assert 'Услуги_1' not in body
        assert 'Услуги_101' not in body
    finally:
        await cleanup_on_teardown(app)


async def test_xml_to_json_options_limit(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, extension='.xml', content=TEST_NP_CONTENT_XML)

    try:
        response = await client.get(
            f'/downloads/{document.id}/xml-to-json?limit=1',
            headers=prepare_auth_headers(user),
        )
        await wait_streaming_response_eof(response)

        assert response.status == 200

        data = await response.json()
        body = data['Документ.РеализацияТоваровУслуг']['РеквизитыТабличнойЧасти_Услуги']
        assert 'Услуги_1' in body
        assert 'Услуги_2' not in body
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'extension, s3_xml_to_pdf_key, content, expected',
    [
        pytest.param(
            '.pdf',
            '123.pdf',
            TEST_CONTENT,
            {
                'status': 400,
                'content': None,
            },
            id='cannot_convert_pdf',
        ),
        pytest.param(
            '.pdf.xz',
            '123.pdf',
            TEST_CONTENT,
            {
                'status': 400,
                'content': None,
            },
            id='cannot_convert_pdf_xz',
        ),
        pytest.param(
            '.xml',
            '123.pdf',
            TEST_CONTENT,
            {
                'status': 200,
                'content': TEST_CONTENT,
            },
            id='download_pdf',
        ),
        pytest.param(
            '.xml',
            '123.pdf.xz',
            TEST_CONTENT_XZ,
            {
                'status': 200,
                'content': TEST_CONTENT_XZ_UNCOMPRESSED,
            },
            id='download_pdf_xz',
        ),
        pytest.param(
            '.xml.xz',
            '123.pdf',
            TEST_CONTENT,
            {
                'status': 200,
                'content': TEST_CONTENT,
            },
            id='download_pdf',
        ),
        pytest.param(
            '.xml.xz',
            '123.pdf.xz',
            TEST_CONTENT_XZ,
            {
                'status': 200,
                'content': TEST_CONTENT_XZ_UNCOMPRESSED,
            },
            id='download_pdf_xz',
        ),
        pytest.param(
            '.xml',
            None,
            None,
            {
                'status': 404,
                'content': None,
            },
            id='no_s3_key',
        ),
        pytest.param(
            '.xml.xz',
            None,
            None,
            {
                'status': 404,
                'content': None,
            },
            id='no_s3_key_xz',
        ),
    ],
)
async def test_xml_to_pdf(
    aiohttp_client,
    s3_emulation,
    extension: str,
    s3_xml_to_pdf_key: str | None,
    content: bytes | None,
    expected: DataDict,
):
    app, client, owner = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app,
        owner,
        title='test-document',
        extension=extension,
        s3_xml_to_pdf_key=s3_xml_to_pdf_key,
    )

    if content:
        s3_pdf_key = get_xml_to_pdf_key(
            document_id=document.id,
            s3_xml_to_pdf_key=document.s3_xml_to_pdf_key,
        )
        s3_emulation[s3_pdf_key] = s3_utils.UploadFile(
            key=s3_pdf_key,
            body=content,
        )

    response = await client.get(
        f'/downloads/{document.id}/xml-to-pdf',
        headers=prepare_auth_headers(owner),
    )
    await wait_streaming_response_eof(response)
    assert response.status == expected['status']

    if expected['status'] == 200:
        assert len(s3_emulation.download_calls) == 1
        assert response.headers['Content-Disposition'] == 'Attachment; filename="test-document.pdf"'
        assert await response.read() == expected['content']


async def test_xml_to_pdf_translited(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        title='привіт',
        extension='.xml',
        s3_xml_to_pdf_key='pryvit.pdf',
        content=TEST_CONTENT,
    )
    s3_pdf_key = get_xml_to_pdf_key(
        document_id=document.id,
        s3_xml_to_pdf_key=document.s3_xml_to_pdf_key,
    )
    s3_emulation[s3_pdf_key] = s3_utils.UploadFile(
        key=s3_pdf_key,
        body=b'converted-content',
    )

    response = await client.get(
        f'/downloads/{document.id}/xml-to-pdf', headers=prepare_auth_headers(user)
    )
    await wait_streaming_response_eof(response)
    assert response.status == 200
    assert response.headers['Content-Disposition'] == 'Attachment; filename="pryvit.pdf"'


async def test_basic_multi_archive(aiohttp_client, s3_emulation, mailbox, send_email_mock):
    app, client, user = await prepare_client(aiohttp_client, company_name='KarpCorp')
    document1 = await prepare_document_data(app, user, id=TEST_DOCUMENT_ID_1)
    document2 = await prepare_document_data(app, user, id=TEST_DOCUMENT_ID_2)

    resp = await client.post(
        '/downloads/multi-archive',
        data=ujson.dumps({'docIds': [document1.id, document2.id]}),
        headers=prepare_auth_headers(user),
    )
    assert resp.status == 200, f'status mismatch, {resp.status} != 200'
    json_response = await resp.json()
    assert json_response == {
        'type': 'success',
        'message': (
            'Архів формується, на вашу пошту буде відправлено листа з посиланням для завантаження'
        ),
    }
    assert len(mailbox) == 1
    assert mailbox[0]['Subject'] == 'Архів документів компанії 11111111, KarpCorp'
    assert mailbox[0]['To'] == user.email

    send_email_kwargs = send_email_mock.mock_calls[0].kwargs

    assert send_email_kwargs == {
        'recipient_mixed': '<EMAIL>',
        'subject': 'Архів документів компанії 11111111, KarpCorp',
        'template_name': 'download_archive',
        'context': ANY,
    }
    download_links: list[str] = send_email_kwargs['context']['download_links']
    assert len(download_links) == 1
    download_link = download_links[0]
    assert download_link.startswith('http://localhost:8000/downloads/multi-archive/')

    archive_id = download_link.split('/')[-1].split('?')[0]

    s3_key = f'documents_archives/{archive_id}'

    assert s3_key in s3_emulation
    archive = s3_emulation[s3_key]
    assert archive.encrypt is True
    assert archive.body.startswith(b'PK\x03\x04')  # zip magic bytes

    async with services.db.acquire() as conn:
        archive_row = await select_download_document_archive(conn, archive_id=archive_id)
        assert archive_row is not None
        assert archive_row.documents_ids == [document1.id, document2.id]
        assert archive_row.edrpou == user.company_edrpou
        assert archive_row.role_id == user.role_id
        assert archive_row.filename == 'Doc_Vchasno.zip'
        assert archive_row.size == len(archive.body)

    expected_names = [
        'Test Document/Test Document.pdf',
        'Test Document/Instruktsiia.pdf',
        'Test Document_0/Test Document.pdf',
        'Test Document_0/Instruktsiia.pdf',
    ]
    expected_names = sorted(expected_names)

    with zipfile.ZipFile(io.BytesIO(archive.body), 'r') as zip_file:
        names = zip_file.namelist()
        names = sorted(names)
        assert names == expected_names

    # Check that we can download the archive
    resp = await client.get(
        f'/downloads/multi-archive/{archive_id}',
        headers=prepare_auth_headers(user),
    )
    assert resp.status == 200, f'status mismatch, {resp.status} != 200'
    assert resp.content_disposition.filename == 'Doc_Vchasno.zip'
    # content is equal, no reason to check content of zip
    assert await resp.read() == archive.body


@pytest.mark.parametrize(
    'request_data, expected',
    [
        pytest.param(
            {
                'json': {},
                'headers': {},
            },
            {
                'archive_filename': 'Doc_Vchasno.zip',
                'names': [
                    # each part of the path is shortened to 255 characters
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_2[:255]}/Instruktsiia.pdf',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_2[:255]}/{TEST_LONG_FILENAME_TRANSLIT_BASE_2[: 255 - 4]}.pdf',  # noqa: E501
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[:255]}/Instruktsiia.pdf',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[:255]}/{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.pdf',  # noqa: E501
                ],
            },
            id='default_mode_no_params',
        ),
        pytest.param(
            {
                'json': {'filenames_mode': 'default'},
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                },
            },
            {
                'archive_filename': 'Doc_Vchasno.zip',
                'names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[:255]}/{TEST_LONG_FILENAME_TRANSLIT_BASE_1[:251]}.pdf',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[:255]}/Instruktsiia.pdf',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_2[:255]}/{TEST_LONG_FILENAME_TRANSLIT_BASE_2[:251]}.pdf',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_2[:255]}/Instruktsiia.pdf',
                ],
            },
            id='default_mode_requested',
        ),
        pytest.param(
            {
                'json': {},
                'headers': {},
            },
            {
                'archive_filename': 'Doc_Vchasno.zip',
                'names': [
                    'liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liubit_Ukrainu_u_sni_i_naiavu_vyshnevu_svoiu_Ukrainu_krasu_ii_vichno_zhyvu_i_novu_i_movu_ii_solovinu_bez_nei_nishcho_my_iak_porokh_i_dym_r/Instruktsiia.pdf',
                    'liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liubit_Ukrainu_u_sni_i_naiavu_vyshnevu_svoiu_Ukrainu_krasu_ii_vichno_zhyvu_i_novu_i_movu_ii_solovinu_bez_nei_nishcho_my_iak_porokh_i_dym_r/liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liubit_Ukrainu_u_sni_i_naiavu_vyshnevu_svoiu_Ukrainu_krasu_ii_vichno_zhyvu_i_novu_i_movu_ii_solovinu_bez_nei_nishcho_my_iak_porokh_i_d.pdf',
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bulo_vydno_bulo_chuty_iak_reve_revuchyi_iak_ponese_z_Ukrainy_u_synieie_more_krov_vorozhu_otoidi_ia_i_lany_i_hory_vse_pokynu_i_polynu_do_/Instruktsiia.pdf',
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bulo_vydno_bulo_chuty_iak_reve_revuchyi_iak_ponese_z_Ukrainy_u_synieie_more_krov_vorozhu_otoidi_ia_i_lany_i_hory_vse_pokynu_i_polynu_do_/yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bulo_vydno_bulo_chuty_iak_reve_revuchyi_iak_ponese_z_Ukrainy_u_synieie_more_krov_vorozhu_otoidi_ia_i_lany_i_hory_vse_pokynu_i_polynu.pdf',
                ],
            },
            id='default_mode_short_filenames_enabled',
        ),
        pytest.param(
            {
                'json': {},
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                },
            },
            {
                'archive_filename': 'Doc_Vchasno.zip',
                'names': [
                    'liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liub/Instruktsiia.pdf',
                    'liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_v_hodynu_shchaslyvu_i_v_radosti_myt_liubit_u_hodynu_nehody_liub/liubit_Ukrainu_iak_sontse_liubit_iak_viter_i_travy_i_vody_.pdf',
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bu/Instruktsiia.pdf',
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrokopoli_i_Dnipro_i_kruchi_bu/yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoh.pdf',
                ],
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_mode_user_agent',
        ),
    ],
)
async def test_multi_archive_long_names(
    aiohttp_client,
    send_email_mock,
    s3_emulation,
    request_data: dict,
    expected: dict,
):
    app, client, user = await prepare_client(aiohttp_client)
    document1 = await prepare_document_data(
        app,
        user,
        title=TEST_LONG_FILENAME_BASE_1,
        extension='.pdf',
        content=TEST_CONTENT,
    )
    document2 = await prepare_document_data(
        app,
        user,
        title=TEST_LONG_FILENAME_BASE_2,
        extension='.pdf',
        content=TEST_CONTENT,
    )

    response = await client.post(
        path='/downloads/multi-archive',
        json={
            'docIds': [document1.id, document2.id],
            **request_data.get('json', {}),
        },
        headers={
            **prepare_auth_headers(user),
            **request_data.get('headers', {}),
        },
    )
    assert response.status == 200, f'status mismatch, {response.status} != 200'

    json_response = await response.json()
    assert json_response == {
        'type': 'success',
        'message': (
            'Архів формується, на вашу пошту буде відправлено листа з посиланням для завантаження'
        ),
    }
    send_email_kwargs = send_email_mock.mock_calls[0].kwargs
    download_link = send_email_kwargs['context']['download_links'][0]
    archive_id = download_link.split('/')[-1].split('?')[0]

    response = await client.get(
        path=f'/downloads/multi-archive/{archive_id}',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200, f'status mismatch, {response.status} != 200'
    response_body = await response.read()
    with zipfile.ZipFile(io.BytesIO(response_body), 'r') as zip_file:
        names = zip_file.namelist()
        names = sorted(names)
        assert names == sorted(expected['names'])

    archive_filename = response.content_disposition.filename
    assert archive_filename == expected['archive_filename']

    if max_path_length := expected.get('max_path_length'):
        for name in names:
            path = pathlib.Path(archive_filename) / name
            assert len(str(path)) <= max_path_length


async def test_download_multi_archive_illegal_archive_id(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    s3_emulation.upload_document_content(
        document_id=document.id,
        content=TEST_CONTENT,
    )
    response = await client.get(
        f'/downloads/multi-archive/{document.id}',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 404, f'status mismatch, {response.status} != 200'
    assert len(s3_emulation.download_calls) == 0


@pytest.mark.parametrize(
    'params, expected',
    [
        # Without params, it should return a first version available for the company
        pytest.param(
            {},
            {
                'http_status': HTTPStatus.OK,
                's3_key': f'document_versions/{TEST_UUID_1}',
                'content': b'version-1',
            },
            id='no_params',
        ),
        # Latest marker means the latest available version for the company
        pytest.param(
            {'version': 'latest'},
            {
                'http_status': HTTPStatus.OK,
                's3_key': f'document_versions/{TEST_UUID_3}',
                'content': b'version-3',
            },
            id='latest',
        ),
        # We can select a specific version by its id
        pytest.param(
            {'version': TEST_UUID_1},
            {
                'http_status': HTTPStatus.OK,
                's3_key': f'document_versions/{TEST_UUID_1}',
                'content': b'version-1',
            },
            id='select_specific_version',
        ),
        # We can't download a version which was not sent to the company
        pytest.param(
            {'version': TEST_UUID_4},
            {
                'http_status': HTTPStatus.NOT_FOUND,
            },
            id='can_not_download_not_sent_recipient_version',
        ),
        # We can't download a version of another document
        pytest.param(
            {'version': TEST_UUID_5},
            {
                'http_status': HTTPStatus.NOT_FOUND,
            },
            id='can_not_download_other_document_version',
        ),
    ],
)
async def test_download_version(
    aiohttp_client,
    params: dict,
    expected: dict,
    s3_emulation,
):
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=TEST_COMPANY_EDRPOU)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    document1 = await prepare_document_data(app, user, id=TEST_DOCUMENT_ID_1, content=b'content-1')
    document2 = await prepare_document_data(app, user, id=TEST_DOCUMENT_ID_2, content=b'content-2')

    # 1. version from an owner (sent)
    # 2. version from a recipient (sent)
    # 3. version from an owner (sent)
    # 4. version from an owner (not sent)
    # 5. version from a recipient (not sent)
    await prepare_document_version(
        id=TEST_UUID_1,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document1.id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now() - datetime.timedelta(days=4),
        content=b'version-1',
    )
    await prepare_document_version(
        id=TEST_UUID_2,
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        company_id=recipient.company_id,
        document_id=document1.id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now() - datetime.timedelta(days=3),
        content=b'version-2',
    )
    await prepare_document_version(
        id=TEST_UUID_3,
        document_id=document1.id,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now() - datetime.timedelta(days=1),
        content=b'version-3',
    )
    await prepare_document_version(
        id=TEST_UUID_4,
        document_id=document1.id,
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        company_id=recipient.company_id,
        is_sent=False,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now(),
        content=b'version-4',
    )

    # Second document
    await prepare_document_version(
        id=TEST_UUID_5,
        document_id=document2.id,
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        company_id=recipient.company_id,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now(),
        content=b'version-5',
    )

    url = f'/downloads/{document1.id}/original'
    headers = prepare_auth_headers(user)

    # Download existed and valid recipient document version
    response = await client.get(
        url,
        params=params,
        headers=headers,
    )
    assert response.status == expected['http_status']
    if response.status != HTTPStatus.OK:
        return

    data = await response.read()
    assert data == expected['content']
    download_file = s3_emulation.download_calls[0]
    assert download_file.key == expected['s3_key']


@pytest.mark.parametrize(
    'params, expected_content',
    [
        ({'version': 'latest'}, b'test-content-2'),
        ({'version': 'original'}, b'test-content-1'),
        (None, b'test-content-1'),
    ],
)
async def test_download_version_marker(aiohttp_client, params, expected_content):
    app, client, user = await prepare_client(aiohttp_client)

    document1 = await prepare_document_data(app, user, id=TEST_UUID_1, content=b'content')
    await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document1.id,
        date_created=utc_now(),
        content=b'test-content-1',
    )
    await prepare_document_version(
        id=TEST_UUID_3,
        document_id=document1.id,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        date_created=utc_now() + datetime.timedelta(minutes=1),
        content=b'test-content-2',
    )

    response = await client.get(
        path=f'/downloads/{TEST_UUID_1}/original',
        params=params,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    data = await response.read()
    assert data == expected_content


@pytest.mark.parametrize(
    'versions_data, params, expected',
    [
        pytest.param(
            [],
            {'version': 'latest'},
            {
                # version isn't found, because by default we require resolving version
                # marker to something
                'http_status': HTTPStatus.NOT_FOUND,
                'content': b'test-content-2',
                'error_code': 'object_does_not_exist',
                'error_reason': 'Версію документу не знайдено у базі даних',
            },
            id='latest_default',
        ),
        pytest.param(
            # If version marker not provided, we should return the first version as usual
            [],
            {'version': 'latest', 'version_required': '1'},
            {
                # version isn't found, because by default we require resolving version
                # marker to something
                'http_status': HTTPStatus.NOT_FOUND,
                'content': b'test-content-2',
                'error_code': 'object_does_not_exist',
                'error_reason': 'Версію документу не знайдено у базі даних',
            },
            id='latest_with_required',
        ),
        pytest.param(
            # If version marker not provided, we should return the first version as usual
            [],
            None,
            {'http_status': HTTPStatus.OK, 'content': b'content'},
            id='no_versioned_no_params',
        ),
        pytest.param(
            [],
            {'version': 'latest', 'version_required': '0'},
            {'http_status': HTTPStatus.OK, 'content': b'content'},
            id='version_not_required',
        ),
        pytest.param(
            [
                {'id': TEST_UUID_2, 'content': b'content-2'},
                {'id': TEST_UUID_3, 'content': b'content-3'},
            ],
            {'version': 'latest', 'version_required': '0'},
            {'http_status': HTTPStatus.OK, 'content': b'content-3'},
            id='version_not_required_2',
        ),
        # if uuid not found, we should return the original document
        pytest.param(
            [
                {'id': TEST_UUID_2, 'content': b'content-2'},
                {'id': TEST_UUID_3, 'content': b'content-3'},
            ],
            {'version': TEST_UUID_5, 'version_required': '0'},
            {'http_status': HTTPStatus.OK, 'content': b'content'},
            id='version_not_required_3',
        ),
    ],
)
async def test_download_version_marker_not_required(
    aiohttp_client,
    versions_data: list[dict],
    params: dict,
    expected: dict,
):
    app, client, user = await prepare_client(aiohttp_client)

    document1 = await prepare_document_data(app, user, id=TEST_UUID_1, content=b'content')

    for version_data in versions_data or []:
        await prepare_document_version(
            role_id=user.role_id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            document_id=document1.id,
            date_created=utc_now(),
            **version_data,
        )

    response = await client.get(
        path=f'/downloads/{TEST_UUID_1}/original',
        params=params,
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected['http_status']
    if response.status == HTTPStatus.OK:
        data = await response.read()
        assert data == expected['content']
    else:
        data = await response.json()
        assert 'code' in data
        assert 'reason' in data
        assert data['code'] == expected['error_code']
        assert data['reason'] == expected['error_reason']


async def test_download_review_history_xlsx(aiohttp_client) -> None:
    app, client, user = await prepare_client(aiohttp_client)
    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    coworker3 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(
        app=app,
        owner=user,
        id=TEST_UUID_1,
        date_listing='2022-01-01T00:00:00+2:00',
    )
    await prepare_review_requests(client, document, user, reviewers=[coworker1, coworker2])
    await prepare_review(client, user=coworker2, document=document, review_type=ReviewType.reject)
    await prepare_review(client, user=coworker3, document=document, review_type=ReviewType.approve)

    response = await client.get(
        path=DOWNLOAD_REVIEW_HISTORY_XLSX_URL.format(document_id=TEST_UUID_1),
        headers=prepare_auth_headers(user),
    )

    # asserts
    assert response.status == HTTPStatus.OK
    headers = response.headers
    assert headers['Content-Disposition'] == (
        f'Attachment; filename="IstoriiaPohodzhennia-{TEST_UUID_1}.xlsx"'
    )

    data = await response.read()
    assert data is not None
    workbook = openpyxl.load_workbook(filename=io.BytesIO(data))
    sheet = workbook.active
    assert sheet.title == 'Погодження'
    result = [[cell.value for cell in row] for row in sheet.iter_rows(max_row=6, max_col=2)]
    expected = [
        # Info about document in format key: value
        ['Документ', None],
        ['ID', '41a2d812-d8d5-4f5e-bff7-ca9e79a653a7'],
        ['Назва', 'Test Document'],
        ['Номер', 'Test Number'],
        ['Дата завантаження або отримання', '01.01.2022 00:00:00'],
        [None, None],
    ]
    assert result == expected

    result = [[cell.value for cell in row] for row in sheet.iter_rows(min_row=7)]
    expected = [
        # Review table
        ['Співробітник', 'Електронна адреса', 'Дія', 'Дата'],
        [None, '<EMAIL>', 'Запросив співробітника <EMAIL>', ANY],
        [None, '<EMAIL>', 'Запросив співробітника <EMAIL>', ANY],
        [None, '<EMAIL>', 'Погоджує', ANY],
        [None, '<EMAIL>', 'Відхиляє', ANY],
    ]
    assert_list_any_order(data=result, expected=expected)


@pytest.mark.parametrize('url', [DOWNLOAD_ARCHIVE_URL, DOWNLOAD_ARCHIVE_API_URL])
@pytest.mark.parametrize(
    'convert_to_signature_format',
    [SignatureArchiveFormat.internal_appended.value, None],
)
async def test_download_original_as_container(aiohttp_client, url, convert_to_signature_format):
    """
    Test case - only original raw file (without signatures)
    """
    title, extension = 'file', '.txt'

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        title=title,
        extension=extension,
        content=TEST_CONTENT,
    )

    response = await client.get(
        url.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': convert_to_signature_format}
        if convert_to_signature_format
        else {},
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == title + '.zip'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert set(names) == {title + extension, 'Instruktsiia.pdf'}

        with zip_handler.open(title + extension) as file_handler:
            assert file_handler.read() == TEST_CONTENT

    document_actions = await select_document_actions_for(document_id=document.id)
    if url == DOWNLOAD_ARCHIVE_URL:
        assert len(document_actions) == 1
    else:
        assert len(document_actions) == 0


@pytest.mark.parametrize(
    'url',
    [
        DOWNLOAD_ARCHIVE_URL,
        DOWNLOAD_ARCHIVE_API_URL,
    ],
)
async def test_download_container_with_signatures_single(aiohttp_client, s3_emulation, url):
    title, extension = 'тестовий_файл_123', '.txt'
    title_translate = translit(title)
    app, client, user = await prepare_client(aiohttp_client, company_edrpou='55555555')
    document = await prepare_document_data(
        app, user, title=title, extension='.txt', is_internal=True
    )
    document_content = b'123test\n'
    s3_emulation.upload_document_content(
        document_id=document.id,
        content=document_content,
    )

    expected_names = {
        title_translate + extension,
        title_translate + extension + '.p7s',
        'Instruktsiia.pdf',
        'Kvytantsiia.pdf',
    }

    signature1 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.external_separated,
        _set_stamp_fields=False,
    )
    key_content = decode_base64_str((DATA_FILES_PATH / 'sign_by_key').read_text())
    s3_emulation.upload_external_signature_key_content(
        document_id=document.id,
        signature_id=signature1.id,
        content=key_content,
    )

    response = await client.get(
        url.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': SignatureArchiveFormat.internal_appended.value},
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == title_translate + '.zip'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert set(names) == expected_names
        with zip_handler.open(title_translate + extension) as file_handler:
            assert file_handler.read() == document_content
        expected_content = (DATA_FILES_PATH / 'test.txt.p7s').read_bytes()
        with zip_handler.open(title_translate + extension + '.p7s') as file_handler:
            assert file_handler.read() == expected_content


@pytest.mark.parametrize(
    'url',
    [
        DOWNLOAD_ARCHIVE_URL,
        DOWNLOAD_ARCHIVE_API_URL,
    ],
)
async def test_download_container_with_signatures_multiple(aiohttp_client, s3_emulation, url):
    title, extension = 'тестовий_файл_123', '.txt'
    title_translate = translit(title)
    app, client, user = await prepare_client(aiohttp_client, company_edrpou='55555555')
    document = await prepare_document_data(
        app, user, title=title, extension='.txt', is_internal=True
    )
    document_content = b'123test\n'
    s3_emulation.upload_document_content(
        document_id=document.id,
        content=document_content,
    )

    expected_names = {
        title_translate + extension,
        title_translate + extension + '.p7s',
        'Instruktsiia.pdf',
        'Kvytantsiia.pdf',
    }

    signature1 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.external_separated,
        _set_stamp_fields=False,
    )
    key_content = decode_base64_str((DATA_FILES_PATH / 'sign_by_key').read_text())
    s3_emulation.upload_external_signature_key_content(
        document_id=document.id,
        signature_id=signature1.id,
        content=key_content,
    )

    signature2 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.external_separated,
        _set_stamp_fields=True,
        _set_key_fields=False,
    )
    stamp_content = decode_base64_str((DATA_FILES_PATH / 'sign_by_stamp').read_text())
    s3_emulation.upload_external_signature_stamp_content(
        document_id=document.id,
        signature_id=signature2.id,
        content=stamp_content,
    )

    response = await client.get(
        url.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': SignatureArchiveFormat.internal_appended.value},
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == translit(title) + '.zip'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert set(names) == expected_names
        with zip_handler.open(title_translate + extension) as file_handler:
            assert file_handler.read() == document_content
        # Sample container with signature and stamp
        expected_content = (DATA_FILES_PATH / 'test_2.txt.p7s').read_bytes()
        with zip_handler.open(title_translate + extension + '.p7s') as file_handler:
            assert file_handler.read() == expected_content


async def test_download_p7s_container_without_signatures(aiohttp_client, s3_emulation):
    """
    Test not signed document does not return empty container.
    """
    title, extension = 'file', '.txt'
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, title=title, extension=extension)

    document_content = b'123test\n'
    s3_emulation.upload_document_content(
        document_id=document.id,
        content=document_content,
    )

    response = await client.get(
        DOWNLOAD_P7S_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.NOT_FOUND


@pytest.mark.parametrize(
    'url',
    [
        DOWNLOAD_P7S_URL,
        DOWNLOAD_P7S_API_URL,
    ],
)
@pytest.mark.parametrize(
    'signatures, expected_content',
    [
        pytest.param(
            [
                {
                    'format': SignatureFormat.external_separated,
                    'set_key_fields': True,
                    'set_stamp_fields': False,
                    'content': decode_base64_str((DATA_FILES_PATH / 'sign_by_key').read_text()),
                },
                {
                    'format': SignatureFormat.external_separated,
                    'set_key_fields': False,
                    'set_stamp_fields': True,
                    'content': decode_base64_str((DATA_FILES_PATH / 'sign_by_stamp').read_text()),
                },
            ],
            (DATA_FILES_PATH / 'test_2.txt.p7s').read_bytes(),
            id='multiple_external_separated',
        ),
        pytest.param(
            [
                {
                    'format': SignatureFormat.internal_appended,
                    'set_key_fields': True,
                    'set_stamp_fields': False,
                    'content': (DATA_FILES_PATH / 'test.txt.p7s').read_bytes(),
                },
                {
                    'format': SignatureFormat.internal_appended,
                    'set_key_fields': False,
                    'set_stamp_fields': True,
                    'content': (DATA_FILES_PATH / 'test_2.txt.p7s').read_bytes(),
                },
            ],
            (DATA_FILES_PATH / 'test_2.txt.p7s').read_bytes(),
            id='multiple_internal_appended',
        ),
    ],
)
async def test_download_p7s_container_with_signatures_multiple(
    aiohttp_client,
    s3_emulation,
    url: str,
    signatures: list[dict],
    expected_content: bytes,
):
    """
    Test p7s contains an original document with all signatures inside (internal_appended).
    """
    title, extension = 'file', '.txt'
    title_translate = translit(title)
    app, client, user = await prepare_client(aiohttp_client, company_edrpou='55555555')
    document = await prepare_document_data(app, user, title=title, extension='.txt')

    document_content = b'123test\n'
    s3_emulation.upload_document_content(
        document_id=document.id,
        content=document_content,
    )

    for signature_data in signatures:
        sign = await prepare_signature_row(
            document_id=document.id,
            user_id=user.id,
            role_id=user.role_id,
            user_email=user.email,
            format=signature_data['format'],
            _set_key_fields=signature_data['set_key_fields'],
            _set_stamp_fields=signature_data['set_stamp_fields'],
        )
        if sign.format.is_internal:
            s3_emulation.upload_internal_signature_content(
                document_id=document.id,
                signature_id=sign.id,
                content=signature_data['content'],
            )
        else:
            uploader = (
                s3_emulation.upload_external_signature_key_content
                if signature_data['set_key_fields']
                else s3_emulation.upload_external_signature_stamp_content
            )
            uploader(
                document_id=document.id,
                signature_id=sign.id,
                content=signature_data['content'],
            )

    expected_name = title_translate + extension + '.p7s'

    response = await client.get(
        url.format(document_id=document.id),
        headers=prepare_auth_headers(user),
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == expected_name

    data = await response.read()
    assert data == expected_content


async def test_generate_converted_archive_with_internal_appended_and_external_separated(
    aiohttp_client,
    s3_emulation,
):
    """
    Test convert different signature formats.
    Use multiple internal_appended signatures to test choosing the last one before conversation.
    (internal_appended, internal_appended, external_separated) => internal_appended.
    """
    title, extension = 'test', '.txt'
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, title='test', extension='.txt')

    s3_emulation.upload_document_content(
        document_id=document.id,
        content=b'123test\n',
    )

    expected_names = {
        title + extension,
        title + extension + '.p7s',
        'Instruktsiia.pdf',
        'Kvytantsiia.pdf',
    }

    signature1 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.internal_appended,
        date_created=utc_now(),
        _set_stamp_fields=False,
    )
    content = (DATA_FILES_PATH / 'test.txt.p7s').read_bytes()
    s3_emulation.upload_internal_signature_content(
        document_id=document.id,
        signature_id=signature1.id,
        content=content,
    )

    signature2 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.internal_appended,
        date_created=utc_now() + datetime.timedelta(minutes=1),
        _set_stamp_fields=True,
        _set_key_fields=False,
    )
    content = (DATA_FILES_PATH / 'test_2.txt.p7s').read_bytes()
    s3_emulation.upload_internal_signature_content(
        document_id=document.id,
        signature_id=signature2.id,
        content=content,
    )

    signature3 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        date_created=utc_now() + datetime.timedelta(minutes=2),
        format=SignatureFormat.external_separated,
        _set_stamp_fields=False,
    )
    stamp_content = decode_base64_str((DATA_FILES_PATH / 'sign_by_key_3').read_text())
    s3_emulation.upload_external_signature_key_content(
        document_id=document.id,
        signature_id=signature3.id,
        content=stamp_content,
    )

    response = await client.get(
        DOWNLOAD_ARCHIVE_API_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': SignatureArchiveFormat.internal_appended.value},
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == title + '.zip'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert set(names) == expected_names
        expected_content = (
            DATA_FILES_PATH / 'test_internal_appended_with_external_converted.txt.p7s'
        ).read_bytes()

        with zip_handler.open(title + extension + '.p7s') as file_handler:
            x = file_handler.read()
            assert x == expected_content


async def test_generate_converted_archive_with_different_signatures_format(
    aiohttp_client, s3_emulation
):
    """
    Test convert different signature formats:
    (external_separated, internal_separated => internal_appended
    """
    title, extension = 'test', '.txt'
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, title='test', extension='.txt')

    s3_emulation.upload_document_content(
        document_id=document.id,
        content=b'123test\n',
    )

    expected_names = {
        title + extension,
        title + extension + '.p7s',
        'Instruktsiia.pdf',
        'Kvytantsiia.pdf',
    }

    signature_1 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.internal_separated,
    )
    # Set s3 mock for internal_separated format signature
    sign = (DATA_FILES_PATH / 'test. Pidpys 55555555.p7s').read_bytes()
    s3_emulation.upload_internal_signature_content(
        document_id=document.id,
        signature_id=signature_1.id,
        content=sign,
    )

    signature_2 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.external_separated,
    )
    # Read external_separated signature (base64 encoded)
    key = (DATA_FILES_PATH / 'sign_by_key_2').read_text()
    s3_emulation.upload_external_signature_key_content(
        document_id=document.id,
        signature_id=signature_2.id,
        content=decode_base64_str(key),
    )

    response = await client.get(
        DOWNLOAD_ARCHIVE_API_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': SignatureArchiveFormat.internal_appended.value},
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    assert response.content_disposition.filename == title + '.zip'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert set(names) == expected_names
        expected_content = (
            DATA_FILES_PATH / 'Internal_and_external_sign_converted.txt.p7s'
        ).read_bytes()

        with zip_handler.open(title + extension + '.p7s') as file_handler:
            x = file_handler.read()
            assert x == expected_content


@pytest.mark.parametrize(
    'document_data, request_data, expected',
    [
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {},
            {
                'archive_filename': f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.zip',
                'expected_names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.txt',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.p7s',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': None,
            },
            id='default_mode_no_request_params',
        ),
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {},
            {
                'archive_filename': f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.zip',
                'expected_names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.txt',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.p7s',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': None,
            },
            id='default_mode_no_request_params',
        ),
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_5)',
                },
            },
            {
                'archive_filename': f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.zip',
                'expected_names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.txt',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.p7s',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': None,
            },
            id='default_mode_user_agent',
        ),
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                },
                'params': {
                    'filenames_mode': 'default',
                },
            },
            {
                'archive_filename': f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.zip',
                'expected_names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.txt',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 255 - 4]}.p7s',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': None,
            },
            id='default_mode_requested_in_params',
        ),
        # In Windows mode filenames should be shortened
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                },
            },
            {
                'archive_filename': 'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyrok.zip',  # noqa: E501
                'expected_names': [
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyroko.p7s',
                    'yak_umru_to_pokhovaite_mene_na_mohyli_sered_stepu_shyrokoho_na_Vkraini_mylii_shchob_lany_shyroko.txt',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_mode_user_agent',
        ),
        # Some companies may want to make filenames even shorten then we do by default
        pytest.param(
            {
                'title': TEST_LONG_FILENAME_BASE_1,
                'extension': '.txt',
            },
            {
                'params': {
                    'filenames_max_length': 30,
                }
            },
            {
                'archive_filename': f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 30 - 4]}.zip',
                'expected_names': [
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 30 - 4]}.txt',
                    f'{TEST_LONG_FILENAME_TRANSLIT_BASE_1[: 30 - 4]}.p7s',
                    'Instruktsiia.pdf',
                    'Kvytantsiia.pdf',
                ],
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='custom_limit',
        ),
    ],
)
async def test_archive_long_filenames(
    aiohttp_client,
    s3_emulation,
    document_data: dict,
    request_data: dict,
    expected: dict,
    test_flags,
):
    """
    Test that we on windows we make filenames in archive shorter
    """

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app=app,
        owner=user,
        title=document_data['title'],
        extension=document_data['extension'],
    )

    s3_emulation.upload_document_content(
        document_id=document.id,
        content=b'123test\n',
    )

    signature_1 = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=SignatureFormat.internal_separated,
        key_owner_edrpou='55555555',
    )
    # Set s3 mock for internal_separated format signature
    sign = (DATA_FILES_PATH / 'test. Pidpys 55555555.p7s').read_bytes()
    s3_emulation.upload_internal_signature_content(
        document_id=document.id,
        signature_id=signature_1.id,
        content=sign,
    )

    response = await client.get(
        path=DOWNLOAD_ARCHIVE_API_URL.format(document_id=document.id),
        params=request_data.get('params', {}),
        headers={
            **prepare_auth_headers(user),
            **request_data.get('headers', {}),
        },
    )
    await wait_streaming_response_eof(response)
    assert response.status == HTTPStatus.OK
    archive_filename = response.content_disposition.filename
    assert archive_filename == expected['archive_filename']

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        names = sorted(names)

        assert names == sorted(expected['expected_names'])

    if max_path_length := expected.get('max_path_length'):
        for name in names:
            path = pathlib.Path(archive_filename) / name
            assert len(str(path)) <= max_path_length


@pytest.mark.parametrize(
    'signature_format, expected_http_status',
    [
        [SignatureFormat.internal_separated, HTTPStatus.OK],
        [SignatureFormat.external_separated, HTTPStatus.OK],
        [SignatureFormat.internal_appended, HTTPStatus.OK],
        [SignatureFormat.internal_wrapped, HTTPStatus.BAD_REQUEST],
        [SignatureFormat.internal_asic, HTTPStatus.BAD_REQUEST],
        [SignatureFormat.external_wrapped, HTTPStatus.BAD_REQUEST],
    ],
)
async def test_download_archive_convert_validation(
    aiohttp_client,
    signature_format: SignatureFormat,
    expected_http_status,
    s3_emulation,
):
    app, client, user = await prepare_client(aiohttp_client, company_edrpou='55555555')
    document = await prepare_document_data(
        app,
        user,
        is_internal=True,
        content=b'123test\n',
    )

    key = (DATA_FILES_PATH / 'sign_by_key').read_text()

    signature = await prepare_signature_row(
        document_id=document.id,
        user_id=user.id,
        role_id=user.role_id,
        user_email=user.email,
        format=signature_format,
        key=key,
    )
    if signature_format.is_internal:
        s3_emulation.upload_internal_signature_content(
            document_id=document.id,
            signature_id=signature.id,
            content=decode_base64_str(key),
        )
    else:
        s3_emulation.upload_external_signature_key_content(
            document_id=document.id,
            signature_id=signature.id,
            content=decode_base64_str(key),
        )

    response = await client.get(
        DOWNLOAD_ARCHIVE_API_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        params={'convert_to_signature_format': SignatureArchiveFormat.internal_appended.value},
    )
    await wait_streaming_response_eof(response)
    assert response.status == expected_http_status.value
