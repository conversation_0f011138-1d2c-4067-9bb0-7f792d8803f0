import logging

import sqlalchemy as sa

from api.public.data import to_recipient_company_data, to_recipient_user_data
from api.public.types import RetrieveRecipientOptions
from app.auth.tables import company_table, user_active_role_company_join, user_table
from app.contacts.tables import contact_person_table, contact_table
from app.documents.db import (
    build_is_document_multi_sign_clause,
)
from app.documents.tables import (
    document_recipients_table,
    document_table,
    documents_processed_table,
)
from app.documents_fields.tables import (
    document_parameters_table,
    documents_fields_table,
)
from app.documents_fields.types import DocumentsField
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.database import DBConnection, DBRow
from app.lib.enums import DocumentStatus, enum_values
from app.lib.types import DataDict
from app.models import select_all, select_one
from app.signatures.tables import signature_table

logger = logging.getLogger(__name__)


async def select_company_summary(
    conn: DBConnection, options: RetrieveRecipientOptions
) -> DBRow | None:
    recipient_edrpou = options.edrpou
    user_edrpou = options.user_edrpou

    clause = sa.and_(
        document_table.c.edrpou_owner == user_edrpou,
        document_recipients_table.c.edrpou == recipient_edrpou,
        build_is_document_multi_sign_clause(),
    )

    finished = (
        sa.select(
            [
                document_recipients_table.c.edrpou,
                sa.func.count(sa.distinct(document_table.c.id)).label('count_finished'),
                sa.func.max(signature_table.c.date_created).label('date_last_signature'),
            ]
        )
        .select_from(
            document_table.outerjoin(
                signature_table, signature_table.c.document_id == document_table.c.id
            ).outerjoin(
                document_recipients_table,
                document_recipients_table.c.document_id == document_table.c.id,
            )
        )
        .where(sa.and_(clause, document_table.c.status_id == DocumentStatus.finished.value))
        .group_by(document_recipients_table.c.edrpou)
    ).alias()

    sent = (
        sa.select(
            [
                document_recipients_table.c.edrpou,
                sa.func.count(sa.distinct(document_table.c.id)).label('count_sent'),
            ]
        )
        .select_from(
            document_table.outerjoin(
                document_recipients_table,
                document_recipients_table.c.document_id == document_table.c.id,
            )
        )
        .where(sa.and_(*clause, document_table.c.status_id >= DocumentStatus.sent.value))
        .group_by(document_recipients_table.c.edrpou)
    ).alias()

    return await select_one(
        conn,
        sa.select(
            [
                company_table.c.edrpou,
                company_table.c.name,
                company_table.c.date_created,
                finished.c.count_finished,
                finished.c.date_last_signature,
                sent.c.count_sent,
            ]
        )
        .select_from(
            company_table.outerjoin(
                finished, finished.c.edrpou == company_table.c.edrpou
            ).outerjoin(sent, sent.c.edrpou == company_table.c.edrpou)
        )
        .where(company_table.c.edrpou == recipient_edrpou),
    )


async def get_all_company_accessible_fields(
    conn: DBConnection, company_id: str
) -> list[DocumentsField]:
    """Get all document fields by company_id"""
    rows = await select_all(
        conn=conn,
        query=(
            documents_fields_table.select().where(documents_fields_table.c.company_id == company_id)
        ),
    )
    return [DocumentsField.from_db(row) for row in rows]


async def select_available_document_additional_fields(
    conn: DBConnection, company_id: str, document_ids: set[str] | list[str]
) -> list[DBRow]:
    """Get document fields which available for user by his company_id"""
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    document_parameters_table.c.field_id,
                    document_parameters_table.c.document_id,
                    document_parameters_table.c.is_required,
                    document_parameters_table.c.value,
                    document_parameters_table.c.date_updated,
                    document_parameters_table.c.date_created,
                    documents_fields_table.c.name,
                    documents_fields_table.c.type,
                ]
            )
            .select_from(
                document_parameters_table.outerjoin(
                    documents_fields_table,
                    documents_fields_table.c.id == document_parameters_table.c.field_id,
                )
            )
            .where(
                sa.and_(
                    document_parameters_table.c.company_id == company_id,
                    document_parameters_table.c.document_id.in_(document_ids),
                    documents_fields_table.c.date_deleted.is_(None),
                )
            )
        ),
    )


async def select_incoming_documents_by_ids(
    conn: DBConnection, ids: list[str], company_id: str
) -> list[DBRow]:
    filters = [document_table.c.id.in_(ids)]

    if get_flag(FeatureFlags.ENABLE_INCOMING_DOCUMENTS_DB_FILTERING_STATUS):
        filters.append(
            document_table.c.status_id.in_(
                [value for value in enum_values(DocumentStatus) if value != 7000]
            )
        )

    return await select_all(
        conn,
        (
            sa.select(
                [
                    document_table,
                    company_table.c.name.label('company_name'),
                    documents_processed_table.c.processed,
                ]
            )
            .select_from(
                document_table.outerjoin(
                    company_table,
                    sa.and_(
                        company_table.c.edrpou == document_table.c.edrpou_owner,
                        company_table.c.is_legal.is_(True),
                    ),
                ).outerjoin(
                    documents_processed_table,
                    sa.and_(
                        documents_processed_table.c.document_id == document_table.c.id,
                        documents_processed_table.c.company_id == company_id,
                    ),
                )
            )
            .where(sa.and_(*filters))
            .order_by(document_table.c.seqnum.desc())
        ),
    )


async def select_recipient_summary(
    conn: DBConnection, options: RetrieveRecipientOptions
) -> DataDict:
    result: DataDict = {}

    company_summary = await select_company_summary(conn, options)
    if company_summary:
        result.update(to_recipient_company_data(company_summary))

    company_users = await select_users_summary(conn, options)
    if company_summary and company_users:
        result['users'] = [to_recipient_user_data(item) for item in company_users]

    return result


async def select_users_summary(
    conn: DBConnection, options: RetrieveRecipientOptions
) -> list[DBRow]:
    recipient_edrpou = options.edrpou
    user_company_id = options.user_company_id

    contact = (
        sa.select([contact_person_table.c.email, contact_person_table.c.main_recipient])
        .select_from(
            contact_table.outerjoin(
                contact_person_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            )
        )
        .where(
            sa.and_(
                contact_table.c.company_id == user_company_id,
                contact_table.c.edrpou == recipient_edrpou,
            )
        )
    ).alias()

    return await select_all(
        conn,
        (
            sa.select(
                [
                    user_table.c.email,
                    user_table.c.first_name,
                    user_table.c.last_name,
                    user_table.c.phone,
                    user_table.c.date_created,
                    user_table.c.email_confirmed,
                    contact.c.main_recipient,
                ]
            )
            .select_from(
                user_active_role_company_join.outerjoin(
                    contact, contact.c.email == user_table.c.email
                )
            )
            .where(
                sa.and_(
                    company_table.c.edrpou == recipient_edrpou,
                    company_table.c.is_legal.is_(True),
                )
            )
        ),
    )


async def select_id_document_fields(conn: DBConnection, document_id: str) -> list[str]:
    """Returns ids of all document fields by document_id"""

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_parameters_table.c.field_id]).where(
                document_parameters_table.c.document_id == document_id
            )
        ),
    )
    return [row.field_id for row in rows]
