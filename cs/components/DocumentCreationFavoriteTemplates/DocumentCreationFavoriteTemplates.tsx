import React from 'react';

import ListPagination from 'components/DocumentCreationCompanyTemplates/ListPagination';
import PageLayout from 'components/DocumentCreationCompanyTemplates/PageLayout';
import SearchBar from 'components/DocumentCreationCompanyTemplates/SearchBar';
import SearchFetchProvider, {
    useSearchFetch,
} from 'components/DocumentCreationCompanyTemplates/SearchFetchProvider';
import { SearchFormProvider } from 'components/DocumentCreationCompanyTemplates/SearchFormProvider';
import EmptyScreen from 'components/DocumentCreationFavoriteTemplates/EmptyScreen';
import FavoriteTemplateList from 'components/DocumentCreationFavoriteTemplates/FavoriteTemplateList';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

const DocumentCreationFavoriteTemplates: React.FC = () => {
    const { isNoData } = useSearchFetch();

    if (isNoData) {
        return (
            <PageLayout title={t`Обрані шаблони`}>
                <EmptyScreen />
            </PageLayout>
        );
    }

    return (
        <PageLayout title={t`Обрані шаблони`}>
            <SearchBar sticky />
            <FavoriteTemplateList />
            <ListPagination />
        </PageLayout>
    );
};

export default () => {
    return (
        <SearchFormProvider>
            <SearchFetchProvider
                onSearch={() => {
                    eventTracking.sendToGTMV4({
                        event: 'ec_template_search',
                    });
                }}
                type="favorite"
            >
                <DocumentCreationFavoriteTemplates />
            </SearchFetchProvider>
        </SearchFormProvider>
    );
};
