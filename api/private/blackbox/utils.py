import asyncio
import json
import logging
import os
import uuid

from api.private.blackbox.types import (
    BufferDeta<PERSON>,
    UploadCtx,
    UploadDocumentOptions,
)
from api.uploads.types import MetaData
from api.uploads.utils import handle_encoding, is_one_sign_type, unarchive_file_name
from app.auth.db import decrement_company_upload_documents_left, select_role_by, update_company_ipn
from app.auth.types import User
from app.billing.api import charge_document
from app.contacts.db import update_main_recipient
from app.delayed_task.utils import add_delayed_task
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import (
    add_document_antivirus_check,
)
from app.document_automation.utils import start_document_automation
from app.documents.constants import (
    DEFAULT_EXPECTED_OWNER_SIGNATURES,
    DEFAULT_EXPECTED_RECIPIENT_SIGNATURES,
    DEFAULT_SIGNATURES_TO_FINISH,
)
from app.documents.db import (
    add_documents_meta,
    insert_documents,
    insert_listings,
    insert_recipients,
)
from app.documents.enums import AccessSource, DocumentSource, FirstSignBy
from app.documents.types import (
    Document,
    DocumentDetails,
    UpsertDocumentRecipientDict,
)
from app.documents.utils import (
    connect_document_with_tags_by_contact,
    get_internal_signature_s3_key,
    send_bilateral_document_to_recipient,
)
from app.events import document_actions
from app.lib import eusign_utils, s3_utils
from app.lib.chunks import gather_chunks
from app.lib.database.types import DBConnection
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus, SignatureFormat, SignatureType, Source
from app.lib.eusign_utils import generate_hash_base64_sync
from app.lib.helpers import join_comma_separated_emails, run_sync
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.lib.validators import int_or_none
from app.services import services
from app.sign_sessions import utils as sign_session
from app.signatures.db import insert_signature
from app.signatures.types import SignatureInfoData
from worker import topics

logger = logging.getLogger(__name__)
BLACKBOX_DOCUMENTS_NOTIFICATION_REDIS_KEY = 'blackbox-documents-notification:{role_id}'


def ensure_file_name(file_name: str, meta: MetaData) -> str:
    if meta.number is None or meta.date is None:
        return file_name

    _, ext = os.path.splitext(unarchive_file_name(file_name))

    _, extra_ext = os.path.splitext(file_name)
    extra_ext = '' if ext == extra_ext else extra_ext

    return f'{meta.number}_{meta.date:%Y%m%d}{ext}{extra_ext}'


async def hash_buffer(
    content: bytes,
) -> BufferDetails:
    return await run_sync(sync_hash_buffer, content=content)


def prepare_file_name(file_name: str) -> str:
    return os.path.basename(handle_encoding(file_name))


def sync_hash_buffer(content: bytes) -> BufferDetails:
    content_hash = generate_hash_base64_sync(content)
    content_length = len(content)

    return BufferDetails(
        content_hash=content_hash,
        content_length=content_length,
    )


async def upload_files(options: UploadDocumentOptions) -> None:
    document = options.document
    files: list[s3_utils.UploadFile] = []

    # Original document
    content_length = len(options.original_content)
    files.append(
        s3_utils.UploadFile(
            key=document.id,
            body=options.original_content,
        )
    )
    logger.info(
        'Append original file to upload to S3',
        extra={'content_length': content_length},
    )

    # Document signature
    content_length = len(options.p7s_content)
    key = get_internal_signature_s3_key(
        document_id=options.document.id,
        signature_id=options.signature_data['id'],
    )
    files.append(
        s3_utils.UploadFile(
            key=key,
            body=options.p7s_content,
        )
    )
    logger.info(
        'Append p7s file to upload to S3',
        extra={'content_length': content_length},
    )

    async with asyncio.timeout(services.config.app.upload_s3_timeout):
        logger.info(
            'Uploading files to S3',
            extra={
                'body_sizes': [item.body_size for item in files],
                'keys': [item.key for item in files],
            },
        )
        await gather_chunks(s3_utils.upload, files)


async def process_blackbox_upload(options: UploadDocumentOptions, user: User) -> UploadCtx:
    """
    Process of upload document via Blackbox, includes:
      - save to S3
      - create records in DB
      - send async jobs
    """

    await upload_files(options)

    document = options.document
    document_id = document.id
    meta = options.meta
    recipient_contact_details = options.recipient_contact_details
    recipient_edrpou = options.recipient_edrpou
    recipient_emails = options.recipient_emails or None

    async with services.db.acquire() as conn:
        async with conn.begin():
            await insert_documents(conn, data=[document.as_db()])

            await add_documents_meta(conn, data=[options.document_meta.as_db()])

            await insert_listings(
                conn,
                data={
                    'document_id': document_id,
                    'access_edrpou': document.edrpou_owner,
                    'sources': AccessSource.default,
                },
            )
            if recipient_edrpou:
                await insert_recipients(
                    conn=conn,
                    recipient=UpsertDocumentRecipientDict(
                        document_id=document_id,
                        emails=recipient_emails,
                        edrpou=recipient_edrpou,
                    ),
                )

            await insert_signature(conn, data=options.signature_data)

            # Send document to recipient.
            if options.share_with_recipient:
                assert recipient_edrpou, 'recipient edrpou expected'
                await send_bilateral_document_to_recipient(
                    conn=conn,
                    document_id=document_id,
                    recipient_edrpou=recipient_edrpou,
                    recipient_emails=recipient_emails,
                    reset_date_delivered=False,
                )

            if recipient_emails and recipient_edrpou:
                for recipient_email in recipient_emails:
                    await add_blackbox_document_notification_for_role(
                        conn=conn,
                        recipient_email=recipient_email,
                        recipient_edrpou=recipient_edrpou,
                        document_id=document_id,
                    )

            vendor = await sign_session.get_sign_session_vendor(conn, user.company_edrpou)
            if vendor:
                await sign_session.create_blackbox_view_session(
                    conn=conn,
                    user=user,
                    vendor=vendor,
                    document_id=document_id,
                )

            if charge_context := options.charge_context:
                await charge_document(conn, context=charge_context)
                await decrement_company_upload_documents_left(
                    conn=conn,
                    company_id=charge_context.payer_id,
                )

            if recipient_contact_details:
                await update_main_recipient(
                    conn=conn,
                    company_id=user.company_id,
                    details=recipient_contact_details,
                )

            await update_company_ipn(conn, meta.owner_edrpou, meta.owner_ipn)
            await update_company_ipn(conn, meta.recipient_edrpou, meta.recipient_ipn)

            document_details = DocumentDetails(document_id, document.title)

            await add_document_antivirus_check(
                conn=conn,
                check=DocumentAntivirusCheck(
                    document_id=document_id,
                    status=AntivirusCheckStatus.checking,
                ),
            )

        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_blackbox_upload,
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
            )
        )

        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_sign,
                document_id=document_details.id_,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
            )
        )

    # Send document to recipient jobs.
    if options.share_with_recipient:
        assert recipient_edrpou, 'recipient edrpou expected'
        # blackbox doesn't support multiple signatures
        await connect_document_with_tags_by_contact(
            edrpou=recipient_edrpou,
            contact_edrpou=user.company_edrpou,
            documents_ids=[document_id],
        )
        # Find and apply template for document in recipient company
        await start_document_automation(
            document_id=document_id,
            company_edrpou=recipient_edrpou,
            source=Source.api_blackbox,
        )

    return UploadCtx(
        document_id=document_id,
        options=options,
    )


def prepare_signature_data(
    document: Document,
    user: User,
    signatures_info: list[DataDict],
    internal_file_name: str,
    recipient_edrpou: str | None,
    recipient_emails: list[str] | None,
) -> DataDict:
    key_info = None
    stamp_info = None
    for sign_info in signatures_info:
        if eusign_utils.guess_type(sign_info) == SignatureType.stamp:
            stamp_info = sign_info
        else:
            key_info = sign_info

    data = {
        'id': str(uuid.uuid4()),
        'document_id': document.id,
        'user_id': user.id,
        'user_role_id': user.role_id,
        'user_role': user.user_role,
        'user_edrpou': user.company_edrpou,
        'user_company_id': user.company_id,
        'user_email': user.email,
        'is_internal': True,
        'algo': None,
        'internal_file_name': internal_file_name,
        # blackbox support uploading only with internal signed documents
        'format': SignatureFormat.internal_separated,
        # The next fields are for "documents" and "document_recipient" tables
        'edrpou_recipient': recipient_edrpou,
        'emails_recipient': recipient_emails,
        'is_first_sign_by_recipient_document': False,
        'is_owner_signature': True,
        'is_recipient_emails_hidden': False,
        'next_status_id': document.status_id,
    }
    data.update(to_signature_key_item_data(key_info))
    data.update(to_signature_stamp_item_data(stamp_info))

    return data


def to_signature_key_item_data(signature_info: DataDict | None) -> DataDict:
    if signature_info is None:
        key_info = SignatureInfoData.to_empty_key()
        return {
            **key_info.to_key_dict(),
            'key': None,
        }

    key_info = SignatureInfoData.from_dict(signature_info)
    return {
        **key_info.to_key_dict(),
        'key': None,
    }


def to_signature_stamp_item_data(signature_info: DataDict | None) -> DataDict:
    if signature_info is None:
        stamp_info = SignatureInfoData.to_empty_stamp()
        return {
            **stamp_info.to_stamp_dict(),
            'stamp': None,
            'is_stamp_uploaded': False,
        }

    stamp_info = SignatureInfoData.from_dict(signature_info)
    return {
        **stamp_info.to_stamp_dict(),
        'stamp': None,
        'is_stamp_uploaded': True,
    }


def prepare_document(
    user: User,
    meta: MetaData,
    file_name: str,
    edrpou_recipient: str | None,
    emails_recipient: list[str] | None,
    share_with_recipient: bool,
) -> Document:
    title, ext = os.path.splitext(unarchive_file_name(file_name))

    _, extra_ext = os.path.splitext(file_name)
    ext = '{}{}'.format(ext, '' if ext == extra_ext else extra_ext)

    is_one_sign = is_one_sign_type(meta.type_)
    expected_recipient_signatures = 0 if is_one_sign else DEFAULT_EXPECTED_RECIPIENT_SIGNATURES

    status = DocumentStatus.approved.value if is_one_sign else DocumentStatus.signed.value
    if share_with_recipient:
        status = (
            DocumentStatus.finished.value if is_one_sign else DocumentStatus.signed_and_sent.value
        )

    now = utc_now()
    doc_id = str(uuid.uuid4())
    return Document(
        id=doc_id,
        role_id=user.role_id,
        uploaded_by=user.role_id,
        edrpou_owner=user.company_edrpou,
        edrpou_recipient=edrpou_recipient,
        # The old column "email_recipient" in "documents" table store multiple emails in single
        # text field, so we need to join them with comma for backward compatibility
        email_recipient=join_comma_separated_emails(emails_recipient),
        title=title,
        extension=ext,
        status_id=status,
        date_document=meta.date,
        type=meta.type_,
        category=int_or_none(meta.category),
        amount=meta.amount,
        number=meta.number,
        source=DocumentSource.vchasno_container,
        vendor=user.token_vendor,
        vendor_id=meta.external_id,
        # VBox will not support uploading 3P documents as first signature will
        # not come from document recipient
        first_sign_by=FirstSignBy.owner,
        signatures_to_finish=DEFAULT_SIGNATURES_TO_FINISH,
        expected_recipient_signatures=expected_recipient_signatures,
        expected_owner_signatures=DEFAULT_EXPECTED_OWNER_SIGNATURES,
        signature_format=SignatureFormat.internal_separated,
        date_created=now,
        date_updated=now,
        # Fill default values
        user_id=None,
        is_internal=False,
        is_multilateral=False,
        archive_name=None,
        date_delivered=None,
        date_finished=None,
        has_changed_for_public_api=True,
        is_invalid_signed=False,
        s3_archive_key=None,
        s3_xml_to_pdf_key=None,
        _row=None,  # type: ignore[arg-type]
    )


async def add_blackbox_document_notification_for_role(
    conn: DBConnection,
    recipient_email: str,
    recipient_edrpou: str,
    document_id: str,
) -> None:
    """
    Append blackbox document_id to document_ids list in redis, which is assigned to the role.
    This list will be parsed by a delayed job after 30 mins to send info about new documents.

    App has a time period of 30 minutes to append all documents before
    the email sending job will be triggered.

    This job should be marked as deprecated once we find a way to fetch all roles in bulk
    which have documents in wait_for_my_sign filter. Currently there is no way to do that
    easily without database models.
    """

    recipient_role = await select_role_by(
        conn=conn,
        mixed=recipient_email,
        company_edrpou=recipient_edrpou,
    )
    if not recipient_role:
        logger.info('Role not found for blackbox document notification')
        return

    async with redis_lock(f'lock_blackbox_documents_notification_{recipient_role.id}'):
        redis_key = BLACKBOX_DOCUMENTS_NOTIFICATION_REDIS_KEY.format(role_id=recipient_role.id)
        document_ids_raw: str | None = await services.redis.get(redis_key)
        if document_ids_raw is None:
            # If document ids are None, we need to create a list with single entry
            # of this document_id
            # And schedule a delayed task to send a job after 30 minutes,
            # when all documents initiated by blackbox process would be assigned to the redis key
            await services.redis.setex(
                name=redis_key,
                time=60 * 60 * 3,  # 3 hours
                value=json.dumps([document_id]),
            )
            await add_delayed_task(
                conn=conn,
                topic=topics.SEND_BLACKBOX_INBOX_DOCUMENTS_NOTIFICATION,
                delay_min=30,
                data={'role_id': recipient_role.id},
            )
        else:
            document_ids: list[str] = json.loads(document_ids_raw)
            document_ids.append(document_id)
            await services.redis.setex(
                name=redis_key,
                time=60 * 60 * 3,  # 3 hours
                value=json.dumps(document_ids),
            )
