from typing import Any
from unittest import mock

import pytest

from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.lib.types import DataDict
from app.reviews.db import (
    insert_review_request,
    select_review_requests,
)
from app.reviews.enums import ReviewRequestStatus, ReviewStatus, ReviewType
from app.reviews.tests.utils import (
    insert_review,
    prepare_review_db,
    prepare_review_request_db,
    prepare_review_settings_db,
    prepare_review_status_db,
    select_reviews,
)
from app.reviews.types import ReviewState
from app.reviews.utils import is_required_reviews_approved
from app.services import services
from app.tests.common import (
    cleanup_on_teardown,
    datetime_test,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
)

TEST_UUID_1 = '00000000-0000-0000-0000-000000000001'
TEST_UUID_2 = '00000000-0000-0000-0000-000000000002'
TEST_UUID_3 = '00000000-0000-0000-0000-000000000003'
TEST_UUID_4 = '00000000-0000-0000-0000-000000000004'


async def test_get_review_status(aiohttp_client):
    app, client, user_1 = await prepare_client(aiohttp_client)
    user_2 = await prepare_user_data(app, email='<EMAIL>')
    user_3 = await prepare_user_data(app, email='<EMAIL>')
    user_4 = await prepare_user_data(app, email='<EMAIL>')
    doc = await prepare_document_data(app, user_1)

    try:
        async with app['db'].acquire() as conn:
            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_1.role_id,
                    'type': ReviewType.approve,
                },
            )
            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_1.role_id,
                    'type': ReviewType.reject,
                },
            )
            await insert_review(
                conn, {'document_id': doc.id, 'role_id': user_1.role_id, 'type': None}
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])
            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.approved

            await insert_review_request(
                conn,
                {
                    'document_id': doc.id,
                    'from_role_id': user_2.role_id,
                    'to_role_id': user_1.role_id,
                },
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])

            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.pending

            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_1.role_id,
                    'type': ReviewType.approve,
                },
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])

            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.approved

            await insert_review_request(
                conn,
                {
                    'document_id': doc.id,
                    'from_role_id': user_1.role_id,
                    'to_role_id': user_3.role_id,
                },
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])

            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.pending

            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_2.role_id,
                    'type': ReviewType.approve,
                },
            )
            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_3.role_id,
                    'type': ReviewType.approve,
                },
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])

            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.approved

            await insert_review(
                conn,
                {
                    'document_id': doc.id,
                    'role_id': user_4.role_id,
                    'type': ReviewType.reject,
                },
            )

            reviews = await select_reviews(
                conn=conn,
                edrpou=user_1.company_edrpou,
                document_ids=[doc.id],
            )
            review_requests = await select_review_requests(conn, user_1.company_id, [doc.id])

            state = ReviewState(
                reviews=reviews,
                requests=review_requests,
                status=None,
                settings=None,
                document_version=None,
                statuses_versions=[],
            )
            assert state.calc_review_status() == ReviewStatus.rejected
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'settings, requests, reviews, is_approved_expected',
    [
        # === Without settings ===
        # No settings, no requests, no reviews
        (
            None,
            [],
            [],
            True,
        ),
        # No settings, no requests, have reviews
        (
            None,
            [],
            [{'role_id': TEST_UUID_1, 'type': ReviewType.approve}],
            True,
        ),
        # No settings (not required by default), have requests, no reviews
        (
            None,
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [],
            True,
        ),
        # === With settings for required review ===
        # no requests, no reviews — consider that the required review is approved
        (
            {'is_required': True, 'is_parallel': True},
            [],
            [],
            True,
        ),
        # requested user doesn't review a document — required review is in progress
        (
            {'is_required': True, 'is_parallel': True},
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [],
            False,
        ),
        # requested user reviewed a document — required review is approved
        (
            {'is_required': True, 'is_parallel': True},
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [{'role_id': TEST_UUID_2, 'type': ReviewType.approve}],
            True,
        ),
        # review is reviewed by another user, but not reviewed by requested user — required
        # review is in progress, until all requested users review a document
        (
            {'is_required': True, 'is_parallel': True},
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [{'role_id': TEST_UUID_1, 'type': ReviewType.approve}],
            False,
        ),
        # review is rejected by requested user — required review is not approved
        (
            {'is_required': True, 'is_parallel': True},
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [{'role_id': TEST_UUID_2, 'type': ReviewType.reject}],
            False,
        ),
        # review was approved by the requested user, but then canceled by another user —
        # consider such review as not approved
        (
            {'is_required': True, 'is_parallel': True},
            [{'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2}],
            [
                {'role_id': TEST_UUID_2, 'type': ReviewType.approve},
                {'role_id': TEST_UUID_1, 'type': ReviewType.reject},
            ],
            False,
        ),
        # Review was approved by more than one user — consider such review as approved
        (
            {'is_required': True, 'is_parallel': True},
            [
                {'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2},
                {'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_1},
            ],
            [
                {'role_id': TEST_UUID_1, 'type': ReviewType.approve},
                {'role_id': TEST_UUID_2, 'type': ReviewType.approve},
            ],
            True,
        ),
        # One user from two requested users removed his review — consider such review as
        # not approved, until all requested users review a document
        (
            {'is_required': True, 'is_parallel': True},
            [
                {'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_2},
                {'from_role_id': TEST_UUID_1, 'to_role_id': TEST_UUID_1},
            ],
            [
                {'role_id': TEST_UUID_1, 'type': ReviewType.approve},
                {'role_id': TEST_UUID_2, 'type': None},
            ],
            False,
        ),
    ],
)
async def test_is_required_reviews_approved(
    aiohttp_client,
    settings: dict[Any, Any] | None,
    requests: list[dict[Any, Any]],
    reviews: list[dict[Any, Any]],
    is_approved_expected: bool,
) -> None:
    app, client, user_1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        role_id=TEST_UUID_1,
    )
    await prepare_user_data(
        app=app,
        email='<EMAIL>',
        role_id=TEST_UUID_2,
    )

    document = await prepare_document_data(app, user_1)

    async with services.db.acquire() as conn:
        if settings:
            await prepare_review_settings_db(
                conn=conn,
                user=user_1,
                document_id=document.id,
                is_required=settings['is_required'],
                is_parallel=settings['is_parallel'],
            )

        for request in requests:
            await prepare_review_request_db(
                conn=conn,
                document_id=document.id,
                from_role_id=request['from_role_id'],
                to_role_id=request['to_role_id'],
            )

        for review in reviews:
            await prepare_review_db(
                conn=conn,
                document_id=document.id,
                role_id=review['role_id'],
                type_=review['type'],
            )

        await prepare_review_status_db(
            conn=conn,
            user=user_1,
            document_id=document.id,
        )

        is_approved = await is_required_reviews_approved(
            conn=conn,
            document_id=document.id,
            edrpou=user_1.company_edrpou,
            version_id=None,
        )
        assert is_approved == is_approved_expected


async def test_is_required_reviews_approved_versioned(
    aiohttp_client,
) -> None:
    """
    Check that is_required_reviews_approved works correctly for versioned documents
    """
    app, client, user_1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        role_id=TEST_UUID_1,
    )
    user_2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        role_id=TEST_UUID_2,
    )

    document = await prepare_document_data(app, user_1)

    async with services.db.acquire() as conn:
        # Review is required for the whole document
        await prepare_review_settings_db(
            conn=conn,
            user=user_1,
            document_id=document.id,
            is_required=True,
            is_parallel=True,
        )

        # The first version of a document is approved by one user
        await prepare_document_version(
            id=TEST_UUID_3,
            document_id=document.id,
            type=DocumentVersionType.new_upload,
            date_created=datetime_test('2021-01-01 00:00:00+00:00'),
            role_id=user_1.role_id,
            company_edrpou=user_1.company_edrpou,
            company_id=user_1.company_id,
        )
        await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user_1.role_id,
            to_role_id=user_2.role_id,
        )
        await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=user_2.role_id,
            type_=ReviewType.approve,
            document_version_id=TEST_UUID_3,
        )
        await prepare_review_status_db(
            conn=conn,
            user=user_1,
            document_id=document.id,
        )

        # The second version of a document is approved by one user and rejected by another
        await prepare_document_version(
            id=TEST_UUID_4,
            document_id=document.id,
            type=DocumentVersionType.new_upload,
            date_created=datetime_test('2021-01-02 00:00:00+00:00'),
            name='version 2',
            extension='.pdf',
            role_id=user_1.role_id,
            company_edrpou=user_1.company_edrpou,
            company_id=user_1.company_id,
        )
        await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user_1.role_id,
            to_role_id=user_2.role_id,
        )
        await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user_1.role_id,
            to_role_id=user_2.role_id,
        )
        await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=user_2.role_id,
            type_=ReviewType.approve,
            document_version_id=TEST_UUID_4,
        )
        await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=user_1.role_id,
            type_=ReviewType.reject,
            document_version_id=TEST_UUID_4,
        )
        await prepare_review_status_db(
            conn=conn,
            user=user_1,
            document_id=document.id,
        )

        # Consider that the first version of a document is approved (1 request, 1 approve)
        is_approved = await is_required_reviews_approved(
            conn=conn,
            document_id=document.id,
            edrpou=user_1.company_edrpou,
            version_id=TEST_UUID_3,
        )
        assert is_approved is True

        # Consider that the second version of a document is not approved (2 requests, 1 approve,
        # 1 reject)
        is_approved = await is_required_reviews_approved(
            conn=conn,
            document_id=document.id,
            edrpou=user_1.company_edrpou,
            version_id=TEST_UUID_4,
        )
        assert is_approved is False


@pytest.mark.parametrize(
    'prepare, expected',
    [
        # Parallel
        pytest.param(
            {
                'reviews': [],
                'requests': [],
            },
            [],
            id='without_reviews_and_requests',
        ),
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    }
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_1],
            id='parallel_one_request_no_reviews',
        ),
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '4',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3],
            id='parallel_multiple_requests_no_reviews',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                    }
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '4',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_2, TEST_UUID_3],
            id='parallel_multiple_requests_one_review',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '3',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '4',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '4',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [],
            id='parallel_multiple_requests_multiple_reviews',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    # pay attention to this review, it's not requested
                    {
                        'role_id': '4',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_1, TEST_UUID_3],
            id='parallel_multiple_requests_multiple_reviews_one_not_requested',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '2',
                        'type': ReviewType.reject,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_3],
            id='parallel_multiple_requests_multiple_reviews_one_not_reviewed',
        ),
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.deleted,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_1, TEST_UUID_2],
            id='parallel_active_and_deleted_requests_no_reviews',
        ),
        # Ordered
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    }
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_1],
            id='ordered_one_request_no_reviews',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                    }
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_2],
            id='ordered_multiple_requests_one_review',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '3',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [],
            id='ordered_multiple_requests_multiple_reviews',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '4',  # not requested
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_2],
            id='ordered_multiple_requests_one_review_one_extra',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                    },
                    {
                        'role_id': '2',
                        'type': ReviewType.reject,
                        'group_id': None,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_3],
            id='ordered_multiple_requests_multiple_reviews_one_not_reviewed',
        ),
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.deleted,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_2],
            id='ordered_active_and_deleted_requests_no_reviews',
        ),
        # Groups
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'group_id': None,
                        'type': ReviewType.approve,
                    }
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_1, TEST_UUID_2],
            id='parallel_multiple_requests_one_review_group2',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'group_id': 'g1',
                        'role_id': '1',  # who reviewed from group
                        'type': ReviewType.approve,
                    }
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_2],
            id='parallel_multiple_requests_one_review_group2',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'group_id': 'g1',
                        'role_id': '1',  # who reviewed from group
                        'type': ReviewType.approve,
                    },
                    {
                        'role_id': '1',
                        'group_id': None,
                        'type': ReviewType.approve,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [TEST_UUID_2],
            id='parallel_multiple_requests_multiple_reviews_group',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'group_id': None,
                        'type': ReviewType.approve,
                    },
                    {
                        'group_id': 'g1',
                        'role_id': '1',
                        'type': ReviewType.approve,
                    },
                    {
                        'group_id': 'g2',
                        'role_id': '1',
                        'type': ReviewType.approve,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
            },
            [],
            id='parallel_multiple_requests_multiple_reviews_group',
        ),
        pytest.param(
            {
                'reviews': [],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_1],
            id='ordered_multiple_requests_no_reviews_group',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'group_id': 'g1',
                        'role_id': '1',
                        'type': ReviewType.approve,
                    }
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [TEST_UUID_2],
            id='ordered_multiple_requests_one_review_group',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'group_id': None,
                        'type': ReviewType.approve,
                    },
                    {
                        'group_id': 'g1',
                        'role_id': '1',
                        'type': ReviewType.approve,
                    },
                    {
                        'group_id': 'g2',
                        'role_id': '1',
                        'type': ReviewType.approve,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g1',
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': None,
                        'to_group_id': 'g2',
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
            },
            [],
            id='ordered_multiple_requests_multiple_reviews_group',
        ),
        # Versioned documents
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                        'document_version_id': TEST_UUID_1,  # prev version
                    },
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                        'document_version_id': TEST_UUID_2,
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                    },
                ],
                'settings': {'is_parallel': True},
                'document_version': {'id': TEST_UUID_2},
            },
            [TEST_UUID_1, TEST_UUID_3],
            id='parallel_multiple_requests_one_review_versioned',
        ),
        pytest.param(
            {
                'reviews': [
                    {
                        'role_id': '1',
                        'type': ReviewType.approve,
                        'group_id': None,
                        'document_version_id': TEST_UUID_2,
                    },
                    {
                        'role_id': '2',
                        'type': ReviewType.approve,
                        'group_id': None,
                        'document_version_id': TEST_UUID_1,  # prev version
                    },
                ],
                'requests': [
                    {
                        'id': TEST_UUID_1,
                        'from_role_id': '1',
                        'to_role_id': '1',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 1,
                    },
                    {
                        'id': TEST_UUID_2,
                        'from_role_id': '1',
                        'to_role_id': '2',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 2,
                    },
                    {
                        'id': TEST_UUID_3,
                        'from_role_id': '1',
                        'to_role_id': '3',
                        'to_group_id': None,
                        'status': ReviewRequestStatus.active,
                        'order': 3,
                    },
                ],
                'settings': {'is_parallel': False},
                'document_version': {'id': TEST_UUID_2},
            },
            [TEST_UUID_2],
            id='ordered_multiple_requests_one_review_versioned',
        ),
    ],
)
async def test_review_state_calc_next_review_requests(
    prepare: DataDict,
    expected: list[str],
):
    # attributes that are expected to be in the objects
    review_spec = ['role_id', 'type', 'group_id', 'document_version_id']
    request_spec = ['id', 'from_role_id', 'to_role_id', 'to_group_id', 'status', 'order']
    settings_spec = ['is_parallel']
    status_spec = ['status']
    version_spec = ['id']

    reviews = [mock.Mock(spec_set=review_spec, **r) for r in prepare['reviews']]
    requests = [mock.Mock(spec_set=request_spec, **r) for r in prepare['requests']]

    settings = None
    if raw_settings := prepare.get('settings'):
        settings = mock.Mock(spec_set=settings_spec, **raw_settings)

    status = None
    if raw_status := prepare.get('status'):
        status = mock.Mock(spec_set=status_spec, **raw_status)

    version = None
    if raw_document_version := prepare.get('document_version'):
        version = mock.Mock(spec_set=version_spec, **raw_document_version)

    state = ReviewState(
        reviews=reviews,
        requests=requests,
        status=status,
        settings=settings,
        document_version=version,
        statuses_versions=[],  # doesn't have any effect on the result
    )

    actual_requests_ids = state.calc_next_review_requests()
    assert sorted(actual_requests_ids) == sorted(expected)
