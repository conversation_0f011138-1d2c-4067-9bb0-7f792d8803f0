import sqlalchemy as sa

from app.models import columns, metadata

# Table to store information about archived documents. The archive is part of the product that
# allows to organize documents at the separate place in the system.
#
# INFO: Don't confuse with "documents_archive_table" table that store information about downloaded
# documents as ZIP archive. Check "app.downloads.tables" for this table.
document_archive_table = sa.Table(
    'document_archives',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'company_id',
        'companies.id',
        nullable=False,
        ondelete='CASCADE',
    ),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        nullable=False,
        ondelete='CASCADE',
    ),
    columns.ForeignKey(
        'created_by',
        'roles.id',
        nullable=False,
        ondelete='CASCADE',
    ),
    # Dates
    columns.DateCreated(),
    columns.DateUpdated(),
    # Indexes
    sa.UniqueConstraint('document_id', 'company_id', name='uix_company_id_document_id'),
)
