from aiohttp import web

from api.errors import InvalidRequest
from app.auth.decorators import login_required
from app.auth.types import User
from app.documents_ai.structured_data_poc.utils import extract_structured_data_from_document_content
from app.documents_ai.validators import validate_upload_file
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _

ALLOWED_EXTRACT_DATA_EXTENSIONS = {'.pdf', '.jpg', '.png', '.jpeg'}


@login_required()
async def extract_structured_data_from_document(request: web.Request, __: User) -> web.Response:
    if not get_flag(FeatureFlags.ENABLE_AI_STRUCTURED_DATA_EXTRACTION):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    uploaded_file = await validate_upload_file(
        request, allowed_extensions=ALLOWED_EXTRACT_DATA_EXTENSIONS
    )
    data = await extract_structured_data_from_document_content(raw_bytes=uploaded_file.content)
    return web.json_response(data)
