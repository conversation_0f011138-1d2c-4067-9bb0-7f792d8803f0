# Reviews

Модуль відповідає за процес погодження документів в рамках компанії.

Процес погодження починається з того, що його потрібно створити:

## Таблиці

### reviews_settings

Відповідає за налаштування процесу погодження.

Наприклад:

- Чи обов'язкове погодження, щоб підписати документ (`is_required`)
- Чи будуть користувачі погоджувати документ в будь-якому порядку чи важлива черга погодження
  (`is_parallel`)

### review_requests

Зберігає список користувачів, які мають погодити документ.

- для послідовного погодження важливе поле `order`, це число, яке відповідає за чергу. 1 погоджує
  раніше, ніж 2.
- при редагуванні погодження ми не видаляємо його із цього списку, а виставляємо йому
  `ReviewRequestStatus.deleted`

### reviews

Безпосередньо дії користувачів над погодженням.

- користувач може відхилити документ, а потім погодити, ми збережемо 2 дії в цій таблиці для
  історії.
- Однак для останнього виставимо `is_last`.
- при додаванні нового запису буде вирахувано стан погодження, його новий статус і так далі
  (`start_reviews_update_transaction`).

### reviews_statuses

Відповідає за статус погодження в компанії.

- `status` - змінюється, коли задіяні ролі завершили погодження:
  - `approved` - всі погодили
  - `rejected` - будь-хто не погодив (статус ставиться одразу після відхилення, адже немає сенсу
    далі погоджувати)
- `next_review_requests` - містить інформацію, хто далі має погодити документ.
  (`review_requests.id`)
  - Для паралельного погодження - всі користувачі, які ще не погодили
  - Для послідовного погодження - наступний користувач, який має погодити

## Важливо:

- Якщо робляться якісь дії, які можуть змінити стан погодження, їх потрібно робити в середині
  `start_reviews_update_transaction`.
