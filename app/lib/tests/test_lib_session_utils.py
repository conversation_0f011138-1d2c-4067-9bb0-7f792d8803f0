"""Tests for session utilities."""

import collections.abc as col_abc
import copy
import functools as ft
import typing as t
import uuid
from unittest import mock

import aiohttp.web
import aiohttp_session
import aiohttp_session.redis_storage
import pytest

import app.app as app_utils
import app.tests.common as tests_common
from app.config import read_app_config
from app.lib import session_utils
from app.services import services as app_services

REDIS_TTL_FOREVER = -1


class FakeSessionMaker(t.Protocol):
    """Protocol for a fake session factory.

    Needed to annotate callables with keyword arguments.
    """

    def __call__(
        self,
        identity: str | None = None,
        *,
        data: col_abc.MutableMapping | None = None,
        new: bool | None = None,
        max_age: int | None = None,
    ) -> aiohttp_session.Session: ...


@pytest.fixture
def valid_session_identity() -> str:
    """Return a valid session identity."""
    return uuid.uuid4().hex


@pytest.fixture(params=[2_592_000, 2_592_000 * 3])
def session_max_age_set(request) -> int:
    """Return a max age that would make a session expire."""
    return request.param


@pytest.fixture
def session_max_age_not_set() -> None:
    """Return a value that signifies that an `aiohttp_session.Session` does not
    have a max age.
    """
    return


@pytest.fixture(
    params=[
        None,
        {'session': {'2fa': True}},
        {'session': {'user_id': 'some_id', '2fa': True, 'other_key': 'val'}},
    ]
)
def valid_session_data(request) -> dict | None:
    return request.param


@pytest.fixture(params=[True, False])
def valid_session_new(request) -> bool:
    return request.param


@pytest.fixture
def make_fake_session(
    valid_session_identity: str,
    valid_session_data: dict,
    valid_session_new: bool,
) -> FakeSessionMaker:
    """Return a fake session factory."""

    def make_fake_session_(
        identity: str | None = valid_session_identity,
        *,
        data: col_abc.MutableMapping | None = valid_session_data,
        new: bool | None = valid_session_new,
        max_age: int | None = None,
    ) -> aiohttp_session.Session:
        session = aiohttp_session.Session(
            identity=identity,
            data=data,
            new=new,
            max_age=max_age,
        )
        # Do not enforce an identity if an explicit `identity = None` was
        # passed
        if identity is not None:
            assert session.identity is not None
        return session

    return make_fake_session_


@pytest.fixture(params=[-2, -1, 0, 1, 100, 10_000])
def key_ttl_valid(request) -> int:
    """Return a valid TTL for an active sesisons Redis key."""
    return request.param


@pytest.fixture(params=[-1000, -12, -3])
def key_ttl_invalid(request) -> int:
    """Return an invalid TTL for a session Redis key."""
    return request.param


@pytest.fixture(params=[-1, 0, 1, 100, 10_000])
def session_ttl_valid(request) -> int:
    """Return a valid session TTL."""
    return request.param


@pytest.fixture(params=[-2, -3, -10, -100, -10_000])
def session_ttl_invalid(request) -> int:
    """Return an invalid session TTL."""
    return request.param


@pytest.fixture
def user_id() -> str:
    """Return a valid user ID."""
    return str(uuid.uuid4())


class SessionStorageStubAttacher(t.Protocol):
    """Protocol for storage stub attacher fixtures. Used for type checking."""

    def __call__(self, request: aiohttp.web.Request, stub: t.Any) -> None: ...


@pytest.fixture
def attach_session_storage_stub() -> SessionStorageStubAttacher:
    """Return a function that attaches an `aiohttp_session` storage stub to a request."""

    session_storage_key = 'aiohttp_session_storage'

    def attach_storage_stub_(request: aiohttp.web.Request, stub: t.Any) -> None:
        request[session_storage_key] = stub

    return attach_storage_stub_


class TestGenerateSessionIdentity:
    """Tests for the function that generates identities for a session."""

    def test_returns_strings(self) -> None:
        """The function should return strings."""
        identity = session_utils.generate_session_identity()
        assert isinstance(identity, str)


class TestGetSessionId:
    """Tests for the function that returns the identity of a session."""

    def test_set_id_should_return_sessions_identity(
        self, make_fake_session: FakeSessionMaker
    ) -> None:
        """Sessions with a set identity should return their identity."""
        fake_session = make_fake_session()

        actual_session_id = session_utils.get_session_id(fake_session)

        expected_session_id = fake_session.identity
        assert actual_session_id == expected_session_id

    def test_unset_identity_factory_provided_should_return_values_from_factory(
        self, make_fake_session: FakeSessionMaker
    ) -> None:
        """Sessions with no identity set should have their identity set to a
        value provided by a factory.

        Given:
            - A session that has no `identity` set.
            - The caller provides a factory for session identities.
        When:
            - The caller calls the function.
        Then:
            - The function sets the session's identity to a value returned by
              the provided session identities factory.
        """

        def session_factory_stub() -> str:
            return '42'

        # Only new sessions can have an unset identity
        session = make_fake_session(identity=None, new=True)

        actual_session_id = session_utils.get_session_id(
            session, session_id_factory=session_factory_stub
        )

        expected_session_id = session_factory_stub()
        assert actual_session_id == expected_session_id


class TestGetActiveSessionsKeyFor:
    """Tests for the function that returns the Redis key that stores active
    sessions for a user."""

    @pytest.mark.parametrize(
        'user_id',
        [
            '9a154eae18c74d779555dbe1fe0cfafd',
            '280db704c20843509e5f440adc849350',
            '7a4a1a24765a4398adc34eae55d0c135',
            '4bc0bfb119474cb5a97650750769dfb7',
        ],
    )
    def test_user_id_should_adehere_to_format(self, user_id: str) -> None:
        """User IDs should adhere to the `session_id_{user_id}` format.

        Given:
            - A string `user_id`.
        When:
            - Calling the function with the provided user ID.
        Then:
            - The function returns the result in the `session_id_{user_id}`
              format.
        """
        expected_result = f'session_id_{user_id}'
        actual_result = session_utils.get_active_sessions_key_for(user_id)
        assert expected_result == actual_result


class TestGetSessionRedisKey:
    """Tests for getting the redis key for a given session."""

    async def test_session_with_identity_returns_key_with_cookie_name(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
    ) -> None:
        """Sessions that have `identity` attribute set should return the key
        that follows the format: `{session_cookie_name}_{session.identity}.

        Given:
            - A session that has the `identity` attribute set to some value
              that is not `None`.
        When:
            - Attempting to get a Redis key under which the session should be
              stored.
        Then:
            - The function returns a value of the format:
              '{session_cookie_name}_{session.identity}'.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        config = app_services.config
        cookie_name = config.app.cookie_name
        fake_session = make_fake_session()

        try:
            redis_key = session_utils.get_session_redis_key(fake_session.identity)

            assert redis_key == f'{cookie_name}_{fake_session.identity}'
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

        await tests_common.cleanup_on_teardown(app)


class TestNewSession:
    """Tests for creating a new session."""

    async def test_new_session_identity_string(
        self, attach_session_storage_stub: SessionStorageStubAttacher
    ) -> None:
        """New sessions should have a string identity.

        Given:
            - A request.
        When:
            - A caller creates a new session for a request.
        Then:
            - The function returns a new session.
            - The session has an `identity` parameter set.
            - The `identity` parameter is a string.
        """

        storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
        storage_stub.max_age = None
        # Instance method stubs should always take a `self` parameter
        storage_stub.new_session.side_effect = ft.partial(
            aiohttp_session.AbstractStorage.new_session, storage_stub
        )
        request_stub = tests_common.make_mocked_request('POST', '/')
        attach_session_storage_stub(request_stub, storage_stub)

        new_session = await session_utils.new_session(request_stub)

        assert isinstance(new_session.identity, str)


class TestDestroySession:
    """Tests for destroying sessions."""

    async def test_destroyed_session_invalidated(
        self, aiohttp_client, make_fake_session: FakeSessionMaker
    ) -> None:
        """Destroyed sessions should be invalidated.

        Given:
            - A session that has an identity set.
        When:
            - Destroying the session.
        Then:
            - The function invalides the session.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        fake_session = make_fake_session()

        await session_utils.destroy_session(fake_session)

        # `aiohttp_session` chose `session._mapping == {}` as a magic value for
        # invalidated sessions
        assert fake_session._mapping == {}
        await tests_common.cleanup_on_teardown(app)

    async def test_destroyed_session_deleted_from_redis(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
    ) -> None:
        """Destroyed sessions should be deleted from Redis.

        Given:
            - A session that has an identity set.
        When:
            - Destroying the session.
        Then:
            - The function deletes the key associated with the session from
              Redis.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis

        fake_session = make_fake_session()
        config = app_services.config
        cookie_name = config.app.cookie_name
        session_key = f'{cookie_name}_{fake_session.identity}'
        await redis.set(session_key, '{"some": "value"}')

        try:
            await session_utils.destroy_session(fake_session)

            session_in_redis = await redis.get(session_key)
            assert session_in_redis is None
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

    async def test_session_without_identity_raises_value_error(self, aiohttp_client) -> None:
        """Attempts to destroy sessions without an identity should raise `ValueError`.

        Given:
            - A session that does not have an identity set (`session.identity is None`).
        When:
            - Destroying the session.
        Then:
            - The function raises `ValueError`.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)

        fake_session = aiohttp_session.Session(identity=None, data=None, new=False)
        assert fake_session.identity is None

        with pytest.raises(ValueError):
            await session_utils.destroy_session(fake_session)

        await tests_common.cleanup_on_teardown(app)


class TestEnsureNewSession:
    """Tests for ensuring new sessions for a given request."""

    @pytest.fixture(autouse=True, scope='class')
    def _setup(self, app_dummy) -> None:
        config = read_app_config()
        app_services.ctx_config.set(config)
        app_services.ctx_redis.set(mock.AsyncMock())
        app_services.ctx_app.set(app_dummy)

    @pytest.fixture(scope='class')
    def app_dummy(self) -> mock.MagicMock:
        """Returns an app dummy."""
        app_dummy = mock.MagicMock(spec=aiohttp.web.Application)

        # The `'redis'` key stores an async Redis client, so its test double
        # should also be async
        dummy_dict = {'redis': mock.AsyncMock()}

        # Since it's impossible to patch a single key in a mapping, work
        # around this with a custom `__getitem__` that falls back to the
        # original one from the test double object
        original_getitem = app_dummy.__getitem__

        def getitem_with_fallback(self, key):
            try:
                return dummy_dict[key]
            except KeyError:
                return original_getitem(self, key)

        app_dummy.__getitem__ = getitem_with_fallback
        app_dummy.get = lambda key: dummy_dict.get(key)
        return app_dummy

    async def test_no_current_sessions_returns_new_session(
        self,
        session_max_age_set: int,
        attach_session_storage_stub: SessionStorageStubAttacher,
        app_dummy: mock.MagicMock,
    ) -> None:
        """The function should return new sessions for requests with no
        sessions attached.
        """
        storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
        # No session should be present on a request, so no session cookie is
        # set
        storage_stub.load_cookie.return_value = None
        storage_stub.max_age = session_max_age_set

        # `aiohttp_session.load_session()` returns a new session when no
        # previous session exists on request
        storage_stub.load_session.side_effect = ft.partial(
            aiohttp_session.redis_storage.RedisStorage.load_session, storage_stub
        )

        # Stub the `new_session` method, since no session is supposed to be
        # defined in the test
        storage_stub.new_session.side_effect = ft.partial(
            aiohttp_session.AbstractStorage.new_session, storage_stub
        )
        request_stub = tests_common.make_mocked_request('POST', '/', app=app_dummy)
        attach_session_storage_stub(request_stub, storage_stub)

        session = await session_utils.ensure_new_session(request_stub)

        assert session.new is True

    async def test_request_with_existing_session_returns_new_session(
        self,
        attach_session_storage_stub: SessionStorageStubAttacher,
        make_fake_session: FakeSessionMaker,
        app_dummy: mock.MagicMock,
    ) -> None:
        """On requests with existing sessions, the function should return a new
        session, that differs from the session currently bound to the request.

        Given:
            - A request that has a session bound to it.
        When:
            - Ensuring that a request has a new session.
        Then:
            - The function returns a new session.
            - The new session does not match the previous session.
        """
        app_utils.create_app()

        storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
        storage_stub.max_age = session_max_age_set

        previous_session = make_fake_session(new=False)

        # Since the test expects a session to be bound to a request, stub the
        # `load_session` method
        storage_stub.load_session.return_value = previous_session
        # Since the test expects the function to create a new session, stub the
        # `new_session` method
        storage_stub.max_age = None
        storage_stub.new_session.side_effect = ft.partial(
            aiohttp_session.AbstractStorage.new_session, storage_stub
        )
        request_stub = tests_common.make_mocked_request('POST', '/', app=app_dummy)
        attach_session_storage_stub(request_stub, storage_stub)

        actual_session = await session_utils.ensure_new_session(request_stub)
        assert actual_session.new
        assert isinstance(actual_session.identity, str)
        assert actual_session.identity != previous_session.identity

    async def test_request_with_previous_session_invalidates_previous_session(
        self,
        make_fake_session: FakeSessionMaker,
        attach_session_storage_stub: SessionStorageStubAttacher,
        app_dummy: mock.MagicMock,
    ) -> None:
        """On requests with existing sessions, the function should invalidate
        existing sessions.
        """
        storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
        storage_stub.max_age = session_max_age_set

        previous_session = make_fake_session(new=False)

        storage_stub.load_session.return_value = previous_session
        storage_stub.max_age = None
        storage_stub.new_session.side_effect = ft.partial(
            aiohttp_session.AbstractStorage.new_session, storage_stub
        )
        request_stub = tests_common.make_mocked_request('POST', '/', app=app_dummy)
        attach_session_storage_stub(request_stub, storage_stub)

        _ = await session_utils.ensure_new_session(request_stub)

        assert previous_session._mapping == {}

    async def test_request_with_previous_session_previous_session_deleted(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
        session_max_age_set: int,
        attach_session_storage_stub: SessionStorageStubAttacher,
    ) -> None:
        """On requests with existing sessions, the function should delete the
        previous session from Redis.
        """
        try:
            app, client = await tests_common.prepare_app_client(aiohttp_client)
            redis = app_services.redis
            dummy_session_data = '{"some": "session_data"}'
            previous_session = make_fake_session(new=False)
            previous_session_key = session_utils.get_session_redis_key(previous_session.identity)

            storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
            storage_stub.max_age = session_max_age_set
            storage_stub.load_session.return_value = previous_session
            storage_stub.max_age = None
            storage_stub.new_session.side_effect = ft.partial(
                aiohttp_session.AbstractStorage.new_session, storage_stub
            )

            request_stub = tests_common.make_mocked_request('POST', '/', app=app)
            attach_session_storage_stub(request_stub, storage_stub)

            await redis.set(previous_session_key, dummy_session_data)

            _ = await session_utils.ensure_new_session(request_stub)

            previous_session_in_redis = await redis.get(previous_session_key)
            assert previous_session_in_redis is None
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

    async def test_existing_session_data_transfers_to_new_ensured_session(
        self,
        make_fake_session: FakeSessionMaker,
        app_dummy: mock.AsyncMock,
        attach_session_storage_stub: SessionStorageStubAttacher,
    ) -> None:
        """On requests with existing sessions, the function should transfer
        session data from the previous session to the new one.

        Given:
            - A request with an existing session.
            - An existing session may or may not contain session data.
        When:
            - The function ensures a new session.
        Then:
            - The function transfers session data from the old session to the
              new one.
        """
        previous_session = make_fake_session()
        # Create a snapshot of the previous session object to compare the newly
        # created session to the snapshot
        previous_session_snapshot = copy.deepcopy(previous_session)

        storage_stub = mock.Mock(aiohttp_session.redis_storage.RedisStorage)
        storage_stub.max_age = session_max_age_set
        storage_stub.load_session.return_value = previous_session
        storage_stub.max_age = None
        storage_stub.new_session.side_effect = ft.partial(
            aiohttp_session.AbstractStorage.new_session, storage_stub
        )

        request_stub = tests_common.make_mocked_request('POST', '/', app=app_dummy)
        attach_session_storage_stub(request_stub, storage_stub)

        new_session = await session_utils.ensure_new_session(request_stub)

        # Session identity should not match
        assert new_session.identity != previous_session_snapshot.identity
        # But the session data should
        assert new_session == previous_session_snapshot


class TestCopySessionData:
    """Tests for copying session data from one session to another one."""

    @pytest.fixture
    def new_session(self, valid_session_identity: str) -> aiohttp_session.Session:
        """Return a clean new session."""
        return aiohttp_session.Session(identity=valid_session_identity, data=None, new=True)

    def test_existing_session_data_transfers_to_new_session(
        self,
        make_fake_session: FakeSessionMaker,
        valid_session_data: dict | None,
        new_session: aiohttp_session.Session,
    ) -> None:
        """Session data from the source session should transfer fully to the
        destination session.
        """
        source_data = valid_session_data
        source_session = make_fake_session(data=source_data)
        destination_session = new_session

        session_utils.copy_session_data(source_session, destination_session)

        assert source_session == destination_session


class TestGetSessionTtl:
    """Tests for the utility function that transforms session max age to a
    Redis TTL.
    """

    def test_max_age_set_returns_matching_ttl(
        self,
        session_max_age_set: int,
        make_fake_session: FakeSessionMaker,
    ) -> None:
        """Redis TTL value for sessions that expire should be equal to the
        session's max age.

        Given:
            - A session with an integer `max_age`.
        When:
            - The function is called on the session.
        Then:
            - A value is returned that matches the session's max age.
        """
        fake_session = make_fake_session(max_age=session_max_age_set)
        session_ttl = session_utils.session_max_age_to_redis_ttl(fake_session)
        assert session_ttl == session_max_age_set

    def test_max_age_not_set_returns_redis_ttl_forever(
        self,
        session_max_age_not_set: t.Literal[None],
        make_fake_session: FakeSessionMaker,
    ) -> None:
        """Redis TTL value for permanent sessions should be "forever".

        Given:
            - A session with `max_age == None`.
        When:
            - The function is called on the session.
        Then:
            - The function returns a value that matches Redis' "forever" TTL.
        """
        fake_session = make_fake_session(session_max_age_not_set)
        redis_ttl = session_utils.session_max_age_to_redis_ttl(fake_session)
        assert redis_ttl == REDIS_TTL_FOREVER


class TestTTLUpdateRequired:
    """Tests for the `_ttl_update_required()` utility function."""

    def test_key_ttl_lower_than_session_ttl_returns_true(self, session_max_age_set: int) -> None:
        """Key TTLs that are lower than session TTLs should require an update.

        Given:
            - An expiring session.
            - An expiring key.
            - The key's ttl is lower than the session's TTL.
        When:
            - Calling the function on given key and session.
        Then:
            - The function returns `True`.
        """
        session_ttl = session_max_age_set
        key_ttl = session_ttl - 1

        update_required = session_utils._ttl_update_required(key_ttl, session_ttl)

        assert update_required is True

    def test_key_ttl_greater_than_session_ttl_returns_false(self, session_max_age_set: int) -> None:
        """Key TTLs that are greater than session TTLs should NOT require an
        update.

        Given:
            - A key has a greater TTL than a session.
        When:
            - Calling the function on given key and session TTLs.
        Then:
            - The function returns `False`.
        """
        session_ttl = session_max_age_set
        key_ttl = session_ttl + 1

        update_required = session_utils._ttl_update_required(key_ttl, session_ttl)

        assert update_required is False

    def test_key_ttl_not_exists_session_has_ttl_returns_true(
        self, session_max_age_set: int
    ) -> None:
        """Key TTLs that mean "the key does not exist", while a session has a
        valid TTL, should require an update.

        Given:
            - A key does not exist (has a TTL of -2).
            - A session has a TTL.
        When:
            - Calling the function on given key and session TTLs.
        Then:
            - The function returns `True`.
        """
        session_ttl = session_max_age_set
        key_ttl = session_utils.REDIS_TTL_KEY_DOES_NOT_EXIST

        update_required = session_utils._ttl_update_required(key_ttl, session_ttl)

        assert update_required is True

    def test_key_ttl_less_than_minus_2_raises_value_error(
        self,
        key_ttl_invalid: int,
        session_ttl_valid: int,
    ) -> None:
        """Attempting to check if an update is required for key TTLs < -2
        should raise a `ValueError`.

        Given:
            - A key with TTL < -2.
            - A valid session.
        When:
            - Calling the function.
        Then:
            - The function raises `ValueError`.
        """
        with pytest.raises(ValueError):
            session_utils._ttl_update_required(key_ttl_invalid, session_ttl_valid)

    def test_session_ttl_less_than_minus_1_raises_value_error(
        self,
        key_ttl_valid: int,
        session_ttl_invalid: int,
    ) -> None:
        """Attempting to check if an update is required for session TTLs < -1
        should raise a `ValueError`.

        Given:
            - A valid key TTL.
            - A session TTL < -1.
        When:
            - Calling the function.
        Then:
            - The function raises `ValueError`.
        """
        with pytest.raises(ValueError):
            session_utils._ttl_update_required(key_ttl_valid, session_ttl_invalid)


class TestScheduleUpdateTTL:
    """Test for the function that schedules TTL updates."""

    @pytest.fixture
    def key_name(self) -> str:
        return 'testing_key'

    @pytest.fixture(params=[1, 10, 100])
    def positive_ttl(self, request) -> int:
        """Return positive TTL for a key."""
        return request.param

    @pytest.fixture(params=[-2, -10])
    def nonpositive_ttl_not_forever(self, request) -> int:
        """Return a negative TTL that is not a "forever" value reserved by Redis."""
        return request.param

    def test_session_ttl_positive_schedules_expire(self, key_name: str, positive_ttl: int) -> None:
        """Positive new TTLs should schedule an `EXPIRE` command.

        Given:
            - A session TTL that is positive.
        When:
            - Calling the function.
        Then:
            - The function schedules an `EXIPIRE` call on the transaction.
        """
        transaction = mock.Mock()
        new_ttl = positive_ttl

        session_utils._schedule_ttl_update(transaction, key_name, new_ttl)

        transaction.expire.assert_called_once_with(key_name, new_ttl)

    def test_session_ttl_forever_schedules_persist(self, key_name: str) -> None:
        """TTLs that mean "forever" should schedule a `PERSIST` command.

        Given:
            - A session TTL that means "forever".
        When:
            - Calling the function under test.
        Then:
            - The function schedules a `PERSIST` call on the transaction.
        """
        transaction = mock.Mock()
        new_ttl = session_utils.REDIS_TTL_FOREVER

        session_utils._schedule_ttl_update(transaction, key_name, new_ttl)

        transaction.persist.assert_called_once_with(key_name)

    def test_session_ttl_nonpositive_ttl_not_forever_raises_value_error(
        self, key_name: str, nonpositive_ttl_not_forever: int
    ) -> None:
        """Non-positive TTLs that are not "forever" should raise `ValueError`.

        Given:
            - A session TTL that is non-positive and is not a "forever" TTL (-1).
        When:
            - Calling the function under test.
        Then:
            - The function raises `ValueError`.
        """
        transaction = mock.Mock()
        new_ttl = nonpositive_ttl_not_forever

        with pytest.raises(ValueError):
            session_utils._schedule_ttl_update(transaction, key_name, new_ttl)


class TestAddActiveUserSession:
    """Tests for the `add_active_user_session()` function."""

    @pytest.mark.parametrize(
        'session_max_age, expected_key_ttl',
        [(None, REDIS_TTL_FOREVER), (2_592_000, 2_592_000)],
    )
    async def test_no_existing_list_new_session_added_with_proper_ttl(
        self,
        aiohttp_client,
        user_id: str,
        make_fake_session: FakeSessionMaker,
        session_max_age: int,
        expected_key_ttl: int,
    ) -> None:
        """New sessions should be added with proper TTLs when no previous key
        exists.

        Given:
        - A list of active sessions does not exists in Redis.
        When:
        - Caller passes a session instance that has a `max_age` attribute set
          to `None` or an int.
        Then:
        - After creation, a list of the user's active sessions exists.
        - The session is stored in the list.
        - The list of active sessions has a time-to-live that matches the newly
          added active session.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis
        fake_session = make_fake_session(max_age=session_max_age)
        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)
        active_session_id = session_utils.get_session_id(fake_session)

        try:
            await session_utils.add_active_user_session(redis, user_id, fake_session)

            active_sessions_in_redis = await redis.lrange(active_sessions_key, 0, -1)
            assert active_session_id in active_sessions_in_redis
            assert await redis.ttl(active_sessions_key) == expected_key_ttl
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

    async def test_existing_list_greater_ttl_keeps_current_ttl(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
        session_max_age_set: int,
        user_id: str,
    ) -> None:
        """Existing lists with greater TTLs should keep their current TTLs.

        Given:
        - A key with a list of all active sessions exists.
        - The list has a time-to-live.
        When:
        - A caller passes a session that has a max age that is less than the
          time-to-live of the list of currently active sessions.
        Then:
        - The new session is added to the list of active sessions.
        - The list of active sessions keeps its old time-to-live.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis

        # Ensure a list of active sessions exists, and has a TTL greater than
        # the new session's max age
        existing_fake_session = make_fake_session(max_age=session_max_age_set + 100)
        existing_fake_session_id = session_utils.get_session_id(existing_fake_session)

        new_fake_session = make_fake_session(max_age=session_max_age_set)
        new_session_id = session_utils.get_session_id(new_fake_session)

        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)

        try:
            async with redis.pipeline(transaction=True) as pipe:
                await (
                    pipe.rpush(active_sessions_key, existing_fake_session_id)
                    .expire(active_sessions_key, existing_fake_session.max_age)
                    .execute()
                )

            await session_utils.add_active_user_session(redis, user_id, new_fake_session)

            current_sessions = await redis.lrange(active_sessions_key, 0, -1)
            assert new_session_id in current_sessions

            active_sessions_key_ttl = await redis.ttl(active_sessions_key)
            assert active_sessions_key_ttl > new_fake_session.max_age
            assert active_sessions_key_ttl == existing_fake_session.max_age
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

    async def test_existing_list_lower_ttl_updates_to_new_session_max_age(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
        session_max_age_set: int,
        user_id: str,
    ) -> None:
        """Existing lists with TTLs lower than the session being added should
        update their TTLs to match the session's.

        Given:
        - A key with a list of all active sessions exists.
        - The list has a time-to-live.
        When:
        - A caller passes a session that has a max age that is greater than the
          time-to-live of the list of currently active sessions.
        Then:
        - The new session is added to the list of active sessions.
        - The list of active sessions updates its time-to-live to match the max.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis

        # Ensure a list of active sessions exists, and has a TTL lower than
        # the new session's max age
        existing_ttl = session_max_age_set - 100
        existing_fake_session = make_fake_session(max_age=existing_ttl)
        existing_fake_session_id = session_utils.get_session_id(existing_fake_session)

        new_fake_session = make_fake_session(max_age=session_max_age_set)
        new_session_id = session_utils.get_session_id(new_fake_session)

        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)

        try:
            async with redis.pipeline(transaction=True) as pipe:
                await (
                    pipe.rpush(active_sessions_key, existing_fake_session_id)
                    .expire(active_sessions_key, existing_fake_session.max_age)
                    .execute()
                )

            await session_utils.add_active_user_session(redis, user_id, new_fake_session)

            current_sessions = await redis.lrange(active_sessions_key, 0, -1)
            assert new_session_id in current_sessions

            active_sessions_key_ttl = await redis.ttl(active_sessions_key)
            assert active_sessions_key_ttl > existing_ttl
            assert active_sessions_key_ttl == new_fake_session.max_age
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)

    async def test_existing_list_forever_ttl_stays_forever(
        self,
        aiohttp_client,
        make_fake_session: FakeSessionMaker,
        session_max_age_set: int,
        user_id: str,
    ) -> None:
        """Existing lists with "forever" TTLs should keep their "forever" TTLs
        when adding a session that expires.

        Given:
        - A key with a list of all active sessions exists.
        - The list has a time-to-live of "forever" (-1).
        When:
        - Caller passes a session that has a max age that is lower than the
          time-to-live of the list of currently active sessions, that is any
          value that is not `-1`.
        Then:
        - The new session is added to the list of active sessions.
        - The list of active sessions keeps its "forever" time-to-live (-1).
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis

        # Ensure a list of active sessions exists and has a "forever" TTL
        existing_fake_session = make_fake_session()
        existing_fake_session_id = session_utils.get_session_id(existing_fake_session)

        new_fake_session = make_fake_session(max_age=session_max_age_set)
        new_session_id = session_utils.get_session_id(new_fake_session)

        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)

        try:
            await redis.rpush(active_sessions_key, existing_fake_session_id)

            await session_utils.add_active_user_session(redis, user_id, new_fake_session)

            current_sessions = await redis.lrange(active_sessions_key, 0, -1)
            assert new_session_id in current_sessions

            active_sessions_key_ttl = await redis.ttl(active_sessions_key)
            assert active_sessions_key_ttl == REDIS_TTL_FOREVER
        finally:
            await tests_common.cleanup_on_teardown(app, clean_redis=True)


class TestRemoveActiveUserSession:
    """Test the removal of active user sessions."""

    async def test_existing_session_removed(
        self, aiohttp_client, user_id: str, make_fake_session: FakeSessionMaker
    ) -> None:
        """Existing active sessions should be removed from a list.

        Given:
            - An active session.
        When:
            - Removing the session from the list.
        Then:
            - The list of active sessions does not include the removed session.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis
        fake_session = make_fake_session()
        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)

        # Insert the session ID into a list of active sessions
        await redis.lpush(active_sessions_key, fake_session.identity)

        await session_utils.remove_active_user_session(redis, user_id, fake_session)

        active_sessions = await redis.lrange(name=active_sessions_key, start=0, end=-1)
        assert fake_session.identity not in active_sessions
        await tests_common.cleanup_on_teardown(app)

    async def test_nonexistant_session_not_in_active_sessions_list(
        self, aiohttp_client, make_fake_session: FakeSessionMaker, user_id: str
    ) -> None:
        """After removal nonexistent sessions should not be included in the
        list of active sessions.

        Given:
            - A session that does not exist in the list of active sessions.
        When:
            - Removing the session from the list.
        Then:
            - The session is not in the list of active sessions.
        """
        app, client = await tests_common.prepare_app_client(aiohttp_client)
        redis = app_services.redis
        fake_session = make_fake_session()
        active_sessions_key = session_utils.get_active_sessions_key_for(user_id)

        await session_utils.remove_active_user_session(redis, user_id, fake_session)

        active_sessions = await redis.lrange(name=active_sessions_key, start=0, end=-1)
        assert fake_session.identity not in active_sessions
        await tests_common.cleanup_on_teardown(app)
