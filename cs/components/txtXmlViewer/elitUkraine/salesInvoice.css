.root {
    position: relative;
    font-size: 80%;
}

.table,
.tableFixed {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

.tableFixed {
    table-layout: fixed;
}

.row {
    display: table-row;
}

.cell,
.cellUnderlined {
    display: table-cell;
    padding: 0.2cm 0.4cm 0 0;
}

.cellUnderlined {
    position: relative;
    width: 70%;
    border-bottom: 0.075cm solid var(--content-color);
    vertical-align: baseline;
}

.header {
    width: 100%;
    margin: 1cm 0;
}

.header td {
    vertical-align: top;
}

.storage {
    display: inline-block;
    padding: 0.2cm 0.8cm;
    border: 0.05cm solid var(--content-color);
}

.textRight {
    text-align: right;
}

.textCenter {
    text-align: center;
}

.textNoWrap {
    white-space: nowrap;
}

.denotation {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    margin-top: 0.1cm;
    text-align: center;
}

.location {
    margin-bottom: 1cm;
    text-align: right;
}

.title {
    margin: 1cm 0;
    font-weight: bold;
    text-align: center;
}

.content {
    width: 100%;
    border-collapse: collapse;
}

.content th {
    border: 0.06cm solid var(--content-color);
    font-weight: bold;
}

.content td {
    padding: 0.1cm;
    border: 0.06cm solid var(--content-color);
    vertical-align: top;
}

.content .noBorder {
    border: none;
    font-weight: bold;
}

.signatures {
    display: table;
    width: 100%;
    margin: 1cm 0;
    table-layout: fixed;
}

.info {
    padding-top: 20px;
    border-top: 0.05cm solid var(--content-color);
    margin-top: 20px;
}

.taxes {
    margin-top: 15px;
    font-weight: bold;
}
