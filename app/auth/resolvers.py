from datetime import datetime
from typing import Any

import sqlalchemy as sa
from hiku.engine import Context, pass_context
from hiku.expr.core import define
from hiku.graph import Nothing
from hiku.types import Any as HikuAny

from api.graph.constants import DB_READONLY_KEY
from api.graph.utils import get_base_graph_user, get_raw_graph_user
from app.auth.tables import role_table, user_onboarding_table
from app.auth.types import OnboardingBase, is_wide_user_type
from app.auth.utils import is_admin
from app.lib.datetime_utils import to_utc_datetime
from app.models import select_all

NEW_UPLOADING_DATE = to_utc_datetime(datetime(2024, 4, 19))


@pass_context
async def resolve_user_onboarding(ctx: Context, users_ids: list[str]) -> list[OnboardingBase]:
    user = get_base_graph_user(ctx)
    if not user:
        return []

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                user_onboarding_table.select().where(user_onboarding_table.c.user_id.in_(users_ids))
            ),
        )

    mapping = {row.user_id: row for row in rows}

    result = []
    for user_id in users_ids:
        row = mapping.get(user_id)
        row_dict: dict[str, Any] = dict(row) if row else {}

        # It ugly hack to make sure that new users will not see new uploading onboarding
        # because this new feature is not new for them, it's new for old users.
        if user.date_created and user.date_created > NEW_UPLOADING_DATE:
            has_seen_new_uploading = True
        else:
            has_seen_new_uploading = row_dict.get('has_seen_new_uploading', False)

        result.append(
            OnboardingBase(
                has_checked_companies=row_dict.get('has_checked_companies', False),
                has_invited_recipient=row_dict.get('has_invited_recipient', False),
                has_invited_coworker=row_dict.get('has_invited_coworker', False),
                has_uploaded_document=row_dict.get('has_uploaded_document', False),
                has_seen_new_uploading=has_seen_new_uploading,
                is_skipped=row_dict.get('is_skipped', False),
            )
        )

    return result


@pass_context
async def resolve_user_by_role_id(ctx: Context, role_ids: list[str]) -> list[str | Nothing]:
    user = get_raw_graph_user(ctx)
    if len(role_ids) == 0 or not user or not user.id:
        return []

    if len(role_ids) == 1 and is_wide_user_type(user) and user.role_id == role_ids[0]:
        return [user.id]

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.select([role_table.c.user_id, role_table.c.id]).where(
                    role_table.c.id.in_(role_ids)
                )
            ),
        )

    mapping = {row.id: row.user_id for row in rows}
    return [mapping.get(role_id, Nothing) for role_id in role_ids]


@define(HikuAny)
def is_auth_phone_enabled(field: Any) -> bool:
    return bool(field)


@define(HikuAny, HikuAny)
def has_role_permission(permission_value: bool, user_role: int | None) -> bool:
    """
    Admin users can make any action, so we return True for them.
    This function is used to hide user_role in the graph for external companies
    """
    if is_admin(user_role):
        return True

    return permission_value
