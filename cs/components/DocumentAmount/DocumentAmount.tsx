import React, { useEffect, useMemo, useState } from 'react';
import NumberFormat, { NumberFormatProps } from 'react-number-format';

import cn from 'classnames';
import { t } from 'ttag';

import { Nullable } from '../../types/general';
import { Props } from './types';

import Icon from '../ui/icon/icon';
import Input from '../ui/input/input';

import closeSvg from './images/close.svg';

import css from './DocumentAmount.css';

const MAX_VALUE = 100000000000;
const MIN_VALUE = -100000000000;

const MAX_VALUE_STR = MAX_VALUE.toLocaleString();
const MIN_VALUE_STR = MIN_VALUE.toLocaleString();

const isValueInAllowRange = (value: number): boolean =>
    value >= MIN_VALUE && value <= MAX_VALUE;

const withValueLimit: NumberFormatProps<string>['isAllowed'] = ({
    floatValue,
}) => !floatValue || isValueInAllowRange(floatValue);

// fix paste value with comma
// TODO refactor this when resolve https://github.com/s-yadav/react-number-format/pull/556
const NumberFormatCommaPasteHackTextField = (
    onChange: (value: string) => void,
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void,
    isNeedClearButton?: boolean,
) => (inputProps: Record<string, any>) => (
    <div className={css.inputWrapper}>
        <Input
            {...inputProps}
            required // потрібно для коректної роботи :global(.document-amount-input):has(input:valid) - і відображення плейсхолдера зверху згідно нового дизайну
            onBlur={onBlur}
            onPaste={(e: React.ClipboardEvent<HTMLTextAreaElement>) => {
                const pastedText = e.clipboardData.getData('text');

                if (pastedText.includes(',')) {
                    e.preventDefault();
                    onChange(pastedText.replace(',', '.'));
                }
            }}
        />
        {isNeedClearButton && inputProps.value && (
            <span className={css.icon} onClick={() => onChange('')}>
                <Icon glyph={closeSvg} />
            </span>
        )}
    </div>
);

const DocumentAmount: React.FC<React.PropsWithChildren<Props>> = (props) => {
    const [errorMessage, setErrorMessage] = useState<Nullable<string>>(null);

    const onValueChange: NumberFormatProps<string>['onValueChange'] = ({
        value,
    }) => {
        props.onValueChange(value);
        if (errorMessage) {
            setErrorMessage(null);
        }
    };
    const TextField = useMemo(
        () =>
            NumberFormatCommaPasteHackTextField(
                props.onValueChange,
                props.onBlur,
                props.isNeedClearButton,
            ),
        [],
    );

    useEffect(() => {
        setErrorMessage(
            props.value && !isValueInAllowRange(parseFloat(props.value))
                ? t`Можливий діапазон від ${MIN_VALUE_STR} до ${MAX_VALUE_STR}`
                : null,
        );
    }, [props.value]);

    const isShowHint = props.hintText && !errorMessage && !props.errorText;

    return (
        <div>
            <NumberFormat
                isNumericString
                error={!!errorMessage || !!props.errorText || undefined}
                id={props.id}
                decimalScale={2}
                value={props.value}
                customInput={TextField}
                thousandSeparator=" "
                disabled={props.disabled}
                isAllowed={withValueLimit}
                onValueChange={onValueChange}
                displayType={props.displayType}
                allowedDecimalSeparators={[',', '.']}
                decimalSeparator={'.'}
                fixedDecimalScale={props.fixedDecimalScale}
                placeholder={props.placeholder}
            />
            {isShowHint && <p className={css.hint}>{props.hintText}</p>}
            {(props.errorText || errorMessage) && (
                <p className={cn(css.hint, css.error)}>
                    {props.errorText || errorMessage}
                </p>
            )}
        </div>
    );
};

export default DocumentAmount;
