MIN_TEXT_LEN = 100
TEXT_CHUNK_LEN = 1000

ALLOWED_AI_SUGGEST_EXTENSIONS = {'.pdf', '.doc', '.docx'}

PROMPT_TEMPLATE_SUGGEST_META = """
    Extract the following information from the document:

    Document content:
    {doc_text}

    1. category: int - one of the keys from category_map or null.
       category_map = {category_map}
    2. number: Document number as string or null.
    3. date: Document date (ISO 8601) or null.
    4. amount: Document amount as float or null.
    5. edrpou: ЄДРПОУ of the recipient
       (it must contains only digits or be a valid Ukraine passport number) or null,
       mention that document owner ЄДРПОУ is {owner_edrpou}, which differs from recipient one.

    Return the information as a JSON object without additional explanations.
"""

PROMPT_DOC_SUMMARY = """
Сформуй коротке резюме договору для керівника компанії. Виділи: сторон<PERSON>, предме<PERSON>, суму, строки,
ключові зобов'язання, ризики та відповідальність. Вказуй тільки важливе для прийняття рішення про
підписання. Пиши стисло, без юридичних формулювань, зрозуміло нефахівцю.
Відповідь має вкладатись у 1500 слів та мінімально відформатуватись у Markdown.
"""
