import React, { useState } from 'react';
import Helmet from 'react-helmet';
import { useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';

import { Button, FlexBox, Text, Title } from '@vchasno/ui-kit';

import { motion } from 'framer-motion';
import { MEDIA_WIDTH } from 'lib/constants';
import { openInNewTab } from 'lib/navigation';
import { getCurrentUser } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';
import Icon from 'ui/icon';
import Popup from 'ui/popup/popup';

import { updateBannerPromoAppShownCount } from './utils';

import SignatureDrawAnimation from './SignatureDrawAnimation';
import SignedTextAnimation from './SignedTextAnimation';
import { useAutoClosePopup } from './hooks';

import EdoAppSVG from './images/edo-app.svg';

import adQrSrc from './images/edo-app.png';
import phoneSrc from './images/iPhone.png';

import css from './SignSuccessPopupPromoApp.css';

const APP_LINK =
    'https://service.vchasno.ua/vchasno-edo-app?utm_source=web&utm_medium=qr&utm_campaign=link-after-sign\n';

type Props = {
    onClose: () => void;
};

const SignSuccessPopupPromoApp: React.FC<Props> = (props) => {
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });
    const [view, setView] = useState<'init' | 'signature' | 'checkmark'>(
        'init',
    );
    const currentUser = useSelector(getCurrentUser);

    const handleClose = () => {
        eventTracking.sendToGTMV4({
            event: 'sign-thankyoupopup-app-close',
        });
        props.onClose();
    };

    const handleSignatureAnimationComplete = () => {
        updateBannerPromoAppShownCount(currentUser);
        setView('checkmark');
    };

    // Після того як відпрацювала анімація підпису, ми запускуємо таймер на 3 секунди,
    // після якого попап закриється автоматично. Якщо користувач якось взаємодіє з попапом,
    // то цей таймер скасовуємо, щоб дати можливість користувачу прийняти рішення самостійно.
    const { cancelAutoClose } = useAutoClosePopup(handleClose, {
        delay: 3000,
        enabled: !isMobile && view === 'checkmark',
    });

    const handleClick = () => {
        eventTracking.sendToGTMV4({
            event: 'sign-thankyoupopup-app-click',
        });
        cancelAutoClose();
        openInNewTab(APP_LINK);
        handleClose();
    };

    const getMobileUI = () => (
        <>
            {view === 'signature' && (
                <SignatureDrawAnimation
                    onComplete={handleSignatureAnimationComplete}
                />
            )}
            {view === 'checkmark' && (
                <>
                    <SignedTextAnimation
                        className={css.label}
                        isVisible={view === 'checkmark'}
                    />
                    <FlexBox direction="column" gap={0} align="center">
                        <Text
                            style={{
                                fontSize: '14px',
                                textAlign: 'center',
                                lineHeight: '20px',
                            }}
                            strong
                        >{t`Застосунок “Вчасно”`}</Text>
                        <Text
                            style={{
                                fontSize: '14px',
                                textAlign: 'center',
                                lineHeight: '20px',
                            }}
                            type="secondary"
                        >{t`Підписуйте та надсилайте документи за декілька секунд`}</Text>
                    </FlexBox>
                    <Button
                        onClick={handleClick}
                    >{t`Завантажити застосунок`}</Button>
                </>
            )}
        </>
    );

    const getDesktopUI = () => (
        <>
            <FlexBox direction="column" gap={12} align="center">
                <Title level={3}>{t`Застосунок “Вчасно”`}</Title>
                <Text
                    style={{ textAlign: 'center' }}
                    type="secondary"
                >{t`Підписуйте та надсилайте документи за декілька секунд`}</Text>
            </FlexBox>
            <motion.img
                variants={{
                    hidden: {
                        scale: 0,
                    },
                    visible: {
                        scale: 1,
                        transition: {
                            duration: 1,
                            delay: 1.7,
                        },
                    },
                }}
                initial="hidden"
                animate="visible"
                exit="hidden"
                onClick={handleClick}
                onMouseEnter={cancelAutoClose}
                src={adQrSrc}
                alt="qr-code"
                className={css.qrCode}
            />
            <Button
                onClick={handleClick}
                onMouseEnter={cancelAutoClose}
                onFocus={cancelAutoClose}
            >{t`Завантажити застосунок`}</Button>
        </>
    );

    return (
        <>
            <Helmet>
                <link rel="prefetch" href={adQrSrc} as="image" />
                <link rel="prefetch" href={phoneSrc} as="image" />
            </Helmet>
            <Popup
                fullContent
                className={css.popup}
                active
                inPortal
                onClose={handleClose}
            >
                <div className={css.root}>
                    <div className={css.bgSection}>
                        <span className={css.phoneWrap}>
                            <motion.img
                                variants={{
                                    hidden: { opacity: 0, y: -400 },
                                    visible: {
                                        opacity: 1,
                                        y: 0,
                                        transition: { duration: 1, delay: 0.7 },
                                    },
                                }}
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                src={phoneSrc}
                                alt="Телефон"
                                className={css.phone}
                            />
                        </span>

                        <motion.div
                            variants={{
                                hidden: {
                                    opacity: 0,
                                    y: 200,
                                },
                                visible: {
                                    opacity: 1,
                                    y: 0,
                                    transition: { duration: 1, delay: 0.7 },
                                },
                            }}
                            initial="hidden"
                            animate="visible"
                            exit="hidden"
                            className={css.adMenu}
                            onAnimationComplete={() => {
                                setView('signature');
                            }}
                        >
                            <Icon glyph={EdoAppSVG} className={css.appSvg} />
                            <FlexBox direction="column" gap={20} align="center">
                                {isMobile && getMobileUI()}
                                {!isMobile && getDesktopUI()}
                            </FlexBox>
                        </motion.div>
                    </div>
                    {!isMobile && (
                        <div className={css.content}>
                            <SignatureDrawAnimation
                                onComplete={handleSignatureAnimationComplete}
                            />
                            <SignedTextAnimation
                                className={css.label}
                                isVisible={view === 'checkmark'}
                            />
                        </div>
                    )}
                </div>
            </Popup>
        </>
    );
};

export default SignSuccessPopupPromoApp;
