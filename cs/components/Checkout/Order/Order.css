.titleBlock {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 500;
    gap: 20px;
    line-height: 28px;
}

.title {
    display: flex;
    flex: 1;
    align-items: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    gap: 20px;
    line-height: 28px;
}

.previousIconWrapper {
    display: flex;
    height: 40px;
    flex: 0 0 40px;
    align-items: center;
    justify-content: center;
    background: var(--grey-bg);
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
        background: var(--hover-bg);
    }
}

.previousIcon {
    flex: 0 0 20px;
}

.hrLine {
    height: 2px;
    padding: 0;
    border: 0;
    margin: 40px 0;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="397" height="2" viewBox="0 0 397 2" fill="none"><path d="M396.648 0.973633L0.647644 0.973633" stroke="%23DBE5EA" stroke-dasharray="6 4"/></svg>');
}
