import { DOC_CATEGORIES_LIMIT_PER_PAGE } from 'components/DocCategorySelect/constants';
import { GetDocumentCategoriesArgs } from 'services/documentCategories/types';
import graph from 'services/graph';
import io from 'services/io';

export interface DocumentCategoriesGraphItem {
    /**
     * Document category ID
     */
    id: number;
    title: string;
    companyId: string | null;
}

interface GetDocCategoriesResponse {
    count: number;
    documentCategories: DocumentCategoriesGraphItem[];
}

export async function getDocCategories({
    offset = 0,
    limit = DOC_CATEGORIES_LIMIT_PER_PAGE,
    title = '',
    onlyInternal = false,
    onlyPublic = false,
    ids,
}: GetDocumentCategoriesArgs): Promise<GetDocCategoriesResponse> {
    const { allDocumentCategories } = await graph.query({
        query: /* GraphQL */ `
            query AllDocumentCategories(
                $onlyPublic: Boolean!
                $onlyInternal: Boolean!
                $limit: Int!
                $offset: Int!
                $title: String!
                $ids: [Int!]
            ) {
                allDocumentCategories(
                    onlyPublic: $onlyPublic
                    onlyInternal: $onlyInternal
                    limit: $limit
                    offset: $offset
                    title: $title
                    ids: $ids
                ) {
                    count
                    documentCategories {
                        id
                        title
                        companyId
                    }
                }
            }
        `,
        variables: { onlyPublic, onlyInternal, limit, offset, title, ids },
    });
    return allDocumentCategories;
}

export async function createDocCategory(title: string) {
    return await io.request('POST', `/internal-api/document-categories`, {
        title,
    });
}

export async function deleteDocCategory(documentCategoryId: string) {
    return await io.request(
        'DELETE',
        `/internal-api/document-categories/${documentCategoryId}`,
    );
}

export async function editDocCategory({
    documentCategoryId,
    title,
}: {
    documentCategoryId: string;
    title: string;
}) {
    return await io.request(
        'PATCH',
        `/internal-api/document-categories/${documentCategoryId}`,
        {
            title,
        },
    );
}
