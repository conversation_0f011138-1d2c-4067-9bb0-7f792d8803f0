# Editor

<PERSON><PERSON><PERSON> implements WOPI interface for editing MS Office's documents

As a server is used [CollaboraOnline](https://github.com/CollaboraOnline/online)

- [Collabora Online SDK — SDK https://sdk.collaboraonline.com/ documentation](https://sdk.collaboraonline.com/contents.html)
- [Configuration](https://sdk.collaboraonline.com/docs/installation/Configuration.html)
  - [Coolwsd - Configuration file example](https://github.com/CollaboraOnline/online/blob/master/coolwsd.xml.in)

## Run

```bash
docker-compose up caddy
```

Issue a certificate for `localhost` domain using [mkcert](https://github.com/FiloSottile/mkcert)

```bash
mkcert -install

# issue a certificate for localhost
mkdir -p config/vchasno/local/certs
mkcert \
    -key-file config/vchasno/local/certs/localhost-key.pem \
    -cert-file config/vchasno/local/certs/localhost.pem \
    localhost
```

Go to `https://localhost` and accept the certificate.

### Why different flow for local development?

- It's required https for local development due to browser's security restrictions for ws protocol
- Collabora requires using the same domain for WOPI and Collabora server, so it's required to use
  reverse proxy for local development
