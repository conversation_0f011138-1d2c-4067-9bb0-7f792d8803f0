import secrets
from http import H<PERSON><PERSON>tatus

import pytest

from app.auth import db as auth_db
from app.auth import two_factor
from app.mobile import constants
from app.mobile.tests import common as mobile_tests_common
from app.registration import types as registration_types
from app.services import services
from app.tests import common

GQL_CURRENT_ROLE = '{ currentRole { status } }'


async def test_login(aiohttp_client):
    """
    Given user without 2FA enabled
    When attempting to login
    Expected user to login successfully
    """
    # Arrange
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
    )

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )

    # Assert
    data = await response.json()

    # Verify that user got access token and refresh token.
    assert response.status == 200, data

    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'expires_in' in data
    assert data['is_2fa_required'] is False


async def test_login_with_2fa(aiohttp_client, monkeypatch, evo_sender_mock):
    """
    Given a user with 2FA enabled
    When attempting to login
    Expected user to receive an SMS with 2FA
    """

    async def mock_enable_2fa(conn, user, second_factor):
        return True

    # Arrange
    otp_code = '123456'
    monkeypatch.setattr(two_factor, 'has_2fa_enabled', mock_enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # 2FA enabled due to internal user rule
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
    )

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )

    # Assert
    data = await response.json()

    # Verify that user got access token and refresh token.
    assert response.status == 200, data

    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'expires_in' in data
    assert data['is_2fa_required'] is True

    # Verify that SMS with code was sent.
    assert otp_code in evo_sender_mock.message
    assert user.phone == evo_sender_mock.phone


async def test_2fa_verify(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When verifying 2FA code with valid code
    Expected user to get new access token
    """
    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/verify-2fa',
        json={'code': user_data['code']},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_2fa_verify_invalid_code(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When attempting to verify 2FA code with invalid code
    Expected an error to be raised
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/verify-2fa',
        json={'code': '000000'},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 403, data


async def test_2fa_resend(aiohttp_client, evo_sender_mock, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When attempting to resend 2FA code
    Expected user to receive a new code
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    otp_code = '654321'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/resend-2fa',
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data

    # Verify that SMS with code was sent.
    assert otp_code in evo_sender_mock.message
    assert user_data['user'].phone == evo_sender_mock.phone


async def test_refresh_2fa_required(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and refresh token
    When refreshing token
    Expected user to be unable to refresh the token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 403, data


async def test_refresh(aiohttp_client, monkeypatch):
    """
    Given: user with 2FA verified
    When: refresh token
    Then: user gets new access token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    firebase_id = 'fake_firebase_id'

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
            'firebase_id': firebase_id,
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_logout(aiohttp_client, monkeypatch):
    """
    Given: user with 2FA verified
    When: logout
    Then: user can't use access token or refresh token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/logout',
        json={
            'token': user_data['refresh_token'],
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data

    # Verify that user can't use access token or refresh token.
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
        },
    )
    data = await response.json()
    assert response.status == 403, data

    # Also not able to use access token.
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )
    data = await response.json()
    assert response.status == 403, data


@pytest.mark.parametrize(
    'include_headers, expected_status',
    [
        (False, HTTPStatus.FORBIDDEN),
        (True, HTTPStatus.OK),
    ],
)
async def test_mobile_auth_required(
    aiohttp_client,
    monkeypatch,
    include_headers,
    expected_status,
):
    """
    Given a user with 2FA verified
    When accessing route with `mobile_auth_required` decorator with/without token
    Expected error to be raised / response returned
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']}
        if include_headers
        else None,
    )

    # Assert
    data = await response.json()
    assert response.status == expected_status, data


async def test_mobile_auth_required_2fa_required(aiohttp_client, monkeypatch):
    """
    Given a user without 2FA verified
    When accessing route with `mobile_auth_required` decorator
    Expected error to be raised
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()
    assert response.status == 403, data


@pytest.mark.parametrize(
    'source, redirect_url',
    [
        (
            registration_types.RegistrationSource.mobile_edo,
            None,
        ),
        (
            registration_types.RegistrationSource.mobile_kasa,
            'https://kasa.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
        (
            registration_types.RegistrationSource.mobile_kep,
            'https://cap.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
        (
            registration_types.RegistrationSource.mobile_ttn,
            'https://ttn.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
    ],
)
async def test_registration(
    aiohttp_client, source, redirect_url, mailbox, email_templates_renderer
):
    """
    Given a new user with the mobile app
    When calling registration API request
    Expected registration process to be performed correctly
    """

    # Prepare
    _, client = await common.prepare_app_client(aiohttp_client)

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/register',
        json={
            'email': common.TEST_USER_EMAIL,
            'password': common.TEST_USER_PASSWORD,
            'name': common.TEST_USER_FULL_NAME,
            'phone': common.TEST_USER_PHONE,
            'redirect': redirect_url,
            'source': source.value,
        },
    )

    # Assert
    assert response.status == HTTPStatus.OK
    data = await response.json()

    # Assert user exists
    async with services.db.acquire() as conn:
        user = await auth_db.select_base_user(conn=conn, email=common.TEST_USER_EMAIL)
    assert user is not None
    assert user.source == source.value

    # Assert response JSON is valid
    assert data['user'] == {
        'id': user.id,
        'first_name': None,
        'last_name': None,
        'email': common.TEST_USER_EMAIL,
        'phone': None,
        'is_email_confirmed': False,
    }
    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'expires_in' in data
    assert data['is_2fa_required'] is False

    # Assert confirmation email was sent
    assert len(mailbox) == 1
    assert email_templates_renderer[0]['template_name'] == 'registration_confirmation'
    if redirect_url:
        assert (
            redirect_url.replace('=', '%3D').replace('&', '%26')
            in email_templates_renderer[0]['context']['url']
        )
