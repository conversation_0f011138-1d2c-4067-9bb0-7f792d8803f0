"""Add users.is_banned

Revision ID: a46b6935081a
Revises: 95c185c0ba30
Create Date: 2025-07-17 16:59:21.767405

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a46b6935081a'
down_revision = '95c185c0ba30'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('users', sa.Column('is_banned', sa.<PERSON>(), server_default='0', nullable=False))



def downgrade():
    op.drop_column('users', 'is_banned')
 