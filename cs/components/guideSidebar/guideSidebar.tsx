import React, { FC, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { useDispatch, useSelector } from 'react-redux';

import { FlexBox, Text, Title } from '@vchasno/ui-kit';

import cn from 'classnames';
import RenderBlock from 'components/guideSidebar/RenderBlock';
import {
    INFO_BLOCKS_FOP,
    INFO_BLOCKS_TOV,
} from 'components/guideSidebar/constants';
import { openInNewTab } from 'lib/navigation';
import { getIsCurrentCompanyTov } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import Icon from '../ui/icon/icon';

import { usePortalContainerRef } from '../../hooks/usePortalContainerRef';
import { useOnClickOutside } from '../../lib/reactHelpers/hooks';
import {
    closeSidebar,
    getIsGuidSidebarOpen,
    onSidebarMount,
} from './guideSidebarReducer';

import SvgClose from './images/close.svg';

import css from './guideSidebar.css';

const GuideSidebar: FC<React.PropsWithChildren<unknown>> = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getIsGuidSidebarOpen);
    const containerRef = usePortalContainerRef();
    const ref = useRef(null);
    const isCurrentCompanyTov = useSelector(getIsCurrentCompanyTov);

    const onClose = () => dispatch(closeSidebar());

    useOnClickOutside(ref, onClose);

    useEffect(() => {
        dispatch(onSidebarMount());
    }, []);

    return (
        <>
            {containerRef.current &&
                ReactDOM.createPortal(
                    <>
                        <div
                            className={cn({
                                [css.background]: isOpen,
                            })}
                        />
                        <div
                            ref={ref}
                            className={
                                isOpen ? css.sidebarAnimated : css.sidebar
                            }
                            data-qa="qa_features_and_service"
                        >
                            <FlexBox
                                gap={10}
                                direction="column"
                                className={css.header}
                            >
                                <div
                                    className={css.closeIcon}
                                    onClick={onClose}
                                >
                                    <Icon glyph={SvgClose} />
                                </div>
                                <Title
                                    level={3}
                                >{t`Корисні можливості сервісу`}</Title>
                                <Text
                                    type="link"
                                    onClick={() => {
                                        eventTracking.sendToGTMV4({
                                            event: `ec_tutorial_${
                                                isCurrentCompanyTov
                                                    ? 'tov'
                                                    : 'fop'
                                            }_go_to_help`,
                                        });
                                        openInNewTab(
                                            'https://help.vchasno.com.ua',
                                        );
                                    }}
                                >{t`Перейти у довідку`}</Text>
                            </FlexBox>
                            <FlexBox
                                direction="column"
                                gap={12}
                                className={css.items}
                            >
                                {isCurrentCompanyTov &&
                                    INFO_BLOCKS_TOV.map((info) => (
                                        <RenderBlock
                                            key={info.title}
                                            {...info}
                                        />
                                    ))}
                                {!isCurrentCompanyTov &&
                                    INFO_BLOCKS_FOP.map((info) => (
                                        <RenderBlock
                                            key={info.title}
                                            {...info}
                                        />
                                    ))}
                            </FlexBox>
                        </div>
                    </>,
                    containerRef.current,
                )}
        </>
    );
};

export default GuideSidebar;
