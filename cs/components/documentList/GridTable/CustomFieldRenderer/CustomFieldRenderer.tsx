import React from 'react';
import { useSelector } from 'react-redux';

import { CustomFieldRendererParams } from '../types';

import { getDocumentListDocumentsMap } from '../../selectors';

import CompanyField from '../../companyField/companyField';
import AnchorWrapper from '../AnchorWrapper';

import css from './CustomFieldRenderer.css';

const CustomFieldRenderer = (params: CustomFieldRendererParams) => {
    const doc = useSelector(getDocumentListDocumentsMap)[params.data.id];

    if (!doc) {
        return null;
    }

    const {
        item: { field },
    } = params;

    if (!doc) {
        return <AnchorWrapper docId={params.data.id} />;
    }

    return (
        <AnchorWrapper overflowHidden docId={params.data.id}>
            <CompanyField className={css.root} doc={doc} field={field} />
        </AnchorWrapper>
    );
};

export default CustomFieldRenderer;
