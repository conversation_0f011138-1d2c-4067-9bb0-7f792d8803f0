import { Moment } from 'moment';

import { Nullable } from '../../types/general';
import { QueryParams } from '../../types/url';

import { ITag } from '../ui/tags/tagsTypes';

export interface FilterSlice {
    isExportPopupActive: boolean;
    isNotDeliveredChecked: boolean;
    tags: ITag[];
    selectedTags: string[];
    query: Record<QueryParams, string>;
    dateFrom: Nullable<Moment>;
    dateTo: Nullable<Moment>;
    signFolder: Nullable<string>;
    documentCategory: Nullable<string>;
    reviewFolder: Nullable<string>;
    dateFilterPreset: Nullable<string>;
    isDeliveryFilterStateDisabled: boolean;
    isWithoutTagsChecked: boolean;
    amountGte: Nullable<string>;
    amountLte: Nullable<string>;
    isLoading: boolean;
    amountGteError: string;
    amountLteError: string;
}

export enum ReviewStatusQueryParam {
    pending = 'pending',
    approved = 'approved',
    rejected = 'rejected',
    fromMe = 'from_me',
    withMyReview = 'with_my_review',
    waitMyReview = 'wait_my_review',
}
