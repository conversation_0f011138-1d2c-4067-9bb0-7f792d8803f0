"""Update companies table.

Revision ID: b8cacd8f4252
Revises: db161812db35
Create Date: 2017-07-30 13:53:59.989405


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b8cacd8f4252'
down_revision = 'db161812db35'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('full_name', sa.String(length=255), nullable=True))
    op.add_column('companies', sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False))
    op.add_column('companies', sa.Column('is_legal', sa.<PERSON>an(), server_default='1', nullable=False))
    op.create_unique_constraint('uix_edrpou_is_legal', 'companies', ['edrpou', 'is_legal'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uix_edrpou_is_legal', 'companies', type_='unique')
    op.drop_column('companies', 'is_legal')
    op.drop_column('companies', 'id')
    op.drop_column('companies', 'full_name')
    # ### end Alembic commands ###
