import React from 'react';

import PropTypes from 'prop-types';

// styles
import css from './steps.css';

const Steps = (props) => {
    const step = props.step || 1;
    const total = props.total || 1;
    return (
        <div className={css.root}>
            {step} крок з {total}
        </div>
    );
};

Steps.propTypes = {
    step: PropTypes.number,
    total: PropTypes.number,
};

export default Steps;
