import datetime
import logging

from aiohttp import web

from app.billing import db as billing_db
from app.lib import types as core_types
from app.services import services
from worker import topics
from worker import utils as worker_utils
from worker.emailing.reminders_for_unpaid_bills import db, emailing, types


@worker_utils.retry_config(max_attempts=5)
async def remind_about_employees_extension_bill_when_trial_expires(
    app: web.Application,
    data: core_types.DataDict,
    logger: logging.Logger,
) -> None:
    """
    https://tabula-rasa.atlassian.net/browse/DOC-6770

    Send a reminder about employees extension bill when trial expires.

    Remind that the trial period is about to end
    and the bill should be paid to continue using the service.
    """
    cursor: int = data.get('cursor', -1)

    # Get all extensions that are set to expire tomorrow
    date_expired = datetime.date.today() + datetime.timedelta(days=1)

    async with services.db.acquire() as conn:
        reminder = await db.select_bill_reminder_for_expiring_extension_trial(
            conn=conn,
            cursor=cursor,
            date_expired=date_expired,
        )
        if not reminder:
            logger.info(msg='No bills to remind', extra={'cursor': cursor})
            return
        bill = reminder.bill

        recipients = await db.select_bill_reminder_recipients(conn=conn, bill=bill)
        for recipient in recipients:
            await emailing.send_employees_extension_bill_reminder(
                reminder_data=reminder,
                recipient=recipient,
            )

    await services.kafka.send_record(
        topic=topics.REMIND_ABOUT_EMPLOYEES_EXTENSION_BILL_WHEN_TRIAL_EXPIRES,
        value={'cursor': bill.seqnum},
    )


async def gather_reminders_for_unpaid_bills(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    https://tabula-rasa.atlassian.net/browse/DOC-6935

    Step 1: Gather all unpaid bills, send them to distribution job `send_reminder_for_unpaid_bill`

    Note: employee extension bills are handled by
        remind_about_employees_extension_bill_when_trial_expires
    """

    # Gather unpaid bill ids
    async with services.db_readonly.acquire() as conn:
        unpaid_bill_ids = await db.select_unpaid_bill_ids_for_reminder(conn=conn, logger=logger)

    if not unpaid_bill_ids:
        logger.info('All unpaid bills were processed')
        return

    # Send one reminder job per bill_id
    await services.kafka.send_records(
        topic=topics.INITIATE_REMINDERS_FOR_UNPAID_BILL,
        values=[{'bill_id': bill_id} for bill_id in unpaid_bill_ids],
    )


async def initiate_reminders_for_unpaid_bill(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    https://tabula-rasa.atlassian.net/browse/DOC-6935

    Step 2: Initiate reminders about unpaid bill to admins and bill creator
    """

    bill_id = data['bill_id']

    async with services.db_readonly.acquire() as conn:
        bill = await billing_db.select_bill_by_id(conn=conn, bill_id=bill_id)
        if not bill:
            return

        recipients = await db.select_bill_reminder_recipients(conn=conn, bill=bill)

    if not recipients:
        logger.info('No recipients found for unpaid bill reminer', extra={'bill_id': bill_id})
        return

    # Send one reminder job per recipient
    await services.kafka.send_records(
        topic=topics.SEND_REMINDER_FOR_UNPAID_BILL,
        values=[{'bill_id': bill_id, **recipient.__dict__} for recipient in recipients],
    )


async def send_reminder_for_unpaid_bill(
    app: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    https://tabula-rasa.atlassian.net/browse/DOC-6935

    Step 3: Send reminder about unpaid bill
    """
    bill_id = data['bill_id']

    recipient = types.BillReminderRecipient(email=data['email'], first_name=data['first_name'])

    async with services.db_readonly.acquire() as conn:
        bill = await billing_db.select_bill_by_id(conn=conn, bill_id=bill_id)
        if not bill:
            return

    await emailing.send_email_for_unpaid_bill(recipient=recipient, bill=bill, logger=logger)
