import { AgGridReactProps } from '@ag-grid-community/react';

import { DEFAULT_PINNED_LEFT_COLUMN_WIDTH } from './constants';

// визначаємо що таблиця з множинним вибором та що вибір буде тільки по натисненню на чекбокс
export const rowSelection = {
    enableClickSelection: false,
    mode: 'multiRow',
} as AgGridReactProps['rowSelection'];

// визначаємо колонку з чекбоксами що вона запінена зліва
export const selectionColumnDef = {
    resizable: false,
    lockPinned: true,
    pinned: 'left',
    width: DEFAULT_PINNED_LEFT_COLUMN_WIDTH,
} as AgGridReactProps['selectionColumnDef'];
