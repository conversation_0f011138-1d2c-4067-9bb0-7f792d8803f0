import { useMutation } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { createDocumentFromDraftTemplate } from 'services/creationTemplates';
import { t } from 'ttag';

export const useCreateDocFromDraftTemplateMutation = () => {
    return useMutation({
        mutationFn: (
            params: Parameters<typeof createDocumentFromDraftTemplate>,
        ) => createDocumentFromDraftTemplate(...params),
        onSuccess: () => {
            snackbarToast.success(t`Документ успішно створений`);
        },
        onError: () => {
            snackbarToast.error(t`Помилка при створенні документа`);
        },
    });
};
