import logging

from aiohttp import web

from app.auth.db import select_user
from app.contacts import es
from app.contacts.db import select_unregistered_contacts_for_invite
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.types import DataDict
from app.registration.emailing import send_invite_email
from app.registration.utils import is_valid_email_domains
from app.services import services
from worker import topics
from worker.utils import is_migration_duplicate_detected

INVITE_CONTACTS_QUERY_LIMIT = 100

# How much contacts to index in one iteration:
#
# NOTE: The number of elasticsearch documents might be
# bigger than that number, because we are joining contacts with companies and contacts persons.
# On average, one contact has 2 persons and 1 company, but some contacts can have up to 1k persons.
CONTACTS_INDEXATION_LIMIT = 200


async def invite_unregistered_contacts(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Ad<PERSON> can request to invite unregistered contacts to register in the system.
    This job will find all unregistered contacts for current company and send
    them invite emails.
    """
    user_role_id: str = data['user_role_id']
    offset = data.get('offset', 0)

    async with app['db'].acquire() as conn:
        user = await select_user(conn, role_id=user_role_id)
        if not user:
            logger.warning('Inviter user not found')
            return

        contacts = await select_unregistered_contacts_for_invite(
            conn=conn,
            company_id=user.company_id,
            offset=offset,
            limit=INVITE_CONTACTS_QUERY_LIMIT,
        )

        if not contacts:
            logger.info('Sending invites for contacts is finished.')
            return

        for contact in contacts:
            recipient_email: str = contact.email
            recipient_edrpou: str = contact.edrpou
            recipient_email_domains: list[str] | None = contact.email_domains

            if not is_valid_email_domains(
                email=recipient_email,
                domains=recipient_email_domains,
            ):
                continue

            await send_invite_email(
                conn=conn,
                recipient_edrpou=recipient_edrpou,
                recipient_email=recipient_email,
                current_user=user,
                email_domains=None,
            )

    data['offset'] = offset + INVITE_CONTACTS_QUERY_LIMIT
    await app['kafka'].send_record(topics.INVITE_UNREGISTERED_CONTACTS, value=data)


async def index_contact_recipients(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send contacts to indexator. You can send contacts ids or companies or both.
    """
    # by default, we select only contacts that weren't processed before
    min_iteration: int = data.get('min_iteration', 0)
    max_iteration: int = data.get('max_iteration', 0)
    cursor: int = data.get('cursor', -1)

    if not get_flag(FeatureFlags.ENABLE_CONTACT_RECIPIENT_INDEX):
        return

    batch = await es.index_contact_recipients(
        limit=CONTACTS_INDEXATION_LIMIT,
        cursor=cursor,
        min_iteration=min_iteration,
        max_iteration=max_iteration,
    )

    if batch.size < CONTACTS_INDEXATION_LIMIT:
        logger.info(
            msg='Indexing contacts recipients is finished',
            extra={'data': data},
        )
        return

    # Continue indexing on the same iteration level
    data['cursor'] = batch.next_cursor
    logger.info(
        msg='Next iteration of indexing contacts recipients',
        extra={'data': data, 'batch_size': batch.size},
    )
    await services.kafka.send_record(topics.INDEX_CONTACT_RECIPIENTS, value=data)


async def delete_contacts_from_index(
    _: web.Application,
    data: DataDict,
    __: logging.Logger,
) -> None:
    """
    Delete given contacts from indexator
    """
    if not get_flag(FeatureFlags.ENABLE_CONTACT_RECIPIENT_INDEX):
        return

    contacts_ids: list[str] = data['contacts_ids']
    await es.delete_contacts(contacts_ids=contacts_ids)


async def delete_contacts_persons_from_index(
    _: web.Application,
    data: DataDict,
    __: logging.Logger,
) -> None:
    """
    Delete given contacts persons from indexator
    """
    if not get_flag(FeatureFlags.ENABLE_CONTACT_RECIPIENT_INDEX):
        return

    persons_ids: list[str] = data['persons_ids']
    await es.delete_contacts_persons(persons_ids=persons_ids)


async def reindex_contact_recipients_by_contacts(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Job to make full re-indexation of contact recipients by contacts
    """
    if get_flag(FeatureFlags.DISABLE_CONTACT_RECIPIENT_REINDEX):
        return

    cursor: str = data.get('cursor', '00000000-0000-0000-0000-000000000000')
    limit: int = data.get('limit', 1000)
    reset_lock: bool = data.pop('reset_lock', False)  # do not propagate this key further

    # You can reindex only contacts from one company
    company_id: str | None = data.get('company_id')
    # You can reindex contacts with given edrpou in all companies
    contact_edrpou: str | None = data.get('contact_edrpou')

    # Combine key to prevent job loss due to locks
    # and cursor collisions between jobs with reset_lock param
    key = 'reindex_contact_recipients_by_contacts'
    if company_id:
        key += f'_{company_id}'
    if contact_edrpou:
        key += f'_{contact_edrpou}'

    if await is_migration_duplicate_detected(
        key=key,
        ttl_seconds=10 * 60,  # 10 minutes
        cursor_uuid=cursor,
        reset=reset_lock,
    ):
        return

    next_cursor = await es.reindex_contact_recipients_by_contacts(
        cursor=cursor,
        limit=limit,
        company_id=company_id,
        contact_edrpou=contact_edrpou,
    )

    if not next_cursor:
        logger.info(
            msg='Reindexing contacts recipients by contacts is finished',
            extra={'data': data},
        )
        return

    data['cursor'] = next_cursor
    await services.kafka.send_record(topics.REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS, data)


async def reindex_contact_recipients_by_companies(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Job to make full re-indexation of contact recipients by companies
    """
    if get_flag(FeatureFlags.DISABLE_CONTACT_RECIPIENT_REINDEX):
        return

    cursor: str = data.get('cursor', '00000000-0000-0000-0000-000000000000')
    limit: int = data.get('limit', 1000)

    # You can reindex only specific companies
    companies_ids: list[str] | None = data.get('companies_ids')
    companies_edrpous: list[str] | None = data.get('companies_edrpous')

    if await is_migration_duplicate_detected(
        key='reindex_contact_recipients_by_companies',
        ttl_seconds=10 * 60,  # 10 minutes
        cursor_uuid=cursor,
    ):
        return

    next_cursor = await es.reindex_contact_recipients_by_companies(
        cursor=cursor,
        limit=limit,
        companies_ids=companies_ids,
        companies_edrpous=companies_edrpous,
    )

    if not next_cursor:
        logger.info(
            msg='Reindexing contacts recipients by companies is finished',
            extra={'data': data},
        )
        return

    data['cursor'] = next_cursor
    await services.kafka.send_record(topics.REINDEX_CONTACTS_RECIPIENTS_BY_COMPANIES, data)


async def reindex_contact_recipients_by_roles(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Job to make full re-indexation of contact recipients by roles
    """
    if get_flag(FeatureFlags.DISABLE_CONTACT_RECIPIENT_REINDEX):
        return

    cursor: str = data.get('cursor', '00000000-0000-0000-0000-000000000000')
    limit: int = data.get('limit', 1000)

    # You can reindex only specific roles
    roles_ids: list[str] | None = data.get('roles_ids')

    if await is_migration_duplicate_detected(
        key='reindex_contact_recipients_by_roles',
        ttl_seconds=10 * 60,  # 10 minutes
        cursor_uuid=cursor,
    ):
        return

    next_cursor = await es.reindex_contact_recipients_by_roles(
        cursor=cursor,
        limit=limit,
        roles_ids=roles_ids,
    )

    if not next_cursor:
        logger.info(
            msg='Reindexing contacts recipients by roles is finished',
            extra={'data': data},
        )
        return

    data['cursor'] = next_cursor
    await services.kafka.send_record(topics.REINDEX_CONTACTS_RECIPIENTS_BY_ROLES, data)
