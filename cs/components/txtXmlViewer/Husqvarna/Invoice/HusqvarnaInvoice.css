.header {
    padding-bottom: 0.1cm;
    border-bottom: 1px solid #333;
    margin-bottom: 0.1cm;
    font-size: 100%;
}

.headerImage {
    margin-left: -0.4cm;
}

.headerText {
    text-align: center;
}

.buyerSellerTables {
    margin: 0 0.2cm 0.2cm;
    font-size: 82%;
}

.sellerTable {
    margin-bottom: 0.2cm;
}

.buyerTable {
    margin-bottom: 0.4cm;
}

.buyerTable,
.sellerTable {
    width: 100%;
}

.buyersOrder {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.2cm;
}

.title {
    padding-top: 0.4cm;
    padding-bottom: 0.4cm;
    border-top: 1px solid #333;
    margin-top: 0.4cm;
    font-size: 115%;
    font-weight: bold;
    text-align: center;
}

.goodsTable {
    width: 100%;
    border: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
    border-collapse: collapse;
}

.goodsTable thead th {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    background-color: #fcf9ea;
    font-size: 82%;
    font-weight: bold;
    text-align: center;
}

.goodsTable tbody td {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    font-size: 75%;
    text-align: center;
    vertical-align: top;
}

.digitalSignaturesWrapper {
    position: relative;
}

.goodsTableFooterWrapper {
    margin-right: 0.2cm;
}

.goodsTableFooter {
    width: 50%;
    margin-bottom: 1cm;
    margin-left: 50%;
    font-size: 82%;
    text-align: right;
}

.attentionBlock {
    width: 67%;
    margin-left: 0.2cm;
    font-size: 82%;
}

.attentionBlockText + .attentionBlockText {
    margin-top: 0.4cm;
}

.goodsTable tbody td.cellTextLeft {
    text-align: left;
}
