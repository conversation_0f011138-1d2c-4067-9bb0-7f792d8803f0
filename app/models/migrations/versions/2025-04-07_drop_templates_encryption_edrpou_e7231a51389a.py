"""drop_templates_encryption_edrpou

Revision ID: e7231a51389a
Revises: 981f27fbdc43
Create Date: 2025-04-07 17:05:59.274704

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e7231a51389a'
down_revision = '981f27fbdc43'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('templates', 'encryption_edrpou')


def downgrade():
    op.add_column(
        'templates',
        sa.Column('encryption_edrpou', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    )
