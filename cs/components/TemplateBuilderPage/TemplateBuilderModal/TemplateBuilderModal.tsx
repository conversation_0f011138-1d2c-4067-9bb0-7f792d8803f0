import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FullScreenModal } from '@vchasno/ui-kit';

import MultiBrandLogo from 'components/header/components/MultiBrandLogo';
import { getIsTemplateBuilderModalOpen } from 'selectors/documentCreation.selectors';
import { setIsTemplateBuilderOpen } from 'store/documentCreationSlice';
import CloseButton from 'ui/closeButton/closeButton';

import { bc } from '../broadcastChannel';

import css from './TemplateBuilderModal.css';

export interface TemplateBuilderModalProps {
    className?: string;
}

const TemplateBuilderModal: React.FC<TemplateBuilderModalProps> = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getIsTemplateBuilderModalOpen);

    const handleClose = () => dispatch(setIsTemplateBuilderOpen(false));

    useEffect(() => {
        bc.onmessage = () => {};
    }, []);

    if (!isOpen) {
        return null;
    }

    return (
        <FullScreenModal
            contentClassName={css.modalContent}
            headerChildren={
                <>
                    <div className={css.logoWrapper}>
                        <MultiBrandLogo />
                    </div>
                    <CloseButton
                        withHover
                        position="static"
                        onClose={handleClose}
                    />
                </>
            }
        >
            <iframe className={css.iframe} src="/template-builder" />
        </FullScreenModal>
    );
};

export default TemplateBuilderModal;
