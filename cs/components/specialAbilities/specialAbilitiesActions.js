const actions = {
    SPECIAL_ABILITIES__LOAD_USER_DATA: 'SPECIAL_ABILITIES__LOAD_USER_DATA',

    // Delete documents
    SPECIAL_ABILITIES__DELETE_DOCUMENTS__START:
        'SPECIAL_ABILITIES__DELETE_DOCUMENTS__START',
    SPECIAL_ABILITIES__DELETE_DOCUMENTS__ERROR:
        'SPECIAL_ABILITIES__DELETE_DOCUMENTS__ERROR',
    SPECIAL_ABILITIES__DELETE_DOCUMENTS__FINISH:
        'SPECIAL_ABILITIES__DELETE_DOCUMENTS__FINISH',
    SPECIAL_ABILITIES__DELETE_DOCUMENTS__RESTART:
        'SPECIAL_ABILITIES__DELETE_DOCUMENTS__RESTART',

    // Update sign info
    SPECIAL_ABILITIES__UPDATE_SIGN_INFO_START:
        'SPECIAL_ABILITIES__UPDATE_SIGN_INFO_START',
    SPECIAL_ABILITIES__UPDATE_SIGN_INFO_ERROR:
        'SPECIAL_ABILITIES__UPDATE_SIGN_INFO_ERROR',
    SPECIAL_ABILITIES__UPDATE_SIGN_INFO_FINISH:
        'SPECIAL_ABILITIES__UPDATE_SIGN_INFO_FINISH',
    SPECIAL_ABILITIES__UPDATE_SIGN_INFO_SHOW_MESSAGE:
        'SPECIAL_ABILITIES__UPDATE_SIGN_INFO_SHOW_MESSAGE',
    SPECIAL_ABILITIES__UPDATE_SIGN_INFO_RESTART:
        'SPECIAL_ABILITIES__UPDATE_SIGN_INFO_RESTART',

    // Invite new users
    SPECIAL_ABILITIES__INVITE_NEW_USERS_START:
        'SPECIAL_ABILITIES__INVITE_NEW_USERS_START',
    SPECIAL_ABILITIES__INVITE_NEW_USERS_FINISH:
        'SPECIAL_ABILITIES__INVITE_NEW_USERS_FINISH',
    SPECIAL_ABILITIES__INVITE_NEW_USERS_ERROR:
        'SPECIAL_ABILITIES__INVITE_NEW_USERS_ERROR',

    // Refuse
    SPECIAL_ABILITIES__REFUSE: 'SPECIAL_ABILITIES__REFUSE',
    SPECIAL_ABILITIES__REFUSE_COMPANY_TYPE_CHANGE:
        'SPECIAL_ABILITIES__REFUSE_COMPANY_TYPE_CHANGE',
    SPECIAL_ABILITIES__REFUSE_EDRPOU_CHANGE:
        'SPECIAL_ABILITIES__REFUSE_EDRPOU_CHANGE',
    SPECIAL_ABILITIES__REFUSE_SET_MESSAGE:
        'SPECIAL_ABILITIES__REFUSE_SET_MESSAGE',

    // Add bonuses
    SPECIAL_ABILITIES__ADD_BONUSES_START:
        'SPECIAL_ABILITIES__ADD_BONUSES_START',
    SPECIAL_ABILITIES__ADD_BONUSES_FINISH:
        'SPECIAL_ABILITIES__ADD_BONUSES_FINISH',
    SPECIAL_ABILITIES__ADD_BONUSES_ERROR:
        'SPECIAL_ABILITIES__ADD_BONUSES_ERROR',
};

export default actions;
