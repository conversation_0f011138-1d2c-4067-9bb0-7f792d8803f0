import { stringifyLocationQuery } from 'lib/url';
import io from 'services/io';
import { getSignSessionId } from 'services/sign-session';

import {
    DiiaRequestOptions,
    DiiaRequestPayload,
    DiiaRequestSignResponse,
    DiiaRequestStatusResponse,
    SignAlgo,
} from './types';

export const getDiiaRequestStatus = (
    requestId: string,
): Promise<DiiaRequestStatusResponse> => {
    return io.get(`/api/v2/diia/${requestId}?t=${Date.now()}`, {}, true);
};

export const diiaRequestDocumentSign = async (
    docId: string,
    options: DiiaRequestOptions = {},
): Promise<DiiaRequestSignResponse> => {
    const {
        signAlgo = SignAlgo.DSTU,
        composeUrlPath = () =>
            options.isSignAnnulmentAct
                ? `/internal-api/documents/revoke/${options.annulmentActID}/sign/diia`
                : `/internal-api/documents/${docId}/diia`,
    } = options;
    const ssid = getSignSessionId();
    const data: DiiaRequestPayload = {};

    if (signAlgo !== SignAlgo.DSTU) {
        data.sign_algo = signAlgo;
    }

    let url = composeUrlPath();

    if (ssid) {
        url += stringifyLocationQuery({ ssid });
    }

    return await io.post(url, data, true);
};

export const diiaRequestForCompanyVerification = (): Promise<DiiaRequestSignResponse> => {
    return io.post('/internal-api/companies/diia', {}, true);
};
