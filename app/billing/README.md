# Billing

Вся логіка повʼязана з купівлею тарифів, поповненням балансу, виставленням рахунків за послуги.

## Проблеми з білінгом станом на літо 2024

#### 1.Таблиця billing_accounts має дві різні сутності: баланси і тарифи

В таблиці billing_accounts акаунти діляться приблизно на два великі класи:

- billing_accounts (type=client_rate) - для зберігання тарифів клієнтів
- billing_accounts (type=client_debit, client_bonus, ...) - для зберігання балансу документів по
  якомусь тарифу або без тарифу взагалі.

Спочатку не було ніяких тарифів, можна було поповнювати лише документи. Менеджери продажу чи служби
підтримки могли нарахувати документи як бонуси. В той час таблиця billing_accounts містила записи з
двох категорій, які починалися з префіксу "client\_" (client_debit, client_bonus, ...) і "service\_"
(service_debit, service_bonus, ...). Основна ідея була в тому, що документи ні звідки не беруться і
нікуди не діваються. Якщо якомусь клієнту були нараховані бонуси, то з на клієнтський акаунт
нараховувалося N документів, а з сервісного акаунту знімалося N документів. В таблицю
billing_transactions записувався запис про ці дві операції (по принципу double-entry bookkeeping).
До слова, сервісні акаунти завжди були привʼязані до ТОВ "Вчасно Сервіс", що ускладнювало тестування
і створення робочої бази "з нуля", оскільки сервісні акаунти треба було створювати вручну і до них
ще треба було створювати і "ТОВ Вчасно Сервіс".

Згодом в таблицю billing_account додався новий тип "client_rate", які можна було купити окремо від
поповнення балансу і вони мали включати "ПРО" функціональність, типу можливість створювати ярлики
(tags) чи створювати сценарії (document_templates).

На той час виглядало досить логічно розмістити тарифи (type=client_rate) в таблиці billing_accounts.
Оскільки між собою баланси і тарифи не перетиналися, то можна було купляти окремо можливості і можна
було окремо купувати документи.

Згодом в сервісі почали розвиватися тарифи і їх почало ставати більше. Зʼявилися тарифи в які
містили безлімітну кількість документів у собі. Зʼявилися також тарифи з лімітами, типу кількість
тегів яку можна було створити. Також зʼявилися тарифи, які передбачали поповнення документів разом з
купівлею тарифу. В цьому випадку ми в таблиці billing_accounts створювали два записи один з типом
client_rate інший з типом client_debit/client_bonus/... Наступним кроком розвитку було створення
безкоштовного тарифу, який всім компаніям створювався за замовчуванням і тому всі компанії мали хоча
б один активний тариф. Згодом саме поповнення балансу документів без тарифу як такого перестало
існувати в сервісі і поповнити документи можна було лише для API інтеграції (якщо не помиляюся, то
лише за умови наявності тарифу "Інтеграція").

В результаті ми прийшли до того, що в нас тарифи мають провідну роль в білінгу, кожна компанія має
тариф і баланс документів завжди йде в парі з тарифом. Але в таблиці billing_accounts тепер
зберігаються поєднання двох моделей "тарифу" і "балансу", які мають різне призначення і баланс став
залежати від тарифу. Частина полів для тарифу, яка не має бути nullable є nullable, бо цього поля
може не існувати в балансі і навпаки. Також при купівлі тарифу не дуже зрозуміло, який запис треба
робити в таблиці billing_transactions, яка містить записи про операції з балансом документів.

**Пропозиція**: розділити таблицю billing_accounts на дві таблиці: billing_rates і billing_balances.
Таблиця billing_balances буде мати посилання на billing_rates.

UPD (Jul 2024): Створена задача міграції
[DOC-6619](https://tabula-rasa.atlassian.net/browse/DOC-6619)

#### 2. CompanyPermissions і CompanyLimits зберігаються в налаштуваннях компанії

> NOTE: Проблема була вирішена в рамках задачі https://tabula-rasa.atlassian.net/browse/DOC-6619

Раніше, коли ще не було тарифів, ми великі нові можливості робили з можливістю їх
включення/виключення, оскільки не всім були потрібні, для прикладу, теги чи сценарії. Також
можливість відключити чи включити шмат логіки слугувала як засіб для тестування нових можливостей.
Ми включали якісь великій компанії, вона тестувала його і потім ми включали його для всіх або лише
для "крупних" компаній. Ці налаштування ми зберігали в налаштуваннях компанії, спочатку в у YAML
файлах на кожну велику компанію, потім у базі даних в таблиці companies_config.

Згодом, коли зʼявилися тарифи з "ПРО" можливостями здалося гарною ідеєю разом з активацією тарифу
перемикати значення в налаштуваннях компанії. Таким чином, коли компанія купувала тариф, ми
створювали запис в таблиці billing_accounts з типом client_rate і оновлювати companies_config. Це
був гарний старт на той час і давав можливість без тарифів, в обхід білінгу включати "ПРО"
можливості для індивідуальних клієнтів у продажів. З розвитком тарифів почали зʼявлятися нові
налаштування в компанії, які були лише призначені для тарифів. Для прикладу ліміти на кількість
тегів ("max_tags_count"). Ці налаштування вже не можна було змінити через адмінку і вони тісно були
повʼязані з білінгом, але ми продовжували їх зберігати в таблиці companies_config.

Якщо в компанії декілька тарифів, то ми в налаштуваннях зберігали агреговані значення зібрані з усіх
активних тарифів.

З часом стало зрозуміло, що треба розділити налаштування компанії і можливості/ліміти тарифів,
оскільки вони мають різне призначення. Також є проблема, що ми для перевірки можливості тягнемо з
бази весь обʼєкт налаштувань компанії, хоча нам потрібно лише одне поле.

**Пропозиція**: розділити налаштування компанії і можливості/ліміти тарифів. Наприклад, винести їх в
таблицю billing_statuses (або якось інакше назвати), яка буде мати 1 до 1 звʼязок з таблицею
companies і буде місити агреговані значення по активних тарифах:

- можливості (CompanyPermissions)
- ліміти (CompanyLimits)
- баланс документів

#### 3. Проблеми з billing_transactions

В таблиці billing_transactions ми зберігаємо записи про операції з балансом документів. Але через не
консистентність ми не завжди можемо записи в цю таблиці, особливо при роботі з тарифами. Також в цій
таблиці initiator_id містить в різних ситуаціях, то посилання на користувача, то посилання на
документ, то посилання на роль суперадміна.

Звучала пропозиція викинути цю таблицю, але й був такий коментар від аналітиків:

> Привіт, я використовую billing_transactions для підрахунку відправок документів , бо там є
> можливість визначити тариф з якого була відправка) тому якшо шо залиште якось можливість
> підрахунку доків якшо вирішите цю таблицю прибрати )

**Пропозиція**: переглянути цю таблицю і вирішити, чи вона нам потрібна нам. Можливо замість
double-entry bookkeeping використати таблицю з подіями з білінгом по тарифу чи компанії. Щось типу
billing_events.

#### 4. Налаштування тарифів

Кожен тариф у таблиці billing_accounts має поле rate зі enum значенням AccountRate. До кожного
значення цього enum у нас є dict з усіма налаштуваннями по тарифу.

```python
PRO_2021_OLD: RateConfig = {
    'permissions': ALL_PERMITTED,
    'limits': {
        CompanyLimit.additional_fields: None,
        CompanyLimit.employees: 25,
        CompanyLimit.documents: None,
        CompanyLimit.tags: None,
        CompanyLimit.automation: None,
        CompanyLimit.required_fields: None,
    },
}
```

Оскільки тарифи мають мати зворотню сумісність, то при зміні тарифу ми не могли просто змінити
значення у цьому конфігу. Коли команда продажів приходила зі змінами по тарифах, то ми створювали
новий тариф зі схожою назвою на попередній, лише добавляли префікс у форматі
`{name}_{year}_{month}`. Для прикладу, "ПРО" тариф:

```python
class AccountRate(NamedEnum):
    pro_free = auto()
    pro = auto()
    pro_trial = auto()
    pro_2022 = auto()
    pro_2022_01 = auto()
    pro_plus = auto()
    pro_2021 = auto()
    pro_plus_trial = auto()
    pro_2022_04 = auto()
    pro_2022_12 = auto()
    pro_2023_07 = auto()
```

В результаті ми тримаємо досить великий конфіг у коді з усіма історичними значеннями тарифів, поки у
попередніх тарифі не закінчиться термін дії. Також не дуже зручно працювати з цими тарифами, якщо по
коду є якась спеціальна логіка для якогось з тарифів. В такому випадку ми використовуємо @property у
enum AccountRate, яка скаже чи цей тариф є списку "ПРО" тарифів:

```python
class AccountRate(NamedEnum):
    # ...

    @property
    def is_pro(self) -> bool:
        return self in (
            AccountRate.pro,
            AccountRate.pro_2022,
            # ...
        )
```

**Пропозиція**: Від команди звучала пропозиція зберігати ці налаштування разом з тарифом для
компанії в базі даних. Тоді ми зможемо просто змінювати значення в dict конфігу і завжди мати лише
один актуальний тариф:

```
class AccountRate(NamedEnum):
    pro = auto()
    start = auto()
    enterprise = auto()
    ultimate = auto()
    free = auto()
```

#### 5. Тип рахунку

Перша реалізація рахунків у білінгу проєкту працювала лише на поповнення балансу документів, бо ще
тоді не існувало ні тарифів, ні розширень. Для цього в таблиці `bills` було створене поле
`count_documents: int | None`. В наступних ітераціях, коли зʼявилися тарифи, було додано поле
`rate: AccountRate | None`, яке вказувало на те який тариф був куплений компанією. Відповідно, якщо
активовувався тариф з `bill.count_documents is not None`, то нараховувалися документи. Якщо
активовувався тариф з `bill.rate is not None`, то ми включали можливості і ліміти для компанії
відповідно купленому тарифу.

Наступним кроком було створення спільного рахунку для поповнення документів і купівлі тарифу для
тарифу інтеграції. Це було зроблено ексклюзивно для тарифу інтеграція для зручності клієнтів. Для
цих цілей ми додали нове значення `integration_with_documents` в enum тарифів AccountRate і в
таблиці `bills` такий рахунок мав і `bill.rate == AccountRate.integration_with_documents` і
`bill.count_documents is not None`. Коли менеджер активував тариф, то ми по цьому значенню
створювали тариф `AccountRate.integration` і поповнювали баланс документів. Як на мене, з цього
моменту ми завернули не туди, бо тарифу `integration_with_documents` по факту ніколи не існувало і
не планується, а це значення потрібне лише для поведінки активації рахунку.

Наразі планується ще два нових типи рахунків для білінгу:

- купівля розширень (extensions) - додаткові можливості для тарифу
- купівля WEB тарифу і Архіву тарифу одночасно

І `AccountRate` буде не найкращим місцем для цих типів рахунків, бо по цей enum відповідає переліку
тарифів у білінгу, а не типам рахунків. Також при роботі з тарифами завжди треба памʼятати, що не
всі значення серед `AccountRate` є справжніми тарифами.

**Пропозиції для покращень**: https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/*********

## Розширення (extensions)

У клієнтів з'явилася потреба докуповувати до тарифу, який у них є додаткову кількість документів на
перегляд і додаткову кількість співробітників. Для прикладу у користувача є 20 документів на
перегляд на поточному тарифі і він хоче докупити ще 20 документів без зміни тарифу. Основні вимоги:

- докуплені ліміти мають бути дійсні разом з тарифом в якому вони куплені. Якщо тариф закінчився, то
  і докуплені ліміти мають бути неактивні.
- для докупівлі документів має бути можливість створити як і окремий рахунок, так і рахунок спільний
  разом з тарифом.
- при докупівлі співробітників мають мають бути "нараховані" відразу ж і якщо клієнт не заплатить
  протягом N днів, то вони мають бути відключені.
- повинна бути можливість ручної активації архіву до тарифу (на випадок, якщо поламається
  інтеграція, то хоч вручну будемо активувати)

Є пропозиція створити нову сутність розширення (extensions), яка буде привʼязана до тарифу і буде
містити поля, щоб відслідкувати дату активації, ціну, номер рахунку, кількість нарахованих
документів/співробітників, чи очікується оплата і т.д.

**Приблизний план роботи:**

1. Створити таблицю rate_extensions в яку будемо записувати всі куплені розширення куплені для
   поточного тарифу. Поля:
   - id
   - account_id
   - type:
     - "archive_documents" - поповнення limit-у на архів
     - "employees" - поповнення limit-у на співробітників
   - config: { units: int } - кількість додаткових одиниць
   - status:
     - "active" - розширення активне і значення limit-у було збільшено
     - "pending" - розширення очікує оплати
     - "deactivated_trial" - розширення деактивовано після закінчення тестового періоду
     - "deactivated_rate" - розширення було скасовано по закінченню тарифу
     - "canceled" - розширення було скасовано користувачем
   - bill_id - ID рахунку
   - date_created
   - date_updated
   - date_expiring - дата коли має закінчитися тріл

2 Створити таблицю billing_company в яку будемо переносити агреговані значення по активних тарифах з
таблиці companies_config. Для початку пропоную лише створити лише одне нове поле для нового ліміту
"max_archive_documents_count" і в наступних ітераціях переносити всі можливості і ліміти з
налаштувань компанії у цю таблицю. Поля:

- company_id (1 до 1 зcompanies)
- config: { max_archive_documents_count: int }

3. Додати rate_extension у відповідь графу, щоб на фронтенді можна було показати як користувачу його
   розширення так і в адмінці.

4. Створити методи для створення/видалення/редагування розширень через API адмінки. На фронтенді
   відповідно треба буде реалізувати можливість редагувати через адмінку.

5. Додати в граф новий об'єкт яким можна буде дістати ліміти і можливості по компанії. На початках
   це буде обʼєднання companies_configs і billing_company. Після міграції всіх лімітів і можливостей
   в billing_company, то можна буде просто віддавати значення billing_company. Також треба не забути
   оновити resolve_is_viewable з врахуванням max_documents_count

   - max_archive_documents_count.

6. Додати логіку деактивації лімітів розширення після закінчення тарифу

> **_NOTE:_** В цьому місці в нас вже зʼявиться можливість в ручному режимі активувати архіви для
> компанії через адмінку. Тому в цьому місці можна починати тестувати і починати робити розширення
> для співробітників. Наступні кроки будуть про автоматизацію цього процесу.

7. Додати API метод, щоб можна було сформувати рахунок лише на розширення. Зверстати сам рахунок і
   перевірити чи відправляється цей рахунок в CRM.

8. Додати активацію розширення через метод адмінки "activate_bill_service"

9. Додати логіку активації розширення через банківські інтеграції "process_evopay_payment",
   "process_bank_transaction"

...? Що ще забув?

** Приклад уявного обʼєкта з розширенням: **

Це не фінальний варіант, але для уявної картини як це буде виглядати:

```jsonc
{
    // Компанія
    "edrpou": 123,
    "name": "ТОВ ВСЕ ДЛЯ ВСІХ",
    "rates": [
        // Тарифи компанії. Їм може бути декілька
        {
            "id": 1,
            "name": "Базовий"
            "status": "active",
            "start_date": "...",
            "end_date": "...",

            // Нова сутність привʼязана до тарифів (FK на запис в таблиці billing_account з типом client_rate)
            // Може називатися як "rate_extensions".
            "extensions": [
                {
                    "id": 1234,
                    "type": "archive_documents",
                    "units": 1000,
                    "status": "active",
                    "bill_id": "...",
                    "date_created": "...",
                },
                {
                    "id": 23332,
                    "type": "employee",
                    "units": 1000,
                    "bill_id": "...",
                    "status": "trial",  // ще не оплатив (видалимо після 5 днів)
                    "date_created": "...",
                },
            ]
        }
    ]
}
```

## Як протестувати оплату карткою локально

Дивись розділ "Тестування локально" на сторінці
[Оплата карткою (backend)](https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/4955160/backend)
в Confluence
