import React, { FC } from 'react';

import DeliveryAcceptanceTareCertificate from 'components/txtXmlViewer/carlsbergUkraine/DeliveryAcceptanceTareCertificate/DeliveryAcceptanceTareCertificate';
import { DeliveryAcceptanceTareCertificateData } from 'components/txtXmlViewer/carlsbergUkraine/DeliveryAcceptanceTareCertificate/DeliveryAcceptanceTatreCertificateParser';
import { XMLDocumentViewOrientation } from 'components/txtXmlViewer/types';

import { XmlTemplateProps } from '../../../../types/xmlDocuments';

import { XmlParser } from '../../classes/XmlParser';
import DeliveryAcceptanceCertificate from '../DeliveryAcceptanceCertificate/DeliveryAcceptanceCertificate';
import { DeliveryAcceptanceCertificateData } from '../DeliveryAcceptanceCertificate/DeliveryAcceptanceCertificateParser';
import Invoice from '../Invoice/Invoice';
import { InvoiceData } from '../Invoice/InvoiceParser';
import ReturnInvoice from '../ReturnInvoice/ReturnInvoice';
import { ReturnInvoiceData } from '../ReturnInvoice/ReturnInvoiceParser';
import { DOCUMENT_TYPES, DOCUMENT_TYPE_TAG } from './constants';

const CARLSBERG_TEMPLATE_MAP = {
    [DOCUMENT_TYPES.deliveryAcceptance]: (props: XmlTemplateProps) => (
        <DeliveryAcceptanceCertificate
            data={props.data as DeliveryAcceptanceCertificateData}
        />
    ),
    [DOCUMENT_TYPES.deliveryAcceptanceTare]: (props: XmlTemplateProps) => (
        <DeliveryAcceptanceTareCertificate
            data={props.data as DeliveryAcceptanceTareCertificateData}
        />
    ),
    [DOCUMENT_TYPES.invoice]: (props: XmlTemplateProps) => (
        <Invoice data={props.data as InvoiceData} />
    ),
    [DOCUMENT_TYPES.returnInvoice]: (props: XmlTemplateProps) => (
        <ReturnInvoice data={props.data as ReturnInvoiceData} />
    ),
};

export const CarlsbergUkraineTemplateResolver: FC<XmlTemplateProps> & {
    orientation: XMLDocumentViewOrientation;
} = (props) => {
    const xml = new XmlParser(props.doc.content as string);
    const documentType = xml.parseTagContent(DOCUMENT_TYPE_TAG);
    const Component = CARLSBERG_TEMPLATE_MAP[documentType];

    return <Component {...props} />;
};
