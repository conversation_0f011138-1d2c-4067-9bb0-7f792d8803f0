import pytest

from api.errors import AccessDenied, Code, DoesNotExist, Error
from app.auth.types import User
from app.document_categories.db import insert_document_category
from app.document_versions.tests.utils import prepare_document_version
from app.lib.datetime_utils import utc_now
from app.services import services
from app.templates.tests.utils import prepare_template
from app.templates.validators import (
    validate_copy_document_as_template,
    validate_template_add,
    validate_template_delete,
    validate_template_duplicate,
    validate_template_update,
)
from app.tests.common import prepare_client, prepare_document_data, prepare_user_data
from app.uploads.constants import MB

TEST_UUID = '00000000-0000-0000-0000-000000000001'
TEST_UUID_2 = '00000000-0000-0000-0000-000000000002'


async def test_validate_template_add_no_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=False)

    # Assert
    with pytest.raises(AccessDenied):
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={
                    'title': 'Test',
                    'extension': '.docx',
                },
            )


async def test_validate_template_add_empty_content(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={
                    'title': 'Test',
                    'extension': '.docx',
                    'content': b'',
                },
            )
    assert e.value.code == Code.empty_upload_file


async def test_validate_template_add_file_too_big(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={'title': 'Test', 'extension': '.docx', 'content': b'x' * 100 * MB},
            )
    assert e.value.code == Code.max_file_size


async def test_validate_template_add_category_not_found(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={
                    'title': 'Test',
                    'extension': '.docx',
                    'category_id': 100500,
                },
            )
    assert e.value.code == Code.object_does_not_exist


async def test_validate_template_add_overdraft(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    monkeypatch.setattr('app.templates.validators.MAX_COMPANY_TEMPLATES', 0)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={
                    'title': 'Test',
                    'extension': '.docx',
                },
            )
    assert e.value.code == Code.overdraft


async def test_validate_template_add_wrong_extension(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_add(
                conn=conn,
                user=User.from_row(user),
                data_raw={
                    'title': 'Test',
                    'extension': '.pdf',
                },
            )
    assert e.value.code == Code.invalid_request


async def test_validate_template_add_success(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    async with services.db.acquire() as conn:
        document_category = await insert_document_category(
            conn=conn,
            title='Title',
            company_id=user.company_id,
        )
        # Act
        validated_data = await validate_template_add(
            conn=conn,
            user=User.from_row(user),
            data_raw={'title': 'Test', 'extension': '.docx', 'category_id': document_category.id},
        )

    # Assert
    assert validated_data.title == 'Test'
    assert validated_data.extension == '.docx'
    assert validated_data.category_id == document_category.id
    assert validated_data.content is None


async def test_validate_template_update_no_permission_for_edit(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=False)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(AccessDenied) as e:
        async with services.db.acquire() as conn:
            await validate_template_update(
                conn=conn,
                user=User.from_row(user),
                template_id=template.id,
                data_raw={'title': template.title + '42'},
            )
    assert e.value.code == Code.access_denied


async def test_validate_template_update_no_permission_wrong_company(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')
    template = await prepare_template(user=User.from_row(recipient))

    # Assert
    with pytest.raises(DoesNotExist) as e:
        async with services.db.acquire() as conn:
            await validate_template_update(
                conn=conn,
                user=User.from_row(user),
                template_id=template.id,
                data_raw={'title': template.title + '42'},
            )
    assert e.value.code == Code.object_does_not_exist


async def test_validate_template_update_category_not_found(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(DoesNotExist) as e:
        async with services.db.acquire() as conn:
            await validate_template_update(
                conn=conn,
                user=User.from_row(user),
                template_id=template.id,
                data_raw={'category_id': 100500},
            )
    assert e.value.code == Code.object_does_not_exist


async def test_validate_template_update_template_wrong_extension(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_update(
                conn=conn,
                user=User.from_row(user),
                template_id=template.id,
                data_raw={'extension': '.pdf'},
            )
    assert e.value.code == Code.invalid_request


async def test_validate_template_update_success(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    template = await prepare_template(user=User.from_row(user))

    # Act
    async with services.db.acquire() as conn:
        validated_data = await validate_template_update(
            conn=conn,
            user=User.from_row(user),
            template_id=template.id,
            data_raw={'title': template.title + '42'},
        )

    # Assert
    assert validated_data.title == template.title + '42'
    assert validated_data.category_id == template.category


async def test_validate_template_delete_no_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=False)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(AccessDenied):
        async with services.db.acquire() as conn:
            await validate_template_delete(
                conn=conn, user=User.from_row(user), template_id=template.id
            )


async def test_validate_template_no_permission_wrong_company(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')
    template = await prepare_template(user=User.from_row(recipient))

    # Assert
    with pytest.raises(DoesNotExist):
        async with services.db.acquire() as conn:
            await validate_template_delete(
                conn=conn, user=User.from_row(user), template_id=template.id
            )


async def test_validate_template_delete_success(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    template = await prepare_template(user=User.from_row(user))

    # Act
    async with services.db.acquire() as conn:
        validated = await validate_template_delete(
            conn=conn, user=User.from_row(user), template_id=template.id
        )

    assert validated.id == template.id


async def test_validate_template_duplicate_no_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=False)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(AccessDenied):
        async with services.db.acquire() as conn:
            await validate_template_duplicate(
                conn=conn, user=User.from_row(user), template_id=template.id
            )


async def test_validate_template_duplicate_no_permission_wrong_company(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')
    template = await prepare_template(user=User.from_row(recipient))

    # Assert
    with pytest.raises(DoesNotExist):
        async with services.db.acquire() as conn:
            await validate_template_duplicate(
                conn=conn, user=User.from_row(user), template_id=template.id
            )


async def test_validate_template_duplicate_overdraft(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    monkeypatch.setattr('app.templates.validators.MAX_COMPANY_TEMPLATES', 0)

    template = await prepare_template(user=User.from_row(user))

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_template_duplicate(
                conn=conn, user=User.from_row(user), template_id=template.id
            )
    assert e.value.code == Code.overdraft


async def test_validate_template_duplicate_success(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    template = await prepare_template(user=User.from_row(user))

    # Act
    async with services.db.acquire() as conn:
        validated = await validate_template_duplicate(
            conn=conn, user=User.from_row(user), template_id=template.id
        )

    assert validated.id == template.id


async def test_validate_template_duplicate_public_template_success(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')
    template = await prepare_template(is_private=False, user=User.from_row(recipient))

    # Act
    async with services.db.acquire() as conn:
        validated = await validate_template_duplicate(
            conn=conn, user=User.from_row(user), template_id=template.id
        )

    assert validated.id == template.id


async def test_validate_template_add_from_document_no_permission(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=False)

    # Assert
    with pytest.raises(AccessDenied):
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': TEST_UUID},
            )


async def test_validate_template_add_from_document_overdraft(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    monkeypatch.setattr('app.templates.validators.MAX_COMPANY_TEMPLATES', 0)

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': TEST_UUID},
            )
    assert e.value.code == Code.overdraft


async def test_validate_template_add_from_document_no_access_to_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    # Assert
    with pytest.raises(DoesNotExist):
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': TEST_UUID},
            )


async def test_validate_template_add_from_document_not_allowed_extension(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    document = await prepare_document_data(app, user, extension='.pdf')

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': document.id},
            )
    assert e.value.code == Code.invalid_file_extension


async def test_validate_template_add_from_document_not_allowed_version_extension(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    document = await prepare_document_data(app, user, extension='.docx')
    version = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
        extension='.pdf',
    )

    # Assert
    with pytest.raises(Error) as e:
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': document.id, 'version_id': version.id},
            )
    assert e.value.code == Code.invalid_file_extension


async def test_validate_template_add_from_document_version_does_not_exist(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_templates=True)

    document = await prepare_document_data(app, user)

    # Act
    with pytest.raises(DoesNotExist):
        async with services.db.acquire() as conn:
            await validate_copy_document_as_template(
                conn=conn,
                user=User.from_row(user),
                data_raw={'document_id': document.id, 'version_id': TEST_UUID},
            )
