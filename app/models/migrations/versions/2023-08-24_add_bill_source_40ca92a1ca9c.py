"""add_bill_source

Revision ID: 40ca92a1ca9c
Revises: 2c070aeded9c
Create Date: 2023-08-24 18:12:18.395867

"""
from alembic import op
import sqlalchemy as sa

from app.billing.types import BillSource
from app.models.types import SoftEnum


# revision identifiers, used by Alembic.
revision = '40ca92a1ca9c'
down_revision = '2c070aeded9c'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('bills', sa.Column('source', SoftEnum(BillSource), nullable=True))


def downgrade():
    op.drop_column('bills', 'source')
