import React, { useEffect } from 'react';

import { t } from 'ttag';

import CircleSpinner from '../ui/CircleSpinner/CircleSpinner';
import Icon from '../ui/icon/icon';

import { loadScript } from '../../lib/loadScript';
import { IIT_WIDGET_SCRIPT_ID } from '../IitSignWidget/constants';
import { VERIFICATION_WIDGET_PARENT_ID } from './constants';
import { useVerificationWidget } from './useVerificationWidget';

import KeySvg from '../usbSignForm/images/key.svg';

import css from './styles.css';

const IitVerificationWidget: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const { isLoading, widgetInit } = useVerificationWidget();

    useEffect(() => {
        const iitWidgetScript = document.getElementById(IIT_WIDGET_SCRIPT_ID);

        const loadIitWidgetScript = async () =>
            await loadScript(
                `${config.STATIC_HOST}/js/lib/iit/eusign.js`,
                IIT_WIDGET_SCRIPT_ID,
            );

        if (!iitWidgetScript) {
            loadIitWidgetScript().then(widgetInit);
        } else {
            widgetInit();
        }
    }, []);

    return (
        <>
            {/*
		        Батківський елемент для відображення iframe,
		        який завантажує сторінку SignWidget
	        */}
            <div id={VERIFICATION_WIDGET_PARENT_ID} style={{ height: 530 }} />

            {isLoading && (
                <div className={css.containerAlert}>
                    <div className={css.alert}>
                        <div className={css.icon}>
                            <Icon glyph={KeySvg} />
                        </div>
                        <CircleSpinner />
                        <div className={css.message}>
                            <div>{t`Завантажується бібліотека для роботи з апаратним ключем.`}</div>
                            <div>{t`Це може зайняти деякий час.`}</div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default IitVerificationWidget;
