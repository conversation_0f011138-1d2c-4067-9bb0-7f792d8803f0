import I from 'immutable';
import { isMoment, toDate } from 'lib/date';
import { joinStrings } from 'lib/helpers';
import { XmlCompany, XmlData, XmlProduct, XmlProducts } from 'records/xml';

import { parseTableData, textContent } from '../utils';

import { parseXml as domParseXml } from '../../../services/xml';
import * as constants from './constants';

const XmlExtra = new I.Record({
    deliveryPoint: null,
    deliveryPointStreetNumber: null,
    deliveryPointCity: null,
    sellerGLN: null,
    addDoc: null,
    addDocSigner: null,
    compositionChecked: null,
    shipped: null,
});

const REPLACE_QUANTITY_UNITS = ['pce'];

export const parseXmlPartner = (xml) => {
    const postalCode = textContent(
        xml,
        constants.BUYER_ADDRESS_POSTAL_CODE_QUERY,
        false,
    );
    const city = textContent(xml, constants.BUYER_ADDRESS_CITY_QUERY, false);
    const street = textContent(
        xml,
        constants.BUYER_ADDRESS_STREET_QUERY,
        false,
    );

    return new XmlCompany({
        address: joinStrings([postalCode, city, street], ', '),
        fullName: textContent(xml, constants.BUYER_NAME_QUERY),
        edrpou: textContent(xml, constants.BUYER_EDRPOU_QUERY),
        mfo: textContent(xml, constants.BUYER_MFO_QUERY),
        bank: {
            taxID: textContent(xml, constants.BUYER_TAX_ID_QUERY),
            mfo: textContent(xml, constants.BUYER_MFO_QUERY),
        },
        iban: textContent(xml, constants.BUYER_IBAN_QUERY),
        taxId: textContent(xml, constants.BUYER_TAX_ID_QUERY),
        iln: textContent(xml, constants.BUYER_ILN_QUERY),
    });
};

export const parseXmlOwner = (xml) => {
    const postalCode = textContent(
        xml,
        constants.SELLER_ADDRESS_POSTAL_CODE_QUERY,
        false,
    );
    const city = textContent(xml, constants.SELLER_ADDRESS_CITY_QUERY, false);
    const street = textContent(
        xml,
        constants.SELLER_ADDRESS_STREET_QUERY,
        false,
    );

    return new XmlCompany({
        address: joinStrings([postalCode, city, street], ', '),
        fullName: textContent(xml, constants.SELLER_NAME_QUERY),
        edrpou: textContent(xml, constants.SELLER_EDRPOU_QUERY),
        bank: {
            taxID: textContent(xml, constants.SELLER_TAX_ID_QUERY),
            mfo: textContent(xml, constants.SELLER_MFO_QUERY),
        },
        iban: textContent(xml, constants.SELLER_IBAN_QUERY),
        iln: textContent(xml, constants.SELLER_TAX_ILN_QUERY),
    });
};

export const parseXmlGoods = (serviceXml) => {
    const unit = textContent(serviceXml, constants.GOODS_UNIT_QUERY).trim();

    return new XmlProduct({
        item: textContent(serviceXml, constants.GOODS_ITEM_QUERY),
        unit: REPLACE_QUANTITY_UNITS.includes(unit.toLowerCase())
            ? 'шт.'
            : unit,
        name: textContent(serviceXml, constants.GOODS_ITEM_NAME_QUERY),
        code: textContent(serviceXml, constants.GOODS_ITEM_CODE_QUERY),
        ean: textContent(serviceXml, constants.GOODS_ITEM_EAN_QUERY),
        taxRate: textContent(serviceXml, constants.GOODS_ITEM_TAX_RATE_QUERY),
        taxAmount: textContent(
            serviceXml,
            constants.GOODS_ITEM_TAX_AMOUNT_QUERY,
        ),
        priceGross: textContent(
            serviceXml,
            constants.GOODS_ITEM_PRICE_GROSS_QUERY,
        ),
        sumGross: textContent(
            serviceXml,
            constants.GOODS_ITEM_SUM_GROSS_WITH_TAX_PRICE_QUERY,
        ),
        codeSelf: textContent(serviceXml, constants.GOODS_ITEM_CODE_SELF_QUERY),
        number: textContent(serviceXml, constants.GOODS_ITEM_NUMBER_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.GOODS_ITEM_PRICE_QUERY),
        ),
        quantity: textContent(serviceXml, constants.GOODS_ITEM_QUANTITY_QUERY),
        externalCode: textContent(
            serviceXml,
            constants.GOODS_ITEM_EXTERNAL_CODE_QUERY,
        ),
        supplierCode: textContent(
            serviceXml,
            constants.GOODS_ITEM_SUPPLIER_CODE_QUERY,
        ),
        sum: parseFloat(
            textContent(
                serviceXml,
                constants.GOODS_ITEM_SUM_WITH_TAX_PRICE_QUERY,
            ),
        ),
        additionalNetAmount: parseFloat(
            textContent(
                serviceXml,
                constants.GOODS_ITEM_ADDITIONAL_NET_AMOUNT_QUERY,
            ),
        ),
        additionalUnitOfMeasure: textContent(
            serviceXml,
            constants.GOODS_ITEM_ADDITIONAL_UNIT_OF_MEASURE_QUERY,
        ),
        additionalInvoiceQuantity: textContent(
            serviceXml,
            constants.GOODS_ITEM_ADDITIONAL_INVOICE_QUANTITY_QUERY,
        ),
    });
};

export const parseXmlServices = (xml) => {
    const goodsXmls = xml.querySelector(constants.GOODS_QUERY)
        ? parseTableData(xml, constants.GOODS_QUERY, constants.GOODS_ITEM_QUERY)
        : [];

    const items = new I.List(goodsXmls.map(parseXmlGoods));

    return new XmlProducts({
        items,
        totalSum: parseFloat(textContent(xml, constants.GOODS_SUM_QUERY)),
        totalTaxes: parseFloat(
            textContent(xml, constants.GOODS_SUM_TAXES_QUERY),
        ),
        totalDocumentSum: parseFloat(
            textContent(xml, constants.GOODS_SUM_WITH_TAX_QUERY),
        ),
        totalDocumentSumStr: textContent(
            xml,
            constants.GOODS_SUM_WITH_TAX_TEXT_QUERY,
        ),
    });
};

export const parseExtraXml = (xml) => {
    return new XmlExtra({
        deliveryPoint: textContent(
            xml,
            constants.EXTRA_BUYER_DELIVERY_POINT_QUERY,
        ),
        deliveryPointStreetNumber: textContent(
            xml,
            constants.EXTRA_DELIVERY_POINT_STREET_NUMBER_QUERY,
        ),
        deliveryPointCity: textContent(
            xml,
            constants.EXTRA_DELIVERY_POINT_CITY_QUERY,
        ),
        sellerGLN: textContent(xml, constants.EXTRA_SELLER_GLN_QUERY),
        addDoc: textContent(xml, constants.EXTRA_ADD_DOC_QUERY),
        addDocSigner: textContent(xml, constants.EXTRA_ADD_DOC_SIGNER_QUERY),
        compositionChecked: textContent(
            xml,
            constants.EXTRA_COMPOSITION_CHECKED_QUERY,
        ),
        shipped: textContent(xml, constants.EXTRA_SHIPPED_QUERY),
    });
};

export const parseXml = (content) => {
    const xml = domParseXml(content);
    const date = textContent(xml, constants.DATE_QUERY).trim();
    const formatDateStr = (inputDate) => {
        if (isMoment(inputDate)) toDate(inputDate);
        return inputDate;
    };
    const dateTo = textContent(
        xml,
        constants.DATE_DELIVERY_QUERY,
        false,
    ).trim();

    return new XmlData({
        date: date && formatDateStr(date),
        dateTo,
        number: textContent(xml, constants.NOTE_NUMBER_QUERY),
        deliveryAddress: textContent(xml, constants.DELIVERY_ADDRESS),
        agreementName: textContent(xml, constants.NOTE_AGREEMENT_NUMBER_QUERY),
        agreementDate: textContent(xml, constants.NOTE_AGREEMENT_DATE_QUERY),
        place: textContent(xml, constants.PLACE_QUERY),
        paymentTerm: textContent(xml, constants.PAYMENT_TERM),
        buyerOrder:
            textContent(xml, constants.BUYER_ORDER_OVERWRITE_QUERY) ||
            textContent(xml, constants.BUYER_ORDER_QUERY),
        buyerOrderDate: textContent(xml, constants.BUYER_ORDER_DATE_QUERY),
        managerOrder: textContent(xml, constants.MANAGER_ORDER_QUERY),
        paymentTernOrder: textContent(xml, constants.PAYMENT_TERN_ORDER_QUERY),

        services: parseXmlServices(xml),
        partner: parseXmlPartner(xml),
        owner: parseXmlOwner(xml),
        extra: parseExtraXml(xml),
    });
};

export default (data) => parseXml(data.content);
