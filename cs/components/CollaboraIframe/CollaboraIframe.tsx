import React, { useEffect, useRef } from 'react';

import { Alert } from '@vchasno/ui-kit';

import cn from 'classnames';
import {
    HIDE_UI_BUTTON_ID_LIST,
    HIDE_UI_MENU_ITEM_LIST,
} from 'components/document/edit/DocumentEditor/constants';
import {
    ActionSaveResp,
    AppLoadingMessage,
    DocModifiedStatus,
} from 'components/document/edit/DocumentEditor/types';
import { useCollaboraPostMessageActions } from 'components/document/edit/DocumentEditor/useCollaboraPostMessageActions';
import { usePostMessageSubscribe } from 'components/document/edit/DocumentEditor/usePostMessageSubscribe';
import { generateCssVarsRoot } from 'components/document/edit/DocumentEditor/utils';
import {
    CollaboraExcelExt,
    CollaboraExcelExtOld,
    CollaboraWordExt,
    CollaboraWordExtOld,
} from 'services/creationTemplates';

import type { CollaboraData, CollaboraIframeFont } from './types';

import {
    appendHeader,
    generateCssDisplayNone,
    getHideElementSelectors,
    makeAppendLink,
    makeAppendStyle,
} from './utils';

import { fontConfigMap } from './fonts';
import { useLocalStorageSyncDefaultsEffect } from './useLocalStorageSyncDefaults';

import css from './CollaboraIframe.css';

const ADDITIONAL_ELEMENT_SELECTORS_TO_HIDE = [
    'nav.main-nav',
    '#toolbar-wrapper',
] as const;

type AdditionalElementSelectorsToHide = typeof ADDITIONAL_ELEMENT_SELECTORS_TO_HIDE[number];

export interface CollaboraIframeProps {
    // повинно передавати ззовні для взаємодії з іфреймом
    iframeRef: React.RefObject<HTMLIFrameElement>;
    // передаємо додаткові пропси для іфрейму
    iframeProps?: React.IframeHTMLAttributes<HTMLIFrameElement>;
    // якщо декілька іфреймів на сторінці, то можна вказати id для кожного - потрібно для взаємодії з іфреймом та формою
    iframeId?: string;
    // посилання на документ в системі collabora
    docData: CollaboraData | null | undefined;
    className?: string;
    error?: string;
    onInitialized?: () => void;
    onFrameReady?: () => void;
    // коли іфрейм готовий до взаємодії і доступний для відправки повідомлень
    onReady?: () => void;
    onSave?: () => void;
    // коли змінюється статус зміненості документу - тільки в режимі редагування
    onModifiedChange?: (modified: boolean) => void;
    // по замовчуванню скролінг по документу перехоплюється і не вспливає на документ, тому не працює скролл основної сторінки
    scrollPropagation?: boolean;
    // можливість приховати деякі елементи від UI
    hideElementSelectors?: AdditionalElementSelectorsToHide[];
    customFont?: CollaboraIframeFont;
    initialZoom?: number;
    // Перевірка орфографії за замовчуванням
    spellCheck?: boolean;
}

const CollaboraIframe: React.FC<CollaboraIframeProps> = ({
    docData,
    className,
    iframeProps,
    iframeRef,
    error,
    iframeId = 'collabora-online-viewer',
    onReady,
    onSave,
    onInitialized,
    onFrameReady,
    onModifiedChange,
    scrollPropagation,
    hideElementSelectors,
    customFont = 'Roboto',
    initialZoom,
    spellCheck,
}) => {
    // треба навчитися розпізнавати типи файлів, щоб відповідно до них виконувати приховувати елементи
    const extension = useRef<
        | CollaboraWordExt
        | CollaboraWordExtOld
        | CollaboraExcelExt
        | CollaboraExcelExtOld
    >('.docx');
    const formRef = useRef<HTMLFormElement>(null);
    const collaboraPostMessageActions = useCollaboraPostMessageActions(
        iframeRef,
    );

    if (config.DEBUG) {
        Object.assign(window, {
            __collaboraPostMessageActions: collaboraPostMessageActions,
        });
    }

    // задаємо через localStorage значення за замовчуванням для деяких налаштувань
    // @see <https://sdk.collaboraonline.com/docs/cookies_and_local_storage.html>
    useLocalStorageSyncDefaultsEffect({ spellCheck });

    const applyFont = () => {
        if (customFont === 'Roboto') {
            const link = makeAppendLink(fontConfigMap[customFont].fontUrl);
            const style = makeAppendStyle(
                generateCssVarsRoot({
                    '--default-font-size': '13px',
                    '--cool-font': fontConfigMap[customFont].fontName,
                }),
            );
            appendHeader(iframeRef, link);
            appendHeader(iframeRef, style);
        }
    };

    const hideUiElements = () => {
        HIDE_UI_BUTTON_ID_LIST.forEach(collaboraPostMessageActions.hideButton);
        HIDE_UI_MENU_ITEM_LIST.forEach(
            collaboraPostMessageActions.hideMenuItem,
        );

        // приховуємо за допомогою CSS не потрібні UI елементи
        const hideElementStyle = makeAppendStyle(
            generateCssDisplayNone(
                getHideElementSelectors(extension.current).concat(
                    hideElementSelectors || [],
                ),
            ),
        );

        appendHeader(iframeRef, hideElementStyle);
    };

    usePostMessageSubscribe(
        {
            // @see <https://sdk.collaboraonline.com/docs/postmessage_api.html#initialization>
            App_LoadingStatus: (data: AppLoadingMessage) => {
                if (data.Values?.Status === 'Initialized') {
                    collaboraPostMessageActions.postMessageReady();

                    onInitialized?.();

                    collaboraPostMessageActions.uiMode('notebookbar');

                    applyFont();

                    hideUiElements();
                }

                if (data?.Values?.Status === 'Frame_Ready') {
                    onFrameReady?.();
                }

                if (data?.Values?.Status === 'Document_Loaded') {
                    onReady?.();

                    if (initialZoom) {
                        // @ts-ignore
                        iframeRef.current?.contentWindow?.app?.map?.setZoom(
                            initialZoom,
                        );
                    }

                    // todo: треба більш детально розбрітися з процесом коли треба зупиняти подію скролу
                    if (scrollPropagation) {
                        iframeRef.current?.contentWindow?.document
                            .querySelector('.leaflet-layer') // елемент на якому відловлюється подія скролу та передається на
                            ?.addEventListener('wheel', (e) => {
                                e.stopPropagation(); // зупиняємо поширення події скролу, та відміняємо дію за замовчуванням
                            });
                    }
                }
            },
            Action_Save_Resp: (data: ActionSaveResp) => {
                if (data?.Values?.success) {
                    onSave?.();
                }
            },
            Doc_ModifiedStatus: (data: DocModifiedStatus) => {
                onModifiedChange?.(data?.Values?.Modified || false);
            },
        },
        iframeRef,
    );

    // запускаємо редактор після отримання даних від сервера
    useEffect(() => {
        if (docData) {
            formRef.current?.submit();
        }
    }, [docData]);

    // перед закриттям посилаємо сигнал до collabora, щоб закрити сесію
    useEffect(() => {
        return () => {
            collaboraPostMessageActions.close();
        };
    }, []);

    return (
        <div
            className={cn(className, {
                [css.error]: Boolean(error),
            })}
        >
            {error && (
                <Alert wide type="error">
                    {error}
                </Alert>
            )}
            <iframe
                ref={iframeRef}
                {...iframeProps}
                style={{
                    width: '100%',
                    height: '100%',
                    ...iframeProps?.style,
                }}
                id={iframeId}
                name={iframeId}
            />
            <div style={{ display: 'none' }}>
                <form
                    ref={formRef}
                    action={docData?.url || ''}
                    encType="multipart/form-data"
                    method="post"
                    target={iframeId}
                >
                    <input
                        name="access_token"
                        value={docData?.token || ''}
                        type="hidden"
                        id="access-token"
                    />
                    <input type="submit" value="" />
                </form>
            </div>
        </div>
    );
};

export default CollaboraIframe;
