import React, { useEffect, useMemo } from 'react';
import { Controller } from 'react-hook-form';

import { TextInput } from '@vchasno/ui-kit';

import cn from 'classnames';
import { queryClient } from 'lib/queries';
import { SEARCH_COMPANY_EMAIL } from 'lib/queriesConstants';
import { findRecipientEmail } from 'services/documents/api';
import { RecipientEmail } from 'services/documents/ts/types';
import { t } from 'ttag';
import CircleSpinner from 'ui/CircleSpinner/CircleSpinner';
import Input from 'ui/input/input';
import PseudoLink from 'ui/pseudolink/pseudolink';

import { edrpouSchema } from './schema';
import { useAddCounterpartyForm } from './useAddCounterpartyForm';

import css from './AddCounterpartyForm.css';

interface EmailControlProps {
    methods: ReturnType<typeof useAddCounterpartyForm>;
    autoFillEmail?: boolean;
    onSuccessfulAutoFill?: (email: string) => void;
}

const NOT_FOUND_ERROR = 'NOT_FOUND_EMAIL_BY_EDRPOU_ERROR_TYPE';

const EmailControl: React.FC<EmailControlProps> = ({
    methods,
    autoFillEmail = true,
    onSuccessfulAutoFill,
}) => {
    const { control, watch, setValue, getValues } = methods;
    const isHidden = watch('isHidden');
    const edrpou = watch('edrpou') || '';
    const email = watch('email') || '';
    const isEdrpouValid = useMemo(
        () => Boolean(edrpou && edrpouSchema.isValidSync(edrpou)),
        [edrpou],
    );
    const isFetchingAutoFill = Boolean(
        queryClient.isFetching([SEARCH_COMPANY_EMAIL, edrpou]),
    );

    useEffect(() => {
        if (methods.getFieldState('email').error?.type === NOT_FOUND_ERROR) {
            methods.clearErrors('email');
        }
    }, [email]);

    useEffect(() => {
        if (getValues('isHidden')) {
            setValue('isHidden', false, {
                shouldValidate: methods.formState.isSubmitted,
            });
        }
    }, [edrpou]);

    const handleAutoFillEmail = async () => {
        const list = await queryClient.fetchQuery<RecipientEmail[]>(
            [SEARCH_COMPANY_EMAIL, edrpou],
            () => findRecipientEmail(null, [edrpou]),
            {
                staleTime: Infinity,
            },
        );

        if (!list || !list[0]) {
            methods.setError('email', {
                message: t`Не вдалося підібрати email`,
                type: NOT_FOUND_ERROR,
            });
            return;
        }

        const [{ is_hidden, emails }] = list;

        if (is_hidden) {
            setValue('isHidden', true, {
                shouldValidate: methods.formState.isSubmitted,
            });
            setValue('email', '', {
                shouldValidate: methods.formState.isSubmitted,
            });
        } else if (emails[0]) {
            setValue('email', emails[0], {
                shouldValidate: methods.formState.isSubmitted,
            });
            onSuccessfulAutoFill?.(emails[0]);
        }
    };

    if (isHidden) {
        return <Input disabled placeholder={t`Прихований email`} />;
    }

    return (
        <Controller
            key={edrpou}
            control={control}
            name="email"
            render={({ field, fieldState }) => (
                <TextInput
                    wide
                    className={cn({ [css.inputWithControl]: isEdrpouValid })}
                    value={field.value}
                    onChange={field.onChange}
                    label={t`Email`}
                    error={fieldState.error?.message}
                    endElement={
                        <span className={css.emailInputBtn}>
                            {isFetchingAutoFill && <CircleSpinner />}
                            {!isFetchingAutoFill &&
                                isEdrpouValid &&
                                autoFillEmail && (
                                    <PseudoLink
                                        disabled={!isEdrpouValid}
                                        onClick={handleAutoFillEmail}
                                    >{t`Підібрати`}</PseudoLink>
                                )}
                        </span>
                    }
                />
            )}
        />
    );
};

export default EmailControl;
