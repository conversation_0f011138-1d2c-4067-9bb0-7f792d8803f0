import logging

from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.sql import ClauseElement

from app.auth.tables import user_table
from app.auth.types import Auth<PERSON><PERSON>, User
from app.lib.database import DBConnection, DBRow
from app.lib.types import DataDict
from app.models import exists, select_one
from app.notifications.tables import (
    notification_table,
    unsubscription_table,
)

logger = logging.getLogger(__name__)


async def insert_notifications(conn: DBConnection, data: list[DataDict]) -> None:
    if not data:
        logger.warning('Attempt to insert empty notifications list')
        return

    await conn.execute(notification_table.insert().values(data))


async def insert_unsubscription(conn: DBConnection, email: str) -> None:
    await conn.execute(
        insert(unsubscription_table).values({'email': email}).on_conflict_do_nothing()
    )


async def remove_unsubscription(conn: DBConnection, email: str) -> None:
    await conn.execute(unsubscription_table.delete().where(unsubscription_table.c.email == email))


async def is_unsubscribed(conn: DBConnection, email: str) -> bool:
    result = await exists(
        conn=conn,
        select_from=unsubscription_table,
        clause=unsubscription_table.c.email == email,
    )
    if result:
        logger.info('Email is unsubscribed', extra={'email': email})
    return result


async def select_notification_by(conn: DBConnection, clause: ClauseElement) -> DBRow:
    return await select_one(conn, (notification_table.select().where(clause)))


async def update_popup_data_for_user(
    conn: DBConnection, auth_user: AuthUser | User, popup_data: DataDict
) -> None:
    extra = auth_user.extra or {}
    extra.update(popup_data)
    data = {'extra': extra}

    await conn.scalar(
        user_table.update()
        .values(data)
        .where(user_table.c.id == auth_user.id)
        .returning(user_table.c.id)
    )
