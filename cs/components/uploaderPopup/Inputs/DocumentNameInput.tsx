import React from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import { t } from 'ttag';

import { getDocTitle } from '../../../selectors/uploader.selectors';
import { useUploadActions } from '../../uploader/useUploadActions';

import Input from '../../ui/input/input';

export interface DocumentNameInputProps {
    className?: string;
}

const DocumentNameInput: React.FC<
    React.PropsWithChildren<DocumentNameInputProps>
> = ({ className }) => {
    const uploadActions = useUploadActions();
    const value = useSelector(getDocTitle);

    return (
        <Input
            value={value}
            className={cn(className)}
            label={t`Назва документа`}
            name="docTitle"
            type="text"
            onChange={uploadActions.onInfoChangeField}
            dataQa="qa_name_of_document"
        />
    );
};

export default DocumentNameInput;
