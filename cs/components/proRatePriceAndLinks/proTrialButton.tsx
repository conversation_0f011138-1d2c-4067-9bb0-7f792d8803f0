import React, { useState } from 'react';
import { connect } from 'react-redux';

import { LATEST_PRO_PLUS_TRIAL_RATE } from 'services/billing/constants';
import { t } from 'ttag';

import appActionCreators from '../app/appActionCreators';
import proRateInfoPopupActionCreators from '../proRateInfoPopup/proRateInfoPopupActionCreators';
import proRateTrialPopupActionCreators from '../proRateTrialPopup/proRateTrialPopupActionCreators';

import Button from '../ui/button/button';
import Message from '../ui/message/message';

import eventTracking from '../../services/analytics/eventTracking';
import { createTrialRate } from '../../services/billing';

interface DispatchToProps {
    onRefreshCurrentUser: () => void;
    onShowTrialPopup: () => void;
    onCloseProRatePopup: () => void;
}

interface OwnProps {
    trackingLabel: string;
}

type Props = OwnProps & DispatchToProps;

const ProTrialButtonFC = (props: Props) => {
    const [error, setError] = useState('');
    const [buttonLoading, setButtonLoading] = useState(false);

    const handleTrialClick = async () => {
        setButtonLoading(true);
        eventTracking.sendEvent(
            'pro-rate',
            'click_get_pro_btn',
            props.trackingLabel,
        );
        try {
            await createTrialRate(LATEST_PRO_PLUS_TRIAL_RATE);
        } catch (e) {
            setError(e.toString());
            return;
        } finally {
            setButtonLoading(false);
        }
        await props.onRefreshCurrentUser();
        await props.onCloseProRatePopup();
        await props.onShowTrialPopup();
    };
    return (
        <>
            <Button
                theme="cta"
                width="full"
                onClick={handleTrialClick}
                isLoading={buttonLoading}
            >
                {t`Підключити`}
            </Button>
            {error && (
                <Message sizeSmall type="error">
                    {error}
                </Message>
            )}
        </>
    );
};

const mapDispatchToProps = {
    onRefreshCurrentUser: appActionCreators.onRefreshCurrentUser,
    onShowTrialPopup: proRateTrialPopupActionCreators.onShow,
    onCloseProRatePopup: proRateInfoPopupActionCreators.onClose,
};

const connector = connect<Record<string, unknown>, DispatchToProps, OwnProps>(
    () => ({}),
    mapDispatchToProps,
);

export const ProTrialButton = connector(ProTrialButtonFC);
