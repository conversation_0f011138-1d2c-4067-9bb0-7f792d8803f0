.root {
    display: inline-flex;
    height: 20px;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    line-height: 14px;
}

.viewport {
    position: relative;
    overflow: hidden;
    width: 18px;
    height: 14px;
}

.values {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(0);
    transition: transform 0.7s;
}

.value {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.yellow {
    background-color: #ffe551;
    color: #252d3d;
}
