import React from 'react';

import { Text } from '@vchasno/ui-kit';

import { openInNewTab } from 'lib/navigation';
import { t } from 'ttag';

export interface GoToSettingsLintProps {
    className?: string;
}

const GoToSettingsLink: React.FC<GoToSettingsLintProps> = ({ className }) => {
    const handleOpenSettings = () => {
        openInNewTab('/app/settings/document-settings#document-fields');
    };

    return (
        <Text type="link" onClick={handleOpenSettings} className={className}>
            {t`Перейти до налаштувань параметрів`}
        </Text>
    );
};

export default GoToSettingsLink;
