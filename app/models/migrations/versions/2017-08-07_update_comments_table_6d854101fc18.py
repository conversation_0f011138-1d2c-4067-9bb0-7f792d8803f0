"""Update comments table.

Revision ID: 6d854101fc18
Revises: f8572b5dbb55
Create Date: 2017-08-07 14:41:09.542773


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6d854101fc18'
down_revision = 'f8572b5dbb55'
branch_labels = None
depends_on = None

db_enum = postgresql.ENUM('comment', 'rejection', name='commenttype')

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    db_enum.create(op.get_bind())
    op.add_column('comments', sa.Column('role_id', postgresql.UUID(), nullable=True))
    op.add_column('comments', sa.Column('type', db_enum, nullable=True))
    op.create_foreign_key(None, 'comments', 'roles', ['role_id'], ['id'], ondelete='CASCADE')
    op.drop_column('comments', 'read_by')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('comments', sa.Column('read_by', postgresql.JSON(astext_type=sa.Text()), server_default=sa.text("'[]'::json"), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.drop_column('comments', 'type')
    op.drop_column('comments', 'role_id')
    db_enum.drop(op.get_bind())
    # ### end Alembic commands ###
