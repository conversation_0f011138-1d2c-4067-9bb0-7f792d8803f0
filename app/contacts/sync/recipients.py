import logging

import sqlalchemy as sa
from sqlalchemy import and_, case, desc, select

from app.auth.enums import RoleStatus
from app.auth.tables import company_table, role_table, user_table
from app.contacts import db, sync
from app.contacts.tables import contact_person_table, contact_table
from app.contacts.utils import save_to_contacts
from app.documents.db import update_email_recipient
from app.documents.tables import listing_table
from app.lib.database import DBConnection, DBRow
from app.models import select_one
from app.signatures.tables import signature_table

logger = logging.getLogger(__name__)


class AutocompleteRecipient:
    """
    Server side autocomplete most appropriate recipient (if missing).

    Note: this class is a part of the "sync_contacts" logic.
    """

    def __init__(
        self, conn: DBConnection, owner_edrpou: str, owner_company_id: str, edrpou: str
    ) -> None:
        self.conn = conn
        self.recipient_edrpou = edrpou
        self.owner_edrpou = owner_edrpou
        self.owner_company_id = owner_company_id

    async def get_main_recipient(self) -> DBRow:
        """Get most recent email marked as ``main`` in contacts list."""
        return await select_one(
            self.conn,
            (
                select([contact_person_table.c.email])
                .select_from(
                    contact_person_table.join(
                        contact_table,
                        contact_table.c.id == contact_person_table.c.contact_id,
                    ).join(company_table, company_table.c.id == contact_table.c.company_id)
                )
                .where(
                    and_(
                        contact_table.c.edrpou == self.recipient_edrpou,
                        company_table.c.id == self.owner_company_id,
                        contact_person_table.c.main_recipient.is_(True),
                    )
                )
            ),
        )

    async def get_accountant_email(self) -> DBRow:
        """Get contact.accountant_email provided by API."""
        return await select_one(
            self.conn,
            (
                select([contact_table.c.accountant_email.label('email')])
                .select_from(
                    contact_table.join(
                        company_table, company_table.c.id == contact_table.c.company_id
                    )
                )
                .where(
                    and_(
                        contact_table.c.edrpou == self.recipient_edrpou,
                        contact_table.c.accountant_email.like('%@%'),
                        company_table.c.id == self.owner_company_id,
                    )
                )
            ),
        )

    async def get_last_mutual_signer(self) -> DBRow:
        """
        Check registered signers, signed documents of current edrpou.
        Return most recent signature's user (ok or reject, whatever).
        """
        mutual_connection_check = (
            select([listing_table.c.access_edrpou])
            .select_from(
                listing_table.join(
                    company_table,
                    company_table.c.edrpou == listing_table.c.access_edrpou,
                )
            )
            .where(
                and_(
                    listing_table.c.access_edrpou == self.recipient_edrpou,
                    company_table.c.id == self.owner_company_id,
                )
            )
            .group_by(listing_table.c.access_edrpou)
        ).alias()
        return await select_one(
            self.conn,
            (
                select([user_table.c.email, company_table.c.edrpou, company_table.c.name])
                .select_from(
                    mutual_connection_check.outerjoin(
                        company_table,
                        company_table.c.edrpou == mutual_connection_check.c.access_edrpou,
                    )
                    .outerjoin(role_table, role_table.c.company_id == company_table.c.id)
                    .outerjoin(user_table, user_table.c.id == role_table.c.user_id)
                    .join(signature_table, role_table.c.id == signature_table.c.role_id)
                )
                .where(
                    sa.and_(
                        company_table.c.is_legal.is_(True),
                        role_table.c.status == RoleStatus.active,
                    )
                )
                .order_by(desc(signature_table.c.date_created))
                .limit(1)
            ),
        )

    async def custom_filter(self) -> DBRow:
        """Custom logic to find appropriate recipient."""
        # is_admin > useless > 99% zakupki.user.role = admin
        return await select_one(
            self.conn,
            (
                select([contact_person_table.c.email])
                .select_from(
                    contact_person_table.join(
                        contact_table,
                        contact_table.c.id == contact_person_table.c.contact_id,
                    ).join(company_table, company_table.c.id == contact_table.c.company_id)
                )
                .where(
                    and_(
                        company_table.c.id == self.owner_company_id,
                        contact_table.c.edrpou == self.recipient_edrpou,
                    )
                )
                .group_by(contact_person_table.c.email)
                .order_by(
                    case(
                        [
                            (contact_person_table.c.email.ilike('tender'), 1),
                            (contact_person_table.c.email.ilike('sale'), 2),
                            (contact_person_table.c.email.ilike('zakaz'), 3),
                        ],
                        else_=4,
                    )
                )
                .limit(1)
            ),
        )

    async def select_contact_person(self) -> DBRow:
        """Get most recent email marked as ``main`` in contacts list."""
        return await select_one(
            self.conn,
            (
                select([contact_person_table.c.email])
                .select_from(
                    contact_person_table.join(
                        contact_table,
                        contact_table.c.id == contact_person_table.c.contact_id,
                    ).join(company_table, company_table.c.id == contact_table.c.company_id)
                )
                .where(
                    and_(
                        contact_table.c.edrpou == self.recipient_edrpou,
                        company_table.c.id == self.owner_company_id,
                    )
                )
            ),
        )

    async def suggest(self) -> str | None:
        """Suggest most appropriate email for document's recipient."""
        # PIPELINE
        # 1) get main_recipient email (if exists)
        main_recipient = await self.get_main_recipient()
        if main_recipient:
            return main_recipient.email

        accountant = await self.get_accountant_email()
        if accountant:
            return accountant.email

        # 2) find edrpou in mutual signatures - choose the most recent
        last_mutual_signer = await self.get_last_mutual_signer()
        if last_mutual_signer:
            email = last_mutual_signer.email

            # 2.1) Mark this email as main_recipient for this edrpou
            existing_contact = await db.select_contact_person(
                conn=self.conn,
                email=email,
                edrpou=self.recipient_edrpou,
                company_id=self.owner_company_id,
            )

            if existing_contact:
                await sync.db.mark_contact_as_main_recipient(
                    conn=self.conn,
                    email=email,
                    edrpou=self.recipient_edrpou,
                    company_id=self.owner_company_id,
                )
            else:
                await save_to_contacts(
                    conn=self.conn,
                    email=last_mutual_signer.email,
                    edrpou=last_mutual_signer.edrpou,
                    name=last_mutual_signer.name,
                    company_id=self.owner_company_id,
                    main_recipient=True,
                )
            return email

        # 3) use custom filter to choose recipient
        custom_filter = await self.custom_filter()
        if custom_filter:
            return custom_filter.email
            # if this email will sign the doc
            # -> will be marked as main_recipient next time

        # 4) Chose any contact person
        contact_person = await self.select_contact_person()
        if contact_person:
            return contact_person.email

        return None


async def update_missing_recipients(
    conn: DBConnection, edrpou: str, company_id: str, offset: int, limit: int
) -> tuple[int, int]:
    """
    Update missing recipient
    """
    pool = await sync.utils.select_edrpou_without_recipient(
        conn, edrpou_owner=edrpou, offset=offset, limit=limit
    )

    edrpous_without_emails = []
    for missing in pool:
        resolver = AutocompleteRecipient(
            conn,
            owner_edrpou=edrpou,
            owner_company_id=company_id,
            edrpou=missing.edrpou,
        )

        email_recipient = await resolver.suggest()
        if email_recipient:
            await update_email_recipient(
                conn=conn,
                edrpou=missing.edrpou,
                email=email_recipient,
                edrpou_owner=edrpou,
            )
        else:
            edrpous_without_emails.append(missing.edrpou)

    if edrpous_without_emails:
        logger.warning(
            'Not for all edrpous we can find email in database',
            extra={
                'edrpous': edrpous_without_emails,
                'company_id': company_id,
                'company_edrpou': edrpou,
                'offset': offset,
                'limit': limit,
            },
        )

    return len(pool), len(edrpous_without_emails)
