# Написання стилів

В проєкті використовуються модульні стилі. Кожен компонент має свій файл стилів. Називаємо файл
стилів так само, як і компонент, до якого він відноситься. Стилі пишемо на CSS, без використання
препроцесорів та css-in-js (якщо не має такої необхідності). Стилі компонентів ingested в компоненти
через CSS Modules та вставляються в html динамічно. Тому якщо компонент вантажиться асинхронно, то
стилі будуть вантажитися разом з компонентом, а не потраплять до загального bundle.

## Структура

```bash
cs/
  components/
    ComponentName/
      ComponentName.tsx
      ComponentName.css
      index.ts
```

## Імпорт в компонент

```tsx
import css from './ComponentName.css';

const ComponentName: React.FC = () => {
  return <div className={css.root}>...</div>;
};
```

## ClassName

Використовуємо [classnames](https://www.npmjs.com/package/classnames) для об'єднання класів:

```tsx
import cn from 'classnames';

import css from './ComponentName.css';
interface ComponentNameProps {
  isActive: boolean;
  classNames?: string;
}
const ComponentName: React.FC<ComponentNameProps> = (props) => {
  return (
    <div className={cn(css.root, { [css.active]: props.isActive }, props.classNames)}>...</div>
  );
};
```

## Підключення глобальних (не модульних) стилів

Глобальні стилі використовуються для стилізації елементів, які використовуються в багатьох
компонентах. Наприклад, стилізація тексту, змінні кольорів, шрифтів тощо.

Можна створити файл `ComponentName.global.css` та підключити його в компонент:

```tsx
import './ComponentName.global.css';
```

Якщо посилаємося на модуль з node_modules:

Можемо підключити через `@import` в CSS файлі:

```css
@import url('@ag-grid-community/styles/ag-grid.css');
```

Або через імпорт в компоненті:

```tsx
import '@ag-grid-community/styles/ag-grid.css';
```

## Змінні (CSS Variables)

Змінні використовуються для зберігання значень, які можна використовувати в CSS. Це дозволяє легко
змінювати значення в одному місці, а вони автоматично змінюються в усіх місцях використання.

```css
/* cs/styles/assets/_vars.css */
:root {
  --border-radius: 8px;
  /* ... */
}

/* ... */
.root {
  border-radius: var(--border-radius);
}
```

## Modern CSS syntax

За допомогою postcss та webpack можна застосовувати новий синтаксис CSS, який дозволяє
використовувати вкладенні селектори, псевдокласи, псевдоелементи та інші можливості (важливо
зберігати баланс з читабельністю коду).:

```css
.root {
  position: relative;
  background-color: transparent;

  &.active,
  &:hover {
    background-color: red;
  }

  .childClass {
    color: blue;
  }
}
```

## UI-KIT (@vchasno/ui-kit)

Використовуємо власний [UI-KIT](https://web-ui-kit.vchasno.com.ua/), який розробляється в процесі
роботи над проєктом. [@vchasno/ui-kit](https://www.npmjs.com/package/@vchasno/ui-kit) інтегрований
також в інші продукти Вчасно. Це дозволяє швидко розробляти новий функціонал та відповідати дизайну.

UI-KIT експортує стилі, які можна використовувати в додатку. Кастомізувати базові стилі можна через
змінні. Також через перевизначення стилів можна змінювати стилі компонентів.

```css
/* cs/styles/public/page.css */
@import url('@vchasno/ui-kit/dist/css/vchasno-ui.css');
@import url('../assets/_theme-override.global.css');
```

Повернутися до [змісту](../README.md)
