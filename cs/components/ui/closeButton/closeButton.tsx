import React from 'react';

import cn from 'classnames';

import Icon from '../icon/icon';

// icons
import SvgCancel from './images/cancel.svg';

// styles
import css from './closeButton.css';

interface CloseButtonProps {
    typeStuck?: boolean;
    typeRight?: boolean;
    withHover?: boolean;
    position?: React.CSSProperties['position'];
    size?: 'small';
    className?: string;
    onClose: (event: React.MouseEvent) => void;
}

const CloseButton: React.FC<CloseButtonProps> = (props) => {
    const totalClasses = cn(
        css.root,
        {
            [css[props.size!]]: props.size,
            [css[props.position!]]: props.position,
            [css.rootStuck]: props.typeStuck,
            [css.rootStuck]: props.typeStuck,
            [css.withHover]: props.withHover,
        },
        props.className,
    );

    return (
        <div
            className={totalClasses}
            onClick={props.onClose}
            data-qa="qa_close"
            data-tour-id="close-popup-icon"
        >
            <Icon glyph={SvgCancel} />
        </div>
    );
};

export default CloseButton;
