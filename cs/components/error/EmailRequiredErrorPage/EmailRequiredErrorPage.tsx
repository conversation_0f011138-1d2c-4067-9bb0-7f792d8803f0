import React from 'react';

import { Paragraph, Title } from '@vchasno/ui-kit';

import auth from 'services/auth';
import { t } from 'ttag';
import BackButton from 'ui/BackButton/BackButton';

import Footer from '../../Footer';
import MessengersButton from '../../MessengersButton/MessengersButton';
import Header from '../../header/Header';

import css from './EmailRequiredErrorPage.css';

const EmailRequiredErrorPage: React.FC = () => {
    const handleBack = async () => auth.logout({ redirectUrl: '/auth' });

    return (
        <div className={css.root}>
            <Header authButtons typeStatic />

            <main className={css.main}>
                <BackButton className={css.back} onClick={handleBack} />
                <Title>{t`Використовуйте імейл для реєстрації`}</Title>
                <Paragraph>
                    {t`Наразі Вчасно.ЕДО не підтримує реєстрацію за номером телефону.
          Поверніться назад і використовуйте імейл для реєстрації у Вчасно.ЕДО`}
                </Paragraph>
            </main>

            <MessengersButton />
            <Footer />
        </div>
    );
};

export default EmailRequiredErrorPage;
