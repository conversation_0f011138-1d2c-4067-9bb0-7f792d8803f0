.root{
    box-sizing: border-box;
    padding: 20px;
    margin: 20px 20px 5px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.iconContainer {
    display: flex;
    width: 64px;
    height: 64px;
    box-sizing: border-box;
    flex-shrink: 0;
    padding: 10px;
    background-color: var(--grey-bg);
    border-radius: var(--border-radius);
}

.iconContainer > img {
    display: block;
    margin: auto;
}

.button {
    flex-shrink: 0;
}

@media screen and (max-width: 768px) {
    .iconContainer {
        display: none;
    }

    .content {
        flex-wrap: wrap;
    }
}
