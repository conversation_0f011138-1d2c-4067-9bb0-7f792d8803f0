from pydantic import BaseModel, Field

from api.downloads.enums import ArchiveFilenamesMode
from app.lib import validators_pydantic as pv
from app.lib.types import DataDict


class DownloadArchivedDocumentsSchema(BaseModel):
    directory_ids: list[int] = Field(default_factory=list)
    document_ids: list[pv.UUID] = Field(default_factory=list)
    filenames_mode: ArchiveFilenamesMode | None = None

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)
