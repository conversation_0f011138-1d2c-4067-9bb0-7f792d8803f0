"""Add users.auth_phone column

Revision ID: f742f21def2b
Revises: 7219fb5616b2
Create Date: 2025-06-25 16:27:49.999367

"""
import logging
from alembic import op
import sqlalchemy as sa
import citext
from app.config import get_level



# revision identifiers, used by Alembic.
revision = 'f742f21def2b'
down_revision = '7219fb5616b2'
branch_labels = None
depends_on = None


def upgrade():
    # this operations should be safe to run on production
    op.add_column('users', sa.Column('auth_phone', sa.String(length=32), nullable=True))
    op.alter_column('users', 'email', existing_type=citext.CIText(), nullable=True)
    
    op.execute('COMMIT;')

    if get_level().is_prod:
        # Should be run manually by the DevOps team on production environment
        # because migration highly likely will be aborted by migration timeout
        logging.warning('Migration is skipped, run it manually on production')
        return
    
    op.execute("""
        CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS ix_users_auth_phone
        ON users (auth_phone);
    """)
    op.execute("""
        ALTER TABLE users
        ADD CONSTRAINT check_users_email_or_auth_phone
        CHECK (email IS NOT NULL OR auth_phone IS NOT NULL)
        NOT VALID;
    """)
    op.execute("""
        ALTER TABLE users
        VALIDATE CONSTRAINT check_users_email_or_auth_phone;
    """)


def downgrade():
    op.drop_index(op.f('ix_users_auth_phone'), table_name='users')
    op.alter_column('users', 'email', existing_type=citext.CIText(), nullable=False)
    op.drop_column('users', 'auth_phone')
