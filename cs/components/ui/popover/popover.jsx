import React from 'react';
import onClickOutsideMixin from 'react-onclickoutside';

import PropTypes from 'prop-types';

import Tooltip from '../tooltip/tooltip';

import css from './popover.css';

const onClickOutside =
    typeof onClickOutsideMixin === 'function'
        ? onClickOutsideMixin
        : onClickOutsideMixin.default;

class Popover extends React.Component {
    static propTypes = {
        isOpened: PropTypes.bool.isRequired,
        typeDropped: PropTypes.bool,
        alignTop: PropTypes.bool,
        position: PropTypes.string,
        pointerPosition: PropTypes.string,
        button: PropTypes.element.isRequired,
        onClose: PropTypes.func.isRequired,
        borderColor: PropTypes.string,
        hideArrow: PropTypes.bool,
        dataQa: PropTypes.string,
        tooltipClassName: PropTypes.string,
    };

    // this function is used by onClickOutside HOC
    handleClickOutside = () => {
        if (this.props.isOpened && this.props.onClose) {
            this.props.onClose();
        }
    };

    render() {
        return (
            <div data-qa={this.props.dataQa} className={css.root}>
                {this.props.button}

                {this.props.isOpened && (
                    <Tooltip
                        typeDropped={this.props.typeDropped}
                        fullWidthContent
                        typeBlurred
                        alignTop={this.props.alignTop}
                        position={this.props.position}
                        pointerPosition={this.props.pointerPosition}
                        borderColor={this.props.borderColor}
                        hideArow={this.props.hideArrow}
                        dataQa={this.props.dataQa}
                        className={this.props.tooltipClassName}
                    >
                        {this.props.children}
                    </Tooltip>
                )}
            </div>
        );
    }
}

export default onClickOutside(Popover);
