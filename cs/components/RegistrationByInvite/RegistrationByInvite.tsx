import React from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import ThirdPartyAuthButtons from 'components/ThirdPartyAuthButtons';
import PageTitle from 'components/pageTitle/pageTitle';
import { useRegistrationSource } from 'components/registration/hooks/useRegistrationSource';
import { useVerifiedInviteTokenData } from 'components/registration/hooks/useVerifiedInviteTokenData';
import { OFFERS_LINK } from 'lib/constants';
import { redirect } from 'lib/navigation';
import { getLocationQuery } from 'lib/url';
import auth from 'services/auth';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import HorizontalLineSeparator from 'ui/HorizontalLineSeparator/HorizontalLineSeparator';
import Button from 'ui/button/button';
import OutlinedInput from 'ui/input/OutlinedInput/OutlinedInput';

import { RegistrationByInviteFormFields } from './types';

import { registrationByInviteFormResolver } from './validation';

import css from './RegistrationByInvite.css';

const RegistrationByInvite: React.FC = () => {
    const [commonErrorMessage, setCommonErrorMessage] = React.useState<string>(
        '',
    );

    const location = useLocation();
    const searchQuery = getLocationQuery(location);
    const { email: invite_email } = useVerifiedInviteTokenData();

    const sourceLink = useRegistrationSource();
    const isTTNAppContext = sourceLink === 'ttn';
    const isShowThirdPartyAuthMethods = !isTTNAppContext;

    const {
        control,
        handleSubmit,
        formState,
    } = useForm<RegistrationByInviteFormFields>({
        resolver: registrationByInviteFormResolver,
        defaultValues: {
            email: invite_email || '',
            password: '',
        },
    });

    const onSubmit: SubmitHandler<RegistrationByInviteFormFields> = async ({
        password,
    }) => {
        try {
            setCommonErrorMessage('');

            await auth.shortRegister({
                password,
                token: searchQuery.token || '',
            });
            redirect(searchQuery.redirect || '/app');
        } catch (error) {
            setCommonErrorMessage(error.message);
        }
    };

    const handleAuthError = ({ reason }: { reason: string }) => {
        setCommonErrorMessage(reason);
    };

    return (
        <form className={css.form} onSubmit={handleSubmit(onSubmit)}>
            <PageTitle>{t`Вітаємо`}</PageTitle>
            <FlexBox
                className={css.container}
                justify="center"
                align="center"
                gap={0}
            >
                <div className={css.content}>
                    <FlexBox className={css.header} direction="column">
                        <PartnerLogos className={css.partnersLogos} />
                        <h1>
                            <span role="img" aria-label={t`Вітаємо`}>
                                👋
                            </span>{' '}
                            {t`Вітаємо`}
                        </h1>
                        <h4>{t`Вас запрошено у Вчасно! Авторизуйтеся за допомогою облікового запису або Email`}</h4>
                    </FlexBox>
                    <FlexBox direction="column" gap={20}>
                        <Controller
                            control={control}
                            name="email"
                            render={({ field, fieldState }) => (
                                <OutlinedInput
                                    type="text"
                                    value={field.value}
                                    onChange={field.onChange}
                                    error={fieldState.error?.message}
                                    disabled
                                />
                            )}
                        />
                        <FlexBox direction="column" gap={8}>
                            <Controller
                                control={control}
                                name="password"
                                render={({ field, fieldState }) => (
                                    <OutlinedInput
                                        type="password"
                                        label={t`Пароль`}
                                        value={field.value}
                                        onChange={(event) => {
                                            field.onChange(
                                                event.target.value.trim(),
                                            );
                                        }}
                                        autoFocus
                                        error={fieldState.error?.message}
                                    />
                                )}
                            />
                            {!formState.errors?.password?.message && (
                                <span
                                    className={css.passwordDescription}
                                >{t`Пароль повинен містити щонайменше 8 символів, хоча б одну цифру і один символ ( ! ' # $ % * - / : ; = ? )`}</span>
                            )}
                        </FlexBox>
                        {isShowThirdPartyAuthMethods && (
                            <>
                                <HorizontalLineSeparator
                                    className={css.horizontalLineSeparator}
                                >{t`або`}</HorizontalLineSeparator>
                                <ThirdPartyAuthButtons
                                    microsoftAuthOn
                                    googleAuthOn
                                    appleAuthOn
                                    onError={handleAuthError}
                                />
                            </>
                        )}
                    </FlexBox>
                    <span className={css.offers}>
                        {t`Реєструючись, я погоджуюсь з`}{' '}
                        <a
                            href={OFFERS_LINK}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            {t`публічними договорами (офертами) та політикою конфіденційності`}
                        </a>
                    </span>
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <Button
                        className={css.submitBtn}
                        type="submit"
                        theme="darkGray"
                        disabled={formState.isSubmitting}
                    >{t`Зареєструватися`}</Button>
                </div>
            </FlexBox>
        </form>
    );
};

export default RegistrationByInvite;
