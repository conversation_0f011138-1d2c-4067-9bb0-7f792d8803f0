import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    getAntivirusPopupAction,
    getAntivirusPopupDocVersionId,
    getAntivirusPopupDocs,
    getAntivirusPopupHref,
    getAntivirusPopupIsAsic,
    getAntivirusPopupIsOpen,
    getAntivirusPopupIsVisualisation,
} from 'selectors/antivirusPopup.selectors';
import { getCurrentCompanyAntivirusConfig } from 'selectors/app.selectors';

import documentListActionCreators from '../documentList/documentListActionCreators';
import actions from './AntivirusPopupActions';

import { getDocumentsDownloadOptions } from '../multiDownloadLink/helpers';
import { getIsUserHasAccessForDownload } from './helpers';

import { useDownloadDocumentHandler } from '../DownloadDocumentButton/useDownloadDocumentHandler';
import { useDownloadDocumentVersionHandler } from '../DownloadDocumentVersionButton/useDownloadDocumentVersionHandler';
import { ANTIVIRUS_POPUP_ACTIONS } from './constants';

export const useAntivirusPopup = () => {
    const dispatch = useDispatch();

    const downloadTimer = useRef<Nullable<ReturnType<typeof setTimeout>>>(null);
    const linkButtonRef = useRef<HTMLAnchorElement>(null);

    const docs = useSelector(getAntivirusPopupDocs);
    const antivirusConfig = useSelector(getCurrentCompanyAntivirusConfig);
    const isOpen = useSelector(getAntivirusPopupIsOpen);
    const action = useSelector(getAntivirusPopupAction);
    const isAsic = useSelector(getAntivirusPopupIsAsic);
    const href = useSelector(getAntivirusPopupHref);
    const docVersionId = useSelector(getAntivirusPopupDocVersionId);
    const isVisualisation = useSelector(getAntivirusPopupIsVisualisation);

    const doc = docs?.[0] || {};

    const {
        downloadDocument,
        onDownloadPdfClick,
        downloadDocumentP7S,
    } = useDownloadDocumentHandler(doc, docVersionId);
    const { downloadDocumentVersion } = useDownloadDocumentVersionHandler(
        doc,
        docVersionId,
    );

    const isUserHasAccessForDownload = getIsUserHasAccessForDownload(
        docs,
        antivirusConfig,
    );

    const onClose = () => {
        if (downloadTimer?.current) {
            clearTimeout(
                downloadTimer.current as ReturnType<typeof setTimeout>,
            );
        }
        dispatch({ type: actions.ANTIVIRUS_POPUP__HIDE });
    };

    const onClickDownload = () => {
        if (action === ANTIVIRUS_POPUP_ACTIONS.downloadDocument) {
            downloadDocument(isAsic);
        }
        if (action === ANTIVIRUS_POPUP_ACTIONS.downloadPdf) {
            onDownloadPdfClick();
        }
        if (action === ANTIVIRUS_POPUP_ACTIONS.downloadP7S) {
            downloadDocumentP7S();
        }
        if (action === ANTIVIRUS_POPUP_ACTIONS.downloadDocumentVersion) {
            downloadDocumentVersion();
        }
        if (action === ANTIVIRUS_POPUP_ACTIONS.downloadDocuments) {
            const docIds = docs.map((document) => document.id);
            const options = getDocumentsDownloadOptions(isVisualisation);

            dispatch(
                documentListActionCreators.onMultiDownloadDocuments({
                    docIds,
                    options,
                }),
            );
        }
        onClose();
    };

    useEffect(() => {
        if (!isOpen) {
            return undefined;
        }
        downloadTimer.current = isUserHasAccessForDownload
            ? setTimeout(() => {
                  if (linkButtonRef?.current) {
                      // use Ref because, window.open() not working in Safari
                      linkButtonRef.current.click();
                  }
                  onClickDownload();
              }, 5000)
            : null;

        return () => {
            if (downloadTimer?.current) {
                clearTimeout(
                    downloadTimer.current as ReturnType<typeof setTimeout>,
                );
            }
        };
    }, [isOpen]);

    return {
        href,
        isOpen,
        linkButtonRef,
        isUserHasAccessForDownload,
        onClose,
        onClickDownload,
    };
};
