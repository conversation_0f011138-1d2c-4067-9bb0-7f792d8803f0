import React from 'react';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import Title from '../ui/title/title';

import css from './dummyViewer.css';

const DummyViewer = (props) => {
    const { childNode, frame, imageUrl, url, title } = props;
    const renderDefaultChildren = () => (
        <p>
            {t`Але всі функції опрацювання доступні.`}
            <br />
            {t`Ви можете підписати або відхилити та додати коментар до документу.`}{' '}
            <br />
            {t`Також ви можете завантажити`}
            <a download href={url}>
                {' '}
                {t`оригінальний файл`}{' '}
            </a>
            {t`та відкрити його на вашому комп'ютері.`}
        </p>
    );

    return (
        <div className={frame ? css.root : css.rootNoFrame}>
            <div className={css.imageWrapper}>
                <img alt={t`Документ неможливо відобразити`} src={imageUrl} />
            </div>

            <Title>{title}</Title>
            <div>{childNode || renderDefaultChildren()}</div>
        </div>
    );
};

DummyViewer.defaultProps = {
    frame: false,
    title: t`Документ у форматі, котрий ми поки не відображаємо`,
    imageUrl: `${config.STATIC_HOST}/images/tanya_11_slim_1x.png`,
};

DummyViewer.propTypes = {
    childNode: PropTypes.node,
    frame: PropTypes.bool,
    title: PropTypes.node,
    imageUrl: PropTypes.string.isRequired,
    url: PropTypes.string,
};

export default DummyViewer;
