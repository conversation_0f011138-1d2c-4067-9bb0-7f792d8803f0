import logging
import typing as t
from collections import (
    Counter,
    defaultdict,
)
from collections.abc import Collection, Iterable, Sized
from http import HTTPStatus

import pydantic
import sqlalchemy as sa
from aiohttp import ClientResponse
from pydantic import StringConstraints
from yarl import URL

from api.errors import (
    FIELD_IS_REQUIRED,
    AccessDenied,
    Code,
    Error,
    InvalidRequest,
)
from api.validators import (
    Vendor,
    validate_api_vendor_config,
)
from app.auth.db import select_roles
from app.auth.enums import (
    AuthMethod,
    RoleStatus,
)
from app.auth.tables import (
    company_table,
    role_table,
    user_role_company_join,
    user_table,
)
from app.auth.types import (
    AuthUser,
    User,
)
from app.auth.utils import get_company_config
from app.auth.validators import (
    validate_user_permission,
)
from app.billing.enums import CompanyPermission
from app.billing.utils import get_billing_company_config
from app.billing.validators import validate_company_payer
from app.comments.constants import MAX_COMMENT_TEXT_LENGTH
from app.document_automation.types import DocumentAutomationAutomationTemplate
from app.document_automation.validators import (
    validate_template_exists,
)
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.const import VERSION_ALLOWED_EXTENSIONS
from app.documents.enums import (
    DocumentAccessLevel,
    DocumentSource,
    FirstSignBy,
)
from app.documents.types import (
    UpdateRecipient,
    UpdateRecipientsCtx,
    UploadDocumentAccessSettingsCtx,
)
from app.documents.utils import find_recipient_emails
from app.documents.validators import (
    DocumentAccessSettingsSchema,
    RecipientsSettingsSchema,
    validate_child_documents_count,
    validate_document_access,
    validate_document_access_settings_on_upload,
    validate_document_category,
    validate_document_exists,
    validate_hidden_emails_in_redis,
    validate_links_not_private,
    validate_recipient_params_for_internal,
    validate_recipients_params_for_bilateral,
    validate_recipients_params_for_multilateral,
    validate_title,
    validate_update_recipients_params,
)
from app.documents_fields.types import AddDocumentFieldSchema
from app.documents_fields.validators import (
    validate_document_parameters_on_upload,
)
from app.documents_required_fields.validators import (
    validate_required_fields,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow.types import (
    CreateFlowCtx,
    ReceiverSchema,
)
from app.flow.utils import to_flows_upload_settings
from app.flow.validators import (
    validate_receivers_count,
    validate_receivers_order,
)
from app.groups.tables import group_table
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.lib import (
    eusign_utils,
    validators,
)
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.enums import (
    SignatureFormat,
    Source,
)
from app.lib.helpers import (
    ZAKUPKI_EDRPOU,
    ensure_ascii,
    split_comma_separated_emails,
)
from app.lib.types import (
    AnyDict,
    DataDict,
)
from app.lib.validators import (
    int_or_none,
    is_malicious,
    validate_url,
)
from app.models import select_all
from app.profile.validators import (
    check_company_permission_by_config,
    validate_company_permission_by_config,
)
from app.reviews.types import ReviewsInfoCtx
from app.reviews.utils import convert_reviewers
from app.reviews.validators import ReviewerItemSchema, validate_review_requests_enabled
from app.services import services
from app.signatures.types import SignersInfoCtx
from app.tags.db import select_tags_by_document_templates_ids
from app.tags.validators import (
    TagName,
    validate_tags_on_document_upload,
)
from app.uploads.constants import MB
from app.uploads.enums import UploadByUrlVendor
from app.uploads.types import (
    ExpectedSignaturesCtx,
    File,
    ShareToGroup,
    UploadCommentCtx,
    UploadDocumentRecipientsLegacyOptions,
    UploadOptions,
)

logger = logging.getLogger(__name__)


class ParametersSettingsSchema(pydantic.BaseModel):
    parameters: list[AddDocumentFieldSchema]


class SignerSchema(pydantic.BaseModel):
    value: str = pydantic.Field(max_length=64)
    signer_type: SignersInfoCtx.EntityType


class SignerSettingsSchema(pydantic.BaseModel):
    signers: list[SignerSchema]


class UploadCommentSchema(pydantic.BaseModel):
    text: t.Annotated[
        str, StringConstraints(strip_whitespace=True, max_length=MAX_COMMENT_TEXT_LENGTH)
    ]
    is_internal: bool = False


class UploadSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU
    parent_id: pv.UUID | None = None
    email_owner: pv.Email | None = None

    share_to: list[pv.UUID] | None = None
    share_to_groups: list[pv.UUID] | None = None

    # deprecated fields {{
    # signer_roles overrides signer_emails
    # so if signer_roles is not null, signer_emails will be ignored
    signer_roles: pv.UniqueListWithOriginalOrder[pv.UUID] | None = None
    signer_emails: pv.UniqueListWithOriginalOrder[pv.Email] | None = None
    # }}
    signer_parameters: SignerSettingsSchema | None = None
    # this parameter is related to document signers
    parallel_signing: bool | None = None

    debug: bool
    source: DocumentSource | None = None
    vendor: Vendor | None = None
    vendor_id: str | None = pydantic.Field(None, max_length=255)

    # ===> [START] Recipient Fields: Specifies which companies should receive the document and
    # who within these companies should sign it. Next fields are discouraged from use in new code.
    # Instead, use `recipients` for a more structured approach to specifying recipients
    first_sign_by: FirstSignBy | None = None
    is_internal: bool | None = None
    is_multilateral: bool | None = None

    expected_owner_signatures: int | None = pydantic.Field(None, ge=0)
    expected_recipient_signatures: int | None = pydantic.Field(None, ge=0)
    recipient_edrpou: pv.EDRPOU | None = None
    recipient_emails: list[pv.Email] | None = pydantic.Field(default_factory=list)
    hide_recipient_emails: bool | None = None
    # deprecated: use `recipient_emails` or `recipients` instead
    email_recipient: list[pv.Email] | None = None

    # check "prepare_upload_query_parameters" how to pass this parameter in query parameters
    flows: list[ReceiverSchema] | None = pydantic.Field(None, min_length=1)

    # It's a modern way to define recipients of the documents, instead of using fields above
    # from the current section
    recipients: RecipientsSettingsSchema | None = None
    # <=== [END] Recipient Fields

    tags: pv.UniqueList[pv.UUID] | None = None
    new_tags: pv.UniqueList[TagName] | None = None

    is_required_review: bool | None = None
    # DEPRECATED: Left for backwards compatibility. Use reviewers instead
    reviewers_ids: list[pv.UUID] | None = None
    reviewers: list[ReviewerItemSchema] | None = None
    parallel_review: pv.BoolNullToTrue = True

    title: str | None = None
    doc_number: str | None = None
    date_document: pv.Datetime | None = None
    amount: int | None = pydantic.Field(None, ge=-100_000_000_000_00, le=100_000_000_000_00)

    # check "prepare_upload_query_parameters" how to pass this parameter in query parameters
    parameters: ParametersSettingsSchema | None = None

    signature_format: SignatureFormat | None = None

    # some documents from EDI may be chargeless
    is_chargeless: bool | None = None

    template_id: pv.UUID | None = None
    category: int | None = pydantic.Field(None, ge=0)

    is_versioned: bool | None = None
    comment: UploadCommentSchema | None = None

    # check "prepare_upload_query_parameters" how to pass this parameter in query parameters
    access_settings: DocumentAccessSettingsSchema | None = None

    # Selectors for public API
    show_recipients: pv.BoolNullToFalse = False
    with_access_settings: pv.BoolNullToFalse = False

    @pydantic.field_validator('email_recipient', mode='before')
    @classmethod
    def validate_email_recipient(cls, v: t.Any) -> list[str] | t.Any:
        if v is None:
            return v
        if isinstance(v, str):
            emails = split_comma_separated_emails(v.strip())
            return emails if emails else None
        return v


def build_unique_key(item: File) -> str:
    """Build unique key for each document.

    This is necessary to allow system mark duplicated files. File is duplicated
    if its unique key duplicates with other file in same upload.
    """
    key = f'{item.type_}-{item.number}'
    if item.first_sign_by == FirstSignBy.recipient.value:
        return f'{item.edrpou_recipient}-{item.edrpou_owner}-{key}'
    return f'{item.edrpou_owner}-{item.edrpou_recipient}-{key}'


def _validate_files_duplication(items: list[File]) -> None:
    # Check if there are any duplicates by number if uploaded files
    docs_map: defaultdict[str, list[File]] = defaultdict(list)
    for item in items:
        if item.number:
            docs_map[build_unique_key(item)].append(item)

    duplicate_keys = [key for key, value in docs_map.items() if len(value) > 1]

    # Check files with the same key using hash
    duplicate_data = {}
    for key in duplicate_keys:
        checksums = Counter(item.content_hash for item in docs_map[key])
        duplicate_data.update(
            {f'{key}-{checksum}': count for checksum, count in checksums.items() if count > 1}
        )

    if duplicate_data:
        raise Error(Code.duplicated_documents_request, details={'files': duplicate_data})


def _validate_files_count(items: list[File], options: UploadOptions) -> None:
    # Check if there are files to upload at all
    if not items:
        raise Error(Code.empty_upload_documents)

    counter = len(items)
    if counter > options.max_total_count:
        raise Error(
            Code.max_total_count,
            details={
                'counter': counter,
                'max_total_count': options.max_total_count,
            },
        )


def _validate_files_sizes(items: list[File], options: UploadOptions) -> None:
    size_counter = 0

    for item in items:
        file_size = item.file_size

        # Check if there are any files that larger then max file size
        if file_size > options.max_file_size:
            raise Error(
                Code.max_file_size,
                details={
                    'file_size': file_size / MB,
                    'file_name': item.file_name,
                    'max_file_size': options.max_file_size / MB,
                },
                status=HTTPStatus.REQUEST_ENTITY_TOO_LARGE,
            )

        if file_size == 0:
            raise Error(Code.empty_upload_file, details={'file_name': item.file_name})

        size_counter += file_size

    # Check that total size is less then max allowed
    if size_counter > options.max_total_size:
        raise Error(
            Code.max_total_size,
            details={
                'counter': size_counter / MB,
                'max_total_size': options.max_total_size / MB,
            },
            status=HTTPStatus.REQUEST_ENTITY_TOO_LARGE,
        )


def _validate_files_edrpou(items: list[File], options: UploadOptions) -> None:
    if not options.check_owner_edrpou:
        return

    edrpou = options.company_edrpou
    invalid_edrpou_data = {}
    for item in items:
        # Remember all files with wrong owner EDRPOU (if enabled)
        if item.edrpou_owner and item.edrpou_owner != edrpou:
            invalid_edrpou_data[item.file_name] = item.edrpou_owner

        recipient_emails = item.recipient_emails or []
        if not all(ensure_ascii(email) for email in recipient_emails):
            logger.warning(
                'Uploaded file has invalid email recipient',
                extra={
                    'document_id': item.id,
                    'edrpou_owner': item.edrpou_owner,
                    'edrpou_recipient': item.edrpou_recipient,
                    'emails_recipient': recipient_emails,
                },
            )

    # Check if there any files with invalid EDRPOU owner
    if invalid_edrpou_data:
        raise Error(Code.invalid_owner_edrpou, details={'files': invalid_edrpou_data})


async def _validate_files_signatures(items: list[File], options: UploadOptions) -> None:
    """Verify all documents with signatures"""

    company_edrpou = options.company_edrpou
    for item in items:
        if not item.signature:
            continue

        # Skip internal signed files, such as file was verified on unpacking step
        if item.signature.format_ == SignatureFormat.internal_wrapped:
            continue

        sign_info = await eusign_utils.verify(
            content=item.body,
            p7s=item.signature.file.body,
        )
        if company_edrpou != eusign_utils.get_edrpou(sign_info):
            raise Error(Code.invalid_sign_info_edrpou_v2)


async def _validate_files_required_fields(
    conn: DBConnection,
    items: list[File],
    options: UploadOptions,
) -> None:
    company_edrpou = options.company_edrpou

    for item in items:
        # Multilateral document can be sent right after upload
        flows_settings = item.flows_settings
        if flows_settings and flows_settings.should_send:
            edrpous = {flow.edrpou for flow in flows_settings.flows}
            edrpous.discard(company_edrpou)
            await validate_required_fields(
                conn=conn,
                edrpous=list(edrpous),
                document_title=item.title,
                document_number=item.number,
                document_date=item.date_document,
                document_amount=item.amount,
                category=int(c) if (c := item.category) else None,
            )


def _validate_files_flows_receivers(items: list[File]) -> None:
    for item in items:
        # Document doesn't contains flows - nothing to validate
        if not item.flows_settings:
            continue

        receivers = [flow.to_upload_dict() for flow in item.flows_settings.flows]

        validate_receivers_order(receivers)
        validate_receivers_count(receivers)

        # document with flows should be multilateral
        if not item.is_multilateral:
            raise InvalidRequest(details={'is_multilateral': item.is_multilateral})


async def validate_files(
    conn: DBConnection,
    data: list[File],
    options: UploadOptions,
) -> list[File]:
    """Validate list of files by various metrics.

    - Exclude all non-supported files
    - Number of files shouldn't exceed X
    - Total size of all files shouldn't exceed Y Mb
    - Size of any file shouldn't exceed Z Mb
    - There shouldn't be a files with duplicated numbers in request
    - Verify signatures
    """
    # List only allowed files to upload

    if get_flag(FeatureFlags.ENABLE_EXTRA_VALIDATION_FOR_FILENAME_ON_UPLOAD):
        items = [
            item
            for item in data
            if item.extension in options.allowed_extensions and not is_malicious(item.file_name)
        ]
    else:
        items = [item for item in data if item.extension in options.allowed_extensions]

    # Check number of allowed files
    _validate_files_count(items, options)
    _validate_files_sizes(items, options)
    _validate_files_duplication(items)
    _validate_files_edrpou(items, options)
    _validate_files_flows_receivers(items)
    await _validate_files_signatures(items, options)
    await _validate_files_required_fields(conn, items, options)

    return items


async def validate_remote_response(
    response: ClientResponse, details: DataDict | None = None, status: int = 200
) -> None:
    response_status = response.status
    if response.status != status:
        response_text = await response.text()
        raise Error(
            Code.invalid_remote_response,
            details=dict(
                details or {},
                expected_status=status,
                response_status=response_status,
                response_text=response_text,
            ),
        )


async def validate_share_to(
    conn: DBConnection,
    user: User | None,
    share_to: list[str] | None,
) -> set[str]:
    if not share_to or not user:
        return set()

    query = sa.select([role_table.c.id]).where(
        sa.and_(
            role_table.c.id.in_(share_to),
            role_table.c.id != user.role_id,
            role_table.c.company_id == user.company_id,
            role_table.c.status == RoleStatus.active,
        )
    )

    roles = await select_all(conn, query)
    return {role.id for role in roles}


async def validate_share_to_groups(
    conn: DBConnection,
    user: User | None,
    share_to_groups: list[str] | None,
) -> list[ShareToGroup]:
    if not share_to_groups or not user:
        return []

    query = sa.select([group_table.c.id]).where(
        sa.and_(
            group_table.c.id.in_(share_to_groups),
            group_table.c.company_id == user.company_id,
            group_table.c.date_deleted.is_(None),
        )
    )
    groups_members = await get_group_members_by_group_ids(
        conn=conn,
        group_ids={group.id for group in await select_all(conn, query)},
    )

    return [
        ShareToGroup(id=group_id, role_ids=role_ids)
        for group_id, role_ids in groups_members.items()
    ]


async def validate_signer_emails(
    conn: DBConnection,
    user: AuthUser | User,
    signer_emails: list[str] | None,
) -> tuple[list[str], dict[str, str]]:
    """
    Validate if given signer emails exist in DB and return their role IDs and email-role map.
    """
    if not signer_emails or not user:
        return [], {}

    query = (
        sa.select([user_table.c.email, role_table.c.id])
        .select_from(user_role_company_join)
        .where(
            sa.and_(
                user_table.c.email.in_(signer_emails),
                company_table.c.edrpou == user.company_edrpou,
                company_table.c.is_legal.is_(True),
                role_table.c.status == RoleStatus.active,
            )
        )
    )
    result = await select_all(conn, query)
    email_role_map = {r.email.lower(): r.id for r in result}

    role_ids: list[str] = []
    for email in signer_emails:
        role_id = email_role_map.get(email.lower())
        if role_id:
            role_ids.append(role_id)

    return role_ids, email_role_map


async def validate_signer_roles(
    conn: DBConnection,
    user: User | None,
    signer_role_ids: list[str] | None,
) -> list[str]:
    if not signer_role_ids or not user:
        return []

    query = sa.select([role_table.c.id]).where(
        sa.and_(
            role_table.c.id.in_(signer_role_ids),
            role_table.c.company_id == user.company_id,
            role_table.c.status == RoleStatus.active,
        )
    )
    roles = await select_all(conn, query)
    role_ids = {role.id for role in roles}

    return [role_id for role_id in signer_role_ids if role_id in role_ids]


async def validate_automation_template_on_upload(
    conn: DBConnection,
    user: User | None,
    valid_data: UploadSchema,
    request_source: Source,
) -> DocumentAutomationAutomationTemplate | None:
    """
    1. Validates what given template exists in DB and returns it.
    2. Apply template to document if it comes from public API

    **NOTICE: Function has side effect -> may override incoming valid_data**

    INFO: check also "prepare_update_data_from_template" that is used to build parameters
    for updating document with template settings
    """
    template_id = valid_data.template_id
    if not template_id:
        return None

    if not user:
        raise InvalidRequest(reason=_('Для призначення сценарію необхідно авторизуватися'))

    template = await validate_template_exists(
        conn,
        template_id=template_id,
        company_id=user.company_id,
    )

    # Apply template to document if it comes from public API
    # because for web it's applied on the frontend side
    # **NOTICE: this override data from request**
    if template and request_source == Source.api_public:
        tags = await select_tags_by_document_templates_ids(conn, [template.id])

        if template.set_signers and (signers_settings := template.signers_settings):
            valid_data.parallel_signing = signers_settings.is_parallel
            valid_data.signer_parameters = SignerSettingsSchema(
                signers=[
                    SignerSchema.model_validate({'value': signer.id, 'signer_type': signer.type})
                    for signer in signers_settings.signers
                ]
            )

        if template.set_review and (reviewers_settings := template.review_settings):
            reviewers = reviewers_settings.reviewers
            if not reviewers:
                raise InvalidRequest(reason=_('reviewers_ids чи reviewers має бути заповнено'))

            valid_data.reviewers = [
                ReviewerItemSchema.model_validate({'id': reviewer.id, 'type': reviewer.type})
                for reviewer in reviewers
            ]
            valid_data.is_required_review = reviewers_settings.is_required
            valid_data.parallel_review = reviewers_settings.is_parallel

        if template.set_viewers and (viewers_settings := template.viewers_settings):
            valid_data.share_to = viewers_settings.viewers_ids

        if (
            template.set_viewers
            and template.viewers_settings
            and template.viewers_settings.viewers_group_ids
        ):
            valid_data.share_to_groups = template.viewers_settings.viewers_group_ids

        if template.set_fields and (fields_settings := template.fields_settings):
            valid_data.parameters = ParametersSettingsSchema(
                parameters=[
                    AddDocumentFieldSchema(
                        field_id=field.field_id,
                        is_required=field.is_required,
                        value=field.value,
                    )
                    for field in fields_settings.fields
                ]
            )

        if tags:
            valid_data.tags = [tag.id for tag in tags]

    return template


def _prepare_document_category(category: int | None) -> int | None:
    if category == PublicDocumentCategory.other.value:
        return None
    return category


async def _validate_signers_parameters(
    conn: DBConnection,
    *,
    user: User | None,
    signers_parameters: SignerSettingsSchema | None,
) -> list[SignersInfoCtx]:
    if not signers_parameters:
        return []

    if not user:
        logger.warning(
            'Signers parameters validation skipped, user is None',
            extra={'signers_parameters': signers_parameters.model_dump(mode='json')},
        )
        return []

    entities = signers_parameters.signers

    def check_signer(expected_ids: Collection[str], actual_ids: Collection[str]) -> None:
        # Check if all signers were found in DB
        if (len(expected_ids) != len(actual_ids)) or (set(expected_ids) - set(actual_ids)):
            logger.info(
                'Signers not found',
                extra={
                    'expected_ids': expected_ids,
                    'actual_ids': actual_ids,
                },
            )
            raise InvalidRequest(signers=_('Неможливо знайти підписанта'))

    signers: dict[SignersInfoCtx.EntityType, list[str]] = {
        SignersInfoCtx.EntityType.role: [],
        SignersInfoCtx.EntityType.email: [],
        SignersInfoCtx.EntityType.group: [],
    }
    for signer in entities:
        signer_type = signer.signer_type
        if signer_type in signers:
            signers[signer_type].append(signer.value)
        else:
            raise NotImplementedError

    if signers_roles := signers[SignersInfoCtx.EntityType.role]:
        # Select signers by roles IDs
        signers_ids = await validate_signer_roles(
            conn=conn,
            user=user,
            signer_role_ids=signers_roles,
        )
        check_signer(expected_ids=signers_ids, actual_ids=signers_roles)

    # { email: role_id } map (!!! email is in lowercase)
    email_role_map: dict[str, str] = {}
    if signers_emails := signers[SignersInfoCtx.EntityType.email]:
        # Select signers by emails
        signers_ids, email_role_map = await validate_signer_emails(
            conn=conn,
            user=user,
            signer_emails=signers_emails,
        )
        check_signer(expected_ids=signers_ids, actual_ids=email_role_map.values())

    # { group_id: [role_id, ...] }
    group_role_map: defaultdict[str, list[str]] = defaultdict(list)
    if signer_groups := signers[SignersInfoCtx.EntityType.group]:
        group_role_map = await get_group_members_by_group_ids(
            conn=conn,
            group_ids=signer_groups,
            company_id=user.company_id,
        )
        check_signer(expected_ids=signer_groups, actual_ids=group_role_map.keys())

    signers_infos: list[SignersInfoCtx] = []
    # iterate over all signers in original order to properly set up an ordered signing process
    for signer in entities:
        if signer.signer_type == SignersInfoCtx.EntityType.role:
            role_id = signer.value
            signers_infos.append(
                SignersInfoCtx(
                    entity=SignersInfoCtx.EntityType.role,
                    role_id=role_id,
                    group_id=None,
                    group_role_ids=[],
                )
            )
        elif signer.signer_type == SignersInfoCtx.EntityType.email:
            email = signer.value
            role_id = email_role_map[email.lower()]
            signers_infos.append(
                SignersInfoCtx(
                    entity=SignersInfoCtx.EntityType.role,
                    role_id=role_id,
                    group_id=None,
                    group_role_ids=[],
                )
            )
        elif signer.signer_type == SignersInfoCtx.EntityType.group:
            group_id = signer.value
            signers_infos.append(
                SignersInfoCtx(
                    entity=SignersInfoCtx.EntityType.group,
                    role_id=None,
                    group_id=group_id,
                    group_role_ids=group_role_map[group_id],
                )
            )

    return signers_infos


async def _validate_document_signers_on_upload(
    conn: DBConnection,
    *,
    signers_roles: list[str] | None,
    signers_emails: list[str] | None,
    signers_parameters: SignerSettingsSchema | None,
    user: User | None,
) -> list[SignersInfoCtx]:
    """
    Validate that given signers exist in DB and return their IDs.
    Priority: signers_parameters > signers_roles > signers_emails
    """

    if not user:
        logger.warning(
            'Signers validation skipped, user is None',
            extra={
                'signers_roles': signers_roles,
                'signers_emails': signers_emails,
                'signers_parameters': (
                    signers_parameters.model_dump(mode='json') if signers_parameters else None
                ),
            },
        )
        return []

    if signers_parameters:
        return await _validate_signers_parameters(
            conn=conn,
            user=user,
            signers_parameters=signers_parameters,
        )

    # DEPRECATED: validate signers by parameters
    # FALLBACK: validate signers by roles or emails
    raw_signers: list[str]
    signers_ids: list[str]
    if signers_roles:
        # Select signers by roles IDs
        raw_signers = signers_roles
        signers_ids = await validate_signer_roles(
            conn=conn,
            user=user,
            signer_role_ids=raw_signers,
        )
    elif signers_emails:
        # Select signers by emails IDs
        raw_signers = signers_emails
        signers_ids, __ = await validate_signer_emails(
            conn=conn,
            user=user,
            signer_emails=raw_signers,
        )
    else:
        # Do not validate signers if they are not provided
        return []

    # Check if all signers were found in DB
    if len(signers_ids) != len(raw_signers):
        raise InvalidRequest(signers=_('Неможливо знайти підписанта'))

    return [
        SignersInfoCtx(
            entity=SignersInfoCtx.EntityType.role,
            role_id=signer_id,
            group_id=None,
            group_role_ids=[],
        )
        for signer_id in signers_ids
    ]


def _validate_expected_signatures_on_upload(
    *,
    is_multilateral: bool,
    expected_owner_signatures: int | None,
    expected_recipient_signatures: int | None,
    signers: list[SignersInfoCtx],
) -> ExpectedSignaturesCtx:
    """
    Validate expected signatures count and return expected signatures' context.
    """

    # For multilateral documents those fields is not used, so we can ignore them
    if is_multilateral:
        return ExpectedSignaturesCtx(
            owner=None,
            recipient=None,
        )

    # If we have signers IDs, we can't calculate expected owner signatures by ourselves
    if signers:
        return ExpectedSignaturesCtx(
            owner=len(signers),
            recipient=expected_recipient_signatures,
        )

    # Otherwise, we expected that user will provide valid expected signatures count
    if expected_owner_signatures == 0 and expected_recipient_signatures == 0:
        raise InvalidRequest(expected_owner_signatures=_('Невалідна кількість підписантів'))

    return ExpectedSignaturesCtx(
        owner=expected_owner_signatures,
        recipient=expected_recipient_signatures,
    )


async def validate_parent_id_on_upload(
    conn: DBConnection,
    parent_id: str | None,
    user: User | None,
    company_edrpou: str,
    access_settings: UploadDocumentAccessSettingsCtx | None,
) -> str | None:
    if not parent_id or not user:
        return None

    await validate_document_exists(conn, data={'document_id': parent_id})
    await validate_document_access(conn, user, document_id=parent_id)

    if access_settings and access_settings.level == DocumentAccessLevel.private:
        raise Error(
            code=Code.invalid_document_link,
            reason=_(
                'Наразі не підтримується звʼязування для приватних документів. '
                'Змініть налаштування приватності документів та спробуйте ще раз'
            ),
        )
    await validate_links_not_private(
        conn=conn,
        documents_ids=[parent_id],
        company_edrpou=company_edrpou,
    )

    await validate_child_documents_count(
        conn=conn,
        parent_id=parent_id,
        company_edrpou=company_edrpou,
    )

    return parent_id


async def validate_payer_on_upload(
    conn: DBConnection,
    user: User | None,
    is_chargeless: bool,
) -> str | None:
    if is_chargeless or not user:
        return None

    return await validate_company_payer(conn=conn, company_id=user.company_id)


def _get_custom_document_source_by_edrpou(edrpou: str) -> DocumentSource | None:
    custom_document_sources = {
        ZAKUPKI_EDRPOU: DocumentSource.zakupki,
    }
    return custom_document_sources.get(edrpou)


async def validate_upload_params(  # noqa: C901
    conn: DBConnection,
    data: DataDict,
    company_edrpou: str,
    *,
    auth_method: AuthMethod | None,
    request_source: Source,
    document_source: DocumentSource | None = None,
    ensure_vendor: bool = False,
    header_vendor: str | None = None,
    user: User | None = None,
) -> UploadOptions:
    """Ensure options for upload files are proper one.

    If they are, prepend company config for files uploads there to provide
    options named tuple that is widely used in uploads utils.

    Signers can be role id or user email.
    """

    if user:
        validate_user_permission(user, {'can_upload_document'})

    initial_raw_data = dict(
        data,
        edrpou=company_edrpou,
        vendor=header_vendor or (user.token_vendor if user else None),
        number=data.get('doc_number'),
    )
    valid_data = validators.validate_pydantic(
        UploadSchema,
        initial_raw_data,
    )

    validate_title(valid_data.title)

    # Prioritize validating "recipients" because it updates valid_data keys
    # such as "is_internal", "is_multilateral", "flows", "expected_owner_signatures",
    # and "expected_recipient_signatures", derived from the "recipients" parameter.
    # WARNING: this function updates fields in valid_data
    recipients_ctx: UpdateRecipientsCtx | None = None
    if recipients_data := valid_data.recipients:
        document_options = await validate_upload_recipients_options(
            data=recipients_data,
            company_edrpou=company_edrpou,
            role_id=user.role_id if user else None,
        )

        if document_options.flows and document_source == DocumentSource.hrs and user:
            await validate_hrs_flow(
                conn=conn,
                flows=document_options.flows,
                user=user,
            )

        # This variable is set only when "recipients" parameter is provided
        # and is a new way to provide recipient settings, instead of using
        # old fields like "is_internal", "is_multilateral", "flows", etc.
        # You can use it for introducing new recipient settings in the future.
        recipients_ctx = document_options.recipients_ctx

        initial_raw_data.update(document_options.to_legacy_dict())
        valid_data = validators.validate_pydantic(
            UploadSchema,
            initial_raw_data,
        )

    # WARNING: this function updates fields in valid_data
    template = await validate_automation_template_on_upload(
        conn=conn,
        user=user,
        valid_data=valid_data,
        request_source=request_source,
    )

    if ensure_vendor and not valid_data.vendor:
        raise InvalidRequest(vendor=FIELD_IS_REQUIRED)

    payer = await validate_payer_on_upload(
        conn=conn,
        user=user,
        is_chargeless=valid_data.is_chargeless or False,
    )

    expected_owner_signatures = valid_data.expected_owner_signatures
    expected_recipient_signatures = valid_data.expected_recipient_signatures
    first_sign_by = valid_data.first_sign_by

    share_to = await validate_share_to(
        conn=conn,
        user=user,
        share_to=valid_data.share_to,
    )
    share_to_groups = await validate_share_to_groups(
        conn=conn,
        user=user,
        share_to_groups=valid_data.share_to_groups,
    )

    if expected_owner_signatures == 0:
        first_sign_by = FirstSignBy.recipient

    valid_signers = await _validate_document_signers_on_upload(
        conn=conn,
        signers_roles=valid_data.signer_roles,
        signers_emails=valid_data.signer_emails,
        signers_parameters=valid_data.signer_parameters,
        user=user,
    )
    parallel_review = valid_data.parallel_review

    reviewers_schema = valid_data.reviewers
    # Only for backwards compatibility
    if not reviewers_schema and valid_data.reviewers_ids:
        role = ReviewsInfoCtx.EntityType.role
        reviewers_schema = [
            ReviewerItemSchema(id=id_, type=role) for id_ in valid_data.reviewers_ids
        ]

    reviewer_infos = []
    if reviewers_schema and user:
        await validate_review_requests_enabled(
            conn=conn,
            company_id=user.company_id,
            company_edrpou=company_edrpou,
        )
        reviewer_infos = await _validate_reviewers_on_upload(
            conn=conn,
            reviewers_schema=reviewers_schema,
            user=user,
        )

    is_internal = valid_data.is_internal or False
    is_multilateral = valid_data.is_multilateral or False
    is_required_review = valid_data.is_required_review or False

    company_config = await get_company_config(conn, company_edrpou=company_edrpou)
    billing_config = await get_billing_company_config(conn, company_edrpou=company_edrpou)

    # HRS internal documents are allowed to be uploaded regardless of company billing config.
    if is_internal and document_source != DocumentSource.hrs:
        validate_company_permission_by_config(
            config=billing_config,
            permission=CompanyPermission.internal_documents,
        )

    parallel_signing = valid_data.parallel_signing
    if parallel_signing is None:
        parallel_signing = True

    if is_internal and is_multilateral:
        raise InvalidRequest(
            reason=_('Документ не може бути одночасно і внутрішнім і багатостороннім'),
        )

    expected_signatures = _validate_expected_signatures_on_upload(
        is_multilateral=is_multilateral,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
        signers=valid_signers,
    )

    uploads_config = company_config.uploads
    allowed_hosts = uploads_config.allow_download_documents_from_hosts

    # TODO: need to move allowed extensions out from companies configs
    allowed_extensions = set(uploads_config.allowed_extensions)
    allowed_extensions.add('.p7s')

    tags_ids = valid_data.tags or []
    tags_names = valid_data.new_tags or []
    create_tags_ctx = await validate_tags_on_document_upload(
        conn=conn,
        tags_ids=tags_ids,
        tags_names=tags_names,
        user=user,
    )
    tags_ids = create_tags_ctx.tags_ids
    tags_names = create_tags_ctx.tags_names

    # WARNING: this function updates fields in valid_data
    valid_data = await validate_recipients_on_upload(
        data=valid_data,
        user=user,
        company_edrpou=company_edrpou,
    )

    parameters: list[AddDocumentFieldSchema] | None = None
    parameters_settings = valid_data.parameters
    if parameters_settings is not None:
        parameters = await validate_document_parameters_on_upload(
            conn=conn,
            user=user,
            items=parameters_settings.parameters,
            auth_method=auth_method,
        )

    access_settings: UploadDocumentAccessSettingsCtx | None = None
    if valid_data.access_settings:
        access_settings = await validate_document_access_settings_on_upload(
            settings=valid_data.access_settings,
        )

    signature_format = valid_data.signature_format

    flows = valid_data.flows
    # When "recipients" parameter is present, it means that we are using a new page/popup in Web
    # UI for uploading documents, which expects that documents will be sent to recipients
    # manually after uploading. For other APIs, it means that after updating to the "new" way of
    # passing recipients, they should add logic to send documents to recipients, or we should add
    # a new API parameter to do it automatically.
    should_send_to_recipients: bool = recipients_ctx is None
    flow_settings = to_flows_upload_settings(
        items=flows,
        company_edrpou=company_edrpou,
        should_send=should_send_to_recipients,
    )

    company_api_enabled = check_company_permission_by_config(
        config=billing_config,
        permission=CompanyPermission.api,
    )
    category = _prepare_document_category(valid_data.category)

    if category is not None:
        await validate_document_category(
            conn=conn,
            category_id=category,
            company_edrpou=company_edrpou,
            is_document_internal=is_internal,
        )

    if custom_source := _get_custom_document_source_by_edrpou(company_edrpou):
        valid_data.source = custom_source

    is_versioned, allowed_extensions, first_sign_by = validate_versioned_documents(
        is_versioned=valid_data.is_versioned,
        first_sign_by=first_sign_by,
        is_multilateral=is_multilateral,
        is_internal=is_internal,
        parallel_signing=parallel_signing,
        allowed_extensions=allowed_extensions,
    )

    comment: UploadCommentCtx | None = None
    if (comment_schema := valid_data.comment) and comment_schema.text:
        comment = UploadCommentCtx(
            text=comment_schema.text,
            is_internal=comment_schema.is_internal,
        )

    parent_id = await validate_parent_id_on_upload(
        conn=conn,
        parent_id=valid_data.parent_id,
        user=user,
        company_edrpou=company_edrpou,
        access_settings=access_settings,
    )

    return UploadOptions(
        payer_id=payer,
        parent_id=parent_id,
        user=user,
        company_edrpou=company_edrpou,
        email_owner=valid_data.email_owner or (user.email if user else None),
        recipient_edrpou=valid_data.recipient_edrpou,
        recipient_emails=valid_data.recipient_emails or [],
        hide_recipient_emails=valid_data.hide_recipient_emails or False,
        first_sign_by=first_sign_by.value if first_sign_by else None,
        is_internal=is_internal,
        is_multilateral=is_multilateral,
        is_required_review=is_required_review,
        expected_owner_signatures=expected_signatures.owner,
        expected_recipient_signatures=expected_signatures.recipient,
        source=valid_data.source.value if valid_data.source else None,
        vendor=valid_data.vendor.value if valid_data.vendor else None,
        vendor_id=valid_data.vendor_id,
        share_to=share_to,
        share_to_groups=share_to_groups,
        signers=valid_signers,
        parallel_signing=parallel_signing,
        tags=tags_ids,
        new_tags=tags_names,
        reviewers=reviewer_infos,
        parallel_review=parallel_review,
        title=valid_data.title,
        doc_number=valid_data.doc_number,
        date_document=valid_data.date_document,
        amount=valid_data.amount,
        allow_download_documents_from_hosts=(set(allowed_hosts) if allowed_hosts else None),
        allow_substitute_email_recipient=uploads_config.allow_substitute_email_recipient,
        allowed_extensions=allowed_extensions,
        archive_extensions=set(uploads_config.archive_extensions),
        check_owner_edrpou=uploads_config.check_owner_edrpou,
        max_file_size=uploads_config.max_file_size * MB,
        max_total_size=uploads_config.max_total_size * MB,
        max_total_count=uploads_config.max_total_count,
        replace_owner_edrpou=uploads_config.replace_owner_edrpou,
        parameters=parameters,
        flows_settings=flow_settings,
        signature_format=signature_format,
        company_api_enabled=company_api_enabled,
        template=template,
        category=category,
        is_versioned=is_versioned,
        comment=comment,
        access_settings=access_settings,
        show_recipients=valid_data.show_recipients,
        with_access_settings=valid_data.with_access_settings,
    )


async def validate_hrs_flow(
    conn: DBConnection,
    flows: list[CreateFlowCtx],
    user: User,
) -> None:
    edrpous_to_fill = [flow.edrpou for flow in flows if not flow.recipient_emails]

    if not edrpous_to_fill:
        return

    recipients = await find_recipient_emails(
        conn=conn,
        recipients_edrpous=edrpous_to_fill,
        user=user,
    )

    for flow in flows:
        if not flow.recipient_emails and flow.edrpou in recipients:
            flow.recipient_emails = recipients[flow.edrpou].emails


async def validate_recipients_on_upload(
    data: UploadSchema,
    company_edrpou: str,
    user: User | None,
) -> UploadSchema:
    recipient_edrpou = data.recipient_edrpou
    recipient_emails = data.recipient_emails or []
    hide_recipient_emails = data.hide_recipient_emails or False

    if not recipient_emails and data.email_recipient:
        recipient_emails = data.email_recipient or []
        logger.warning(
            msg='Field email_recipient is deprecated on uploading',
            extra={
                'company_edrpou': company_edrpou,
                'uploader_email': user and user.email,
                'recipient_emails': data.recipient_emails,
                'email_recipient': data.email_recipient,
            },
        )

    # Verify the existence of hidden recipient emails
    # NOTE: recipients_emails can be filled by previous validation, so check only
    # if it necessary
    if recipient_edrpou and hide_recipient_emails and not recipient_emails:
        if user:
            hidden_emails_mapping = await validate_hidden_emails_in_redis(
                role_id=user.role_id,
                recipient_edrpous=[recipient_edrpou],
            )
            recipient_emails = hidden_emails_mapping[recipient_edrpou]
        else:
            reason = _('Неможливо призначити прихований емейл для запиту без користувача')
            raise InvalidRequest(reason=reason)

    # Make sure that recipient edrpou is not same as company edrpou
    if recipient_edrpou and recipient_edrpou == company_edrpou:
        raise InvalidRequest(
            recipient_edrpou=_('Неможливо вказати власний ЄДРПОУ як одержувача'),
        )

    data.recipient_edrpou = recipient_edrpou
    data.recipient_emails = recipient_emails
    data.hide_recipient_emails = hide_recipient_emails
    return data


async def validate_upload_from_url(url: str, options: UploadOptions) -> URL | None:
    # Ignore non-urls from request
    valid_url = validate_url(url)
    if valid_url is None:
        return None

    # Allowed vendor
    async with services.db.acquire() as conn:
        await validate_api_vendor_config(
            conn, edrpou=options.company_edrpou, vendor_enum=UploadByUrlVendor
        )

    # Enabled option
    details = {'url': valid_url.human_repr()}
    if options.allow_download_documents_from_hosts is None:
        raise Error(
            Code.disabled_option,
            reason=_('Завантаження файлу, вказуючи URL.'),
            details=details,
        )

    # Valid host
    if valid_url.host not in options.allow_download_documents_from_hosts:
        raise Error(Code.invalid_download_host, details=details)

    # Valid response
    async with services.http_client.head(valid_url) as response:
        await validate_remote_response(response, details)

    validate_file_size(
        max_file_size=options.max_file_size,
        content_length=response.headers['Content-Length'],
        error_details=details,
    )
    validate_file_extension(
        filename=valid_url.name,
        allowed_extensions=options.allowed_extensions,
        error_details=details,
    )

    return valid_url


def validate_file_size(
    max_file_size: int,
    content_length: int | str | None,
    error_details: AnyDict,
) -> None:
    content_length = int_or_none(content_length)

    if content_length is None:
        return
    if not content_length:
        raise Error(Code.empty_upload_file)
    if content_length > max_file_size:
        raise Error(
            Code.max_file_size,
            details=dict(
                error_details,
                content_length=content_length,
                max_file_size=max_file_size / MB,
            ),
            status=413,
        )


def validate_file_extension(
    filename: str,
    allowed_extensions: Iterable[str],
    error_details: DataDict,
) -> str:
    # Avoid circular import
    from .utils import prepare_file_details_from_filename

    details = prepare_file_details_from_filename(filename)

    if details.extension not in allowed_extensions:
        raise Error(
            Code.invalid_file_extension,
            details=dict(
                error_details,
                title=details.title,
                extension=details.extension,
                name=details.name,
                allowed_extensions=list(allowed_extensions),
            ),
        )

    return details.extension


async def validate_upload_permission(
    user: AuthUser | User,
) -> None:
    """
    Validate that user is able to upload document according to its role.
    """

    if not user.can_upload_document:
        raise AccessDenied()


def validate_versioned_documents(
    is_versioned: bool | None,
    first_sign_by: FirstSignBy | None,
    is_multilateral: bool,
    is_internal: bool,
    parallel_signing: bool,
    allowed_extensions: set[str],
) -> tuple[bool, set[str], FirstSignBy | None]:
    """
    Extra validation for versioned documents.

    Returns tuple of (
     is_versioned,
     allowed_extensions,
     first_sign_by (set to recipient if versioned),
    )
    """

    if not is_versioned:
        return False, allowed_extensions, first_sign_by

    # TODO[version]: for now all companies can upload versioned documents
    #  after finding out how this feature will be used by companies.
    #  Use this ref (version_rate_limit) to find all places that should be uncommented.

    # check if company's rate allows versioned documents
    # version_config = company_config.get(CompanyLimit.max_versions_count, 0)
    # if company_config is not None and version_config == 0:
    #     raise AccessDenied(reason=DEFAULT_PERMISSION_ERROR_MESSAGE)

    if first_sign_by == FirstSignBy.owner:
        reason = _('Версійний документ може бути тільки з першим підписом від контрагента')
        raise InvalidRequest(reason=reason)

    if is_multilateral and not parallel_signing:
        reason = _('Версійний документ може бути тільки з паралельним підписуванням')
        raise InvalidRequest(reason=reason)

    if not get_flag(FeatureFlags.DOCUMENT_VERSIONS_UPLOAD_ANY):
        allowed_extensions = set(VERSION_ALLOWED_EXTENSIONS)

    # The recipient should sign bilateral versioned documents first (but it's not a strict rule)
    # For internal and multilateral documents, "first_sign_by" is not used, so we use provided value
    if not is_internal and not is_multilateral:
        first_sign_by = FirstSignBy.recipient

    return (
        is_versioned,
        allowed_extensions,
        first_sign_by,
    )


def _validate_reviewers_count(raw_reviewers: Sized, reviewers: Sized) -> None:
    """Check if all reviewers/reviewers_group were found in DB"""
    if len(raw_reviewers) != len(reviewers):
        raise InvalidRequest(reviewers=_('Неможливо знайти погоджувача'))


async def _validate_reviewers_on_upload(
    conn: DBConnection,
    reviewers_schema: list[ReviewerItemSchema],
    user: AuthUser | User,
) -> list[ReviewsInfoCtx]:
    """
    Validate reviewers only if we upload new file
    If we need delete/update document reviewers we need more validation
    """
    group_role_map = defaultdict(list)
    from app.reviews.utils import separate_reviewers

    reviewers = separate_reviewers(reviewers_schema)

    if reviewers_roles := reviewers.get(ReviewsInfoCtx.EntityType.role):
        roles = await select_roles(
            conn,
            roles_ids=reviewers_roles,
            role_status=RoleStatus.active,
            company_id=user.company_id,
        )
        _validate_reviewers_count(reviewers_roles, roles)

    if reviewers_groups := reviewers.get(ReviewsInfoCtx.EntityType.group):
        group_role_map = await get_group_members_by_group_ids(
            conn, group_ids=reviewers_groups, company_id=user.company_id
        )
        _validate_reviewers_count(reviewers_groups, group_role_map)

    reviewer_infos = convert_reviewers(
        reviewers=reviewers_schema,
        group_role_map=group_role_map,
    )
    return reviewer_infos


async def _validate_upload_recipients_hidden_emails(
    role_id: str | None,
    ctx: UpdateRecipientsCtx,
    company_edrpou: str,
) -> UpdateRecipientsCtx:
    """
    Validate hidden emails for recipients and update them in the context.

    WARNING: this function updates fields in ctx
    """

    hidden_recipients = [
        recipient
        for recipient in ctx.recipients
        if recipient.is_email_hidden and recipient.edrpou != company_edrpou
    ]
    hidden_edrpous = [recipient.edrpou for recipient in hidden_recipients]

    if hidden_edrpous:
        # Hidden emails are attached to the role, so we can't extract them without it
        if not role_id:
            raise InvalidRequest(
                reason=_('Неможливо призначити прихований емейл для запиту без користувача')
            )

        hidden_mapping = await validate_hidden_emails_in_redis(
            role_id=role_id,
            recipient_edrpous=hidden_edrpous,
        )
        for recipient in hidden_recipients:
            recipient.emails = hidden_mapping[recipient.edrpou]

    return ctx


async def _validate_upload_recipients_internal_options(
    company_edrpou: str,
    recipient: UpdateRecipient,
    recipients_ctx: UpdateRecipientsCtx,
) -> UploadDocumentRecipientsLegacyOptions:
    """
    Make basic checks of recipients for internal documents and convert them to legacy options.
    """
    await validate_recipient_params_for_internal(
        recipient=recipient,
        document_owner_edrpou=company_edrpou,
    )

    return UploadDocumentRecipientsLegacyOptions(
        is_internal=True,
        is_multilateral=False,
        expected_owner_signatures=1,
        expected_recipient_signatures=0,
        first_sign_by=FirstSignBy.owner,
        recipient_edrpou=None,
        recipient_emails=None,
        hide_recipient_emails=False,
        flows=[],
        recipients_ctx=recipients_ctx,
    )


async def _validate_upload_recipients_bilateral_options(
    recipients_ctx: UpdateRecipientsCtx,
    company_edrpou: str,
) -> UploadDocumentRecipientsLegacyOptions:
    """
    Make basic checks of recipients for bilateral documents and convert them to legacy options.
    """
    recipients = recipients_ctx.recipients

    await validate_recipients_params_for_bilateral(
        document_owner_edrpou=company_edrpou,
        recipients=recipients,
    )

    ctx_bilateral = recipients_ctx.get_bilateral_recipients(document_owner_edrpou=company_edrpou)
    recipient = ctx_bilateral.recipient

    return UploadDocumentRecipientsLegacyOptions(
        is_internal=False,
        is_multilateral=False,
        expected_owner_signatures=ctx_bilateral.expected_signatures_owner,
        expected_recipient_signatures=ctx_bilateral.expected_signatures_recipient,
        first_sign_by=ctx_bilateral.first_sign_by,
        recipient_edrpou=recipient.edrpou,
        recipient_emails=recipient.emails,
        hide_recipient_emails=recipient.is_email_hidden,
        flows=[],
        recipients_ctx=recipients_ctx,
    )


async def _validate_upload_recipients_multilateral_options(
    recipients_ctx: UpdateRecipientsCtx,
    company_edrpou: str,
) -> UploadDocumentRecipientsLegacyOptions:
    """
    Make basic checks of recipients for multilateral documents and convert them to legacy options.
    """
    recipients = recipients_ctx.recipients

    await validate_recipients_params_for_multilateral(
        document_owner_edrpou=company_edrpou,
        recipients=recipients,
    )

    flows = recipients_ctx.get_multilateral_flows()

    return UploadDocumentRecipientsLegacyOptions(
        is_internal=False,
        is_multilateral=True,
        expected_owner_signatures=1,
        expected_recipient_signatures=1,
        first_sign_by=FirstSignBy.owner,
        recipient_edrpou=None,
        recipient_emails=None,
        hide_recipient_emails=False,
        flows=flows,
        recipients_ctx=recipients_ctx,
    )


async def validate_upload_recipients_options(
    data: RecipientsSettingsSchema,
    company_edrpou: str,
    role_id: str | None,
) -> UploadDocumentRecipientsLegacyOptions:
    """
    Validate recipients' options for upload documents. The "recipients" parameter is a modern way to
    define recipients for the document, instead of: "is_internal", "is_multilateral", "flows",
    "expected_owner_signatures", "expected_recipient_signatures", "first_sign_by" and so on.

    This function returns a legacy options object to keep compatibility with the old upload
    logic. In the future, it should be in opposite way, where we will use a modern options object
    to perform upload documents and convert legacy options to modern ones.

    1. If the company has indicated only its employees as recipients, it is an internal document,
    and "signers_parameters" should be used instead of "recipients" parameter. The "recipients"
    parameter in this case should be empty.

    2. If only two recipients are indicated, it is a bilateral document. We prohibit cases where:
        - only one company appears as a signatory
        - the owner is not among the recipients of the document
        - there are no signatories at all

    3. If 2+ recipients are indicated, it is a multilateral document. Even if these two recipients
    are from the same company. For example, we consider a document
    to be multilateral if the signatories provided are: COMPANY_1, COMPANY_1, COMPANY_2.
    """

    recipients_ctx = await validate_update_recipients_params(
        data=data,
        company_edrpou=company_edrpou,
    )

    recipients = recipients_ctx.recipients

    recipients_ctx = await _validate_upload_recipients_hidden_emails(
        role_id=role_id,
        ctx=recipients_ctx,
        company_edrpou=company_edrpou,
    )

    # By default, if no recipients are provided, consider it as a bilateral document
    # without a recipients. Because it's how it was before, and bilaterals are the most common
    # type of documents.
    if recipients_ctx.is_empty:
        return UploadDocumentRecipientsLegacyOptions(
            is_internal=False,
            is_multilateral=False,
            expected_recipient_signatures=1,
            expected_owner_signatures=1,
            first_sign_by=FirstSignBy.owner,
            recipient_edrpou=None,
            recipient_emails=None,
            hide_recipient_emails=False,
            flows=[],
            recipients_ctx=recipients_ctx,
        )

    # For internal documents, we expect that user will provide only one company as a recipient,
    # and it should be the same as the owner company. "Is_ordered" parameter doesn't matter in
    # this case, because use "signers_parameters" to define the order of internal signers.
    if recipients_ctx.is_internal:
        return await _validate_upload_recipients_internal_options(
            recipient=recipients[0],
            company_edrpou=company_edrpou,
            recipients_ctx=recipients_ctx,
        )

    # For bilateral documents, we expect that user will provide only two companies as recipients.
    if recipients_ctx.is_bilateral:
        return await _validate_upload_recipients_bilateral_options(
            recipients_ctx=recipients_ctx,
            company_edrpou=company_edrpou,
        )

    # In other cases, when there are more than 2 recipients, we consider it as a multilateral
    # document.
    return await _validate_upload_recipients_multilateral_options(
        recipients_ctx=recipients_ctx,
        company_edrpou=company_edrpou,
    )
