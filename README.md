![](https://edo.vchasno.ua/cloud-cgi/static/edo-static-files-prd/favicons/favicon.ico)

`Не страшно, бо Вчасно` |
[![build status](https://gitlab.vchasno.com.ua/vchasno/edo/edo/badges/master/pipeline.svg)](https://gitlab.vchasno.com.ua/vchasno/edo/edo/commits/master)

### Quick Links

|                    | Dev                                                                                                                                 | Prod                                                                                                                                  |
| ------------------ | ----------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| 🌐 Website         | [EDO-Dev](https://edo-dev.vchasno.com.ua/)                                                                                          | [EDO-Prod](https://edo.vchasno.ua/)                                                                                                   |
| 🐛 Sentry          | [Sentry-Dev](https://sentry.vchasno.com.ua/organizations/vchasno/issues/?environment=dev&project=2&referrer=sidebar&statsPeriod=7d) | [Sentry-Prod](https://sentry.vchasno.com.ua/organizations/vchasno/issues/?environment=prod&project=2&referrer=sidebar&statsPeriod=7d) |
| 📊 Kibana          | [Kibana-Dev](https://kibana-os-dev.vchasno.com.ua/)                                                                                 | [Kibana-Prod](https://kibana-os-prd.vchasno.com.ua/)                                                                                  |
| 📈 Metabase        | [Metabase-Dev](https://metabase-dev.vchasno.com.ua/)                                                                                | [Metabase-Prod](https://metabase-prd.vchasno.com.ua/)                                                                                 |
| 🚩 Feature flags   | [FeatureFlags-Dev](https://featureflags-dev.vchasno.com.ua/)                                                                        | [FeatureFlags-Prod](https://featureflags-prd.vchasno.com.ua/)                                                                         |
| 📊 Grafana         | [Grafana-Dev](https://grafana-dev.vchasno.com.ua/)                                                                                  | [Grafana-Prod](https://grafana-prd.vchasno.com.ua/)                                                                                   |
| 📚 Confluence      | [Confluence](https://vchasno-group.atlassian.net/wiki/spaces/vchasno/pages/4949339)                                                 |                                                                                                                                       |
| 🔐 Vault (secrets) | [Vault-Dev](https://vault-dev.vchasno.com.ua/)                                                                                      | [Vault-Prod](https://vault-prd.vchasno.com.ua/)                                                                                       |
| 🚀 ArgoCD          | [ArgoCD-Dev](https://argocd-dev.vchasno.com.ua/applications/edo-service)                                                            | [ArgoCD-Prod](https://argocd-prd.vchasno.com.ua/applications/edo-service)                                                             |
| 📋 Other resources | [Additional](https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/11174419/Production)                                       | [Additional](https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/11207145/Development)                                        |

For controlling project we use [Just](https://github.com/casey/just). The tool is used to run
shortcuts in the project

```bash
just -l # List all available commands
```

## Frontend

Frontend can be run under docker and local yarn

- Run with Docker: `just start-frontend`
- Run with NodeJS: `yarn run dev`

To run Linters: `just lint-frontend`

### Frontend GraphQL code generation

Generate from API endpoint graphql `cs/gql/schema.gql` file: `yarn run graphql:schema`.

After updating the schema, you need to run the command to generate types:
`just update-graphql-schema`

## Backend

- [Coding conventions and guidelines](https://vchasno-group.atlassian.net/wiki/spaces/vchasno/pages/4955009/Coding+convention+and+guidelines)
  (TODO: Update)

Check minimal Docker Resources, increase if necessary:

| Resources | Min value | Recommended (for running tests locally) |
| --------- | --------- | --------------------------------------- |
| RAM       | 8 GB      | 12 GB                                   |
| SWAP      | 4 GB      | 4 GB                                    |

### Start backend pods

```bash
just start-backend
# To start backend silently in the background, run
just start-backend -d
```

### Rebuild docker image

```bash
just build-python-image
```

#### Python dependencies update script (Requires some love ❤️):

```bash
just install-python-deps
```

### Pull dev database to local environment

```bash
just copy-dev
# Be aware that pulled files won't show in elasticsearch. To index the files - run
just prepare-elasticsearch
```

### Database migrations

```bash
# Migrate main database
just alembic-upgrade
# Generate migrations for main database
just alembic-autogenerate {{migration name}}
# Migrate events database
just event-alembic-upgrade
# Generate migrations for events database
just events-alembic-autogenerate {{migration name}}
```

### Running tests

```bash
# Run linters (they would format the files)
just lint
# Run whole pytest suite for the project
just run-test
# Run single pytest file/directory
just run-test {{path}}
# Run single pytest file/directory in parallel
just run-test -n {{number of threads}} {{path}}
# Run bash shell in pytest container
just start-test-shell
# Run pytest in CI mode
just run-test -ci
```

### IPython shell

```bash
just ipython
```

IPython has active local database connection. You can execute requests inline (just like in Jupyter
notebook)

### Inspect logs of container (this may trigger long terminal output)

```bash
docker compose logs -f {{container name}}
```

### Elasticsearch

- [Documentation](https://www.elastic.co/guide/en/elasticsearch/reference/current/index.html) ·
- [Cheatsheet](https://elasticsearch-cheatsheet.jolicode.com/) ·
- [How use elastic locally](docs/elasticsearch/run_locally.md)

> If you are facing problems with CPU overload (`docker stats`), try to start and give time for
> executing containers for elasticsearch and kafka separately:
> `docker-compose up -d {{elasticsearch | kafka}}` >
> `docker compose logs -f {{elasticsearch | kafka}}`

### Emails

- [How to create and debug emails](docs/how-debug-emails.md)

### AWS S3 bucket

We use [Minio](https://min.io/) to mock AWS S3 bucket. [Local url](http://localhost:9001)

- login: `minio`
- password: `miniosecret`

### Update certificates

```bash
just update-certs
```

**OR manually:**

1. **Update CAs.json**

   ```bash
   # Production
   wget --output-document ./eusign/prod/CAs.json https://iit.com.ua/download/productfiles/CAs.json

   # Development
   wget --output-document ./eusign/dev/CAs.json https://iit.com.ua/download/productfiles/CAs.Test.All.json
   ```

2. **Update certificates**

   ```bash
   # Production
   wget --output-document ./eusign/prod/certificates/CACertificates.p7b https://iit.com.ua/download/productfiles/CACertificates.p7b

   # Development
   wget --output-document ./eusign/dev/certificates/CACertificates.p7b https://iit.com.ua/download/productfiles/CACertificates.Test.All.p7b
   ```

3. **Update Vchasno-Signer**

   - Update version in package.json
   - ```bash
     yarn install --update @evo/vchasno-signer
     ```

> We are using dev certificates because we have staging integration with Diia, which uses their own
> dev CACertificates.
>
> It's important to know that these certificates are being hosted on IIT website, and you can
> download them directly from there.

### VS Code setup

To make VS Code see packages and enable autocompletion, you need to install Python packages locally,
without using Docker containers.

Below is an instruction for macOS using pyenv. For other OS, the instruction may differ:

```bash
# 1. Create and activate virtual environment
pyenv local 3.13
python3 -m venv venv
source .venv/bin/activate

# or
uv venv --python 3.13
source .venv/bin/activate


# 2. Install system libraries needed for some Python packages.
# For Ubuntu you can use apt-get and check package names in Dockerfile that uses Ubuntu as base image
brew install qpdf # for pikepdf

# 3. Install Python packages
uv pip install -r requirements/dev.txt

# 4. If you encounter problems installing a package and get an error about missing
# C or C++ libraries (fatal error: 'qpdf/QPDF.hh' file not found), google which
# library is missing, repeat steps 2 and 3 until all Python packages are installed.
```

#### Recommended VS Code plugins

- [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python)
- [Mypy](https://marketplace.visualstudio.com/items?itemName=ms-python.mypy-type-checker)
- [Pylance](https://marketplace.visualstudio.com/items?itemName=ms-python.vscode-pylance)
- [Ruff](https://marketplace.visualstudio.com/items?itemName=charliermarsh.ruff)

- [Translations](./translations/README.md)
- [OpenAPI](./app/openapi/README.md)

## Deployment

### Dev

Dev is being deployed automatically from the latest master once branch is updated.

### Production

1. Create new tag with changelog:
   ```bash
   just push-new-tag
   ```
2. Go to [pipelines](https://gitlab.vchasno.com.ua/vchasno/edo/edo/-/pipelines)
3. Post a message in `#edo-dev` channel in Slack\*\* with link to the pipeline and changelog to let
   people know.
4. Wait for the build to finish
5. Run the last `deploy-aws` job manually in the pipeline

## Pre-commit configuration

Pre-commit hooks are run over modified python files that were added to the commit by `git add`

If you want to run pre-commit checks over unadded files, prefer `just lint` command

> **TODO:** Would be nice to find a way to run pytest over modified directory (e.g.
> `pytest -vv -x app/documents/tests`)

### Installation

1. **Install pre-commit** using pip - `pip3 install pre-commit` or brew - `brew install pre-commit`

2. **Configure pre-commit for git** - `pre-commit install`. Should show following output:

```
pre-commit installed at .git/hooks/pre-commit
```

Flow could be altered if needed at `.pre-commit-config.yaml`

> **Hint:** if you need to add files to commit without pre-commit checks, run with no-verify option.
> e.g. `git commit -m "message"/--amend --no-verify`

You're all set for pre-commit checks
