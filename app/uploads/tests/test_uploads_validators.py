from functools import partial

import pytest

from api.errors import Error
from app.document_categories.types import PublicDocumentCategory
from app.documents.enums import FirstSignBy
from app.lib import validators
from app.lib.enums import SignatureFormat
from app.signatures.types import SignersInfoCtx
from app.tests.common import (
    PERMITTED_EMAIL,
    TEST_USER_EMAIL,
    cleanup_on_teardown,
    prepare_client,
    prepare_upload_file,
)
from app.uploads.validators import (
    UploadSchema,
    build_unique_key,
    validate_remote_response,
)

TEST_EDRPOU = '11111111'
TEST_NUMBER = '1'
TEST_RECIPIENT_EDRPOU = '12345678'
TEST_TYPE = 'Invoice'
TEST_UUID = '22b60f5f-5617-47cc-b476-7d1db455fa5e'

UPLOAD_VALIDATOR_DEFAULT_DATA = {
    'debug': True,
    'edrpou': TEST_EDRPOU,
    'email_owner': None,
    'email_recipient': None,
    'recipient_edrpou': None,
    'recipient_emails': [],
    'hide_recipient_emails': None,
    'first_sign_by': None,
    'is_internal': None,
    'is_multilateral': None,
    'is_required_review': None,
    'expected_owner_signatures': None,
    'expected_recipient_signatures': None,
    'source': None,
    'vendor': None,
    'share_to': None,
    'share_to_groups': None,
    'signer_roles': None,
    'signer_emails': None,
    'signer_parameters': None,
    'parallel_signing': None,
    'parallel_review': True,
    'parent_id': None,
    'tags': None,
    'new_tags': None,
    'reviewers': None,
    'reviewers_ids': None,
    'title': None,
    'doc_number': None,
    'date_document': None,
    'amount': None,
    'flows': None,
    'parameters': None,
    'signature_format': None,
    'is_chargeless': None,
    'template_id': None,
    'category': None,
    'vendor_id': None,
    'is_versioned': None,
    'comment': None,
    'recipients': None,
    'access_settings': None,
    'show_recipients': False,
    'with_access_settings': False,
}


@pytest.mark.parametrize(
    'first_sign_by, expected',
    [
        (
            FirstSignBy.owner,
            f'{TEST_EDRPOU}-{TEST_RECIPIENT_EDRPOU}-{TEST_TYPE}-{TEST_NUMBER}',
        ),
        (
            FirstSignBy.recipient,
            f'{TEST_RECIPIENT_EDRPOU}-{TEST_EDRPOU}-{TEST_TYPE}-{TEST_NUMBER}',
        ),
    ],
)
def test_build_unique_key(first_sign_by, expected):
    item = prepare_upload_file(
        edrpou_owner=TEST_EDRPOU,
        edrpou_recipient=TEST_RECIPIENT_EDRPOU,
        type_=TEST_TYPE,
        number=TEST_NUMBER,
        first_sign_by=first_sign_by.value,
    )
    assert build_unique_key(item) == expected


@pytest.mark.parametrize(
    'url, is_valid, code',
    [
        ('/invalid-url', False, 'invalid_remote_response'),
        ('/', True, None),
    ],
)
async def test_validate_remote_response(aiohttp_client, url, is_valid, code):
    app, client, user = await prepare_client(aiohttp_client)
    try:
        response = await client.get(url)
        validate_func = partial(validate_remote_response, response)

        if is_valid:
            assert await validate_func() is None
        else:
            with pytest.raises(Error) as err:
                await validate_func()
            data = err.value.to_dict()
            assert err.value.status == 400
            assert data['code'] == code
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'test_input, expected',
    [
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'first_sign_by': 'recipient',
                'parent_id': TEST_UUID,
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'first_sign_by': 'recipient',
                'parent_id': TEST_UUID,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'expected_owner_signatures': '2',
                'expected_recipient_signatures': '2',
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'expected_owner_signatures': 2,
                'expected_recipient_signatures': 2,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'vendor': '1C',
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'vendor': '1C',
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': PERMITTED_EMAIL,
                'share_to': [TEST_UUID, TEST_UUID],
                'share_to_groups': [TEST_UUID, TEST_UUID],
                'is_internal': '1',
                'is_multilateral': '1',
                'parallel_signing': '1',
                'is_versioned': None,
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': [PERMITTED_EMAIL],
                'is_internal': True,
                'is_multilateral': True,
                'parallel_signing': True,
                'is_versioned': None,
                'share_to': [TEST_UUID, TEST_UUID],
                'share_to_groups': [TEST_UUID, TEST_UUID],
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': PERMITTED_EMAIL,
                'signer_roles': [TEST_UUID, TEST_UUID, TEST_UUID],
                'is_internal': '0',
                'is_multilateral': '0',
                'is_required_review': '0',
                'parallel_signing': '0',
                'tags': [TEST_UUID],
                'parallel_review': 'True',
                'is_versioned': 'True',
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': [PERMITTED_EMAIL],
                'is_internal': False,
                'is_multilateral': False,
                'is_required_review': False,
                'signer_roles': [TEST_UUID],
                'parallel_signing': False,
                'parallel_review': True,
                'tags': [TEST_UUID],
                'is_versioned': True,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': PERMITTED_EMAIL,
                'is_required_review': '1',
                'signer_emails': [TEST_USER_EMAIL, TEST_USER_EMAIL],
                'expected_owner_signatures': '2',
                'expected_recipient_signatures': '1',
                'parallel_signing': '0',
                'parallel_review': '0',
                'new_tags': ['tag'],
                'reviewers_ids': [TEST_UUID],
                'is_chargeless': '0',
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': [PERMITTED_EMAIL],
                'is_required_review': True,
                'expected_owner_signatures': 2,
                'expected_recipient_signatures': 1,
                'signer_emails': [TEST_USER_EMAIL],
                'parallel_signing': False,
                'parallel_review': False,
                'new_tags': ['tag'],
                'reviewers_ids': [TEST_UUID],
                'is_chargeless': False,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': PERMITTED_EMAIL,
                'is_internal': 'true',
                'is_multilateral': 'true',
                'is_required_review': None,
                'parallel_signing': 'true',
                'tags': [TEST_UUID, TEST_UUID],
                'new_tags': ['tag', 'tag'],
                'reviewers_ids': [TEST_UUID, TEST_UUID],
                'is_chargeless': 'true',
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': TEST_USER_EMAIL,
                'email_recipient': [PERMITTED_EMAIL],
                'is_internal': True,
                'is_multilateral': True,
                'is_required_review': None,
                'parallel_signing': True,
                'parallel_review': True,
                'tags': [TEST_UUID],
                'new_tags': ['tag'],
                'reviewers_ids': [TEST_UUID, TEST_UUID],
                'is_chargeless': True,
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_recipient': PERMITTED_EMAIL,
                'recipient_emails': [PERMITTED_EMAIL],
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': None,
                'email_recipient': [PERMITTED_EMAIL],
                'recipient_emails': [PERMITTED_EMAIL],
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'recipient_emails': [PERMITTED_EMAIL],
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'recipient_emails': [PERMITTED_EMAIL],
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_recipient': '<EMAIL>, <EMAIL>',
                'signature_format': SignatureFormat.internal_wrapped.value,
                'template_id': TEST_UUID,
                'category': PublicDocumentCategory.act.value,
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'email_owner': None,
                'email_recipient': ['<EMAIL>', '<EMAIL>'],
                'signature_format': SignatureFormat.internal_wrapped.value,
                'template_id': TEST_UUID,
                'category': int(PublicDocumentCategory.act.value),
            },
        ),
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'signer_parameters': {
                    'signers': [
                        {'value': '123', 'signer_type': SignersInfoCtx.EntityType.role},
                        {'value': '123', 'signer_type': SignersInfoCtx.EntityType.email},
                        {'value': '123', 'signer_type': SignersInfoCtx.EntityType.group},
                    ]
                },
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'signer_parameters': {
                    'signers': [
                        {'signer_type': SignersInfoCtx.EntityType.role, 'value': '123'},
                        {'signer_type': SignersInfoCtx.EntityType.email, 'value': '123'},
                        {'signer_type': SignersInfoCtx.EntityType.group, 'value': '123'},
                    ]
                },
            },
        ),
        # Comment
        (
            {
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'comment': {
                    'text': 'test comment',
                },
            },
            {
                **UPLOAD_VALIDATOR_DEFAULT_DATA,
                'debug': True,
                'edrpou': TEST_EDRPOU,
                'comment': {
                    'text': 'test comment',
                    'is_internal': False,
                },
            },
        ),
    ],
)
def test_validate_upload(test_input, expected):
    assert (
        validators.validate_pydantic(UploadSchema, test_input).model_dump(mode='json') == expected
    )


@pytest.mark.parametrize(
    'invalid_data',
    [
        {'debug': 'wrong'},
        {'debug': True, 'edrpou': 'wrong'},
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'email_owner': 123,
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'email_owner': 'wrong_gmail.com',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'email_recipient': 123,
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'first_sign_by': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'expected_owner_signatures': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'expected_owner_signatures': '2',
            'expected_recipient_signatures': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'source': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'vendor': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'first_sign_by': 'wrong',
            'expected_owner_signatures': 'wrong',
            'expected_recipient_signatures': 'wrong',
            'source': 'wrong',
            'vendor': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'signer_roles': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'signer_emails': 'wrong',
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'tags': ['wrong'],
        },
        {
            'debug': True,
            'edrpou': TEST_EDRPOU,
            'new_tags': [1],
        },
    ],
)
def test_validate_upload_invalid(invalid_data):
    with pytest.raises(Error):
        validators.validate_pydantic(UploadSchema, invalid_data)
