"""Update companies.activity_field column type

Revision ID: 0f7006b7bcb8
Revises: a30bde67e78c
Create Date: 2020-03-24 15:00:02.614710


Author: <PERSON><PERSON><PERSON><PERSON> Hyzy<PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0f7006b7bcb8'
down_revision = 'a30bde67e78c'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        'ALTER TABLE companies '
        'ALTER COLUMN activity_field '
        'SET DATA TYPE text'
    )


def downgrade():
    op.execute(
        'ALTER TABLE companies '
        'ALTER COLUMN activity_field '
        'SET DATA TYPE varchar(64) '
        'USING activity_field::varchar(64)'
    )
