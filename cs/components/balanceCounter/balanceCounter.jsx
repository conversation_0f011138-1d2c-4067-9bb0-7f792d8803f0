import React from 'react';

import FlexBox from 'components/FlexBox/FlexBox';
import Icon from 'components/ui/icon/icon';
import DocumentSvg from 'icons/document.svg';
import PropTypes from 'prop-types';
import { t } from 'ttag';

import Button from '../ui/button/button';

import { redirect } from '../../lib/navigation';
import { BILL_GENERATION_PAGE_PATTERN } from '../../lib/routing/constants';
import eventTracking from '../../services/analytics/eventTracking';
import { getLocationId } from '../../services/navigation-structure';

// styles
import css from './balanceCounter.css';

const renderCounter = (oldRateLeft, integrationRateLeft) => (
    <React.Fragment>
        {oldRateLeft !== 0 && (
            <React.Fragment>
                <span className={css.remain}>{oldRateLeft}</span>
            </React.Fragment>
        )}
        <span className={css.remain}>{integrationRateLeft}</span>
    </React.Fragment>
);

const BalanceCounter = ({
    bonusesLeftOldRate,
    bonusesLeftIntegrationRate,
    documentsLeftOldRate,
    documentsLeftIntegrationRate,
}) => {
    const trackButtonClick = (evt) => {
        evt.preventDefault();
        eventTracking.sendEvent(
            'Tarifs',
            'buy',
            getLocationId(document.location),
            () => {
                redirect(BILL_GENERATION_PAGE_PATTERN);
            },
        );
    };
    return (
        <div className={css.root}>
            <div
                className={css.title}
            >{t`Документи по тарифу “Інтеграція”`}</div>
            <FlexBox className={css.content} gap={25}>
                <FlexBox className={css.bonusCounter}>
                    <FlexBox gap={10}>
                        <Icon className={css.icon} glyph={DocumentSvg} />
                        <FlexBox className={css.column}>
                            <div>
                                <div
                                    className={css.subTitle}
                                >{t`Бонусний баланс`}</div>
                                <div
                                    className={css.hint}
                                >{t`Документів для надсилання`}</div>
                            </div>
                            {renderCounter(
                                bonusesLeftOldRate,
                                bonusesLeftIntegrationRate,
                            )}
                        </FlexBox>
                    </FlexBox>
                </FlexBox>
                <FlexBox className={css.mainCounter} justify="space-between">
                    <FlexBox gap={10}>
                        <Icon className={css.icon} glyph={DocumentSvg} />
                        <FlexBox direction="column">
                            <div>
                                <div
                                    className={css.subTitle}
                                >{t`Основний баланс`}</div>
                                <div
                                    className={css.hint}
                                >{t`Документів для надсилання`}</div>
                            </div>
                            {renderCounter(
                                documentsLeftOldRate,
                                documentsLeftIntegrationRate,
                            )}
                        </FlexBox>
                    </FlexBox>
                    <a
                        className={css.button}
                        href={BILL_GENERATION_PAGE_PATTERN}
                        onClick={trackButtonClick}
                    >
                        <Button
                            theme="blue"
                            typeContour
                            width="full"
                            className={css.whiteBackground}
                        >
                            {t`Поповнити`}
                        </Button>
                    </a>
                </FlexBox>
            </FlexBox>
        </div>
    );
};

BalanceCounter.propTypes = {
    bonusesLeftOldRate: PropTypes.number.isRequired,
    bonusesLeftIntegrationRate: PropTypes.number.isRequired,
    documentsLeftOldRate: PropTypes.number.isRequired,
    documentsLeftIntegrationRate: PropTypes.number.isRequired,
};

export default BalanceCounter;
