import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Checkbox } from '@vchasno/ui-kit';

import { getIsPartnerInvalidSigned } from 'selectors/filter.selectors';
import { t } from 'ttag';

import companyEmployeesActionCreators from '../../../companyEmployees/companyEmployeesActionCreators';
import filtersActionCreators from '../../../filters/filtersActionCreators';

import eventTracking from '../../../../services/analytics/eventTracking';

const PartnerInvalidSignedBlock: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const dispatch = useDispatch();
    const isPartnerInvalidSigned = useSelector(getIsPartnerInvalidSigned);

    const handleCheck = () => {
        dispatch(companyEmployeesActionCreators.resetInvalidSignaturesFilter());
        dispatch(
            filtersActionCreators.onSetIsPartnerInvalidSigned(
                !isPartnerInvalidSigned,
            ),
        );

        eventTracking.sendToGTM({
            category: 'New_filter',
            action: !isPartnerInvalidSigned ? 'Apply_filter' : 'Clear_filter',
            label: 'partner_invalid_signed',
        });
    };

    return (
        <Checkbox
            label={t`Перепідписання контрагентом`}
            onChange={handleCheck}
            checked={isPartnerInvalidSigned}
        />
    );
};

export default PartnerInvalidSignedBlock;
