import logging
from collections import defaultdict
from dataclasses import dataclass
from typing import (
    Any,
)

from app.auth.constants import COMPANY_ID_REQUEST_KEY, PENDING_ROLE_ID_REQUEST_KEY
from app.auth.db import (
    select_user,
    select_users,
)
from app.auth.enums import InviteSource
from app.auth.schemas import CompanyConfig
from app.auth.types import (
    AuthUser,
    User,
)
from app.auth.utils import get_company_config
from app.comments.db import select_document_inbox_email_comment
from app.document_versions.utils import is_versioned_document
from app.documents.db import (
    select_document_for_recipient_email,
    select_document_owner_for_zakupki_email,
    select_document_recipient,
    select_documents,
)
from app.documents.enums import DocumentSource
from app.documents.types import (
    Document,
    DocumentForEmailBase,
    DocumentForRecipientEmail,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import (
    emailing,
    utm_params,
)
from app.lib.database import DBConnection, DBRow
from app.lib.emailing import (
    build_company_label,
    can_receive_notification,
)
from app.lib.enums import (
    DocumentFolder,
    Language,
    NotificationType,
)
from app.lib.types import (
    StrDict,
)
from app.lib.urls import (
    build_static_url,
    build_url,
)
from app.notifications import utils as notifications
from app.notifications.constants import COMPANY_EDRPOUS_REFUSED_TO_RECEIVE_NOTIFICATIONS
from app.services import services
from app.sign_sessions import utils as sign_sessions
from app.tokens import services as tokens
from worker import topics

logger = logging.getLogger(__name__)

YOUTUBE_URL = 'https://youtu.be/2IqxWC8ShD8'  # about PRO


@dataclass
class DocumentInboxEmail:
    """
    Class that represent group of functions and context related to sending notification
    about new inbox document to registered and active user.
    """

    conn: DBConnection
    recipient: User
    document: DocumentForEmailBase
    sender: User | None
    recipient_config: CompanyConfig
    sender_config: CompanyConfig
    # If value is True, it means that recipient should sign document
    # If value is False, it means that recipient should just view document
    is_for_sign: bool

    group_name: str | None = None

    # constants
    SUBJECT_FOR_SIGN = _('Компанія {company} надіслала вам документи на підпис')

    HEADER_FOR_SIGN = _('Ви отримали документ для підписання')

    SUBJECT_FOR_VIEW = _('Компанія {company} надіслала вам документи')

    HEADER_FOR_VIEW = _('Ви отримали документ')

    async def should_send(self) -> bool:
        """Check if we need to send an email address at all"""

        if not can_receive_notification(self.recipient, NotificationType.inbox):
            return False

        if self.recipient.company_edrpou in COMPANY_EDRPOUS_REFUSED_TO_RECEIVE_NOTIFICATIONS:
            return False

        return True

    async def send(self) -> None:
        """Send email about inbox document"""
        if not await self.should_send():
            return

        document_source = self.document.source

        # For documents uploaded from "zakupki.prom.ua" (company from EVO group)
        # we are sending custom email
        if document_source == DocumentSource.zakupki:
            await self.send_zakupki()

        # For other sources we just send generic email
        else:
            await self.send_generic()

    async def send_generic(self) -> None:
        """
        Email notification about new document. That function is a shortcut to
        self.send_base, which has all logic related to building
        and sending inbox email.
        """

        # user can be "None", because document can be sent by automation logic
        sender_email: str | None = self.sender.email if self.sender else None
        sender_name: str | None = self.sender.full_name if self.sender else None

        await self.send_base(
            sender_email=sender_email,
            sender_name=sender_name,
            sender_company_edrpou=self.document.sender_company_edrpou,
            sender_company_name=self.document.sender_company_name,
        )

    def get_document_url(self) -> str:
        """Direct link to the document"""
        return build_url(
            route='app',
            tail=f'/documents/{self.document.id}',
            get={
                **utm_params.DOCUMENT_RECEIVED,
                # Redirect user to the appropriate company
                COMPANY_ID_REQUEST_KEY: self.recipient.company_id,
            },
        )

    async def create_document_url(self) -> str:
        """
        Depends on sender and recipient configuration, create sign session link
        or just return direct link to the document
        """

        sender_role_id = sender.role_id if (sender := self.sender) else None

        # TODO: make sender_role_id optional and create sign session even if
        #   sender_role_id is None
        if not sender_role_id:
            return self.get_document_url()

        if not self.recipient.email:
            return self.get_document_url()

        if _should_use_sign_session_for_inbox_notification(
            recipient_config=self.recipient_config,
            sender_config=self.sender_config,
        ):
            return await sign_sessions.create_sign_session_url_for_inbox_email(
                conn=self.conn,
                document_id=self.document.id,
                email=self.recipient.email,
                role_id=self.recipient.role_id,
                edrpou=self.recipient.company_edrpou,
                created_by=sender_role_id,
                params=utm_params.DOCUMENT_RECEIVED,
            )
        return self.get_document_url()

    async def send_base(
        self,
        sender_email: str | None,
        sender_name: str | None,
        sender_company_edrpou: str | None,
        sender_company_name: str | None,
    ) -> None:
        """
        Notification about new inbox document. That function can be used in
        custom emails also by passing custom sender info.
        """

        if not self.recipient.email:
            return

        # Build subject
        subject_company_name = emailing.build_company_label(
            edrpou=sender_company_edrpou or '',
            company_name=sender_company_name,
        )
        if self.is_for_sign:
            subject = self.SUBJECT_FOR_SIGN.bind(company=subject_company_name)
            header_title = self.HEADER_FOR_SIGN
        else:
            subject = self.SUBJECT_FOR_VIEW.bind(company=subject_company_name)
            header_title = self.HEADER_FOR_VIEW

        document_url = await self.create_document_url()

        is_versioned = await is_versioned_document(
            conn=self.conn,
            document_id=self.document.id,
        )

        comment = await select_document_inbox_email_comment(
            conn=self.conn,
            document_id=self.document.id,
            sender_edrpou=sender_company_edrpou,
        )

        if self.sender_config.hide_sender_email_in_notifications:
            sender_email = None

        context = {
            'header_title': header_title,
            'recipient_first_name': self.recipient.first_name,
            'user_email': sender_email,
            'user_name': sender_name,
            'user_company_name': sender_company_name,
            'recipient_company_edrpou': self.recipient.company_edrpou,
            'recipient_company_name': self.recipient.company_name,
            'user_company_edrpou': sender_company_edrpou,
            'document_title': self.document.title,
            'document_url': document_url,
            'is_versioned': is_versioned,
            'comment': comment and comment.text,
            'group_name': self.group_name,
        }

        await emailing.send_email(
            recipient_mixed=self.recipient.email,
            subject=subject,
            template_name='notification_document',
            context=context,
            language=self.recipient.language,
        )

    async def send_zakupki(self) -> None:
        """
        Send custom email when document sources is from zakupki.prom.ua.
        Main idea: send custom email only for multilateral documents, except if
        the recipient is the owner of the document. In any other cases, send
        a generic email, like we are sending in normal cases.
        """
        document_recipient = await select_document_recipient(
            conn=self.conn,
            document_id=self.document.id,
            edrpou=self.recipient.company_edrpou,
        )
        document_owner = await select_document_owner_for_zakupki_email(
            conn=self.conn,
            document_id=self.document.id,
        )

        if not self.recipient.email:
            return

        # send generic email in case if some object is missing from database
        if not document_recipient or not document_owner:
            await self.send_generic()
            return

        document_owner_config = await get_company_config(
            conn=self.conn,
            company_edrpou=document_owner.company_edrpou,
        )

        external_meta = document_recipient.external_meta
        if not external_meta:
            return

        is_document_owner = bool(external_meta.get('is_document_owner'))
        is_source_client = bool(external_meta.get('is_source_client'))

        # do not send email if recipient is a document owner
        if is_document_owner:
            return

        # Send generic email, but with custom sender, in case if not it is not
        # source client
        if not is_source_client:
            await self.send_base(
                sender_email=document_owner.email,
                sender_name=document_owner.first_name,
                sender_company_edrpou=document_owner.company_edrpou,
                sender_company_name=document_owner.company_name,
            )
            return

        # Build subject
        subject_company_name = emailing.build_company_label(
            edrpou=document_owner.company_edrpou or '',
            company_name=document_owner.company_name,
        )
        header_title = self.HEADER_FOR_SIGN.value

        document_url = await self.create_document_url()

        user_email = document_owner.email
        if document_owner_config.hide_sender_email_in_notifications:
            user_email = None

        context = {
            'header_title': header_title,
            'recipient_first_name': self.recipient.first_name,
            'user_email': user_email,
            'user_name': document_owner.first_name,
            'user_company_name': document_owner.company_name,
            'user_company_edrpou': document_owner.company_edrpou,
            'document_title': self.document.title,
            'document_url': document_url,
            'group_name': self.group_name,
        }

        await emailing.send_email(
            recipient_mixed=self.recipient.email,
            subject=self.SUBJECT_FOR_SIGN.bind(company=subject_company_name),
            template_name='zakupki_notification_document',
            context=context,
        )


def _should_use_sign_session_for_inbox_notification(
    *,
    sender_config: CompanyConfig,
    recipient_config: CompanyConfig,
) -> bool:
    """
    Check if we should use sign session for inbox notification.
    """
    if not get_flag(FeatureFlags.USE_SIGN_SESSION_FOR_INVITE_EMAIL):
        return False

    if not sender_config.use_sign_session_for_outbox_document_notifications:
        return False

    if not recipient_config.use_sign_session_for_inbox_document_notifications:
        return False

    return True


async def send_document_role_invite_email(
    conn: DBConnection,
    recipient_email: str,
    recipient_role_id: str | None,
    recipient_edrpou: str,
    sender: User,
    document: DocumentForRecipientEmail,
    recipient_config: CompanyConfig,
    sender_config: CompanyConfig,
) -> None:
    """
    Inviting a registered user to add a new company to the profile and sign
    the given document.

    It's an email for the user that is already registered in service. If you
    have to send an invitation email to a new user that is not registered yet
    in service at all, use the function "send_document_user_invite_email" instead.
    """

    from app.documents.utils import is_one_sign_document

    should_sign: bool = not is_one_sign_document(document=document._row)

    utm = (
        utm_params.INVITE_ROLE_BY_DOC_TO_SIGN
        if should_sign
        else utm_params.INVITE_ROLE_BY_DOC_TO_VIEW
    )

    invite_url: str
    if _should_use_sign_session_for_inbox_notification(
        sender_config=sender_config,
        recipient_config=recipient_config,
    ):
        # TODO: add different logic for signing and viewing, by should_sign. Also
        #  check the same logic for new user invite
        invite_url = await sign_sessions.create_sign_session_url_for_inbox_email(
            conn=conn,
            document_id=document.id,
            edrpou=recipient_edrpou,
            email=recipient_email,
            role_id=None,
            created_by=sender.role_id,
            params=utm,
        )
    else:
        invite_url = build_url(
            route='app',
            tail='/',
            get={
                **utm,
                PENDING_ROLE_ID_REQUEST_KEY: recipient_role_id,
            },
        )

    recipient_user = await select_user(
        conn=conn,
        email=recipient_email,
        company_edrpou=recipient_edrpou,
    )
    language = recipient_user.language if recipient_user else None

    await send_document_invite_email(
        recipient_email=recipient_email,
        recipient_company_edrpou=recipient_edrpou,
        document_title=document.title,
        sender_company_edrpou=sender.company_edrpou,
        sender_company_name=sender.company_name,
        invite_url=invite_url,
        should_sign=should_sign,
        recipient_language=language,
    )


async def send_document_user_invite_email(
    conn: DBConnection,
    document_id: str,
    recipient_email: str,
    recipient_edrpou: str,
    email_domains: list[str] | None,
    sender: User,
    sender_config: CompanyConfig,
    recipient_config: CompanyConfig,
) -> None:
    """
    Inviting a new user to register and sign the given document.

    It's an email for the user that is not registered in service at all. If you
    have to send an invitation email to a user that is registered but doesn't have
    an active role in that company, use the function "send_document_role_invite_email"
    instead.
    """

    # skip sending notifications, if user is unsubscribed
    if await notifications.is_unsubscribed(conn, email=recipient_email):
        return

    from app.documents.utils import is_one_sign_document

    sender_id: str = sender.id
    sender_role_id: str = sender.role_id
    sender_company_edrpou: str = sender.company_edrpou
    sender_company_name: str | None = sender.company_name

    #  hint: we can use more generic function for selecting document
    document = await select_document_for_recipient_email(
        conn=conn,
        document_id=document_id,
    )
    if not document:
        logger.warning('No document for recipient email')
        return
    should_sign: bool = not is_one_sign_document(document=document._row)

    recipient_user = await select_user(
        conn=conn,
        email=recipient_email,
        company_edrpou=recipient_edrpou,
    )
    language = recipient_user.language if recipient_user else None

    # Prepare UTM parameters
    is_coworker: bool = sender_company_edrpou == recipient_edrpou
    utm_data = utm_params.INVITE_DOCUMENT if should_sign else utm_params.INVITE_DOCUMENT_ONE_SIGN
    if not is_coworker:
        utm_data = {
            'invited_type': InviteSource.document.value,
            'invited_edrpou': sender_company_edrpou,
            **utm_data,
        }

    sender_company_config = await get_company_config(conn, company_edrpou=sender_company_edrpou)

    # Create invite URL
    invite_url: str
    if _should_use_sign_session_for_inbox_notification(
        sender_config=sender_config,
        recipient_config=recipient_config,
    ):
        # Create sign session in which use can sign document without registration
        invite_url = await sign_sessions.create_sign_session_url_for_inbox_email(
            conn=conn,
            document_id=document_id,
            edrpou=recipient_edrpou,
            email=recipient_email,
            role_id=None,
            created_by=sender_role_id,
            params=utm_data,
        )
    elif (
        sender_company_config.allow_unregistered_document_view
        and document.expected_recipient_signatures != 0
    ):
        sign_session = await sign_sessions.create_sign_session_for_inbox_email(
            conn=conn,
            document_id=document_id,
            email=recipient_email,
            edrpou=recipient_edrpou,
            role_id=None,
            created_by=sender_role_id,
            expire_days=365,
        )

        invite_url = build_url(
            route='shared_views.ui',
            document_id=document_id,
            sign_session_id=sign_session.id,
        )
    else:
        # Create URL by which invited user can register
        invite_token = await tokens.create_short_registration_token(
            conn=conn,
            edrpou=recipient_edrpou,
            email=recipient_email,
            invited_by_company_edrpou=sender_company_edrpou,
            invited_by_user_id=sender_id,
            invited_with_document=document_id,
            email_domains=email_domains if is_coworker else None,
        )
        invite_url = build_url(
            route='tokens.check_invite',
            mixed_token=invite_token,
            get=utm_data,
        )

    await send_document_invite_email(
        recipient_email=recipient_email,
        document_title=document.title,
        sender_company_edrpou=sender_company_edrpou,
        sender_company_name=sender_company_name,
        recipient_company_edrpou=recipient_edrpou,
        invite_url=invite_url,
        should_sign=should_sign,
        recipient_language=language,
    )


async def send_document_invite_email(
    recipient_email: str,
    document_title: str,
    sender_company_edrpou: str,
    sender_company_name: str | None,
    recipient_company_edrpou: str | None,
    invite_url: str,
    should_sign: bool,
    recipient_language: Language | None,
) -> None:
    subject_company_name = emailing.build_company_label(
        edrpou=sender_company_edrpou,
        company_name=sender_company_name,
    )
    subject = (
        _('Компанія {name} надіслала вам документи на підпис').bind(name=subject_company_name)
        if should_sign
        else _('Компанія {name} надіслала вам документи').bind(name=subject_company_name)
    )

    context = {
        'invite_url': invite_url,
        'invite_button': _('Підписати документ') if should_sign else _('Переглянути документ'),
        'header_title': _('Почніть обмінюватися документами онлайн - легко і просто'),
        'recipient_name': _('Друзі'),
        'document_title': document_title,
        'should_sign': should_sign,
        'sender_company_edrpou': sender_company_edrpou,
        'sender_company_name': sender_company_name,
        'recipient_company_edrpou': recipient_company_edrpou,
    }

    await emailing.send_email(
        recipient_mixed=recipient_email,
        subject=subject,
        template_name='invite_document',
        context=context,
        language=recipient_language,
    )


async def send_document_rejected_email(
    user: AuthUser | User,
    user_company_config: CompanyConfig,
    document: Document,
    recipients: list[User],
    comment: str | None,
) -> None:
    """
    Notify user after document has been rejected by partner/coworker
    """

    url_tail = f'/documents/{document.id}'
    for recipient in recipients:
        if not can_receive_notification(
            recipient=recipient, notification_type=NotificationType.reject
        ):
            continue

        if not recipient.email:
            logger.info(
                'Recipient email is not set',
                extra={'role_id': recipient.role_id, 'user_id': recipient.id},
            )
            continue

        get_params = utm_params.DOCUMENT_REJECTED.copy()
        get_params[COMPANY_ID_REQUEST_KEY] = recipient.company_id
        url = build_url('app', tail=url_tail, get=get_params)

        user_email: str | None = user.email
        if user_company_config.hide_sender_email_in_notifications:
            user_email = None

        context = {
            'user_email': user_email,
            'user_name': user.full_name,
            'document_url': url,
            'document_title': document.title,
            'recipient_company_name': recipient.company_name,
            'recipient_company_edrpou': recipient.company_edrpou,
            'initiator_company_name': user.company_name,
            'initiator_company_edrpou': user.company_edrpou,
            'reject_message': comment,
            'utm_params': utm_params.DOCUMENT_REJECTED,
        }

        subject = _('Ваш партнер відхилив документ')
        if document.is_internal:
            subject = _('Ваш співробітник відхилив документ')

        await emailing.send_email(
            recipient_mixed=recipient.email,
            subject=subject,
            template_name='notification_reject',
            context=context,
            language=recipient.language,
        )


async def send_archive_link(
    arch_ids: list[str],
    user: User,
    company: DBRow,
    company_has_pro: bool,
    request_data: dict[str, str],
) -> None:
    from .utils import convert_gen_arch_params_to_text_dict

    if not user.email:
        logger.info(
            'User email is not set',
            extra={'role_id': user.role_id, 'user_id': user.id},
        )
        return

    file_download_links = [
        build_url(
            route='downloads.download_multi_archive',
            archive_id=arch_id,
            get={
                COMPANY_ID_REQUEST_KEY: company.id,
            },
        )
        for arch_id in arch_ids
    ]
    request_data = convert_gen_arch_params_to_text_dict(request_data)
    subject = _('Архів документів компанії {edrpou}, {name}').bind(
        edrpou=company.edrpou, name=company.full_name or company.name
    )
    template_name = 'download_archive'

    youtube_link = (
        f'{YOUTUBE_URL}?utm_medium=email&utm_source=youtube_link&utm_campaign=download_archive'
    )

    context = {
        'download_links': file_download_links,
        'recipient': user,
        'company': company,
        'request_data': request_data,
        'image_sofia': build_static_url('images/sofia.png'),
        'youtube_link': youtube_link,
        'enable_pro_advice_block': get_flag(FeatureFlags.ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS)
        and not company_has_pro,
    }
    await emailing.send_email(
        recipient_mixed=user.email,
        subject=subject,
        template_name=template_name,
        context=context,
    )


async def send_documents_to_signer(
    signer: StrDict,
    coworker: StrDict,
    documents: list[Document],
    company_has_pro: bool,
    more_documents_count: int | None = None,
) -> None:
    one_document = len(documents) == 1

    if not can_receive_notification(signer, NotificationType.inbox):
        return

    utm_dict: dict[str, Any] = (
        utm_params.ONE_DOCUMENT_FOR_SIGNERS
        if one_document
        else utm_params.MULTIPLE_DOCUMENTS_FOR_SIGNERS
    )
    url_options = {
        **utm_dict,
        'folder_id': DocumentFolder.wait_my_sign.value,
        COMPANY_ID_REQUEST_KEY: signer['company_id'],
    }

    documents_info = [
        {
            'id': doc.id,
            'title': doc.title,
            'url': build_url('app', tail=f'/documents/{doc.id}', get=url_options),
        }
        for doc in documents
    ]

    tail = f'/documents/{documents[0].id}' if one_document else '/documents'
    urls = {
        'more': build_url('app', tail='/documents', get=url_options),
        'to_documents': build_url('app', tail=tail, get=url_options),
    }

    youtube_link = f'{YOUTUBE_URL}?utm_medium=email&utm_source=youtube_link&utm_campaign=to_signer'

    context = {
        'coworker': coworker,
        'signer': signer,
        'more_files_count': more_documents_count,
        'files': documents_info,
        'urls': urls,
        'recipient_company_edrpou': signer['company_edrpou'],
        'recipient_company_name': signer['company_name'],
        'image_sofia': build_static_url('images/sofia.png'),
        'youtube_link': youtube_link,
        'enable_pro_advice_block': get_flag(FeatureFlags.ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS)
        and not company_has_pro,
    }

    if one_document:
        subject = _('Ви отримали документ на підпис')
    else:
        subject = _('Ви отримали документи на підпис')

    await emailing.send_email(
        recipient_mixed=signer['email'],
        subject=subject,
        template_name='documents_for_signer',
        context=context,
        language=signer.get('language'),
    )


async def send_document_delete_request_notifications(
    initiator_role_id: str,
    message: str,
    document_ids: list[str],
) -> None:
    await services.kafka.send_record(
        topic=topics.SEND_DELETE_REQUEST_NOTIFICATIONS,
        value={
            'initiator_role_id': initiator_role_id,
            'message': message,
            'document_ids': document_ids,
        },
    )


async def send_reject_documents_notifications(
    initiator_role_id: str | None,
    document_ids: list[str],
    comment: str | None,
) -> None:
    await services.kafka.send_records(
        topic=topics.SEND_REJECT_DOCUMENT_NOTIFICATIONS,
        values=[
            {
                'initiator_role_id': initiator_role_id,
                'document_id': document_id,
                'comment': comment,
            }
            for document_id in document_ids
        ],
    )


def build_delete_request_sender_line(
    company_name: str | None,
    company_edrpou: str,
    user_email: str | None,
    company_config: CompanyConfig,
) -> str:
    """
    Build company line for delete request email sender. Examples:
    - "Company Name (ЄДРПОУ: 12345678)"
    - "Company Name (ЄДРПОУ: 12345678, Email: <EMAIL>
    - "(ЄДРПОУ: 12345678, Email: <EMAIL>)
    """
    _tax_label = _('ЄДРПОУ/ІПН')
    _company_name = company_name or ''
    _company_edrpou = company_edrpou or ''
    _user_email = user_email or ''

    if company_config.hide_sender_email_in_notifications:
        _user_email = ''

    if _user_email:
        return f'{_company_name} ({_tax_label}: {_company_edrpou}, Email: {_user_email})'

    return f'{_company_name} ({_tax_label}: {_company_edrpou})'


# TODO: remove `conn` for preventing extra requests to database
async def send_document_delete_request_accepted_emails(
    conn: DBConnection,
    user: User,
    all_documents: list[DBRow | Document],
    delete_requests: list[DBRow],
) -> None:
    """
    Send email notifications that document deletion request has been accepted:
     - each initiator will receive email with information about accepted deletion
     - user that approved deletion will receive email with confirmation about deletion
    """
    if not delete_requests:
        return

    deleter = user

    grouped_requests: defaultdict[str, list[DBRow]] = defaultdict(list)
    for item in delete_requests:
        group_key = f'{item.initiator_role_id}_{",".join(item.recipients_emails or [])}'
        grouped_requests[group_key].append(item)

    initiators = await select_users(
        conn=conn,
        roles_ids=[item.initiator_role_id for item in delete_requests],
    )
    initiators_map = {item.role_id: item for item in initiators if item}
    deleter_config = await get_company_config(conn, company_id=user.company_id)

    # Send email for each initiator
    documents_map = {doc.id: doc for doc in all_documents}
    for val in grouped_requests.values():
        initiator = initiators_map.get(val[0].initiator_role_id)

        if not (initiator and can_receive_notification(initiator)):
            continue

        if not initiator.email:
            continue

        documents = [documents_map[item.document_id] for item in val]
        documents_data = [{'document': doc} for doc in documents]

        if len(documents) == 1:
            subject = _('[ВАЖЛИВО] Контрагент погодив видалення документу з сервісу Вчасно')
        else:
            subject = _('[ВАЖЛИВО] Контрагент погодив видалення документів з сервісу Вчасно')

        deleter_line = build_delete_request_sender_line(
            company_name=deleter.company_name,
            company_edrpou=deleter.company_edrpou,
            user_email=deleter.email,
            company_config=deleter_config,
        )
        context = {
            'header': subject,
            'deleter_line': deleter_line,
            'documents_data': documents_data,
        }
        await emailing.send_email(
            recipient_mixed=initiator.email,
            subject=subject,
            template_name='delete_request_accept',
            context=context,
            language=initiator.language,
        )

    # One additional email for user that approved deletion
    if not can_receive_notification(deleter):
        return

    if not deleter.email:
        return

    documents_data = [{'document': doc} for doc in all_documents]
    if len(all_documents) == 1:
        subject = _('[ВАЖЛИВО] Документ погоджено для видалення з сервісу Вчасно')
    else:
        subject = _('[ВАЖЛИВО] Документи погоджено для видалення з сервісу Вчасно')

    context = {
        'header': subject,
        'documents_data': documents_data,
    }
    await emailing.send_email(
        recipient_mixed=deleter.email,
        subject=subject,
        template_name='delete_request_accept_self',
        context=context,
        language=deleter.language,
    )


async def send_reject_delete_request_emails(
    conn: DBConnection,
    user: User,
    delete_requests: list[DBRow],
) -> None:
    if not delete_requests:
        return

    grouped_requests: defaultdict[str, list[DBRow]] = defaultdict(list)
    for item in delete_requests:
        group_key = f'{item.initiator_role_id}_{",".join(item.recipients_emails or [])}'
        grouped_requests[group_key].append(item)

    initiators = await select_users(
        conn=conn,
        roles_ids=[item.initiator_role_id for item in delete_requests],
    )
    initiators_map = {item.role_id: item for item in initiators if item}
    rejector = await select_user(conn, role_id=user.role_id)
    if not rejector:
        return

    rejector_config = await get_company_config(conn, company_id=user.company_id)
    all_documents = await select_documents(
        conn=conn,
        documents_ids=[item.document_id for item in delete_requests],
    )
    documents_map = {doc.id: doc for doc in all_documents}
    for val in grouped_requests.values():
        initiator = initiators_map.get(val[0].initiator_role_id)

        if not (initiator and can_receive_notification(initiator)):
            continue

        if not initiator.email:
            continue

        documents = [documents_map[item.document_id] for item in val]
        documents_data = []
        for doc in documents:
            documents_data.append(
                {
                    'document': doc,
                    'url': build_url(
                        route='app',
                        tail=f'/documents/{doc.id}',
                        get={COMPANY_ID_REQUEST_KEY: initiator.company_id},
                    ),
                }
            )

        if len(documents) == 1:
            subject = _('[ВАЖЛИВО] Контрагент відхилив видалення документу з сервісу Вчасно')
        else:
            subject = _('[ВАЖЛИВО] Контрагент відхилив видалення документів з сервісу Вчасно')

        rejector_line = build_delete_request_sender_line(
            company_name=rejector.company_name,
            company_edrpou=rejector.company_edrpou,
            company_config=rejector_config,
            user_email=rejector.email,
        )

        context = {
            'recipient_company_name': initiator.company_name,
            'recipient_company_edrpou': initiator.company_edrpou,
            'rejector_line': rejector_line,
            'documents_data': documents_data,
            'reject_message': val[0].reject_message,
            'header': subject,
        }
        await emailing.send_email(
            recipient_mixed=initiator.email,
            subject=subject,
            template_name='delete_request_reject',
            context=context,
            language=initiator.language,
        )


async def send_signed_by_coworkers_documents(
    *,
    email: str,
    company_id: str,
    first_name: str | None,
    company_edrpou: str,
    company_name: str | None,
    documents: list[dict[str, str]],
    as_sign_assigner: bool,
    as_document_uploader: bool,
) -> None:
    """
    Email about fully signed documents for sign process initiator

    :param email: receiver email
    :param company_id: receiver role id
    :param first_name: receiver first name
    :param company_edrpou: receiver company edrpou will be used when
        company_name will not be provided
    :param company_name: receiver company name
    :param documents: list of dict with keys `id` and `title`
    :param as_sign_assigner: if True, email will be for sign process initiator
    :param as_document_uploader: if True, email will be for document uploader
    """

    company_title = build_company_label(
        edrpou=company_edrpou,
        company_name=company_name,
    )
    if as_sign_assigner and as_document_uploader:
        subject = _(
            'Співробітники компанії {company_title} підписали документи, які ви призначили '
            'або завантажили'
        ).bind(company_title=company_title)
    elif as_sign_assigner:
        subject = _(
            'Співробітники компанії {company_title} підписали призначені вами документи'
        ).bind(company_title=company_title)
    elif as_document_uploader:
        subject = _(
            f'Співробітники компанії {company_title} підписали завантажені вами документи'
        ).bind(company_title=company_title)
    else:
        # Not expected case when recipient is nor sign process initiator, nor document uploader
        subject = _('Співробітники компанії {company_title} підписали документи').bind(
            company_title=company_title
        )

    prepared_documents = [
        {
            'title': document['title'],
            'url': build_url(
                route='app',
                tail=f'/documents/{document["id"]}',
                get={COMPANY_ID_REQUEST_KEY: company_id},
            ),
        }
        for document in documents
    ]

    context = {
        'first_name': first_name,
        'subject': subject,
        'documents': prepared_documents,
    }

    await emailing.send_email(
        recipient_mixed=email,
        subject=subject,
        template_name='signed_by_coworkers_documents',
        context=context,
    )


async def send_about_document_access(
    *,
    user: User,
    coworker_email: str,
    document_title: str,
    document_id: str,
    comment: str,
) -> None:
    """Email about opening access to document by coworker"""

    if not user.can_receive_access_to_doc:
        logger.info(
            'Email not sent, because role can not receive notifications about new documents',
            extra={
                'recipient_email': user.email,
                'company_edrpou': user.company_edrpou,
            },
        )
        return

    if not user.email:
        logger.info(
            'User email is not set',
            extra={'recipient_email': user.email, 'company_edrpou': user.company_edrpou},
        )
        return

    document_url = build_url(
        route='app',
        tail=f'/documents/{document_id}',
        get={COMPANY_ID_REQUEST_KEY: user.company_id},
    )

    company_name = build_company_label(
        edrpou=user.company_edrpou,
        company_name=user.company_name,
    )
    context = {
        'first_name': user.first_name,
        'coworker_email': coworker_email,
        'company_name': company_name,
        'company_edrpou': user.company_edrpou,
        'comment': comment,
        'document_title': document_title,
        'document_url': document_url,
    }

    await emailing.send_email(
        recipient_mixed=user.email,
        subject=_('Ви отримали доступ до документу'),
        template_name='notification_document_access',
        context=context,
    )
