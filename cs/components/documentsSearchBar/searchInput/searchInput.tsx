import React, { <PERSON><PERSON><PERSON>, <PERSON>, MouseEvent, SetStateAction } from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import cn from 'classnames';
import { t } from 'ttag';

import { SearchKey, SearchOptions } from '../types';

import { convertSearchOptionsToText } from './utils';

import IconButton from '../../ui/iconButton/iconButton';

import SearchOption from '../searchOption/searchOption';
import SearchInputWrapper from './searchInputWrapper';

import CloseSvg from '../images/close.svg';
import DownArrow from '../images/downarrow.svg';
import UpArrow from '../images/uparrow.svg';

import css from './searchInput.css';

interface Props {
    options: SearchOptions;
    expanded: boolean;
    onSearch: () => void;
    onRemoveOption: (key: SearchKey, index: number) => void;
    onRemoveOptions: () => void;
    openForm: () => void;
    closeForm: () => void;
    inputValue: string;
    setInputValue: Dispatch<SetStateAction<string>>;
}

const SearchInput: FC<React.PropsWithChildren<Props>> = ({
    options,
    expanded,
    onSearch,
    onRemoveOption,
    onRemoveOptions,
    openForm,
    closeForm,
    inputValue,
    setInputValue,
}) => {
    const searchKeys = Object.keys(options) as SearchKey[];
    const isEmpty = searchKeys.every((key) => options[key].length === 0);
    const removeClasses = cn(css.removeIcon, {
        [css.hidden]: isEmpty && !inputValue.length,
    });

    const arrowIcon = expanded ? (
        <div className={cn(css.icon, css.iconArrow)} onClick={closeForm}>
            <IconButton sizeSmall svg={UpArrow} />
        </div>
    ) : (
        <BlackTooltip title={t`Відкрити розширений пошук`} disableInteractive>
            <div
                className={cn(css.icon, css.iconArrow)}
                onClick={openForm}
                data-qa="qa_search_settings"
            >
                <IconButton sizeSmall svg={DownArrow} />
            </div>
        </BlackTooltip>
    );

    const handleChange = (event: React.FormEvent<HTMLInputElement>): void => {
        setInputValue(event.currentTarget.value);
    };

    const handleRemoveOptions = (evt: MouseEvent) => {
        evt.stopPropagation();
        setInputValue('');
        onRemoveOptions();
    };

    if (isEmpty) {
        return (
            <SearchInputWrapper expanded={expanded}>
                <form className={css.emptySearchInput} onSubmit={onSearch}>
                    <input
                        className={css.emptySearchInputPlaceholder}
                        value={inputValue}
                        onChange={handleChange}
                        placeholder={t`Введіть ЄДРПОУ, назву, email компанії`}
                        data-qa="qa_search_input"
                    />
                    <div className={removeClasses}>
                        <IconButton
                            svg={CloseSvg}
                            onClick={handleRemoveOptions}
                        />
                    </div>
                    {arrowIcon}
                </form>
            </SearchInputWrapper>
        );
    }

    const handleRemoveOption = (
        evt: MouseEvent,
        key: SearchKey,
        number: number,
    ) => {
        evt.stopPropagation();
        onRemoveOption(key, number);
    };

    const optionClasses = cn(css.searchOptions, {
        [css.expanded]: expanded,
    });
    const textOptions = convertSearchOptionsToText(options);

    return (
        <SearchInputWrapper expanded={expanded}>
            <div className={optionClasses}>
                {searchKeys.map((key) =>
                    textOptions[key].map((text: string, index: number) => {
                        const onDelete = (evt: MouseEvent) =>
                            handleRemoveOption(evt, key, index);

                        return (
                            <div key={index} className={css.searchOption}>
                                <SearchOption text={text} onDelete={onDelete} />
                            </div>
                        );
                    }),
                )}
            </div>
            <div className={removeClasses}>
                <IconButton svg={CloseSvg} onClick={handleRemoveOptions} />
            </div>
            {arrowIcon}
        </SearchInputWrapper>
    );
};

export default SearchInput;
