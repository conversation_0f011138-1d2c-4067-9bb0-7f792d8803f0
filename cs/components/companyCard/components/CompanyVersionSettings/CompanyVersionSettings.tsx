import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FlexBox, Paragraph, Text } from '@vchasno/ui-kit';

import companyCardActionCreators from 'components/companyCard/companyCardActionCreators';
import { getCurrentCompanyVersionSettings } from 'selectors/app.selectors';
import { t } from 'ttag';
import RadioGroup from 'ui/RadioGroup';

import { VersionSettings } from '../../../../types/user';

interface Option {
    value: VersionSettings['review_flow'];
    label: string;
}

const options: Option[] = [
    {
        value: 'continued',
        label: t`Продовжити розпочате погодження`,
    },
    {
        value: 'restarted',
        label: t`Почати погодження спочатку`,
    },
];

const CompanyVersionSettings: React.FC = () => {
    const dispatch = useDispatch();
    const archiveSettings = useSelector(getCurrentCompanyVersionSettings);
    const [reviewFlow, setReviewFlow] = React.useState<
        VersionSettings['review_flow']
    >(options[0].value);

    useEffect(() => {
        if (archiveSettings.review_flow) {
            setReviewFlow(archiveSettings.review_flow);
        }
    }, [archiveSettings.review_flow]);

    const onChangeConfig = (review_flow: VersionSettings['review_flow']) =>
        dispatch(
            companyCardActionCreators.onChangeAdditionalConfig(
                {
                    version_settings: {
                        review_flow,
                    },
                },
                {
                    version_settings: {
                        ...archiveSettings,
                        review_flow,
                    },
                },
            ),
        );

    return (
        <FlexBox direction="column" gap={20}>
            <Paragraph>
                <Text type="secondary">{t`Як діяти з погодженням після додавання нової внутрішньої версії документа?`}</Text>
            </Paragraph>
            <RadioGroup
                value={reviewFlow}
                options={options}
                onChange={onChangeConfig}
            />
        </FlexBox>
    );
};

export default CompanyVersionSettings;
