import logging

from aiohttp import web

from app.banner import utils
from app.banner.db import select_active_banners, select_planned_banners
from app.banner.enums import BannerStatus
from app.lib.datetime_utils import local_now
from app.lib.types import DataDict
from app.services import services


async def activate_banner(app: web.Application, _: DataDict, logger: logging.Logger) -> None:
    """
    Active/Deactivate banners according to their start and end dates
    """
    now = local_now()

    async with services.db.acquire() as conn:
        active_banners = await select_active_banners(conn)

        # Hide expired banners
        for banner in active_banners:
            if banner.end_date and banner.end_date < now:
                await utils.update_banner_status(
                    conn=conn,
                    banner_id=banner.id_,
                    status=BannerStatus.hidden,
                )

        # Activate planned banners
        planned_banners = await select_planned_banners(conn, now)
        for banner in planned_banners:
            await utils.update_banner_status(
                conn=conn,
                banner_id=banner.id_,
                status=BannerStatus.active,
            )
