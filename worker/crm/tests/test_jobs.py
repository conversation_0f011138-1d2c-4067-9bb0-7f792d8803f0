import logging
from http import HTT<PERSON>tatus

import pytest
from vchasno_crm.enums import CRMBillType

from app.auth.db import insert_role, select_company_by_edrpou, update_role
from app.auth.enums import RoleStatus
from app.auth.types import Role
from app.auth.utils import create_company, update_company_by_edrpou
from app.billing.enums import AccountRate
from app.billing.tests.test_billing_views import CREATE_COMPANY_RATE_URL
from app.billing.tests.utils import (
    INTEGRATION_PER_DOCUMENT_PRICE,
    INTEGRATION_PRICE_TOV,
    PRO_PRICE_TOV,
    START_PRICE_TOV,
    get_bill,
)
from app.crm.utils import (
    send_company_to_crm,
    send_role_to_crm,
    send_user_to_crm,
)
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import local_now
from app.lib.enums import User<PERSON><PERSON>
from app.tests.common import (
    BILLS_URL,
    SUPER_ADMIN_EDRPOU,
    TEST_COMPANY_NAME,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_UUID,
    prepare_auth_headers,
    prepare_client,
    set_company_config,
)

logger = logging.getLogger(__name__)


async def create_update_pending_invited_role(
    conn: DBConnection,
    *,
    edrpou: str,
    user_id: str,
    existed_role: Role | None,
) -> DBRow:
    """
    For existed user and invited not by coworker create company if not exists
    and create/update pending role with user role
    """
    # Pending role for others
    company_id = await create_company(
        conn=conn,
        data={'edrpou': edrpou},
        ignore_existing=True,
    )
    if existed_role:
        role = await update_role(
            conn=conn,
            role_id=existed_role.id_,
            data={'status': RoleStatus.pending},
        )
    else:
        role = await insert_role(
            conn,
            {
                'company_id': company_id,
                'status': RoleStatus.pending,
                'user_id': user_id,
                'user_role': UserRole.user.value,
            },
        )

    # NOTE: remember to schedule async jobs about a potential new company outside of the transaction
    # in which this function is called

    return role


@pytest.mark.parametrize('is_new_user', [True, False])
@pytest.mark.parametrize('is_real_id, expected', [(True, 1), (False, 0)])
async def test_send_user_to_crm(
    aiohttp_client, crm_box, test_flags, is_real_id, is_new_user, expected
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    assert len(crm_box) == 0

    user_id = user.id
    if not is_real_id:
        user_id = TEST_UUID

    await send_user_to_crm(user_id)

    assert len(crm_box) == expected


@pytest.mark.parametrize('is_real_id, expected', [(True, 1), (False, 0)])
async def test_send_role_to_crm(aiohttp_client, crm_box, test_flags, is_real_id, expected):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    assert len(crm_box) == 0

    role_id = user.role_id
    if not is_real_id:
        role_id = TEST_UUID

    await send_role_to_crm(role_id)

    assert len(crm_box) == expected


@pytest.mark.parametrize(
    'params, expected',
    [
        pytest.param(
            {'services': [{'type': 'rate', 'rate': AccountRate.latest_start()}]},
            {
                'purpose': (
                    'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                    'з додатковим функціоналом згідно тарифу "Старт" на 1 рік'
                ),
                'service_details': 'Старт',
                'status': 'new',
                'type': 'start_tov',
                'period': 'twelve_months',
                'amount': START_PRICE_TOV,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='start',
        ),
        pytest.param(
            {
                'services': [{'type': 'rate', 'rate': AccountRate.latest_pro()}],
            },
            {
                'purpose': (
                    'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                    'з додатковим функціоналом згідно тарифу "Професійний" на 1 рік'
                ),
                'service_details': 'Професійний',
                'status': 'new',
                'type': 'pro_tov',
                'period': 'twelve_months',
                'amount': PRO_PRICE_TOV,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='pro',
        ),
        pytest.param(
            {
                'services': [{'type': 'rate', 'rate': AccountRate.archive_small}],
            },
            {
                'purpose': (
                    'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                    'для роботи з електронними документами згідно тарифу "Архів +1000" на 1 рік'
                ),
                'service_details': 'Архів +1000',
                'status': 'new',
                'type': 'archive_small',
                'period': 'twelve_months',
                'amount': 480.0,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='archive_small',
        ),
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.integration.value,
                    },
                    {
                        'type': 'units',
                        'units': 100,
                    },
                ]
            },
            {
                'purpose': (
                    'Доступ до інтеграції для обміну електронними документами між Онлайн-сервісом '
                    'електронного документообігу Вчасно та обліковою системою Користувача (100) '
                    'документів'
                ),
                'service_details': 'Інтеграція',
                'status': 'new',
                'type': 'integration',
                'period': 'twelve_months',
                'amount': INTEGRATION_PRICE_TOV + 100 * INTEGRATION_PER_DOCUMENT_PRICE,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='combined_bill_integration_with_documents',
            marks=pytest.mark.skip(
                reason='Temporarily disabled this bill type due to accounting department issues'
            ),
        ),
        pytest.param(
            {
                'services': [
                    {'type': 'units', 'units': 100},
                ]
            },
            {
                'purpose': (
                    'Надання доступу до онлайн-сервісу електронного документообігу (100 документів)'
                ),
                'service_details': 'Документи',
                'status': 'new',
                'type': 'documents',
                'period': 'twelve_months',
                'amount': 100 * INTEGRATION_PER_DOCUMENT_PRICE,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='top_up_balance',
        ),
        pytest.param(
            {
                'services': [
                    {'type': 'rate', 'rate': AccountRate.latest_start()},
                    {'type': 'rate', 'rate': AccountRate.archive_small},
                ]
            },
            {
                'purpose': (
                    'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                    'з додатковим функціоналом згідно тарифу "Старт" на 1 рік'
                ),
                'service_details': 'Старт',
                'status': 'new',
                'type': 'start_tov',
                'period': 'twelve_months',
                'amount': START_PRICE_TOV + 480.0,
                'documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
            },
            id='web_and_archive',
        ),
    ],
)
async def test_send_bill_to_crm(aiohttp_client, crm_box, params: dict, expected: dict):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        enable_pro_functionality=False,
    )

    response = await client.post(
        path=BILLS_URL,
        json={
            'date_from': local_now().strftime('%Y-%m-%d'),
            'email': user.email,
            'edrpou': user.company_edrpou,
            'name': 'test company name',
            **params,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    response_json = await response.json()
    bill_id = response_json['bill_id']

    bill = await get_bill(bill_id=bill_id)

    assert len(crm_box) == 1
    event: dict = crm_box[0]

    # Common for all types of bills
    assert event['name'].startswith('ВЧ-0000')
    assert event['transaction_id'] is not None
    assert event['date_created'] == bill.date_created.isoformat()
    assert event['company']['edrpou'] == user.company_edrpou
    assert event['company']['date_created'] is not None
    assert event['company']['creator']['email'] == user.email
    assert event['company']['creator']['mobile_phone'] is not None

    assert expected == {
        'purpose': event['purpose'],
        'service_details': event['service_details'],
        'status': event['status'],
        'type': event['type'],
        'period': event['period'],
        'amount': event['amount'],
        'documents_cost': event['documents_cost'],
    }


async def test_send_crm_company(aiohttp_client, test_flags, crm_box):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    assert len(crm_box) == 0

    async with app['db'].acquire() as conn:
        await create_update_pending_invited_role(
            conn,
            edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            user_id=user.id,
            existed_role=None,
        )

        # Do not send company without name to CRM
        assert len(crm_box) == 0

        await update_company_by_edrpou(
            conn=conn,
            edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            data={'name': TEST_COMPANY_NAME},
        )
        company = await select_company_by_edrpou(conn, TEST_DOCUMENT_EDRPOU_RECIPIENT)
        await send_company_to_crm(company.id)
        assert len(crm_box) == 1


@pytest.mark.parametrize(
    'rate, expected',
    [
        (AccountRate.integration, CRMBillType.integration),
        # Trials
        (AccountRate.pro, CRMBillType.unlimited),
        (AccountRate.pro_plus_2022_12, CRMBillType.pro_plus),
        (AccountRate.integration_trial, CRMBillType.trial_api),
        (AccountRate.pro_plus_trial_2022_12, CRMBillType.trial_web),
    ],
)
async def test_send_rate_to_crm(aiohttp_client, crm_box, rate, expected):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    headers = prepare_auth_headers(super_admin)

    response = await client.post(
        CREATE_COMPANY_RATE_URL,
        json={
            'rate': rate,
            'amount': 2000,
            'company_id': super_admin.company_id,
            'start_date': '2019-12-19',
            'end_date': '3019-12-19',
        },
        headers=headers,
    )
    assert response.status == 201
    assert len(crm_box) == 1
    assert crm_box[0]['name'] == expected.value
