import { yupResolver } from '@hookform/resolvers/yup';

import { STATIC_ERROR_PHRASES } from 'components/auth/constants';
import { EMAIL_MAX_LENGTH, EMAIL_PATTERN } from 'lib/constants';
import { t } from 'ttag';
import * as yup from 'yup';

import {
    EmailVerifyPopupField,
    ErrorResponseBody,
    PasswordVerifyPopupField,
    TwoFaCodeVerifyPopupField,
} from './types';

export const getErrorMessage = (error: ErrorResponseBody): string => {
    const passwordMessage = error.details?.password;

    return passwordMessage ? passwordMessage : error.reason;
};

const emailChangeConfirmPopupValidationSchema: yup.ObjectSchema<
    EmailVerifyPopupField | PasswordVerifyPopupField
> = yup
    .object({
        email: yup
            .string()
            .default('')
            .trim()
            .required()
            .max(EMAIL_MAX_LENGTH, STATIC_ERROR_PHRASES.LOGIN_MAX_LENGTH_ERROR)
            .matches(EMAIL_PATTERN, STATIC_ERROR_PHRASES.EMAIL_ERROR),
        password: yup.string().trim().required(),
    })
    .required();

const code2faValidationSchema: yup.ObjectSchema<TwoFaCodeVerifyPopupField> = yup
    .object()
    .shape({
        code: yup
            .string()
            .trim()
            .matches(/^\d+$/, `${t`Код має містити лише цифр`}`)
            .required(),
    })
    .required();

export const emailChangeConfirmPopupResolver = yupResolver(
    emailChangeConfirmPopupValidationSchema,
);

export const code2faConfirmPopupResolver = yupResolver(code2faValidationSchema);
