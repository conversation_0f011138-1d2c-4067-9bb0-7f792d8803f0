import { CommentForm } from 'components/DocumentsUploader/CommentSection/types';
import {
    DocumentAccessFlow,
    DocumentsUploadForm,
    FilesFields,
    MultiUpdateMode,
} from 'components/DocumentsUploader/types';
import { DocumentAccessLevel } from 'services/documents/ts/types';

interface TagUpdateMode {
    tagsUpdateMode: MultiUpdateMode;
    removeAllTags: boolean;
}

interface EmployeeUpdateMode {
    employeeViewUpdateMode: MultiUpdateMode;
    removeAllViewers: boolean;
}

interface AdditionalParametersUpdateMode {
    additionalParametersUpdateMode: MultiUpdateMode;
    removeAllParameters: boolean;
}

interface DocumentAccessFlowForMultiEdit {
    // DOCUMENTS_PRIVATE_ACCESS
    accessLevel: DocumentAccessLevel | null;
}

export type MultiEditDocumentForm = Omit<
    DocumentsUploadForm,
    keyof (FilesFields & CommentForm & DocumentAccessFlow)
> &
    TagUpdateMode &
    EmployeeUpdateMode &
    AdditionalParametersUpdateMode &
    DocumentAccessFlowForMultiEdit;
