import React, { FC, ReactNode } from 'react';
import { Field, Form, FormRenderProps } from 'react-final-form';
import { connect, useSelector } from 'react-redux';

import { PHONE_PREFIX } from 'lib/constants';
import { getIsCurrentCompanyUnPayedFOP } from 'selectors/app.selectors';
import { t } from 'ttag';

import { Thunk } from '../../types';
import { StoreState } from '../../types/store';

import Button from '../ui/button/button';
import Input from '../ui/input/input';
import Popup from '../ui/popup/popup';
import PseudoLink from '../ui/pseudolink/pseudolink';

import {
    Add2FAStep,
    handle2FASubmit,
    onChangePhone as onChangePhoneAction,
    onClose2FAPopup,
    onResendCode as onResendCodeAction,
} from './add2FAPopupReducer';

import css from './Add2FAPopup.css';

interface StateProps {
    isActive: boolean;
    step: Add2FAStep;
}

interface DispatchProps {
    onClose: typeof onClose2FAPopup;
    onSubmit: (fields: Record<string, string>) => Thunk;
    onResendCode: (fields: Record<string, string>) => Promise<void>;
    onChangePhone: () => Thunk;
}

const Add2FAPopup: FC<React.PropsWithChildren<StateProps & DispatchProps>> = ({
    isActive,
    step,
    onClose,
    onSubmit,
    onResendCode,
    onChangePhone,
}) => {
    const isCurrentCompanyUnPayedFop = useSelector(
        getIsCurrentCompanyUnPayedFOP,
    );
    let title = (
        <h2 className={css.title}>
            {t`Увімкніть двофакторну аутентифікацію для доступу до компанії`}
        </h2>
    );

    let renderText = (_: FormRenderProps): ReactNode =>
        t`Увімкніть двофакторну аутентифікацію для доступу до компанії`;

    let renderFormContent = (formProps: FormRenderProps) => (
        <>
            <Field name="phone">
                {({ input, meta }) => (
                    <>
                        <label className={css.label} htmlFor="add2FA_phone">
                            {t`Ваш телефон`}
                        </label>
                        <div className={css.field}>
                            <Input
                                id="add2FA_phone"
                                type="tel"
                                placeholder="+38 0 _ _   _ _ _   _ _   _ _"
                                value={input.value}
                                onChange={input.onChange}
                                onFocus={() =>
                                    input.onChange(input.value || PHONE_PREFIX)
                                }
                                error={meta.error && meta.submitFailed}
                                errorMessage={meta.error}
                            />
                        </div>
                    </>
                )}
            </Field>
            <Field name="password">
                {({ input, meta }) => (
                    <>
                        <label className={css.label} htmlFor="add2FA_password">
                            {t`Пароль облікового запису`}
                        </label>
                        <div className={css.field}>
                            <Input
                                id="add2FA_password"
                                name="password"
                                type="password"
                                value={input.value}
                                onChange={input.onChange}
                                error={meta.error && meta.submitFailed}
                                errorMessage={meta.error}
                            />
                        </div>
                    </>
                )}
            </Field>
            <div className={css.buttonsBlock}>
                <div className={css.button}>
                    <Button
                        type="submit"
                        theme="blue"
                        width="full"
                        disabled={
                            formProps.submitFailed &&
                            (formProps.hasValidationErrors ||
                                !formProps.dirtySinceLastSubmit)
                        }
                        isLoading={formProps.submitting}
                    >
                        {t`Увімкнути двофакторну`}
                    </Button>
                </div>
                <PseudoLink onClick={onClose}>{t`Скасувати`}</PseudoLink>
            </div>
            {formProps.submitError &&
                formProps.submitFailed &&
                !formProps.dirtySinceLastSubmit && (
                    <div className={css.error}>{formProps.submitError}</div>
                )}
        </>
    );

    let validate = (fields: Record<string, string>) => {
        const errors: Record<string, string> = {};

        if (!fields.phone) {
            errors.phone = t`Вкажіть телефон`;
        }

        if (!fields.password) {
            errors.password = t`Вкажіть пароль`;
        }

        return errors;
    };

    if (step === 'SUBMIT_CODE') {
        renderFormContent = (formProps: FormRenderProps) => (
            <>
                <Field name="code">
                    {({ input, meta }) => (
                        <>
                            <label className={css.label} htmlFor="add2FA_code">
                                {t`Код підтвердження телефону`}
                            </label>
                            <div className={css.field}>
                                <Input
                                    id="add2FA_code"
                                    type="text"
                                    value={input.value}
                                    onChange={input.onChange}
                                    error={meta.error && meta.submitFailed}
                                    errorMessage={meta.error}
                                />
                            </div>
                        </>
                    )}
                </Field>
                <div>
                    <span>{t`Не отримали повідомлення?`}</span>{' '}
                    <PseudoLink
                        onClick={() => onResendCode(formProps.values)}
                    >{t`Надіслати повторно`}</PseudoLink>
                    <span>{', '}</span>
                    <PseudoLink
                        onClick={onChangePhone}
                    >{t`вказати інший телефон`}</PseudoLink>
                    <span>{t` або `}</span>
                    <PseudoLink
                        onClick={onClose}
                    >{t`скасувати підтвердження телефона`}</PseudoLink>
                </div>
                <div className={css.buttonsBlock}>
                    <div className={css.button}>
                        <Button
                            type="submit"
                            theme="blue"
                            width="full"
                            disabled={
                                formProps.submitFailed &&
                                (formProps.hasValidationErrors ||
                                    !formProps.dirtySinceLastSubmit)
                            }
                            isLoading={formProps.submitting}
                        >
                            {t`Підтвердити`}
                        </Button>
                    </div>
                    <PseudoLink onClick={onClose}>{t`Скасувати`}</PseudoLink>
                </div>
                {formProps.submitError &&
                    formProps.submitFailed &&
                    !formProps.dirtySinceLastSubmit && (
                        <div className={css.error}>{formProps.submitError}</div>
                    )}
            </>
        );

        validate = (fields: Record<string, string>) => {
            const errors: Record<string, string> = {};

            if (!fields.code) {
                errors.code = t`Вкажіть код підтвердження з повідомлення`;
            }

            return errors;
        };
    }

    if (step === 'SUCCESS') {
        title = (
            <h2 className={css.successTitle}>
                {t`Ви успішно увімкнули двофакторну аутентифікацію!`}
            </h2>
        );

        renderText = (formProps: FormRenderProps) => (
            <>
                <b>{t`Телефон:`}</b> {formProps.values.phone}
            </>
        );

        renderFormContent = () => (
            <div className={css.buttonsBlock}>
                <Button type="submit" theme="blue" width="full">
                    {t`Перейти на сторінку компанії`}
                </Button>
            </div>
        );

        validate = () => ({});
    }

    return (
        <Popup isCloseByButtonOnly active={isActive} onClose={onClose}>
            <div className={css.root}>
                {title}
                <Form onSubmit={onSubmit} validate={validate}>
                    {(formProps) => (
                        <>
                            <div className={css.text}>
                                {renderText(formProps)}
                            </div>
                            <form onSubmit={formProps.handleSubmit}>
                                {renderFormContent(formProps)}
                            </form>
                        </>
                    )}
                </Form>
                <div className={css.support}>
                    {t`Виникли труднощі? Зверніться до нас:`}{' '}
                    {isCurrentCompanyUnPayedFop ? (
                        <a href={`mailto:${config.SUPPORT_EMAIL}`}>
                            {config.SUPPORT_EMAIL}
                        </a>
                    ) : (
                        config.SUPPORT_PHONE
                    )}
                </div>
            </div>
        </Popup>
    );
};

const mapStateToProps = (state: StoreState) => ({
    ...state.add2FAPopup,
});

const mapDispatchToProps = {
    onClose: onClose2FAPopup,
    onSubmit: handle2FASubmit,
    onResendCode: onResendCodeAction,
    onChangePhone: onChangePhoneAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(Add2FAPopup);
