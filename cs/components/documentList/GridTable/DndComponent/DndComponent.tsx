import React from 'react';

import { CustomDragAndDropImageProps } from '@ag-grid-community/react';
import { FlexBox } from '@vchasno/ui-kit';

import DocumentSvg from 'icons/document.svg';
import { formatDate } from 'lib/date';
import {
    getDirectoryWordByCount,
    getDocumentWordByCount,
} from 'services/documents/ts/utils';
import Icon from 'ui/icon';

import DirectorySvg from '../DirectoryTitleRenderer/images/directory.svg';

import css from './DndComponent.css';

const MAX_LIST_NODES = 5;

const DndComponent: React.FC<CustomDragAndDropImageProps> = (props) => {
    const nodes = props.dragSource.getDragItem().rowNodes;

    if (!nodes) {
        return null;
    }

    const documentsCount =
        nodes?.filter((node) => !node.data.isDirectory).length || 0;
    const directoriesCount =
        nodes?.filter((node) => node.data.isDirectory).length || 0;

    return (
        <FlexBox direction="column" gap={0} className={css.container}>
            {nodes.length > MAX_LIST_NODES && (
                <>
                    {!!directoriesCount && (
                        <FlexBox align="center" gap={10} className={css.node}>
                            <FlexBox
                                gap={8}
                                align="center"
                                className={css.titleBlock}
                            >
                                <div className={css.icon}>
                                    <Icon glyph={DirectorySvg} />
                                </div>
                                <div className={css.title}>
                                    {`${directoriesCount} ${getDirectoryWordByCount(
                                        directoriesCount,
                                    )}`}
                                </div>
                            </FlexBox>
                        </FlexBox>
                    )}
                    {!!documentsCount && (
                        <FlexBox align="center" gap={10} className={css.node}>
                            <FlexBox
                                gap={8}
                                align="center"
                                className={css.titleBlock}
                            >
                                <div className={css.icon}>
                                    <Icon glyph={DocumentSvg} />
                                </div>
                                <div className={css.title}>
                                    {`${documentsCount} ${getDocumentWordByCount(
                                        documentsCount,
                                    )}`}
                                </div>
                            </FlexBox>
                        </FlexBox>
                    )}
                </>
            )}
            {nodes.length <= MAX_LIST_NODES &&
                nodes.map((rowNode) => (
                    <FlexBox
                        align="center"
                        key={rowNode.id}
                        gap={10}
                        className={css.node}
                    >
                        <div className={css.nodeElement}>
                            {formatDate(rowNode.data.dateCreated)}
                        </div>
                        <FlexBox
                            gap={8}
                            align="center"
                            className={css.titleBlock}
                        >
                            {rowNode.data.isDirectory && (
                                <div className={css.icon}>
                                    <Icon glyph={DirectorySvg} />
                                </div>
                            )}
                            <div className={css.title}>
                                {rowNode.data.name ||
                                    rowNode.data.type ||
                                    rowNode.data.title}
                            </div>
                        </FlexBox>
                    </FlexBox>
                ))}
        </FlexBox>
    );
};

export default DndComponent;
