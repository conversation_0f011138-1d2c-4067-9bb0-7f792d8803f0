"""Update sign_sessions table.

Revision ID: 120002bbe06a
Revises: 10b2a7aad967
Create Date: 2017-10-03 13:06:47.294575


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '120002bbe06a'
down_revision = '10b2a7aad967'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sign_sessions', 'on_cancel_url',
               existing_type=sa.TEXT(),
               nullable=True)
    op.alter_column('sign_sessions', 'on_finish_url',
               existing_type=sa.TEXT(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sign_sessions', 'on_finish_url',
               existing_type=sa.TEXT(),
               nullable=False)
    op.alter_column('sign_sessions', 'on_cancel_url',
               existing_type=sa.TEXT(),
               nullable=False)
    # ### end Alembic commands ###
