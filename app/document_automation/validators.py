from functools import partial
from uuid import uuid4

import trafaret as t
from aiohttp import web
from trafaret_validator import TrafaretValidator

from api.errors import AccessDenied, DoesNotExist, InvalidRequest, Object
from app.auth.types import User
from app.auth.validators import validate_coworkers_roles_ids, validate_user_permission
from app.billing.enums import CompanyPermission
from app.document_automation.conditions import flatten_context
from app.document_automation.db import select_automation, select_document_automation_template
from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.types import (
    ActivateAutomationCtx,
    CreateTemplateCtx,
    DocumentAutomationAutomationTemplate,
    SaveAssignedDocumentAutomationTemplateCtx,
    UpdateDocumentAutomationTemplateCtx,
)
from app.documents.validators import Reviewers, validate_document_exists
from app.documents_fields import validators as document_fields_validators
from app.groups.db import select_groups
from app.i18n import _
from app.lib import validators
from app.lib.database import DBConnection, DBRow
from app.lib.helpers import (
    contains_duplicates,
    grouped,
    unique_list,
)
from app.lib.helpers import unique_list_with_original_order as ordered_unique_list
from app.lib.types import DataDict
from app.profile.validators import validate_company_permission
from app.tags.utils import (
    get_deleted_document_template_tags_names,
    spit_existed_and_new_tags,
)
from app.tags.validators import (
    validate_tags_permissions,
)

ROLES_VALIDATOR = t.List(validators.UUID(), min_length=1) >> ordered_unique_list
GROUPS_VALIDATOR = t.List(validators.UUID(), min_length=1) >> ordered_unique_list
FIELD_VALIDATOR = t.Dict(
    {
        'is_required': t.Bool(),
        'field_id': validators.UUID(),
        'value': t.Null() | t.String(min_length=0, max_length=1024),
    }
)
REVIEW_SETTINGS = t.Dict(
    {
        'is_required': t.Bool(),
        t.Key('reviewer_entities', optional=True): Reviewers,
        t.Key('is_ordered', optional=True, default=False): t.Bool(),
        # DEPRECATED: Left for backwards compatibility. Use reviewers instead
        t.Key('reviewers_ids', optional=True): ROLES_VALIDATOR,
    }
)
SIGNERS_SETTINGS = t.Dict(
    {
        t.Key('signer_entities', optional=True): t.List(
            t.Dict(
                {
                    'type': t.Enum('role', 'group'),
                    'id': validators.UUID(),
                }
            )
        ),
        t.Key('is_ordered', optional=True, default=False): t.Bool(),
        # deprecated
        t.Key('signers_ids', optional=True): ROLES_VALIDATOR,
    }
)
VIEWERS_SETTINGS = t.Dict(
    {
        t.Key('viewers_ids', optional=True): ROLES_VALIDATOR,
        t.Key('viewers_group_ids', optional=True): GROUPS_VALIDATOR,
    }
)
FIELDS_SETTINGS = t.Dict({'fields': t.List(FIELD_VALIDATOR, min_length=1)})
TAGS_SETTINGS = t.Dict(
    {'tags': t.List(t.String(min_length=1, max_length=255), min_length=1) >> unique_list}
)

TrafaretTagName = partial(t.String, allow_blank=False, max_length=255)

# Currently we support conditions of such shape:
# { and: [
#    { or: [
#         { eq: ['#column1', 'value1'] },
#         { eq: ['#column1', 'value2'] },
#    ]}
#    { or: [
#         { eq: ['#column2', 'value1'] },
#    ]}
#    { or: [
#         { le: ['#column3', 'value1'] },
#         { ge: ['#column3', 'value2'] },
#    ]}
#    ...
# ]}
_CONDITIONS_COMPARISON_SETTINGS = t.Dict(
    {
        t.Key('or', optional=True): t.List(
            t.Dict({'eq': t.List(t.String(), max_length=2, min_length=2)}),
            min_length=1,
            max_length=2000,
        ),
        t.Key('and', optional=True): t.List(
            t.Dict(
                {
                    t.Key('le', optional=True): t.List(
                        t.String() | t.Int(), max_length=2, min_length=2
                    ),
                    t.Key('ge', optional=True): t.List(
                        t.String() | t.Int(), max_length=2, min_length=2
                    ),
                }
            ),
            min_length=1,
            max_length=2000,
        ),
    }
)
CONDITIONS_SETTINGS = t.Dict({'and': t.List(_CONDITIONS_COMPARISON_SETTINGS, min_length=1)})


class _BaseTemplateValidator(TrafaretValidator):
    name = t.String(min_length=1, max_length=512)

    review_settings = t.Null() | REVIEW_SETTINGS
    signers_settings = t.Null() | SIGNERS_SETTINGS
    viewers_settings = t.Null() | VIEWERS_SETTINGS
    fields_settings = t.Null() | FIELDS_SETTINGS
    # TODO[DOC-4378]: should be removed.
    tags_settings = t.Null() | TAGS_SETTINGS

    conditions = t.Null() | CONDITIONS_SETTINGS

    new_tags = t.Null() | t.List(TrafaretTagName(), min_length=1) >> unique_list
    tags_ids = t.Null() | t.List(validators.UUID(), min_length=1) >> unique_list

    assigned_to = t.Null() | validators.UUID()


class CreateTemplateValidator(_BaseTemplateValidator): ...


class UpdateTemplateValidator(_BaseTemplateValidator):
    template_id = validators.UUID()

    delete_tags_ids = t.Null() | t.List(validators.UUID(), min_length=1) >> unique_list


class DeleteTemplateValidator(TrafaretValidator):
    template_id = validators.UUID()


class SaveAssignedTemplateValidator(TrafaretValidator):
    template_id = validators.UUID()
    document_id = validators.UUID()


class ActivateAutomationValidator(TrafaretValidator):
    automation_id = validators.UUID()
    is_active = t.Bool()


def _get_template_roles_ids(data: DataDict) -> list[str]:
    roles = set()
    roles.update((data.get('review_settings') or {}).get('reviewers_ids', []))
    roles.update((data.get('signers_settings') or {}).get('signers_ids', []))
    roles.update((data.get('viewers_settings') or {}).get('viewers_ids', []))
    if assigned_to := data.get('assigned_to'):
        roles.add(assigned_to)
    return list(roles)


def _get_template_group_ids(data: DataDict) -> list[str]:
    signer_entities = (data.get('signers_settings') or {}).get('signer_entities', [])
    reviewer_entities = (data.get('review_settings') or {}).get('reviewer_entities', [])

    groups = {i['id'] for i in (*signer_entities, *reviewer_entities) if i['type'] == 'group'}

    viewers_group_ids = (data.get('viewers_settings') or {}).get('viewers_group_ids', [])
    groups.update(viewers_group_ids)

    return list(groups)


def _validate_non_empty(data: DataDict) -> None:
    if (
        not data.get('review_settings')
        and not data.get('signers_settings')
        and not data.get('viewers_settings')
        and not data.get('fields_settings')
        and not data.get('tags_settings')
    ):
        raise InvalidRequest(reason=_('Вкажіть хоча б одне налаштування для шаблону'))


async def validate_template_access(
    conn: DBConnection, template_id: str, user: User
) -> DocumentAutomationAutomationTemplate:
    template = await select_document_automation_template(conn, template_id)

    if not template or template.company_id != user.company_id:
        raise AccessDenied(reason=_('У вас немає доступу до шаблону'))

    if template.date_deleted:
        raise AccessDenied(reason=_('Шаблон видалено'))

    return template


async def validate_automation_access(conn: DBConnection, automation_id: str, user: User) -> DBRow:
    automation = await select_automation(conn, automation_id)

    if not automation or automation.company_id != user.company_id:
        raise AccessDenied(reason=_('У вас немає доступу до сценарія'))

    if automation.date_deleted:
        raise AccessDenied(reason=_('Сценарій видалено'))

    return automation


async def _validate_template_fields(conn: DBConnection, data: DataDict, user: User) -> None:
    """Validate document fields for template"""
    settings: DataDict | None = data['fields_settings']
    if not settings:
        return  # no settings, no validations

    fields: list[DataDict] = settings['fields']
    fields_ids: list[str] = [item['field_id'] for item in fields]

    if contains_duplicates(fields_ids):
        raise InvalidRequest(reason=_('Дублюючі поля в сценарії'))

    await document_fields_validators.validate_accesses(conn, fields_ids, user)


async def _validate_template_settings(conn: DBConnection, data: DataDict, user: User) -> None:
    """Validate template settings data"""

    _validate_non_empty(data)

    await validate_coworkers_roles_ids(
        conn=conn,
        roles_ids=_get_template_roles_ids(data),
        company_edrpou=user.company_edrpou,
    )

    group_ids = _get_template_group_ids(data)
    groups = await select_groups(
        conn,
        ids=group_ids,
        is_deleted=False,
        company_id=user.company_id,
    )

    active_group_ids = {group.id for group in groups if not group.date_deleted}

    if active_group_ids != set(group_ids):
        raise DoesNotExist(Object.groups, group_ids=list(set(group_ids) - active_group_ids))

    await _validate_template_fields(conn, data, user)


async def _validate_template_permission(user: User) -> None:
    validate_user_permission(user, {'can_edit_document_automation'})


async def validate_create_template(
    conn: DBConnection, request: web.Request, user: User
) -> CreateTemplateCtx:
    """Validate ability to create document template"""
    await validate_company_permission(
        conn=conn,
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
        permission=CompanyPermission.document_templates_enabled,
    )
    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    data = validators.validate(CreateTemplateValidator, raw_data)

    if (
        (settings := data.get('signers_settings'))
        and not settings.get('signers_ids')
        and not settings.get('signer_entities')
    ):
        raise InvalidRequest(reason=_('signers_ids чи singer_entities має бути заповнено'))

    if (
        (settings := data.get('review_settings'))
        and not settings.get('reviewers_ids')
        and not settings.get('reviewer_entities')
    ):
        raise InvalidRequest(reason=_('reviewers_ids чи reviewer_entities має бути заповнено'))

    if (
        (viewers_settings := data['viewers_settings'])
        and not viewers_settings.get('viewers_ids')
        and not viewers_settings.get('viewers_group_ids')
    ):
        raise InvalidRequest(reason=_('viewers_ids чи viewers_group_ids має бути заповнено'))

    validate_automation_conditions(data['conditions'], user)

    await _validate_template_settings(conn, data, user)

    # TODO[DOC-4378]: data['tags_settings'] should be removed and data['new_tags']
    #  and data['tags_ids'] should be used
    if data['tags_settings']:
        tags_ids, new_tags = await spit_existed_and_new_tags(
            conn=conn, tags=data['tags_settings']['tags'], company_id=user.company_id
        )
        if new_tags:
            await validate_tags_permissions(conn=conn, user=user, count=len(new_tags))
    else:
        tags_ids, new_tags = [], []

    return CreateTemplateCtx(
        id_=str(uuid4()),
        name=data['name'],
        assigned_to=data['assigned_to'],
        review_settings=data['review_settings'],
        signers_settings=data['signers_settings'],
        viewers_settings=data['viewers_settings'],
        fields_settings=data['fields_settings'],
        tags_settings=data['tags_settings'],
        company_id=user.company_id,
        created_by=user.role_id,
        conditions=data['conditions'],
        new_tags=new_tags,
        tags_ids=tags_ids,
    )


async def validate_update_template(
    conn: DBConnection, request: web.Request, user: User
) -> UpdateDocumentAutomationTemplateCtx:
    """Validate ability to update document template"""

    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    raw_data['template_id'] = request.match_info.get('template_id')
    data = validators.validate(UpdateTemplateValidator, raw_data)
    template_id: str = data['template_id']

    signers_settings = data.get('signers_settings')
    if (
        signers_settings
        and not signers_settings.get('signers_ids')
        and not signers_settings.get('signer_entities')
    ):
        raise InvalidRequest(reason=_('signers_ids чи signer_entities має бути заповнено'))

    if (
        (viewers_settings := data['viewers_settings'])
        and not viewers_settings.get('viewers_ids')
        and not viewers_settings.get('viewers_group_ids')
    ):
        raise InvalidRequest(reason=_('viewers_ids чи viewers_group_ids має бути заповнено'))

    template = await validate_template_access(conn, template_id, user)

    validate_automation_conditions(data['conditions'], user)

    await _validate_template_settings(conn, data, user)

    # TODO[DOC-4378]: data['tags_settings'] should be removed and data['new_tags'],
    #  data['tags_ids'], data['deleted_tags_ids'] should be used
    if data['tags_settings']:
        tags_ids, new_tags = await spit_existed_and_new_tags(
            conn=conn, tags=data['tags_settings']['tags'], company_id=user.company_id
        )
        deleted_tags_ids = await get_deleted_document_template_tags_names(
            conn=conn, tags=data['tags_settings']['tags'], template_id=template.id
        )
        if new_tags:
            await validate_tags_permissions(conn=conn, user=user, count=len(new_tags))
    else:
        tags_ids, new_tags = [], []

        deleted_tags_ids = await get_deleted_document_template_tags_names(
            conn=conn, tags=[], template_id=template.id
        )

    return UpdateDocumentAutomationTemplateCtx(
        id_=template.id,
        name=data['name'],
        company_id=user.company_id,
        assigned_to=data['assigned_to'],
        review_settings=data['review_settings'],
        signers_settings=data['signers_settings'],
        viewers_settings=data['viewers_settings'],
        fields_settings=data['fields_settings'],
        tags_settings=data['tags_settings'],
        conditions=data['conditions'],
        new_tags=new_tags,
        tags_ids=tags_ids,
        deleted_tags_ids=deleted_tags_ids,
    )


async def validate_delete_template(
    conn: DBConnection, request: web.Request, user: User
) -> DocumentAutomationAutomationTemplate:
    """Validate ability to delete document template"""

    await _validate_template_permission(user)

    raw_data = {'template_id': request.match_info.get('template_id')}
    data = validators.validate(DeleteTemplateValidator, raw_data)
    template_id: str = data['template_id']

    template = await validate_template_access(conn, template_id, user)

    return template


async def validate_save_assigned(
    conn: DBConnection, request: web.Request, user: User
) -> SaveAssignedDocumentAutomationTemplateCtx:
    raw_data = {
        'template_id': request.match_info.get('template_id'),
        'document_id': request.match_info.get('document_id'),
    }
    data = validators.validate(SaveAssignedTemplateValidator, raw_data)
    template_id: str = data['template_id']
    document = await validate_document_exists(conn, data)
    template = await validate_template_access(conn, template_id, user)
    return SaveAssignedDocumentAutomationTemplateCtx(
        document=document, template=template, user=user
    )


async def validate_activate_automation(
    conn: DBConnection, request: web.Request, user: User
) -> ActivateAutomationCtx:
    """Validate ability to activate/deactivate document template"""

    await _validate_template_permission(user)

    raw_data = await validators.validate_json_request(request)
    raw_data['automation_id'] = request.match_info.get('automation_id')
    data = validators.validate(ActivateAutomationValidator, raw_data)
    automation_id: str = data['automation_id']
    is_active: bool = data['is_active']

    automation = await validate_automation_access(conn, automation_id, user)
    status = DocumentAutomationStatus.disabled
    if is_active:
        status = DocumentAutomationStatus.enabled

    template = await validate_template_exists(
        conn=conn,
        template_id=automation.template_id,
        company_id=user.company_id,
    )

    return ActivateAutomationCtx(
        automation_id=automation_id,
        status=status,
        template=template,
    )


async def validate_template_exists(
    conn: DBConnection, *, template_id: str, company_id: str | None = None
) -> DocumentAutomationAutomationTemplate:
    template = await select_document_automation_template(conn, template_id, company_id=company_id)
    if not template:
        raise DoesNotExist(Object.template, template_id=template_id)

    return template


def validate_automation_conditions(conditions: DataDict, user: User) -> None:
    """Validate new automation conditions.

    `flatten_context` will return an iterator with pairs of condition and value
    consequentially, so every N element is the key and every N+1 element
    is its value. Each value is a dictionary with only 1 key/value pair,
    so extract this dict values to get condition/value pair and then validate it.
    """
    if not conditions:
        return

    flattened = flatten_context(conditions)
    has_inbox_condition = False
    has_self_recipient = False

    value_dict: DataDict
    condition_dict: DataDict
    for condition_dict, value_dict in grouped(flattened, 2):  # type: ignore
        condition, *__ = condition_dict.values()
        value, *__ = value_dict.values()
        if 'document_recipients.edrpou' in condition and value == user.company_edrpou:
            has_self_recipient = True
        elif 'document_side' in condition and value == 'inbox':
            has_inbox_condition = True

    if has_inbox_condition and has_self_recipient:
        raise InvalidRequest(
            reason=_('Для вхідних документів можна вказувати лише ЄДРПОУ ваших контрагентів'),
        )
