.search {
    display: flex;
    margin-top: 22px;
}

.inputWrapper {
    flex-grow: 1;
}

.icon {
    width: 20px;
    height: 20px;
}

.buttonWrapper {
    position: relative;
    width: 50px;
    background-color: #fff;
    border-radius: var(--border-radius);
}

@media screen and (min-width: 768px) {
    .buttonWrapper {
        width: 210px;
        margin-left: 10px;
    }
}

.text {
    margin-top: 18px;
    font-size: 13px;
}

.message {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 20px;
    margin-top: 26px;
    background-color: var(--light-blue-bg);
    border-radius: var(--border-radius);
    gap: 12px;
    text-align: center;
}

@media (min-width: 720px) {
    .message {
        flex-direction: row;
        text-align: left;
    }
}

.messageSuccess {
    background-color: #1cb8001a;
}

.messageTitle {
    font-weight: bold;
}

.messageText {
    font-size: 14px;
}

.messageButton {
    width: 150px;
    font-size: 13px;
}

@media (min-width: 720px) {
    .messageButton {
        margin-left: auto;
        text-align: right;
    }
}

.smileIcon {
    width: 30px;
    height: 30px;
    flex-shrink: 0;
    background-color: #fff;
    border-radius: 50%;
}

.resultsList {
    padding: 10px 0;
}

@media (min-width: 720px) {
    .resultsList {
        padding: 10px 20px;
    }
}

.resultsTable {
    width: 100%;
    font-size: 13px;
    table-layout: fixed;
    text-align: left;
}

.resultsTableHeader {
    border-bottom: 1px solid var(--default-border);
}

.resultsTableRow:hover {
    background-color: var(--grey-bg);
}

.resultsName,
.resultsEdrpou {
    padding: 12px 20px;
}

@media (min-width: 720px) {
    .resultsName,
    .resultsEdrpou {
        padding: 10px;
    }
}

.resultsName {
    width: 75%;
    border-bottom-left-radius: var(--border-radius);
    border-top-left-radius: 3px;
}

.resultsEdrpou {
    width: 25%;
    border-bottom-right-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}
