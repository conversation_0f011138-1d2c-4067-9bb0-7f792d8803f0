import pytest

from app.directories.tests.utils import prepare_directory
from app.lib.enums import DocumentStatus
from app.tests.common import (
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    with_elastic,
)

TEST_UUID_1 = '3d42cac4-0660-4eb8-bee5-ac14f61f2544'
TEST_UUID_2 = '162db85c-1c15-4152-acec-cd9d649febe1'

TEST_PARENT_ID_1 = 10


async def test_resolve_archive_list(aiohttp_client):
    """Test resolve both documents and directories as archive items"""
    app, client, owner = await prepare_client(aiohttp_client)
    directory = await prepare_directory(
        name='test_dir',
        company_id=owner.company_id,
    )
    # Should not apper in a list of root directories
    await prepare_directory(
        name='test_child_dir',
        company_id=owner.company_id,
        parent_id=directory.id,
    )

    d1 = await prepare_document_data(
        app, owner, id=TEST_UUID_1, directory_id=directory.id, is_archived=True
    )
    d2 = await prepare_document_data(app, owner, id=TEST_UUID_2, is_archived=True)
    doc_ids = [d1.id, d2.id]

    query = """
        { allArchiveItems {
            documents { id }
            directories { id }
            count
        } }
    """

    headers = prepare_auth_headers(owner)
    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(client, query, headers)

    data = data['allArchiveItems']

    # Only document without directory_id
    assert len(data['documents']) == 1
    assert data['documents'][0]['id'] == d2.id

    assert len(data['directories']) == 1
    assert data['count'] == 2


@pytest.mark.parametrize(
    'offset, limit, expected_docs, expected_dirs, total_count',
    [
        # fetch all
        (0, 10, 5, 5, 10),
        # fetch only directories
        (0, 5, 0, 5, 10),
        (1, 4, 0, 4, 10),
        (2, 3, 0, 3, 10),
        (5, 0, 0, 0, 10),
        # fetch only documents
        (5, 5, 5, 0, 10),
        (5, 2, 2, 0, 10),
        (7, 5, 3, 0, 10),
        (7, 3, 3, 0, 10),
        (7, 2, 2, 0, 10),
        # fetch intersection
        (3, 5, 3, 2, 10),
        (1, 10, 5, 4, 10),
    ],
)
async def test_resolve_archive_list_limit_offset(
    aiohttp_client,
    offset: int,
    limit: int,
    expected_docs: int,
    expected_dirs: int,
    total_count: int,
):
    """
    Test resolve documents and directories as archive items with limit and offset.
    Prepare 5 directories + 5 documents and test different combinations for offset/limit.
    """
    app, client, owner = await prepare_client(aiohttp_client)

    doc_ids = []
    for i in range(5):
        await prepare_directory(
            name=f'test_dir_{i}',
            company_id=owner.company_id,
        )
        doc = await prepare_document_data(app, owner, is_archived=True)
        doc_ids.append(doc.id)

    query = """
        query GetArchiveItems($offset: Int!, $limit: Int!) {
            allArchiveItems(offset: $offset, limit: $limit) {
                directories { id }
                documents { id }
                count
            }
        }
    """

    headers = prepare_auth_headers(owner)
    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(
            client=client,
            query=query,
            variables={'offset': offset, 'limit': limit},
            headers=headers,
        )

    data = data['allArchiveItems']
    assert len(data['directories']) == expected_dirs
    assert len(data['documents']) == expected_docs
    assert data['count'] == total_count


@pytest.mark.parametrize(
    'names, order, direction, expected_result',
    [
        (['d1', 'd2', 'd3'], 'date', 'asc', ['d1', 'd2', 'd3']),
        (['d1', 'd2', 'd3'], 'date', 'desc', ['d3', 'd2', 'd1']),
        (['d1', 'd2', 'd3'], 'date', None, ['d3', 'd2', 'd1']),
        (['d1', 'd2', 'd3'], None, 'asc', ['d1', 'd2', 'd3']),
        (['d1', 'd2', 'd3'], None, 'desc', ['d3', 'd2', 'd1']),
        (['d1', 'd2', 'd3'], None, None, ['d3', 'd2', 'd1']),
        (['Aaaa', 'Cccc'], 'title', 'asc', ['Aaaa', 'Cccc']),
        (['Aaaa', 'Cccc'], 'title', None, ['Cccc', 'Aaaa']),
        (['Aaaa', 'Cccc'], 'title', 'desc', ['Cccc', 'Aaaa']),
    ],
)
async def test_resolve_archive_list_directories_ordering(
    aiohttp_client, names, order, direction, expected_result
):
    app, client, owner = await prepare_client(aiohttp_client)

    for name in names:
        await prepare_directory(
            name=name,
            company_id=owner.company_id,
        )

    query = """
        query GetArchiveItems($direction: String, $order: String) {
            allArchiveItems(direction: $direction, order: $order) {
                directories { id, name }
            }
        }
    """
    data = await fetch_graphql(
        client=client,
        query=query,
        variables={'direction': direction, 'order': order},
        headers=prepare_auth_headers(owner),
    )
    dir_list = data['allArchiveItems']['directories']
    assert [d['name'] for d in dir_list] == expected_result


@pytest.mark.parametrize('parent_id', [None, TEST_PARENT_ID_1])
async def test_resolve_archive_list_directories_by_parent(aiohttp_client, parent_id):
    """
    Test search directories by parent_id
    """
    app, client, owner = await prepare_client(aiohttp_client)
    if parent_id:
        await prepare_directory(name='parent', company_id=owner.company_id, id=parent_id)

    dir1 = await prepare_directory(name='test-1', company_id=owner.company_id, parent_id=parent_id)
    dir2 = await prepare_directory(name='test-2', company_id=owner.company_id)

    expected_ids = {dir2.id, dir1.id}
    if parent_id:
        expected_ids = {dir1.id}

    query = """
        query SearchArchiveItems($parentDirectoryId: Int) {
            allArchiveItems(parentDirectoryId: $parentDirectoryId) {
                directories { id, name }
            }
        }
    """
    data = await fetch_graphql(
        client=client,
        query=query,
        variables={'parentDirectoryId': parent_id},
        headers=prepare_auth_headers(owner),
    )
    dir_list = data['allArchiveItems']['directories']
    assert {d['id'] for d in dir_list} == set(expected_ids)


@pytest.mark.parametrize(
    'search_input, expected_names',
    [
        (None, ['test-dir', 'my-name', 'my-test-name']),
        ('test', ['test-dir', 'my-test-name', 'child-test']),
        ('test-dir', ['test-dir']),
        ('TEST', ['test-dir', 'my-test-name', 'child-test']),
        ('my name', []),
        ('child', ['child-test']),
    ],
)
async def test_resolve_archive_list_directories_by_name(
    aiohttp_client, search_input, expected_names
):
    """
    Test search directories by name using search input from user
    """
    app, client, owner = await prepare_client(aiohttp_client)

    d1 = await prepare_directory(name='test-dir', company_id=owner.company_id)
    await prepare_directory(name='my-name', company_id=owner.company_id)
    await prepare_directory(name='my-test-name', company_id=owner.company_id)
    await prepare_directory(name='child-test', company_id=owner.company_id, parent_id=d1.id)

    query = """
        query SearchArchiveItems($search: String) {
            allArchiveItems(search: $search) {
                directories { name }
            }
        }
    """
    data = await fetch_graphql(
        client=client,
        query=query,
        variables={'search': search_input},
        headers=prepare_auth_headers(owner),
    )
    dir_list = data['allArchiveItems']['directories']
    assert {d['name'] for d in dir_list} == set(expected_names)


async def test_resolve_archive_list_by_name(aiohttp_client):
    """
    Test search directories and documents by name using search input from user
    """
    app, client, owner = await prepare_client(aiohttp_client)

    d1 = await prepare_directory(name='root-findme', company_id=owner.company_id)
    d2 = await prepare_directory(name='child-findme', company_id=owner.company_id, parent_id=d1.id)
    doc1 = await prepare_document_data(
        app, owner, is_archived=True, title='findme_doc', directory_id=d2.id
    )
    doc2 = await prepare_document_data(
        app, owner, is_archived=True, title='doc2me', directory_id=d2.id
    )

    query = """
        query SearchArchiveItems($search: String) {
            allArchiveItems(search: $search) {
                directories { id }
                documents { id }
            }
        }
    """
    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(
            client=client,
            query=query,
            variables={'search': 'findme'},
            headers=prepare_auth_headers(owner),
        )
    result_items = data['allArchiveItems']
    assert len(result_items['directories']) == 2
    assert len(result_items['documents']) == 1
    assert result_items['documents'][0]['id'] == doc1.id


async def test_resolve_archive_list_directories_additional_filters(aiohttp_client):
    """
    Test search directories returns empty list if not supported filters provided for searching
    """
    app, client, owner = await prepare_client(aiohttp_client)

    await prepare_directory(name='test-dir', company_id=owner.company_id)
    doc = await prepare_document_data(
        app, owner, status_id=DocumentStatus.uploaded.value, is_archived=True
    )

    query = """
        {
            allArchiveItems(statusId: 7000) {
                directories { id }
                documents { id }
            }
        }
    """

    async with with_elastic(app, [doc.id]):
        data = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(owner),
        )
    doc_list = data['allArchiveItems']['documents']
    dir_list = data['allArchiveItems']['directories']
    assert len(dir_list) == 0
    assert len(doc_list) == 1


async def test_resolve_archive_list_only_archived_documents(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    doc1 = await prepare_document_data(app, owner, is_archived=True)
    doc2 = await prepare_document_data(app, owner)

    query = '{ allArchiveItems { documents { id } } }'
    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(owner),
        )

    doc_list = data['allArchiveItems']['documents']
    assert len(doc_list) == 1
    assert doc_list[0]['id'] == doc1.id


async def test_resolve_archive_list_documents_by_parent(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)

    directory = await prepare_directory(name='test-dir', company_id=owner.company_id)
    doc1 = await prepare_document_data(app, owner, is_archived=True, directory_id=directory.id)
    doc2 = await prepare_document_data(app, owner, is_archived=True)

    query = """
        query SearchArchiveItems($parentDirectoryId: Int) {
            allArchiveItems(parentDirectoryId: $parentDirectoryId) {
                documents { id }
            }
        }
    """

    # Query without parent directory
    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(
            client=client,
            query=query,
            variables={'parent_directory_id': None},
            headers=prepare_auth_headers(owner),
        )
    doc_list = data['allArchiveItems']['documents']
    assert len(doc_list) == 1
    assert doc_list[0]['id'] == doc2.id  # doc without directory_id

    # Query wit parent directory
    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(
            client=client,
            query=query,
            variables={'parentDirectoryId': directory.id},
            headers=prepare_auth_headers(owner),
        )

    doc_list = data['allArchiveItems']['documents']
    assert len(doc_list) == 1
    assert doc_list[0]['id'] == doc1.id
