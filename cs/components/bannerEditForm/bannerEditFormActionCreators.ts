import React from 'react';

import { SelectOption } from '@vchasno/ui-kit';

import { createBanner, updateBanner } from 'services/banners';

import { Thunk } from '../../types';
import { Nullable } from '../../types/general';

import bannersListActionCreators from '../bannersList/bannersListActionCreators';
import actions from './bannerEditFormActions';

import {
    AudienceSizeOptionsType,
    BannerRateOptions,
    IncomingDocumentsSignCountOptionsType,
    OutgoingDocumentsCountOptionsType,
    PositionOptions,
} from './constants';

function onChangeText(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_TEXT,
            text: evt.target.value,
        });
    };
}

function onChangePositionList(position: PositionOptions): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_POSITION_LIST,
            payload: position.map((item) => item.value),
        });
    };
}

function onChangeRateList(rate: BannerRateOptions): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_RATE_LIST,
            payload: rate.map((item) => item.value),
        });
    };
}

function onChangeAudienceSize(size: AudienceSizeOptionsType): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_AUDIENCE_SIZE,
            payload: size.map((item) => item.value),
        });
    };
}

function onChangeOutgoingDocumentsCount(
    outgoingCount: OutgoingDocumentsCountOptionsType,
): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_OUTGOING_DOCUMENTS_COUNT,
            payload: outgoingCount.map((item) => item.value),
        });
    };
}

function onChangeIncomingDocumentsSignCount(
    incomingCount: IncomingDocumentsSignCountOptionsType,
): Thunk {
    return (dispatch) => {
        dispatch({
            type:
                actions.BANNER_EDIT_FORM__CHANGE_INCOMING_SIGN_DOCUMENTS_COUNT,
            payload: incomingCount.map((item) => item.value),
        });
    };
}

function onChangeActivityPeriod(activityPeriod: SelectOption): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_ACTIVITY_PERIOD,
            payload: activityPeriod?.value || null,
        });
    };
}

const onChangeAudienceType = (audienceType: SelectOption): Thunk => {
    return (dispatch) => {
        //для ФОП обнуляємо розмір компанії
        if (audienceType?.value === 'FOP') {
            dispatch({
                type: actions.BANNER_EDIT_FORM__CHANGE_AUDIENCE_SIZE,
                payload: null,
            });
        }
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_AUDIENCE_TYPE,
            payload: audienceType?.value || null,
        });
    };
};

function onChangeTextEn(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_TEXT_EN,
            textEn: evt.target.value,
        });
    };
}

function onChangeLinkText(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_LINK_TEXT,
            linkText: evt.target.value,
        });
    };
}

function onChangeLinkTextEn(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_LINK_TEXT_EN,
            linkTextEn: evt.target.value,
        });
    };
}

const onChangeDaysBeforeEndKEP = (evt: string): Thunk => {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_DAYS_BEFORE_END_KEP,
            daysBeforeSignatureExpires: evt,
        });
    };
};

function onChangeLink(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_LINK,
            link: evt.target.value,
        });
    };
}

function onChangeAnalyticsCategory(
    evt: React.ChangeEvent<HTMLInputElement>,
): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_CATEGORY,
            analyticsCategory: evt.target.value,
        });
    };
}

function onChangeColor(color: string): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.BANNER_EDIT_FORM__CHANGE_COLOR, color });
    };
}

function onChangeDateTo(value: Nullable<Date>): Thunk {
    if (value) {
        value.setHours(23, 59, 59, 59);
    }
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_DATE_TO,
            dateTo: value,
        });
    };
}

function onChangeDateFrom(value: Nullable<Date>): Thunk {
    if (value) {
        value.setHours(0, 0, 0, 0);
    }
    return (dispatch) => {
        dispatch({
            type: actions.BANNER_EDIT_FORM__CHANGE_DATE_FROM,
            dateFrom: value,
        });
    };
}

function onSubmit(evt: React.FormEvent): Thunk {
    return async (dispatch, getState) => {
        evt.preventDefault();
        const banner = getState().bannerEditForm;
        try {
            if (banner.id) {
                await updateBanner(banner);
            } else {
                await createBanner(banner);
            }
            dispatch({ type: actions.BANNER_EDIT_FORM__CLOSE });
            bannersListActionCreators.fetchBanners()(
                dispatch,
                getState,
                undefined,
            );
        } catch (err) {
            dispatch({
                type: actions.BANNER_EDIT_FORM__SHOW_ERROR_MESSAGE,
                errorMessage: err.message,
            });
        }
    };
}

function onClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.BANNER_EDIT_FORM__CLOSE });
    };
}

function onReset(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.BANNER_EDIT_FORM__RESET });
    };
}

export default {
    onChangeText,
    onChangePositionList,
    onChangeRateList,
    onChangeAudienceType,
    onChangeAudienceSize,
    onChangeOutgoingDocumentsCount,
    onChangeIncomingDocumentsSignCount,
    onChangeActivityPeriod,
    onChangeTextEn,
    onChangeLinkText,
    onChangeLinkTextEn,
    onChangeDaysBeforeEndKEP,
    onChangeLink,
    onChangeAnalyticsCategory,
    onChangeColor,
    onChangeDateTo,
    onChangeDateFrom,
    onSubmit,
    onClose,
    onReset,
};
