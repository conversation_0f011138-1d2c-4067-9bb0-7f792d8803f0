"""Update redundant and unlimited columns in rates.

Revision ID: 8ee09f66a546
Revises: c1796108bae1
Create Date: 2017-08-23 08:48:03.541257


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8ee09f66a546'
down_revision = 'c1796108bae1'
branch_labels = None
depends_on = None


def upgrade():
    # Remove redundant columns from rates
    op.drop_column('rates', 'period')
    op.drop_column('rates', 'duration')

    # Create new `is_unlimited_upload` column
    op.add_column('rates', sa.Column('is_unlimited_upload', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))
    op.add_column('rate_templates', sa.Column('is_unlimited_upload', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))

    # Set default period for individual rate
    op.execute("""
    UPDATE rate_templates
    SET
      period = 'days',
      duration = 1,
      is_renewable = FALSE
    WHERE name = 'individual';""")


def downgrade():
    # Restore columns
    op.add_column('rates', sa.Column('duration', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False))
    op.add_column('rates', sa.Column('period', postgresql.ENUM('days', 'months', 'years', name='rateperiod'), autoincrement=False, nullable=False))

    # Remove `is_unlimited_upload` column
    op.drop_column('rates', 'is_unlimited_upload')
    op.drop_column('rate_templates', 'is_unlimited_upload')

    # Set default period for individual rate
    op.execute("""
    UPDATE rate_templates
    SET
      period = 'years',
      duration = 1,
      is_renewable = TRUE
    WHERE name = 'individual';""")
