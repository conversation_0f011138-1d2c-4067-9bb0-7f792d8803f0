import uuid
from datetime import timed<PERSON><PERSON>

import pytest

from api.errors import (
    Code,
    Error,
)
from app.auth.types import User
from app.billing.enums import CompanyLimit
from app.document_versions.enums import DocumentVersionType
from app.document_versions.utils import add_document_content_version
from app.document_versions.validators import (
    validate_version_delete,
    validate_version_upload,
)
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus, Source
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    set_company_config,
)


@pytest.mark.parametrize(
    ('extension', 'exception'),
    [
        ('.xml', Error),
        ('.doc', None),
    ],
)
async def test_validate_version_upload_not_allowed_formats(aiohttp_client, extension, exception):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content',
            name='Test name',
            extension='.pdf',
        )

        # Assert
        if exception:
            with pytest.raises(Error) as e:
                await validate_version_upload(
                    conn=conn,
                    content=b'sole content',
                    filename=f'file{extension}',
                    document_id=document.id,
                    user=User.from_row(user),
                )
            assert e.value.code == Code.invalid_file_extension
        else:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename=f'file{extension}',
                document_id=document.id,
                user=User.from_row(user),
            )


async def test_validate_version_upload_not_versioned(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )
        assert e.value.code == Code.access_denied


async def test_validate_version_upload_not_file_content(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )

        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'',
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )

        assert e.value.code == Code.empty_upload_file


async def test_validate_version_upload_for_not_versioned_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )

        assert e.value.code == Code.access_denied


async def test_validate_version_upload_not_to_big_file(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    await set_company_config(
        app,
        company_id=user.company_id,
        uploads={'max_file_size': 1},
    )

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )

        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'some content' * 10**5,
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )

        assert e.value.code == Code.max_file_size


# TODO[version]: for now all companies can upload versioned documents
#  after finding out how this feature will be used by companies.
#  Use this ref (version_rate_limit) to find all places that should be uncommented.
@pytest.mark.skip(reason='Uncomment after finding out how this feature will be used by companies')
@pytest.mark.parametrize(
    'config, error_code',
    [
        # Config with limit
        ({CompanyLimit.max_versions_count.value: 0}, Code.overdraft),
        # Config with unlimited versions
        ({CompanyLimit.max_versions_count.value: None}, None),
        # Empty config is equal to unlimited versions
        ({}, None),
    ],
)
async def test_validate_version_upload_overdraft(aiohttp_client, config, error_code):
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
    )
    document = await prepare_document_data(app, user)

    await set_company_config(app, company_id=user.company_id, **config)

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )

        # Assert
        if error_code:
            with pytest.raises(Error) as e:
                await validate_version_upload(
                    conn=conn,
                    content=b'sole content',
                    filename='file.doc',
                    document_id=document.id,
                    user=User.from_row(user),
                )
            assert e.value.code == error_code


async def test_validate_version_upload_overdraft_temp(aiohttp_client, mocker):
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
    )
    document = await prepare_document_data(app, user)

    mocker.patch(
        'app.document_versions.validators.get_version_count',
        return_value=51,
    )

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )
        assert e.value.code == Code.overdraft


async def test_validate_version_upload_document_status(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, status_id=DocumentStatus.signed.value)

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )

        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename='file.doc',
                document_id=document.id,
                user=User.from_row(user),
            )
        assert e.value.code == Code.invalid_document_status


async def test_validate_version_upload_document_doesnt_exist(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_upload(
                conn=conn,
                content=b'sole content',
                filename='file.doc',
                document_id='some-not-existing-id',
                user=User.from_row(user),
            )
        assert e.value.code == Code.access_denied


async def test_validate_version_delete_version(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        now = utc_now()
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Name',
            extension='.pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now + timedelta(seconds=1),
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Name',
            extension='.pdf',
        )

        # Assert
        await validate_version_delete(
            conn=conn,
            user=User.from_row(user),
            document_id=document.id,
            version_id=document_version_2.id,
            source=Source.api_public,
        )


async def test_validate_version_delete_document_does_not_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_delete(
                conn=conn,
                user=User.from_row(user),
                document_id=uuid.uuid4().hex,
                version_id=uuid.uuid4().hex,
                source=Source.api_public,
            )

        assert e.value.code == Code.object_does_not_exist


async def test_validate_version_delete_version_does_not_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        # Assert
        with pytest.raises(Error) as e:
            await validate_version_delete(
                conn=conn,
                user=User.from_row(user),
                document_id=document.id,
                version_id=uuid.uuid4().hex,
                source=Source.api_public,
            )

        assert e.value.code == Code.object_does_not_exist


async def test_validate_version_delete_version_not_latest(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    now = utc_now()

    async with app['db'].acquire() as conn:
        with pytest.raises(Error) as e:
            document_version_1 = await add_document_content_version(
                conn=conn,
                document_id=document.id,
                company_edrpou=user.company_edrpou,
                company_id=user.company_id,
                date_created=now,
                role_id=user.role_id,
                content=b'new version content',
                upload_type=DocumentVersionType.new_upload,
                name='Test name',
                extension='.pdf',
            )
            await add_document_content_version(
                conn=conn,
                document_id=document.id,
                company_edrpou=user.company_edrpou,
                company_id=user.company_id,
                date_created=now + timedelta(seconds=1),
                role_id=user.role_id,
                content=b'new version content',
                upload_type=DocumentVersionType.new_upload,
                name='Test name',
                extension='.pdf',
            )

            # Assert
            await validate_version_delete(
                conn=conn,
                user=User.from_row(user),
                document_id=document.id,
                version_id=document_version_1.id,
                source=Source.api_public,
            )

        assert e.value.code == Code.invalid_action


async def test_validate_version_delete_version_when_only_1_version_left(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        with pytest.raises(Error) as e:
            document_version_1 = await add_document_content_version(
                conn=conn,
                document_id=document.id,
                company_edrpou=user.company_edrpou,
                company_id=user.company_id,
                role_id=user.role_id,
                content=b'new version content',
                upload_type=DocumentVersionType.new_upload,
                name='Test name',
                extension='.pdf',
            )

            # Assert
            await validate_version_delete(
                conn=conn,
                user=User.from_row(user),
                document_id=document.id,
                version_id=document_version_1.id,
                source=Source.api_public,
            )

        assert e.value.code == Code.invalid_action


async def test_validate_version_delete_version_by_not_uploader(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        with pytest.raises(Error) as e:
            await add_document_content_version(
                conn=conn,
                document_id=document.id,
                company_edrpou=user.company_edrpou,
                company_id=recipient.company_id,
                role_id=user.role_id,
                content=b'new version content',
                upload_type=DocumentVersionType.new_upload,
                name='Name',
                extension='.pdf',
            )
            document_version_2 = await add_document_content_version(
                conn=conn,
                document_id=document.id,
                company_edrpou=recipient.company_edrpou,
                company_id=recipient.company_id,
                role_id=recipient.role_id,
                content=b'new version content',
                upload_type=DocumentVersionType.new_upload,
                name='Name',
                extension='.pdf',
            )

            # Assert
            await validate_version_delete(
                conn=conn,
                user=User.from_row(user),
                document_id=document.id,
                version_id=document_version_2.id,
                source=Source.api_public,
            )

        assert e.value.code == Code.invalid_action
