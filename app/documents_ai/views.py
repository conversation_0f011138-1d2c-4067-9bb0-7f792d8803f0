from aiohttp import web

import app.lib.validators_pydantic as pv
from api.errors import InvalidRequest
from api.utils import api_response
from app.auth.decorators import login_required
from app.auth.types import User
from app.documents.db import select_document_by_id
from app.documents_ai.schemas import DocumentMetaSuggestResponse, DocumentSummaryResponse
from app.documents_ai.utils import (
    get_document_summary,
    suggest_document_meta_by_content,
    suggest_document_meta_by_document_id,
)
from app.documents_ai.validators import validate_document_meta_suggest_enabled, validate_upload_file
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.openapi.decorators import openapi_docs
from app.openapi.types import OpenApiParam
from app.services import services


@openapi_docs(
    summary=_('Визначити реквізити документа по його вмісту'),
    response=DocumentMetaSuggestResponse,
)
@login_required()
async def document_meta_suggest_by_content(request: web.Request, user: User) -> web.Response:
    await validate_document_meta_suggest_enabled(user.company_id)
    uploaded_file = await validate_upload_file(request)

    suggest_obj = await suggest_document_meta_by_content(
        content=uploaded_file.content,
        extension=uploaded_file.extension,
        owner_edrpou=user.company_edrpou,
    )

    if not suggest_obj:
        return web.json_response(DocumentMetaSuggestResponse.empty().to_api())

    resp_obj = DocumentMetaSuggestResponse(
        title=uploaded_file.title,
        category=suggest_obj.category,
        edrpou_recipient=suggest_obj.edrpou_recipient,
        date_document=suggest_obj.date_document,
        number=suggest_obj.number,
        amount=suggest_obj.amount,
        recipient_name=None,
        recipient_email=None,
    )
    return api_response(request, data=resp_obj.to_api())


@openapi_docs(
    summary=_('Визначити реквізити документа по document_id'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
    response=DocumentMetaSuggestResponse,
)
@login_required()
async def document_meta_suggest_by_id(request: web.Request, user: User) -> web.Response:
    try:
        document_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
    except KeyError:
        raise InvalidRequest()

    await validate_document_meta_suggest_enabled(user.company_id)
    suggest_obj = await suggest_document_meta_by_document_id(document_id, user=user)

    if not suggest_obj:
        return web.json_response(DocumentMetaSuggestResponse.empty().to_api())

    async with services.db_readonly.acquire() as conn:
        doc_row = await select_document_by_id(conn, document_id=document_id)

    resp_obj = DocumentMetaSuggestResponse(
        title=doc_row.title if doc_row else None,
        category=suggest_obj.category,
        edrpou_recipient=suggest_obj.edrpou_recipient,
        date_document=suggest_obj.date_document,
        number=suggest_obj.number,
        amount=suggest_obj.amount,
        recipient_name=None,
        recipient_email=None,
    )
    return api_response(request, data=resp_obj.to_api())


@openapi_docs(
    summary=_('Зробити коротке резюме документа по document_id'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
    response=DocumentSummaryResponse,
)
@login_required()
async def document_summary(request: web.Request, user: User) -> web.Response:
    if not get_flag(FeatureFlags.AI_DOCUMENT_SUMMARY_BLOCK):
        raise InvalidRequest(reason=_('Функціонал не доступний'))

    try:
        document_id = validators.validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
    except KeyError:
        raise InvalidRequest()

    doc_summaries = await get_document_summary(document_id, user=user)
    return api_response(request, data={'data': [item.to_api() for item in doc_summaries]})
