from __future__ import annotations

from datetime import datetime
from enum import IntEnum

from elasticmagic import Document as Model
from elasticmagic import Field
from elasticmagic import types as t

from app.es import fields


class ContactRecipientType(IntEnum):
    """
    Type of contact recipient. It also works as a priority for search results.
    """

    contact = 10
    person = 20
    company = 30
    role = 40

    @property
    def is_contact(self) -> bool:
        return self in (ContactRecipientType.contact, ContactRecipientType.person)


def _suggest_field() -> Field:
    """
    A specialized text field designed for supporting search suggestions. This field
    uses the 'nпram_analyzer' analyzer for indexing, which tokenizes the text and converts
    it to lowercase, facilitating case-insensitive searches

    Norms are disabled for this field to reduce index size and improve performance,
    as they are not necessary for the type of queries expected on suggestion fields.
    The 'index_options' are set to 'docs', indicating that only document IDs are
    stored, optimizing the field for use cases where only the existence of terms
    in a document is relevant, not their frequency or position.
    """

    return fields.field(
        type_=t.Text,
        analyzer='ngram_analyzer',
        search_analyzer='standard',
        norms=False,
        index_options='docs',
    )


def _name_field() -> Field:
    """
    This text field is optimized for storing names. It utilizes the 'standard'
    analyzer for both indexing and searching, which tokenizes the text and converts
    it to lowercase, aiding in case-insensitive searches. Norms are disabled to
    optimize index space, as they are not needed for scoring in this context.
    """

    return fields.field(
        type_=t.Text,
        norms=False,
        copy_to='meta_data',
    )


class ContactRecipient(Model):
    """
    Model for recipient search, it holds all contacts and companies
    that is required for recipient search.
    """

    __mapping_options__ = {'dynamic': False, 'date_detection': False}

    __doc_type__ = 'contact_recipient'

    # _id: companies.id OR contact_persons.id OR contacts.id OR roles.id

    # contacts.edrpou OR company.edrpou
    company_edrpou = fields.field(
        type_=t.Keyword,
        copy_to='meta_data',
        # For searching by EDRPOU prefix
        fields={'suggest': _suggest_field()},
    )
    company_registered = fields.field(type_=t.Boolean)
    # IF contact or contact_person:
    #   company_name=contacts.name
    #   company_name_short=contacts.short_name
    #   company_name_extra=companies.name
    # IF company or role:
    #   company_name=companies.name
    #   company_name_short=companies.short_name
    #   company_name_extra=None
    company_name = _name_field()
    company_name_short = _name_field()
    company_name_extra = _name_field()

    #   # old field, delete it and use "type" field instead
    is_contact = fields.field(type_=t.Boolean)

    # see ContactRecipientType for reference
    type = fields.field(type_=t.Integer)

    contact_owner_id = fields.field(type_=t.Keyword)
    contact_person_email = fields.field(
        type_=t.Keyword,
        copy_to='meta_data',
        fields={
            # Use lowercase for case-insensitive search
            'lowercase': fields.field(
                type_=t.Keyword,
                normalizer='lowercase_normalizer',
            )
        },
    )
    contact_person_name = _name_field()
    contact_person_phone = fields.field(type_=t.Keyword)

    # Is this person main recipient for the contact company?
    person_main_recipient = fields.field(type_=t.Boolean)

    contact_id = fields.field(type_=t.Keyword)
    person_id = fields.field(type_=t.Keyword)
    company_id = fields.field(type_=t.Keyword)
    role_id = fields.field(type_=t.Keyword)

    # For sorting results with the same score. For example,
    #  when a user searches only by EDRPOU
    date = fields.field(type_=t.Date)

    meta_data = fields.field(
        type_=t.Text,
        norms=False,
        index_options='docs',
        # For broad search by all fields
        fields={'suggest': _suggest_field()},
    )

    @staticmethod
    def build(
        *,
        _id: str,
        company_edrpou: str,
        company_registered: bool | None,
        company_name: str | None,
        company_name_short: str | None,
        company_name_extra: str | None,
        type_: ContactRecipientType,
        contact_owner_id: str | None,
        contact_person_email: str | None,
        contact_person_name: str | None,
        contact_person_phone: str | None,
        person_main_recipient: bool | None,
        contact_id: str | None,
        person_id: str | None,
        company_id: str | None,
        role_id: str | None,
        date: datetime | None,
    ) -> ContactRecipient:
        """
        Type-safe constructor for ContactRecipient model
        """

        return ContactRecipient(
            _id=_id,
            company_edrpou=company_edrpou,
            company_registered=company_registered,
            company_name=company_name,
            company_name_short=company_name_short,
            company_name_extra=company_name_extra,
            is_contact=type_.is_contact,
            type=type_.value,
            contact_owner_id=contact_owner_id,
            contact_person_email=contact_person_email,
            contact_person_name=contact_person_name,
            contact_person_phone=contact_person_phone,
            person_main_recipient=person_main_recipient or False,
            contact_id=contact_id,
            person_id=person_id,
            company_id=company_id,
            role_id=role_id,
            date=date,
        )
