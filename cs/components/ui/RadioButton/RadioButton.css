.root {
    position: relative;
    display: block;
}

:global(.vchasno-autumn-theme) .root {
    --pigeon-color: var(--autumn-3-color);
    --blue-border: var(--autumn-cta-color);
}

:global(.vchasno-spring-theme) .root {
    --pigeon-color: var(--spring-3-color);
    --blue-border: var(--vchasno-ui-checkbox-bg-color);
}

.icon {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    width: 16px;
    height: 16px;
    border: solid 1px var(--pigeon-color);
    margin-right: 10px;
    background-color: var(--white-bg);
    border-radius: 50%;
}

.iconChecked::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 10px;
    height: 10px;
    margin: auto;
    background-color: var(--vchasno-ui-checkbox-bg-color);
    border-radius: 50%;
    content: '';
}

.iconDisabled::after {
    background-color: var(--dark-pigeon-color);
}

.iconOutlined {
    top: calc(50% - 10px);
    left: 16px;
    background: none;
    cursor: pointer;
}

.iconOutlined.iconChecked {
    border-color: var(--vchasno-ui-checkbox-bg-color);
}

:global(.vchasno-autumn-theme) .iconChecked {
    border-color: var(--autumn-cta-color);
}

.label {
    position: relative;
    z-index: 1;
    display: inline-block;
    padding-left: 26px;
    cursor: pointer;
    vertical-align: middle;
}

.labelDisabled {
    color: var(--grey-color);
    cursor: default;
}

.labelOutlined {
    padding: 12px 16px 12px 46px;
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
}

.labelActiveOutlined {
    border-color: var(--grey-border);
    background: var(--grey-bg);
}

.hint {
    width: 240px;
}
