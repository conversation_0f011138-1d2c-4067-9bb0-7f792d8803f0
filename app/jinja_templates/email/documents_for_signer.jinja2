{% extends "email/layout.jinja2" %}
{% import "email/macros.jinja2" as macros %}
{% from "email/blocks/document_macros.jinja2" import
    get_coworker_info,
    get_files_list,
    to_documents_button
%}
{% import "email/blocks/pro_advice_blocks.jinja2" as pro_macros %}


{% block content %}
    {# Notification documents for signers #}
    {{ macros.greeting_header(fist_name) }}
    <h1 style="{{ macros.style_normal_font() }}">
        {% if files | length == 1 %}
            {% trans %}Ви отримали документ на підпис{% endtrans %}
        {% else %}
            {% trans %}Ви отримали документи на підпис{% endtrans %}
        {% endif %}
    </h1>

    {% if coworker %}
        <strong>{% trans %}Ваш співробітник{% endtrans %}</strong>
        {{ get_coworker_info(coworker) }}

        <strong>
            {% trans %}Просить вас{% endtrans %}
            {% if signer.group_name %}
                <span style="{{ macros.style_group() }}">
                    {% trans %}як учасника групи{% endtrans %} {{ signer.group_name }}
                </span>
            {% endif %}
            {% if files | length == 1 %}
                {% trans %}підписати документ{% endtrans %}
            {% else %}
                {% trans %}підписати документи{% endtrans %}
            {% endif %}
            {% trans %}в компанії{% endtrans %} {{ emailing.build_company_label_v2(edrpou=recipient_company_edrpou, name=recipient_company_name)}}
        </strong>
    {% endif %}
    {{ get_files_list(files, more_files_count, urls) }}

    {{ to_documents_button(files, urls) }}

    {% if enable_pro_advice_block %}
        {{ pro_macros.pro_advice_block('templates_tags_video', image_sofia, youtube_link) }}
    {% endif %}

{% endblock %}
