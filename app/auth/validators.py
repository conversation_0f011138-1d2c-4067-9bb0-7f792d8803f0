from __future__ import annotations

import datetime
import logging
from collections import defaultdict
from contextlib import asynccontextmanager
from typing import Self

import aiohttp_session
import pydantic
import sqlalchemy as sa
import trafaret as t
from aiohttp import web
from trafaret_validator import TrafaretValidator

import app.events.models as event_mdl
import app.events.utils as event_utils
from api.errors import (
    AccessDenied,
    AlreadyExists,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    LoginRequired,
    Object,
    TooManyRequests,
)
from app.auth import db, two_factor
from app.auth.constants import MAX_AGE_SESSION, USER_PERMISSIONS
from app.auth.db import (
    exists_token_by,
    select_company_by_id,
    select_role_by,
    select_role_by_id,
    select_role_ids_with_tokens,
    select_roles,
    select_roles_by_emails,
    select_roles_by_ids,
    select_user,
)
from app.auth.enums import AuthFactor, RoleStatus
from app.auth.helpers import check_password_hash_pbkdf2_hmac_sha512
from app.auth.schemas import (
    AntivirusSettings,
    ArchiveSettings,
    CompanyConfig,
    DefaultRolePermissionsKey,
    ReviewRenderConfig,
    VersionSettings,
)
from app.auth.session_manager.utils import get_user_session_data_by_public_id
from app.auth.tables import role_table, token_table
from app.auth.two_factor import get_2fa_otp_web
from app.auth.types import (
    USER_PERMISSION_KEY,
    AuthUser,
    BaseUser,
    LoginUserCtx,
    LogoutAllSessionsCtx,
    Role,
    User,
    is_wide_user_type,
)
from app.auth.utils import (
    count_invalid_password_attempts,
    get_company_config,
    get_user,
    get_user_id,
    has_direct_permission,
    has_permission,
    is_active_role,
    is_admin,
    update_user,
)
from app.billing.utils import get_billing_company_config, is_employee_limit_reached
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.groups.db import select_group_members
from app.groups.types import GroupMembersMapping
from app.groups.validators import validate_groups_exists
from app.i18n import _
from app.lib import helpers, validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.datetime_utils import ONE_HOUR_DELTA, local_now
from app.lib.enums import RenderSignatureAtPage, UserRole
from app.lib.helpers import (
    get_client_ip,
    reset_rate_limit,
    validate_rate_limit,
)
from app.lib.types import DataDict, StrDict
from app.lib.validators import validate_json_request
from app.models import exists
from app.registration.enums import RegistrationSource
from app.reviews.types import ReviewsInfoCtx, ReviewState
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)

LOGIN_ATTEMPTS_LIMIT = 10


class ActivateRoleValidator(TrafaretValidator):
    role_id = validators.UUID()
    user_id = validators.UUID()


class AddTokenValidator(TrafaretValidator):
    role_id = validators.UUID()
    expire_days = t.Int() | t.Null()


class AddTokensValidator(TrafaretValidator):
    emails = t.List(validators.email())
    expire_days = t.Int() | t.Null()


class DeleteTokenValidator(TrafaretValidator):
    role_id = validators.UUID()


class DeleteTokensValidator(TrafaretValidator):
    emails = t.List(validators.email())


class LoginValidator(TrafaretValidator):
    email = validators.email()
    password = validators.InsecurePassword()
    remember = t.Bool() | t.Null()


class VerifyPhone2faSchema(pydantic.BaseModel):
    code: str = pydantic.Field(min_length=6, max_length=6)
    trusted: bool


class VerifyEmail2faSchema(pydantic.BaseModel):
    password: pv.Password
    trusted: bool


class AdditionalConfigValidator(TrafaretValidator):
    msViewerEnabled = t.Bool() | t.Null()  # noqa: N815
    render_review_in_interface = t.Bool() | t.Null()
    render_review_on_print_document = t.Bool() | t.Null()


class GoogleAuthValidator(TrafaretValidator):
    token = t.String(max_length=1_000_000)  # ~ 1 mb
    source = validators.Enum(RegistrationSource) | t.Null()
    invite_email = validators.email() | t.Null()


class MicrosoftAuthValidator(TrafaretValidator):
    access_token = t.String(max_length=1_000_000)  # ~ 1 mb
    id_token = t.String(max_length=1_000_000)  # ~ 1 mb
    source = validators.Enum(RegistrationSource) | t.Null()
    invite_email = validators.email() | t.Null()


class UpdateCompanyConfigSchema(pydantic.BaseModel):
    """
    Selected fields of CompanyConfig that can be updated by company admin
    """

    ms_viewer_enabled: bool = pydantic.Field(default=True, alias='msViewerEnabled')
    allow_unregistered_document_view: bool = True
    render_review_in_interface: bool = True
    render_review_on_print_document: bool = True
    render_signature_at_page: RenderSignatureAtPage = RenderSignatureAtPage.first
    enable_2fa_for_internal_users: bool = False
    allow_suggesting_document_meta_with_ai: bool = True

    archive_settings: ArchiveSettings | None = None
    antivirus_settings: AntivirusSettings | None = None
    review_render_config: ReviewRenderConfig | None = None
    default_role_permissions_key: DefaultRolePermissionsKey | None = None
    version_settings: VersionSettings | None = None

    @pydantic.model_validator(mode='after')
    def _not_allow_none_as_set_value(self) -> Self:
        """
        For some fields, we use "None" as "not set" value. Those fields are not allowed to pass
        in the request to be able to use "exclude_unset" option during serialization.
        """
        pv.model_validate_no_explicit_nulls(
            model=self,
            include={
                'archive_settings',
                'antivirus_settings',
                'default_role_permissions_key',
                'version_settings',
            },
        )
        return self

    def to_db_config(self) -> DataDict:
        return self.model_dump(
            # - render_signature_at_page enum should be serialized as string for storing in DB
            mode='json',
            # - we use PATCH method for update company config, so we need to exclude unset fields
            exclude_unset=True,
            # - we consider None values as unset values if None is valid value for field enrich
            # - msViewerEnabled is stored in DB in camelCase, as well as on frontend
            by_alias=True,
            # - enable_2fa_for_internal_users — admin config
            exclude={'enable_2fa_for_internal_users'},
        )

    def to_db_admin_config(self) -> DataDict:
        """
        Historically, we have two different configs for company:
        - config (can be updated by company admin)
        - admin_config (can be updated by super admin)
        Some keys could be updated by super admin only.
        But now company admin can update some superadmin fields as well.
        They are stored in admin_config.
        """
        data = {}
        if 'enable_2fa_for_internal_users' in self.model_fields_set:
            data['enable_2fa_for_internal_users'] = self.enable_2fa_for_internal_users

        return data

    def to_event_data(self, prev_config: CompanyConfig) -> DataDict:
        data = {}

        if 'ms_viewer_enabled' in self.model_fields_set:
            data['ms_viewer_enabled'] = self.ms_viewer_enabled
        if 'enable_2fa_for_internal_users' in self.model_fields_set:
            data['enable_2fa_for_internal_users'] = self.enable_2fa_for_internal_users
        if 'allow_suggesting_document_meta_with_ai' in self.model_fields_set:
            data['allow_suggesting_document_meta_with_ai'] = (
                self.allow_suggesting_document_meta_with_ai
            )

        if _settings := self.antivirus_settings:
            if 'is_download_infected_enabled' in _settings.model_fields_set:
                data['is_download_infected_enabled'] = _settings.is_download_infected_enabled
            if 'is_download_pending_enabled' in _settings.model_fields_set:
                data['is_download_pending_enabled'] = _settings.is_download_pending_enabled

        if _new_permissions := self.default_role_permissions_key:
            # Permissions with custom names in event dict
            _prev_permissions = prev_config.default_role_permissions_key
            if (
                'user_role' in _new_permissions.model_fields_set
                and _new_permissions.user_role != _prev_permissions.user_role
            ):
                data['role_permissions_admin'] = _new_permissions.user_role == UserRole.admin
            if (
                'can_view_document' in _new_permissions.model_fields_set
                and _new_permissions.can_view_document != _prev_permissions.can_view_document
            ):
                data['role_permissions_can_view_documents'] = _new_permissions.can_view_document

            # The rest of the fields have the default name in format: role_permissions_<name>
            _prev_dict = _prev_permissions.model_dump(exclude={'user_role', 'can_view_document'})
            _new_dict = _new_permissions.model_dump(
                exclude={'user_role', 'can_view_document'},
                exclude_unset=True,  # check only keys that were sent in the request
            )
            for key, value in _new_dict.items():
                if value != _prev_dict[key]:
                    data[f'role_permissions_{key}'] = value

        return data


class ChangeCorporateDomainSchema(pydantic.BaseModel):
    email_domains: list[pv.EmailDomain] = pydantic.Field(max_length=5)


class ChangeCompanyPermissionsSchema(pydantic.BaseModel):
    email_domains: list[pv.EmailDomain] | None = pydantic.Field(None, max_length=5)
    allowed_ips: list[pv.IP] | None = pydantic.Field(None, max_length=100)
    allowed_api_ips: list[pv.IP] | None = pydantic.Field(None, max_length=100)
    inactivity_timeout: int | None = pydantic.Field(None, ge=0, le=MAX_AGE_SESSION)
    # There is a validation to prevent user from site access loss if
    # current IP is not in the list of allowed IPs.
    # Use this field to bypass validation
    force_ip_update: bool = False

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True, exclude={'force_ip_update'})


class LogoutAllSessionsSchema(pydantic.BaseModel):
    user_id: pv.UUID | None = None  # logout all sessions of given user
    with_mobile: bool = True
    terminate_current_session: bool = True


class LogoutSessionSchema(pydantic.BaseModel):
    session_id: pv.UUID
    user_id: pv.UUID | None = None  # logout session of given user


async def validate_required_user(
    conn: DBConnection,
    auth_user: AuthUser,
    row_user: User | None,
    ensure_active_user: bool,
) -> User:
    """
    For given auth user and row user validate that user exists in database and
    return database user object
    """
    if row_user:
        assert row_user.role_id, 'Row user must have role_id'
        return row_user

    if not auth_user.email or not auth_user.company_edrpou:
        raise LoginRequired(case=2)

    user = await select_user(
        conn,
        email=auth_user.email,
        company_edrpou=auth_user.company_edrpou,
        use_active_filter=ensure_active_user,
    )
    if user:
        return user

    raise LoginRequired(case=1)


async def validate_active_user_exists(
    conn: DBConnection,
    *,
    email: str,
    company_edrpou: str,
) -> User:
    """
    Check if a user with a given email and company exists in a database
    and has an active role
    """
    user = await db.select_user(
        conn=conn,
        email=email,
        company_edrpou=company_edrpou,
        use_active_filter=True,
    )
    if not user:
        raise DoesNotExist(Object.user, email=email)

    return user


async def validate_user_by_role_exists(conn: DBConnection, *, role_id: str) -> User:
    """
    Check if a user with a given role exists in a database
    """
    user = await get_user(conn, role_id=role_id)
    if not user:
        raise DoesNotExist(Object.role, id=role_id)
    return user


async def validate_role_by_user_id_and_edrpou(
    conn: DBConnection, edrpou: str, user_id: str
) -> Role:
    role = await select_role_by(
        conn=conn,
        company_edrpou=edrpou,
        mixed=user_id,
        role_status=RoleStatus.active,
    )
    if not role:
        raise DoesNotExist(
            obj=Object.role,
            reason=_('Роль не знайдено у БД'),
        )

    return role


async def validate_review_request_roles(
    conn: DBConnection, state: ReviewState, role_ids: set[str], user: User
) -> set[str]:
    """
    Validate roles:
    1. Prevent adding inactive role to review request.
    2. If 'RoleStatus' = deleted:
        - if role don't have review - remove from reviewers
        - if have review - leave in reviewers
    3. Raise an error if one of roles associated with the user's company can't found in database.
    """
    if not role_ids:
        return set()
    # list role ids with reviews (approve or reject)
    role_ids_with_review = set()
    for review in state.reviews:
        if review.role_id and review.type:
            role_ids_with_review.add(review.role_id)

    roles = await select_roles(conn, roles_ids=role_ids, company_id=user.company_id)
    if not_found_roles := list(set(role_ids) - {role.id_ for role in roles}):
        raise DoesNotExist(Object.roles, role_ids=not_found_roles)

    validated_role_ids = set()

    for role in roles:
        if role.status == RoleStatus.deleted and role.id_ not in role_ids_with_review:
            continue
        validated_role_ids.add(role.id_)

    return validated_role_ids


def validate_review_request_order(
    is_ordered: bool, state: ReviewState, reviewers: list[ReviewsInfoCtx]
) -> None:
    """
    Validates the order of review requests when sequential approval is required.

    This function checks whether a user without a review
     has been added before a user with an existing review.
    If such an invalid order is found, an AccessDenied exception is raised.
    """
    if not is_ordered:
        return

    previous_reviewer_has_review = None
    for new_reviewer in reviewers:
        has_review = bool(
            state.get_review(
                role_id=new_reviewer.role_id,
                group_id=new_reviewer.group_id,
            )
        )
        if has_review and previous_reviewer_has_review is False:
            raise AccessDenied(
                reason=_(
                    'Некоректний порядок учасників у послідовному погодженні. '
                    'Не можна додати користувача без погодження перед '
                    'існуючим запитом з погодженням'
                )
            )
        previous_reviewer_has_review = has_review


async def validate_review_request_groups(
    conn: DBConnection,
    state: ReviewState,
    group_ids: set[str],
    user: User,
    group_role_map: GroupMembersMapping,
) -> None:
    """
    Validate groups:
    1. Prevent adding a deleted group to review request.
    2. Ignore 'is_deleted' status if the group already has a review (approved or rejected).
    3. Raise an error if one of groups associated with the user's company can't found in database.
    4. Raise an error if one of groups has no members.
    """
    # list group ids with reviews (approve or reject)
    group_ids_with_exist_reviews = set()
    for review in state.reviews:
        if review.group_id and review.type:
            group_ids_with_exist_reviews.add(review.group_id)

    groups = await validate_groups_exists(
        conn=conn,
        group_ids=group_ids,
        company_id=user.company_id,
    )

    for group in groups:
        if group.is_deleted and group.id not in group_ids_with_exist_reviews:
            raise AccessDenied(reason=_('Неможливо додати в погодження видалену групу'))

    # Get group members including deleted groups that already have reviews
    group_members = await select_group_members(
        conn=conn,
        group_ids=list(group_ids),
        include_deleted_groups=True,
    )

    # Create a new mapping of group_id to role_ids
    new_group_role_map = defaultdict(set)
    for member in group_members:
        new_group_role_map[member.group_id].add(member.role_id)

    if set(new_group_role_map.keys()) != set(group_ids):
        raise InvalidRequest(
            reason=_('Неможливо додати в погодження групу без учасників'),
            details={'empty_groups': list(set(group_ids) - set(new_group_role_map.keys()))},
        )


async def validate_coworkers_roles_ids(
    conn: DBConnection,
    roles_ids: list[str],
    company_edrpou: str,
    *,
    exclude_deleted_roles: bool = False,
) -> list[Role]:
    roles = await select_roles_by_ids(conn, roles_ids=roles_ids)
    roles_id_mapping = {role.id_: role for role in roles}
    seen_roles_ids = set()

    roles_result: list[Role] = []
    for role_id in roles_ids:
        role = roles_id_mapping.get(role_id)
        if role is None:
            continue

        # validate roles access
        if role.company_edrpou != company_edrpou:
            raise AccessDenied(
                reason=_('Декілька або всі вказані ролі не є співробітниками вашої компанії')
            )

        # validate
        if not role.is_active:
            if exclude_deleted_roles:
                continue

            reason = _('Деякі співробітники мають неактивну роль')
            raise InvalidRequest(reason=reason)

        seen_roles_ids.add(role.id_)
        roles_result.append(role)

    # validate roles exists
    if seen_roles_ids != set(roles_ids) and not exclude_deleted_roles:
        raise DoesNotExist(Object.roles, role_ids=list(set(roles_ids) - seen_roles_ids))

    return roles_result


async def validate_activate_role(conn: DBConnection, data: StrDict) -> Role:
    """Validate that user has given role."""
    valid_data = validators.validate(ActivateRoleValidator, data)

    role_id = valid_data['role_id']
    role = await select_role_by_id(conn, role_id)
    if not role:
        raise DoesNotExist(Object.role, id=role_id)

    if role.user_id != valid_data['user_id'] or not is_active_role(role):
        raise AccessDenied()

    return role


async def _validate_token_not_exists(conn: DBConnection, role_id: str) -> None:
    if await exists_token_by(conn, token_table.c.role_id == role_id):
        raise AlreadyExists(Object.token)


async def validate_add_token(
    conn: DBConnection, user: User, data: StrDict
) -> tuple[str, datetime.datetime | None]:
    from app.profile.validators import validate_can_edit_role

    validate_user_permission(user, {'can_edit_roles'})

    valid_data = validators.validate(AddTokenValidator, data)
    target_role_id = valid_data['role_id']
    expire_days = valid_data['expire_days']
    target_role = await select_role_by_id(conn, target_role_id)
    if not target_role:
        raise DoesNotExist(Object.role, id=target_role_id)

    await validate_can_edit_role(conn, user, target_role)

    # Authorization token for given role should not exist
    await _validate_token_not_exists(conn, target_role_id)

    date_expired = None
    if expire_days:
        date_expired = local_now() + datetime.timedelta(days=expire_days)

    return target_role_id, date_expired


async def validate_add_tokens(
    conn: DBConnection, user: User, data: StrDict
) -> tuple[list[Role], set[str], datetime.datetime | None]:
    from app.profile.validators import validate_can_edit_role

    validate_user_permission(user, {'can_edit_roles'})

    valid_data = validators.validate(AddTokensValidator, data)
    emails = valid_data['emails']
    expire_days = valid_data['expire_days']

    roles_ = await select_roles_by_emails(conn, company_id=user.company_id, emails=emails)
    roles: list[Role] = [role for role in roles_ if role]

    # validate roles exists
    if len(roles) != len(emails):
        raise DoesNotExist(Object.email, emails=list(set(emails) - {r.user_email for r in roles}))

    for target_role in roles:
        await validate_can_edit_role(conn, user, target_role)

    existed_tokens_for: set[str] = set()
    role_ids_with_tokens = await select_role_ids_with_tokens(conn, [role.id_ for role in roles])
    for target_role in roles:
        if target_role.id_ in role_ids_with_tokens:
            existed_tokens_for.add(target_role.user_email)

    date_expired = None
    if expire_days:
        date_expired = local_now() + datetime.timedelta(days=expire_days)

    return roles, existed_tokens_for, date_expired


async def validate_delete_token(conn: DBConnection, user: User, data: StrDict) -> str:
    from app.profile.validators import validate_can_edit_role

    validate_user_permission(user, {'can_edit_roles'})

    valid_data = validators.validate(DeleteTokenValidator, data)
    target_role_id = valid_data['role_id']
    target_role = await select_role_by_id(conn, target_role_id)
    if not target_role:
        raise DoesNotExist(Object.role, id=target_role_id)

    await validate_can_edit_role(conn, user, target_role)

    return target_role_id


async def validate_delete_tokens(conn: DBConnection, user: User, data: StrDict) -> list[Role]:
    from app.profile.validators import validate_can_edit_role

    validate_user_permission(user, {'can_edit_roles'})

    valid_data = validators.validate(DeleteTokensValidator, data)
    emails = valid_data['emails']

    roles_ = await select_roles_by_emails(conn, company_id=user.company_id, emails=emails)
    roles: list[Role] = [role for role in roles_ if role]

    # validate roles exists
    if len(roles) != len(emails):
        raise DoesNotExist(Object.email, emails=list(set(emails) - {r.user_email for r in roles}))

    for target_role in roles:
        await validate_can_edit_role(conn, user, target_role)

    return roles


def validate_edrpou(edrpou: str | None) -> str | None:
    """Ensure that test EDRPOU does not used on production."""
    app_config = services.config.app
    test_edrpou = app_config.test_edrpou
    if not app_config.debug and edrpou == test_edrpou:
        raise DoesNotExist(Object.company, edrpou=edrpou)
    return edrpou


async def validate_is_user_login_password_valid(
    request: web.Request,
    conn: DBConnection,
    *,
    password: str,
    user: BaseUser,
) -> None:
    """
    Check if the user password is valid during a login attempt.

    See also: "validate_is_user_current_password_valid" for validating password for already
    authenticated users.
    """
    if user.is_valid_password(password=password):
        return

    zvit_user = await db.select_zvit_user(conn=conn, user_id=user.id)

    # https://vchasno-group.atlassian.net/browse/DOC-7355
    # If password is not set, and there is Zvit user instance for the user
    # Check password using PBKDF2-HMAC-SHA512 against Zvit password
    # And update the password in users table by using bcrypt hashing.
    if (
        not user.password
        and zvit_user
        and check_password_hash_pbkdf2_hmac_sha512(
            hash_str=zvit_user.password,
            password=password,
        )
    ):
        # Upsert bcrypt-hashed password to database
        await update_user(
            conn=conn,
            user_id=user.id,
            data={
                'new_password': password,
                'is_autogenerated_password': False,
            },
        )

        logger.info(
            'Login by using Vchasno Zvit credentials',
            extra={'user_id': user.id},
        )
        return

    # https://vchasno-group.atlassian.net/browse/DOC-7355
    # If user has EDO password and Zvit password at the same time
    # And tries to log in with Zvit credentials
    # Prioritize EDO credentials and raise an error to log in with EDO credentials
    if zvit_user and check_password_hash_pbkdf2_hmac_sha512(
        hash_str=zvit_user.password,
        password=password,
    ):
        raise Error(
            code=Code.invalid_credentials,
            reason=_('Будь ласка, авторизуйтесь, використовуючи пароль від Вчасно.ЕДО'),
        )

    logger.warning(
        msg='Password attempt failed',
        extra={'user_email': user.email, 'user_id': user.id},
    )

    await count_invalid_password_attempts(conn, user=user)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        unsuccessful_login_event = await event_mdl.UnsuccessfulLoginEvent.make(
            user=user,
            source=event_utils.get_source(request),
            extra={
                'ip': get_client_ip(request),
                'user_agent': request.headers.get('User-Agent'),
            },
        )
        event_utils.register_event(unsuccessful_login_event, request)

    # To prevent email enumeration attacks, we should not show distinct error messages
    # for invalid email and invalid password during login
    raise Error(Code.invalid_credentials)


async def validate_is_user_current_password_valid(
    conn: DBConnection,
    *,
    password: str | None,
    user: BaseUser,
) -> None:
    """
    Check if the user password is valid when a user is already authenticated. We usually
    ask authenticated user to enter a password to confirm some dangerous action, like email
    or phone change.

    See also: "validate_is_user_login_password_valid" for password validation during login.
    """
    if user.is_valid_password(password=password):
        return

    logger.warning(
        msg='Password attempt failed',
        extra={'user_email': user.email, 'user_id': user.id},
    )

    await count_invalid_password_attempts(conn, user=user)

    # Unlike with login, we can show a more specific error message here that the password
    # is invalid, because the user is already authenticated
    raise Error(Code.invalid_current_password)


@asynccontextmanager
async def login_rate_limit(email: str) -> t.AsyncGenerator[None, None]:
    key = f'login:{email}'
    try:
        await validate_rate_limit(
            key=key,
            limit=LOGIN_ATTEMPTS_LIMIT,
            delta=ONE_HOUR_DELTA,
        )
    except TooManyRequests as exc:
        raise TooManyRequests(
            reason=_(
                'Перевищено кількість спроб увійти до сервісу. Спробуйте увійти пізніше.',
            ),
            details=exc.details,
        ) from exc

    try:
        yield
    except Exception:
        raise
    else:
        await reset_rate_limit(key)


async def validate_login(
    conn: DBConnection,
    data: StrDict,
    request: web.Request,
) -> LoginUserCtx:
    """Validate user credentials and if OK return user data from DB."""
    valid_data = validators.validate(LoginValidator, data)
    email: str = valid_data['email']
    password: str = valid_data['password']
    remember: bool = valid_data['remember']

    logger.info(
        'Attempt to login',
        extra={
            'email': email,
            'ip': get_client_ip(request) or 'UKNOWN_IP',
        },
    )

    async with login_rate_limit(email=email):
        user = await db.select_base_user(conn, email=email)
        if not user:
            logger.warning(
                'Login attempt failed',
                extra={
                    'reason': 'Invalid email',
                    'email': email,
                },
            )
            raise Error(Code.invalid_credentials)

        await validate_is_user_login_password_valid(
            request=request,
            conn=conn,
            password=password,
            user=user,
        )

    return LoginUserCtx(
        email=email,
        password=password,
        remember=remember,
        user=user,
    )


def validate_user_not_banned(request: web.Request, user: BaseUser | User) -> None:
    if user.is_banned:
        log_extra = {
            'user_id': user.id,
            'user_email': user.email,
            'user_company_edrpou': user.company_edrpou if is_wide_user_type(user) else None,
            'user_role_id': user.role_id if is_wide_user_type(user) else None,
            'request_path': request.path,
        }
        logger.warning('Banned user attempted to access the system', extra=log_extra)
        raise AccessDenied()


def validate_user_permission(
    user: AuthUser | User,
    permission: set[USER_PERMISSION_KEY],
    *,
    all_permissions: bool = True,
) -> None:
    """Validate if user able to access restricted area due to given permission"""

    if not has_permission(user, permission, all_permissions=all_permissions):
        logger.warning(
            'Attempt to access restricted area without required permission',
            extra={
                'company_edrpou': user.company_edrpou,
                'permission': permission,
                'user_role': user.user_role,
                'role_id': user.role_id,
                'user_id': user.id,
                'user_email': user.email,
            },
        )
        raise AccessDenied()


async def validate_upload_counter(conn: DBConnection, company_id: str, value: int = 1) -> None:
    company = await select_company_by_id(conn, company_id)
    if not company:
        raise DoesNotExist(Object.company, id=company_id)

    if company.is_unlimited_upload:
        return

    if company.upload_documents_left < 1 or value > company.upload_documents_left:
        raise Error(Code.upload_overlimit)


async def validate_pending_2fa_web(
    request: web.Request,
    second_factor: AuthFactor,
) -> None:
    """
    Validate that pending 2FA factor matches the one requested by user.
    This is used to ensure that user is trying to verify the correct factor.
    """

    session = await aiohttp_session.get_session(request)
    pending_2fa = two_factor.get_pending_2fa_web(session)
    if not pending_2fa:
        raise InvalidRequest(reason=_('Процес двофакторної аутентифікації не розпочато'))

    if pending_2fa.second_factor != second_factor:
        raise InvalidRequest(
            reason=_('Очікується інший тип двофакторної аутентифікації'),
            details={'pending': pending_2fa.second_factor.name, 'current': second_factor.name},
        )


async def validate_verify_phone_2fa(request: web.Request, user: BaseUser) -> VerifyPhone2faSchema:
    """Ensure that user enters valid TOTP code."""

    raw_data = await validate_json_request(request)
    valid_data = validators.validate_pydantic(VerifyPhone2faSchema, raw_data)

    await helpers.validate_rate_limit(
        key=f'web_2fa:phone_verify:{user.id}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    await validate_pending_2fa_web(
        request=request,
        second_factor=AuthFactor.phone,
    )

    otp = await get_2fa_otp_web(user_id=user.id, email=user.email)
    if not otp or valid_data.code != otp:
        raise Error(Code.invalid_totp_code)

    return valid_data


async def validate_verify_email_2fa(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
) -> VerifyEmail2faSchema:
    """
    Validate that user has valid email 2FA code.
    This is used for email verification during registration.
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(VerifyEmail2faSchema, raw_data)

    await helpers.validate_rate_limit(
        key=f'web_2fa:email_verify:{user.id}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    await validate_pending_2fa_web(
        request=request,
        second_factor=AuthFactor.email,
    )
    if not user.email:
        raise InvalidRequest(reason=_('Користувач не має імейлу для верифікації'))

    async with login_rate_limit(email=user.email):
        await validate_is_user_login_password_valid(
            request=request,
            conn=conn,
            password=data.password,
            user=user,
        )

    return data


def _validate_update_company_config_default_permissions(
    ctx: UpdateCompanyConfigSchema,
    prev_config: CompanyConfig,
    user: User,
) -> None:
    new_permissions = ctx.default_role_permissions_key
    if new_permissions is None:
        return

    # Admin can change anything, no checks needed
    if user.is_admin:
        return

    prev_permissions = prev_config.default_role_permissions_key

    # This one is quite sensitive, so only admin can enable it
    if (
        new_permissions.user_role != prev_permissions.user_role
        and new_permissions.user_role == UserRole.admin
    ):
        raise AccessDenied(
            reason=_('У вас недостатньо прав, щоб змінювати налаштування ролі'),
            details={'invalid_permissions': ['user_role']},
        )

    new_dict = new_permissions.model_dump(include=set(USER_PERMISSIONS))
    prev_dict = prev_permissions.model_dump(include=set(USER_PERMISSIONS))

    _invalid_permissions: list[str] = []
    for permission, new_is_enabled in new_dict.items():
        prev_is_enabled = prev_dict.get(permission)

        # Frontend may send a full object with all permissions even for unchanged ones
        if prev_is_enabled == new_is_enabled:
            continue

        # Disabling permission without checking seems safe, so we allow it.
        if not new_is_enabled and prev_is_enabled:
            continue

        # User can't enable permission not enabled for him to prevent granting access
        # higher access to coworker with malicious intents
        if new_is_enabled and not prev_is_enabled:
            _has_permission = has_direct_permission(user, permission=permission)
            if not _has_permission:
                _invalid_permissions.append(permission)

    if _invalid_permissions:
        raise AccessDenied(
            reason=_('У вас недостатньо прав, щоб увімкнути це налаштування'),
            details={'invalid_permissions': _invalid_permissions},
        )


async def validate_update_company_config(
    conn: DBConnection,
    data: DataDict,
    user: User,
) -> tuple[UpdateCompanyConfigSchema, CompanyConfig]:
    """
    Validate that user has permission to update company config (only limited set of fields)
    """
    validate_user_permission(user, {'can_edit_company'})

    ctx = validators.validate_pydantic(UpdateCompanyConfigSchema, data)

    prev_config = await get_company_config(conn, company_id=user.company_id)

    _validate_update_company_config_default_permissions(ctx, prev_config, user)

    if ctx.enable_2fa_for_internal_users:
        config = await get_billing_company_config(
            conn=conn,
            company_id=user.company_id,
        )
        if not config.can_enforce_2fa:
            raise AccessDenied(
                reason=_('Тариф не передбачає можливість вимагати 2FA для співробітників')
            )

    return ctx, prev_config


async def validate_add_employee(
    conn: DBConnection,
    company_id: str,
    employee_user_id: str | None = None,
    send_notification_to_admins: bool = True,
) -> None:
    if not await is_employee_limit_reached(conn=conn, company_id=company_id):
        return

    if employee_user_id and send_notification_to_admins:
        await services.kafka.send_record(
            topic=topics.INITIATE_REMINDERS_FOR_FAILED_TO_REGISTER_EMPLOYEE,
            value={
                'company_id': company_id,
                'employee_user_id': employee_user_id,
            },
        )

    raise Error(
        Code.overdraft,
        reason=_('Досягнуто максимальної кількості співробітників згідно поточного тарифу'),
    )


def validate_is_admin(user_role: int) -> None:
    if not is_admin(user_role):
        raise AccessDenied()


async def validate_google_auth(request: web.Request) -> DataDict:
    """Basic google auth validator, that extract token from request object"""
    raw_data = await validate_json_request(request)
    valid_data = validators.validate(GoogleAuthValidator, raw_data)
    return valid_data


async def validate_microsoft_auth(request: web.Request) -> DataDict:
    """Basic microsoft auth validator, that extract token from request object"""
    raw_data = await validate_json_request(request)
    valid_data = validators.validate(MicrosoftAuthValidator, raw_data)
    return valid_data


async def validate_logout_all_sessions(
    conn: DBConnection,
    user: User | BaseUser,
    raw_data: DataDict,
) -> LogoutAllSessionsCtx:
    """
    Validate that user can make logout from all sessions.
    Rules:
    - user can make logout only from his own sessions
    - admin of a company can make logout from user's sessions where user has an active role
    """
    valid_data = validators.validate_pydantic(LogoutAllSessionsSchema, raw_data)
    ctx = LogoutAllSessionsCtx(
        logout_user_id=valid_data.user_id or user.id,
        with_mobile=valid_data.with_mobile,
        terminate_current_session=valid_data.terminate_current_session,
    )
    if ctx.logout_user_id == user.id:
        return ctx

    if ctx.logout_user_id != user.id and has_permission(user, {'can_edit_roles'}):
        if not isinstance(user, User):
            raise AccessDenied()

        # admin can logout other users' ALL sessions
        # but logout_current makes no sense so return error instead
        if ctx.terminate_current_session:
            raise Error(Code.invalid_request)

        # make sure that user has a role in the admin's company
        can_admin_logout = await exists(
            conn=conn,
            select_from=role_table,
            clause=sa.and_(
                role_table.c.user_id == ctx.logout_user_id,
                role_table.c.company_id == user.company_id,
                role_table.c.status == RoleStatus.active,
            ),
        )
        if can_admin_logout:
            return ctx

    raise AccessDenied()


async def validate_logout_session(
    conn: DBConnection,
    user: User,
    raw_data: DataDict,
) -> str:
    """
    Validate that user can make logout from the specific session.
    Rules:
    - user can make logout only from his own sessions
    - admin of a company can make logout from user's sessions where user has an active role

    Returns session id used by the session manager. (Used in cookies)
    """
    valid_data = validators.validate_pydantic(LogoutSessionSchema, raw_data)
    public_session_id = valid_data.session_id
    logout_user_id = valid_data.user_id or user.id

    # Check if user can logout from the session
    session_data = await get_user_session_data_by_public_id(
        public_session_id=public_session_id,
        user_id=logout_user_id,
    )
    if not session_data:
        raise AccessDenied()

    session_user_id = get_user_id(session_data.session)
    if session_user_id is None:
        raise AccessDenied()

    if session_user_id == user.id:
        return session_data.internal_id

    if user.is_admin:
        # make sure that user has a role in the admin's company
        can_admin_logout = await exists(
            conn=conn,
            select_from=role_table,
            clause=sa.and_(
                role_table.c.user_id == session_user_id,
                role_table.c.company_id == user.company_id,
                role_table.c.status == RoleStatus.active,
            ),
        )
        if can_admin_logout:
            return session_data.internal_id

    raise AccessDenied()
