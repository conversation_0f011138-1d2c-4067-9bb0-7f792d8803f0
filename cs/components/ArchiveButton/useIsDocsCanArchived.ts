import { useSelector } from 'react-redux';

import { DocumentListDocumentItem } from 'components/documentList/types';
import { getCurrentCompanyArchiveSettings } from 'selectors/app.selectors';

export const useIsDocsCanArchivedByArchiveSettings = (
    docs?: DocumentListDocumentItem[],
) => {
    const archiveSettings = useSelector(getCurrentCompanyArchiveSettings);
    const isDocsCanArchivedBySettings = docs?.every((doc) => {
        switch (doc.status) {
            case 'UPLOADED':
            case 'READY':
                return archiveSettings.allow_uploaded_documents;
            case 'SENT':
            case 'SIGNED':
            case 'SIGNED_AND_SENT':
            case 'FLOW_PROCESS':
                return archiveSettings.allow_partially_signed_documents;
            case 'FINISHED':
                return archiveSettings.allow_fully_signed_documents;
            case 'REJECT':
                return archiveSettings.allow_rejected_documents;
            case 'REVOKED':
                return true;
            default:
                return false;
        }
    });
    return { isDocsCanArchivedBySettings };
};
