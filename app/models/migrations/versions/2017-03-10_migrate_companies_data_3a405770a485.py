"""Migrate companies data.

Revision ID: 3a405770a485
Revises: 7595fd4cec07
Create Date: 2017-03-10 15:31:40.927618


Author: <PERSON>
"""
from collections import defaultdict

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3a405770a485'
down_revision = '7595fd4cec07'
branch_labels = None
depends_on = None


def find_name(items):
    non_empty = [item.name for item in items if item.name]
    if not non_empty:
        return None
    return non_empty[0]


def upgrade():
    conn = op.get_bind()

    all_companies = conn.execute("""
SELECT
    companies.user_id, companies.edrpou, companies.name, companies.date_created,
    users.role_id AS user_role
FROM companies
LEFT OUTER JOIN users ON users.id = companies.user_id
WHERE char_length(edrpou) <= 10
ORDER BY date_created
    """).fetchall()

    grouped = defaultdict(list)
    for row in all_companies:
        grouped[row.edrpou].append(row)

    if grouped:
        unique_companies_data = [
            {
                'edrpou': edrpou,
                'name': find_name(items),
                'date_created': items[0].date_created,
                'date_updated': items[-1].date_created,
            }
            for edrpou, items in grouped.items()]
        conn.execute(sa.text("""
        INSERT INTO unique_companies (edrpou, name, date_created, date_updated)
        VALUES (:edrpou, :name, :date_created, :date_updated)
        """), unique_companies_data)

        roles_data = [
            {
                'company_edrpou': edrpou,
                'user_id': item.user_id,
                'user_role': item.user_role,
            }
            for edrpou, items in grouped.items()
            for item in items]
        conn.execute(sa.text("""
        INSERT INTO roles (company_edrpou, user_id, user_role)
        VALUES (:company_edrpou, :user_id, :user_role)
        """), roles_data)


def downgrade():
    """
    Backwards migration omited as it from my point view is useless to
    implement and run. @i.davydenko
    """
