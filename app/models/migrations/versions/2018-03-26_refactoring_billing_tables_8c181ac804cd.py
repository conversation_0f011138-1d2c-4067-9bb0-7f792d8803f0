"""Refactoring billing tables.

Revision ID: 8c181ac804cd
Revises: bb486dc27b80
Create Date: 2018-03-26 19:54:05.111013


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import modify_db_enum

# revision identifiers, used by Alembic.
revision = '8c181ac804cd'
down_revision = 'bb486dc27b80'
branch_labels = None
depends_on = None

account_type_enum = postgresql.ENUM(
    'service_credit_external',
    'service_credit_bonus',
    'service_debit_external',
    'service_debit_bonus',
    'service_debt',
    'client_debit',
    'client_bonus',
    'client_credit',
    name='accounttype',
    create_type=False)
balance_account_enum = postgresql.ENUM(
    'free',
    'paid',
    'upload',
    name='balanceaccount',
    create_type=False)
transaction_type_enum = postgresql.ENUM(
    'invoice_payment',
    'invoice_payment_cancel',
    'debit_charge_off',
    'bonus_income',
    'bonus_cancel',
    'bonus_charge_off',
    'credit_use',
    'credit_repay',
    'debt_clearance',
    'cross_company',
    name='transactiontype',
    create_type=False)


def upgrade():
    modify_db_enum(
        op,
        'bonuses',
        'type',
        'bonustype',
        "('bonus', 'promo')"
    )
    account_type_enum.create(op.get_bind())
    transaction_type_enum.create(op.get_bind())

    op.create_table(
        'billing_accounts',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column('initiator_id', postgresql.UUID(), server_default=None, nullable=True),
        sa.Column('type', account_type_enum, nullable=False),
        sa.Column('amount', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('amount_left', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('units', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('units_left', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('date_expired', sa.DateTime(timezone=True), nullable=True),
        sa.Column('date_deleted', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('company_id')
    )

    op.create_table(
        'billing_transactions',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('from', postgresql.UUID(), nullable=False),
        sa.Column('to', postgresql.UUID(), nullable=False),
        sa.Column('from_company_id', postgresql.UUID(), nullable=False),
        sa.Column('to_company_id', postgresql.UUID(), nullable=False),
        sa.Column('operator_id', postgresql.UUID(), nullable=True),
        sa.Column('initiator_id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('type', transaction_type_enum, nullable=False),
        sa.Column('from_type', account_type_enum, nullable=False),
        sa.Column('to_type', account_type_enum, nullable=False),
        sa.Column('amount', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('units', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('comment', sa.Text(), nullable=True),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['from'], ['billing_accounts.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['from_company_id'], ['companies.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['operator_id'], ['roles.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['to'], ['billing_accounts.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['to_company_id'], ['companies.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id')
    )

    op.add_column('bonuses', sa.Column('created_by', postgresql.UUID(), nullable=True))
    op.add_column('bonuses', sa.Column('date_expired', sa.DateTime(timezone=True), nullable=True))
    op.add_column('bonuses', sa.Column('period', sa.Integer(), server_default=sa.text('0'), nullable=False))
    op.add_column('bonuses', sa.Column('units', sa.Integer(), server_default=sa.text('0'), nullable=False))
    op.alter_column(
        'bonuses',
        'type',
        existing_type=postgresql.ENUM('registration', 'superadmin', name='bonustype'),
        type_=postgresql.ENUM('bonus', 'promo', name='bonustype'),
        existing_nullable=False)
    op.drop_constraint('bonuses_type_key', 'bonuses', type_='unique')
    op.create_foreign_key('bonuses_role_id_fkey', 'bonuses', 'roles', ['created_by'], ['id'], ondelete='NO ACTION')

    op.drop_table('transactions')
    op.drop_table('charges')
    op.drop_table('expirations')
    op.drop_table('balances')
    op.drop_table('promos')
    balance_account_enum.drop(op.get_bind())


def downgrade():
    modify_db_enum(
        op,
        'bonuses',
        'type',
        'bonustype',
        "('registration', 'superadmin')"
    )
    balance_account_enum.create(op.get_bind())

    op.drop_constraint('bonuses_role_id_fkey', 'bonuses', type_='foreignkey')
    op.create_unique_constraint('bonuses_type_key', 'bonuses', ['type'])
    op.alter_column(
        'bonuses',
        'type',
        existing_type=postgresql.ENUM('bonus', 'promo', name='bonustype'),
        type_=postgresql.ENUM('registration', 'superadmin', name='bonustype'),
        existing_nullable=False)
    op.drop_column('bonuses', 'units')
    op.drop_column('bonuses', 'period')
    op.drop_column('bonuses', 'date_expired')
    op.drop_column('bonuses', 'created_by')

    op.create_table(
        'promos',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('created_by', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('name', sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column('amount', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('date_created', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.Column('date_deleted', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column('date_expired', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['roles.id'], name='promos_created_by_fkey'),
        sa.PrimaryKeyConstraint('id', name='promos_pkey')
    )

    op.create_table(
        'balances',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('company_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('free', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('paid', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('upload', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('is_unlimited_upload', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=False),
        sa.Column('date_created', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], name='balances_company_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='balances_pkey'),
        sa.UniqueConstraint('company_id', name='balances_company_id_key')
    )

    op.create_table(
        'expirations',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('balance_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('bonus_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('promo_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('account', balance_account_enum, autoincrement=False, nullable=False),
        sa.Column('amount', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('date_created', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.Column('date_expired', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(['balance_id'], ['balances.id'], name='expirations_balance_id_fkey'),
        sa.ForeignKeyConstraint(['bonus_id'], ['bonuses.id'], name='expirations_bonus_id_fkey'),
        sa.ForeignKeyConstraint(['promo_id'], ['promos.id'], name='expirations_promo_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='expirations_pkey')
    )

    op.create_table(
        'charges',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('balance_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('document_id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('role_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('bonus_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('promo_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('account', balance_account_enum, autoincrement=False, nullable=False),
        sa.Column('date_created', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(['balance_id'], ['balances.id'], name='charges_balance_id_fkey'),
        sa.ForeignKeyConstraint(['bonus_id'], ['bonuses.id'], name='charges_bonus_id_fkey'),
        sa.ForeignKeyConstraint(['promo_id'], ['promos.id'], name='charges_promo_id_fkey'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='charges_role_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='charges_pkey')
    )

    op.create_table(
        'transactions',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('balance_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('bill_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('bonus_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('promo_id', postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column('account', balance_account_enum, autoincrement=False, nullable=False),
        sa.Column('amount', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=False),
        sa.Column('date_created', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(['balance_id'], ['balances.id'], name='transactions_balance_id_fkey'),
        sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], name='transactions_bill_id_fkey'),
        sa.ForeignKeyConstraint(['bonus_id'], ['bonuses.id'], name='transactions_bonus_id_fkey'),
        sa.ForeignKeyConstraint(['promo_id'], ['promos.id'], name='transactions_promo_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='transactions_pkey')
    )

    op.drop_table('billing_transactions')
    op.drop_table('billing_accounts')
    account_type_enum.drop(op.get_bind())
    transaction_type_enum.drop(op.get_bind())
