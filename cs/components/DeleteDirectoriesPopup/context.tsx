import React, { useState } from 'react';

import { GridApi } from '@ag-grid-community/core';

import { useDeleteDirectoriesMutation } from 'components/DeleteDirectoriesPopup/useDeleteDirectoriesMutation';
import { DocumentDirectory } from 'gql-types';

interface IContext {
    isActive: boolean;
    onDelete: () => void;
    onOpen: (
        directoriesForDelete: DocumentDirectory[],
        newGridApi?: Nullable<GridApi>,
    ) => void;
    onClose: () => void;
    directories: DocumentDirectory[];
}

const DeleteDirectoriesPopupContext = React.createContext({} as IContext);

export const DeleteDirectoriesPopupProvider: React.FC = (props) => {
    const [isActive, setIsActive] = useState(false);
    const [directories, setDirectories] = useState<DocumentDirectory[]>([]);

    const deleteDirectoriesMutation = useDeleteDirectoriesMutation();

    const onClose = () => {
        setIsActive(false);
    };

    const onOpen: IContext['onOpen'] = (directoriesForDelete) => {
        setIsActive(true);
        setDirectories(directoriesForDelete);
    };

    const onDelete = () => {
        const directoriesIds = directories.map((directory) => directory.id);
        deleteDirectoriesMutation.mutateAsync([directoriesIds]);
        onClose();
    };

    const value = {
        isActive,
        onOpen,
        onClose,
        onDelete,
        directories,
    };

    return (
        <DeleteDirectoriesPopupContext.Provider value={value}>
            {props.children}
        </DeleteDirectoriesPopupContext.Provider>
    );
};

export function useDeleteDirectoriesPopupContext() {
    const context = React.useContext(DeleteDirectoriesPopupContext);

    if (Object.keys(context).length === 0) {
        throw new Error(
            'useDeleteDirectoriesPopupContext must be inside DeleteDirectoriesPopupProvider',
        );
    }

    return context;
}
