import logging
from datetime import timed<PERSON><PERSON>
from http import HTTPStatus

import sqlalchemy as sa

from api.public.tests.common import DATA_PATH
from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.tables import (
    document_automation_condition_table,
    document_automation_template_table,
)
from app.documents.types import (
    Document,
    UpdateSignersDataSignerEntity,
    UpdateSignersDataSignerType,
)
from app.lib.database import DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import SignersSource
from app.models import select_one
from app.services import services
from app.signatures.db import select_document_signers
from app.signatures.tables import document_signer_table
from app.signatures.utils import update_document_signers
from app.tests.common import (
    API_V2_DOCUMENTS_URL,
    API_V2_SEND_DOCUMENT_URL,
    insert_values,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_form_data,
    prepare_user_data,
    request_document_update,
    sign_document,
)
from worker.emailing.constants import LAST_EXECUTION_TIME_REDIS_KEY
from worker.emailing.jobs import send_notification_about_sign_process
from worker.emailing.utils import (
    select_recipients_for_sign_process_finished,
)

logger = logging.getLogger(__name__)


ROLE_ID_1 = '********-0000-0000-0000-************'
ROLE_ID_2 = '********-0000-0000-0000-************'
ROLE_ID_3 = '********-0000-0000-0000-************'
ROLE_ID_4 = '********-0000-0000-0000-************'

DOCUMENT_ID_1 = '********-0000-0000-0000-************'
DOCUMENT_ID_2 = '********-0000-0000-0000-************'
DOCUMENT_ID_3 = '********-0000-0000-0000-************'
DOCUMENT_ID_4 = '********-0000-0000-0000-************'


async def test_send_email_to_signers(aiohttp_client, mailbox):
    app, client, user1 = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
        can_receive_sign_process_finished=True,
        can_receive_sign_process_finished_assigner=True,
        role_id=ROLE_ID_1,
    )
    user2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        role_id=ROLE_ID_2,
        can_receive_sign_process_finished=True,
        can_receive_sign_process_finished_assigner=True,
    )
    user3 = await prepare_user_data(
        app,
        email='<EMAIL>',
        role_id=ROLE_ID_3,
        can_receive_sign_process_finished=True,
        can_receive_sign_process_finished_assigner=False,
    )
    user4 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        can_receive_sign_process_finished=False,
        can_receive_sign_process_finished_assigner=True,
        role_id=ROLE_ID_4,
    )

    document1 = await prepare_document_data(app, user1, id=DOCUMENT_ID_1)
    document2_with_one_signer = await prepare_document_data(app, user3, id=DOCUMENT_ID_2)
    document3_with_one_signer = await prepare_document_data(app, user4, id=DOCUMENT_ID_3)
    document4_not_fully_signed = await prepare_document_data(app, user1, id=DOCUMENT_ID_4)

    await send_notification_about_sign_process(app, {'start_time': utc_now().timestamp()}, logger)

    assert len(mailbox) == 0

    async with app['db'].acquire() as conn:
        await update_document_signers(
            conn,
            document=Document.from_row(document1),
            company_id=user4.company_id,
            company_edrpou=user4.company_edrpou,
            signers=[
                UpdateSignersDataSignerEntity(
                    type=UpdateSignersDataSignerType.role,
                    id=role_id,
                )
                for role_id in [user2.role_id, user3.role_id]
            ],
            parallel_signing=False,
            is_document_owner=True,
            current_role_id=user4.role_id,
            signers_source=SignersSource.api,
        )
        await update_document_signers(
            conn,
            document=Document.from_row(document4_not_fully_signed),
            company_id=user1.company_id,
            company_edrpou=user1.company_edrpou,
            signers=[
                UpdateSignersDataSignerEntity(
                    type=UpdateSignersDataSignerType.role,
                    id=role_id,
                )
                for role_id in [user2.role_id, user3.role_id]
            ],
            parallel_signing=False,
            is_document_owner=True,
            current_role_id=user1.role_id,
            signers_source=SignersSource.template,
        )
        await update_document_signers(
            conn,
            document=Document.from_row(document2_with_one_signer),
            company_id=user1.company_id,
            company_edrpou=user1.company_edrpou,
            signers=[
                UpdateSignersDataSignerEntity(
                    type=UpdateSignersDataSignerType.role,
                    id=user2.role_id,
                )
            ],
            parallel_signing=False,
            is_document_owner=True,
            current_role_id=user1.role_id,
            signers_source=None,
        )
        await update_document_signers(
            conn,
            document=Document.from_row(document3_with_one_signer),
            company_id=user3.company_id,
            company_edrpou=user3.company_edrpou,
            signers=[
                UpdateSignersDataSignerEntity(
                    type=UpdateSignersDataSignerType.role,
                    id=user2.role_id,
                )
            ],
            parallel_signing=False,
            is_document_owner=True,
            current_role_id=user3.role_id,
            signers_source=None,
        )

        async def select_signer_date_signed(role_id, document_id):
            signer = await select_one(
                conn=conn,
                query=(
                    sa.select([document_signer_table]).where(
                        sa.and_(
                            document_signer_table.c.role_id == role_id,
                            document_signer_table.c.document_id == document_id,
                        )
                    )
                ),
            )
            return signer.date_signed

        # Signatures from first user
        response = await sign_document(
            client,
            document_id=document1.id,
            signer=user2,
            recipient_email='<EMAIL>',
            recipient_edrpou='0101201020',
        )
        assert response.status == HTTPStatus.CREATED
        mailbox.clear()

        assert await select_signer_date_signed(user2.role_id, document1.id) is not None
        assert await select_signer_date_signed(user3.role_id, document1.id) is None

    async def clean_redis_key():
        """
        Clear the redis key to ensure that the next notification will be sent.
        This is necessary because the notification is sent only once per hour.
        We remember when the last notification was sent and skip sending
        for documents that were signed outside that time period.
        But in tests we want to send the notification immediately.
        """
        await services.redis.delete(LAST_EXECUTION_TIME_REDIS_KEY)

    mailbox.clear()
    await send_notification_about_sign_process(app, {'start_time': utc_now().timestamp()}, logger)
    assert len(mailbox) == 0

    response = await sign_document(
        client,
        document_id=document4_not_fully_signed.id,
        signer=user2,
        recipient_email='<EMAIL>',
        recipient_edrpou='0101201020',
    )
    assert response.status == HTTPStatus.CREATED
    mailbox.clear()

    await clean_redis_key()
    await send_notification_about_sign_process(app, {'start_time': utc_now().timestamp()}, logger)
    assert len(mailbox) == 0

    response = await sign_document(
        client,
        document_id=document2_with_one_signer.id,
        signer=user2,
        recipient_email='<EMAIL>',
        recipient_edrpou='0101201020',
    )
    assert response.status == HTTPStatus.CREATED
    mailbox.clear()

    # Signatures from second user
    response = await sign_document(
        client,
        document_id=document1.id,
        signer=user3,
        recipient_email='<EMAIL>',
        recipient_edrpou='0101201020',
    )
    assert response.status == HTTPStatus.CREATED

    # notify assigner
    mailbox.clear()
    await clean_redis_key()
    await send_notification_about_sign_process(app, {'start_time': utc_now().timestamp()}, logger)
    assert len(mailbox) == 3, [m['To'] for m in mailbox]
    assert {m['To'] for m in mailbox} == {user1.email, user3.email, user4.email}

    response = await sign_document(
        client,
        document_id=document3_with_one_signer.id,
        signer=user2,
        recipient_email='<EMAIL>',
        recipient_edrpou='0101201020',
    )
    assert response.status == HTTPStatus.CREATED

    # notify user(assigner) and user3(uploader)
    start_time = utc_now()
    mailbox.clear()
    await clean_redis_key()
    await send_notification_about_sign_process(app, {'start_time': start_time.timestamp()}, logger)
    assert len(mailbox) == 3, [m['To'] for m in mailbox]
    assert {m['To'] for m in mailbox} == {user1.email, user3.email, user4.email}

    async with app['db'].acquire() as conn:
        recipients = await select_recipients_for_sign_process_finished(
            conn=conn,
            from_time=start_time - timedelta(days=1),
            to_time=start_time,
        )

    assert len(recipients) == 4

    is_assigner, is_uploader = 'assigner', 'uploader'
    expected_mapping = {
        # document 1
        (user1.role_id, document1.id, is_uploader),
        (user4.role_id, document1.id, is_assigner),
        # document 2
        (user3.role_id, document2_with_one_signer.id, is_uploader),
        (user1.role_id, document2_with_one_signer.id, is_assigner),
        # document 3
        # user 3 is not notified because he can't receive a sign process finished notification
        # user 4 is not notified because he can't receive a sign process finished notification
    }
    assert {
        (r.role_id, r.document_id, is_assigner if r.is_assigner else is_uploader)
        for r in recipients
    } == expected_mapping

    assert {r.document_id for r in recipients} == {
        document1.id,
        document2_with_one_signer.id,
    }
    assert {r.role_id for r in recipients} == {
        user1.role_id,
        user3.role_id,
        user4.role_id,
    }


async def test_update_signers_by_himself_to_himself_email_notifications_web(
    aiohttp_client, mailbox
):
    async def _assert_update_web_signers_notifications(
        signers: list[DBRow],
        expected_notified_users: set[str],
        parallel_signing: bool = True,
    ):
        response = await request_document_update(
            client,
            user=user,
            document=document,
            signers=signers,
            parallel_signing=parallel_signing,
        )
        assert response.status == HTTPStatus.OK
        assert {m['To'] for m in mailbox} == expected_notified_users
        mailbox.clear()

    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    user1 = await prepare_user_data(app, email='<EMAIL>')
    user2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)
    assert len(mailbox) == 0

    # Parallel from web (internal_api)
    await _assert_update_web_signers_notifications([user, user1, user2], {user1.email, user2.email})

    await _assert_update_web_signers_notifications([user1, user2, user], {user1.email, user2.email})

    # sequential signing from web (internal_api)
    await _assert_update_web_signers_notifications(
        [user, user1, user2], set(), parallel_signing=False
    )

    await _assert_update_web_signers_notifications(
        [user1, user, user2], {user1.email}, parallel_signing=False
    )


async def test_set_signers_by_himself_to_himself_email_notifications_api(aiohttp_client, mailbox):
    async def _assert_send_doc_api_notifications(
        signers: list, expected_signers: set, parallel_signing: bool = True
    ):
        assert len(mailbox) == 0
        response = await client.post(
            API_V2_DOCUMENTS_URL,
            data=prepare_form_data(DATA_PATH / 'file.xml'),
            headers=prepare_auth_headers(user),
            params={
                'signer_emails': signers,
                'parallel_signing': 1 if parallel_signing else 0,
            },
        )
        assert response.status == HTTPStatus.CREATED
        assert {m['To'] for m in mailbox} == expected_signers
        assert len(mailbox) == len(expected_signers)
        mailbox.clear()

    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    user1 = await prepare_user_data(app, email='<EMAIL>')
    user2 = await prepare_user_data(app, email='<EMAIL>')

    # Parallel from api (public)
    await _assert_send_doc_api_notifications(
        [user.email, user1.email, user2.email], {user.email, user1.email, user2.email}
    )

    # sequential signing from api (public)
    await _assert_send_doc_api_notifications(
        [user.email, user1.email, user2.email], {user.email}, False
    )


async def test_set_signers_update_email_notifications_template(aiohttp_client, mailbox):
    async def _assert_update_api_notifications(
        signers: list[DBRow],
        expected_notified_users: set[str],
        is_parallel: bool = True,
    ):
        response = await client.post(
            f'/api/v2/documents/{document.id}/signers',
            json={'is_parallel': is_parallel, 'roles': signers},
            headers=prepare_auth_headers(user),
        )
        assert response.status == HTTPStatus.OK
        assert {m['To'] for m in mailbox} == expected_notified_users
        assert len(mailbox) == len(expected_notified_users)
        mailbox.clear()

    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    user1 = await prepare_user_data(app, email='<EMAIL>')
    user2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)
    assert len(mailbox) == 0
    await _assert_update_api_notifications(
        [user.role_id, user1.role_id, user2.role_id],
        {user.email, user1.email, user2.email},
    )
    await _assert_update_api_notifications(
        [user.role_id, user1.role_id, user2.role_id], {user.email}, is_parallel=False
    )


async def test_set_signers_by_himself_to_himself_email_notifications_template(
    aiohttp_client, mailbox
):
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        email='<EMAIL>',
        company_edrpou='********',
    )
    user1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='********')
    user2 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='********')
    user_sender = await prepare_user_data(
        app, email='<EMAIL>', create_api_account=True
    )

    # Templates
    template1 = await insert_values(
        app=app,
        table=document_automation_template_table,
        company_id=user.company_id,
        name='Template1',
        is_active=True,
        set_review=False,
        review_settings=None,
        set_signers=True,
        signers_settings={
            'is_ordered': False,
            'signers_ids': [user.role_id, user1.role_id, user2.role_id],
        },
        created_by=user.role_id,
    )
    await insert_values(
        app=app,
        table=document_automation_condition_table,
        company_id=user.company_id,
        conditions={'eq': ['#document_uploaded_by', user_sender.email]},
        template_id=template1.id,
        order=1,
        status=DocumentAutomationStatus.enabled,
    )

    response = await client.post(
        API_V2_DOCUMENTS_URL,
        data=prepare_form_data(DATA_PATH / 'file.xml'),
        headers=prepare_auth_headers(user_sender),
        params={'first_sign_by': 'recipient'},
    )
    assert response.status == HTTPStatus.CREATED
    doc_id = (await response.json())['documents'][0]['id']

    response = await client.post(
        API_V2_SEND_DOCUMENT_URL.format(document_id=doc_id),
        headers=prepare_auth_headers(user_sender),
        json={'edrpou': user.company_edrpou, 'emails': [user.email]},
    )
    assert response.status == HTTPStatus.OK

    async with app['db'].acquire() as conn:
        signers = await select_document_signers(
            conn,
            document_id=doc_id,
            company_edrpou=user.company_edrpou,
        )
    assert len(signers) == 3
    assert signers[0].source.value == SignersSource.template.value

    expected_signers_ids = set()
    for m in mailbox:
        if m['Subject'] == 'Ви отримали документ на підпис':
            expected_signers_ids.add(m['To'])

    assert expected_signers_ids == {user.email, user2.email, user1.email}
    mailbox.clear()
