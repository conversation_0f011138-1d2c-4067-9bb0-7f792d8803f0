"""Users can delete documents.

Revision ID: 4f8588099e97
Revises: 8ecd58210fa1
Create Date: 2017-04-24 18:14:20.827670


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4f8588099e97'
down_revision = '8ecd58210fa1'
branch_labels = None
depends_on = None

def upgrade():
    op.execute("""
UPDATE roles
SET can_delete_document = TRUE;
    """)


def downgrade():
    pass
