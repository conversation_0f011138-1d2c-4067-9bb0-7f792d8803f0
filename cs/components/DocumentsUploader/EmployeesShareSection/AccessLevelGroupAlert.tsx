import React from 'react';

import { Alert } from '@vchasno/ui-kit';

import { Actor } from 'components/DocumentsUploader/types';
import { useUploadDocumentsFormContext } from 'components/DocumentsUploader/useUploadDocumentsFormContext';
import { t } from 'ttag';

const isGroup = (actor: Actor) => actor.type === 'group';

const AccessLevelGroupAlert: React.FC = () => {
    const methods = useUploadDocumentsFormContext();
    const accessLevel = methods.watch('accessLevel');
    const employeesViewers = methods.watch('employeesViewers');
    const employeesReviewers = methods.watch('employeesReviewers');
    const employeesSigners = methods.watch('employeesSigners');

    if (accessLevel !== 'private') {
        return null;
    }

    if (
        employeesViewers.some(isGroup) ||
        employeesReviewers.some(isGroup) ||
        employeesSigners.some(isGroup)
    ) {
        return (
            <Alert
                wide
                type="warning"
            >{t`При додаванні команди, кожен її учасник отримає доступ до перегляду документу. Якщо склад команди змінюється, нові учасники отримують доступ до документів команди.`}</Alert>
        );
    }

    return null;
};

export default AccessLevelGroupAlert;
