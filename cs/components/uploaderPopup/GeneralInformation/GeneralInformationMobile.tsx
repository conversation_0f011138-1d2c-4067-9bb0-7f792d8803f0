import React from 'react';

import DocumentIsVersionedSwitcherBlock from '../../DocumentIsVersionedSwitcherBlock/DocumentIsVersionedSwitcherBlock';
import FlexBox from '../../FlexBox';
import * as Inputs from '../Inputs';

import css from './GeneralInformation.css';

const GeneralInformationMobile: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    return (
        <FlexBox direction="column" gap={10}>
            <FlexBox className={css.inputRow}>
                <Inputs.DocumentNameInput />
            </FlexBox>
            <FlexBox className={css.inputRow}>
                <Inputs.DocumentNumberInput />
            </FlexBox>
            <FlexBox className={css.inputRow}>
                <Inputs.DocumentDatePicker />
            </FlexBox>
            <FlexBox className={css.inputRow}>
                <Inputs.DocumentCategoryDropdown />
            </FlexBox>
            <FlexBox className={css.version}>
                <DocumentIsVersionedSwitcherBlock />
            </FlexBox>
        </FlexBox>
    );
};

export default GeneralInformationMobile;
