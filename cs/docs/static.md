# Процес збірки

Генерація статики відбувається за допомогою **Webpack**. Для локальної розробки запускаємо
**webpack** у **watch** режимі. Для продакшену генеруємо статику з додаванням **hash-ів**. Під час
локальної розробки всі запити йдуть на локальний сервер, а під час продакшену — на сервери **S3**
сервісу **AWS**.

## Entry points

Основний **entry point** — це **cs/pages/app.bootstrap.ts**
[https://edo.vchasno.ua/app](https://edo.vchasno.ua/app).

Веб-додаток авторизації — це **cs/pages/auth.bootstrap.ts**
[https://edo.vchasno.ua/auth](https://edo.vchasno.ua/auth).

**Entry points** для перегляду документів (використовуються в основному через **iframe**):

- **cs/pages/txtXmlViewer.bootstrap.ts**
- **cs/pages/imageViewer.bootstrap.ts**
- **cs/pages/pdfViewer.bootstrap.ts**
- **cs/pages/officeViewer.bootstrap.ts**

Статичні сторінки, такі як **cs/pages/invalidToken.bootstrap.ts** та
**cs/pages/billGeneration.bootstrap.ts**, — це не вичерпний перелік статичних сторінок, а лише
приклади.

Лендінг [https://vchasno.ua](https://vchasno.ua) розробляється і підтримується WordPress
розробником.

---

## Згенерувати статику

Щоб згенерувати статику, необхідно виконати наступні кроки:

1. Встановити залежності проєкту (використовується **Yarn**):

```bash
yarn install
```

2. Згенерувати статику js/css/html/fonts/images:

```bash
yarn run build
```

3. Статика буде згенерована в папці `static`.

---

## Деплой

Деплой статики відбувається в процесі проходження **pipeline** в **Gitlab CI**.

- Для **dev** оточення запуск **pipelines** відбувається після merge в гілку `master`.
- Для **prod** оточення запуск **pipelines** відбувається після додавання тегу.
  (`just push-new-tag`)

Створення статики в Gitlab CI оточенні відбувається за допомогою **Docker**. Для цього створюється
**image** на базі `config/docker/deploy/frontend.dockerfile`. Він будує статику та вивантажує її на
**S3**.

---

## Структура папки **static**:

<table>
<tr><th>path</th><th>опис</th></tr>
<tr><td>js</td><td>скомпільовані js файли з hash-ами, entry point-ами, chunk-ами та mf модулями</td></tr>
<tr><td>js/lib</td><td>бібліотеки iit/pdfjs</td></tr>
<tr><td>css</td><td>скомпільовані css файли з hash-ами</td></tr>
<tr><td>html</td><td>статичні html файли для кожного entry point</td></tr>
<tr><td>assets</td><td>статичні файли, які не підлягають компіляції (наприклад, зображення)</td></tr>
<tr><td>assets/fonts</td><td>шрифти з hash-ами</td></tr>
<tr><td>assets/images</td><td>зображення з hash-ами</td></tr>
<tr><td>mf-manifest.json</td><td>маніфест для федерації модулів</td></tr>
<tr><td>mf-stats.json</td><td>статистика для федерації модулів</td></tr>
</table>

Також є частина статики, яка не підлягає компіляції, і вона знаходиться в корені папки **static** та
збережена під **git**.

<table>
<tr><th>path</th><th>опис</th></tr>
<tr><td>files</td><td>файли, які не підлягають компіляції (наприклад, відео)</td></tr>
<tr><td>fonts</td><td>шрифти, старий підхід без hash-ів</td></tr>
<tr><td>images</td><td>зображення, старий підхід без hash-ів</td></tr>
<tr><td>favicons</td><td>favicons</td></tr>
</table>

[<- Повернутися назад](../README.md)
