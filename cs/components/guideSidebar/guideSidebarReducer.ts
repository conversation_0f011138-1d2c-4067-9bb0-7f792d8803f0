import { Thunk } from '../../types';
import { StoreState } from '../../types/store';

import { getCurrentUserRole } from '../../selectors/app.selectors';

import {
    WS_KEY_GUIDE_ICON_HIGHLIGHTED,
    WS_KEY_GUIDE_SIDEBAR_SHOWN,
} from '../../lib/constants';
import { getLocalStorageItem, setLocalStorageItem } from '../../lib/webStorage';
import eventTracking from '../../services/analytics/eventTracking';
import { WS_KEY_FLOW_SHOWN } from '../Onboarding/constants';

const SET_VISIBLE_ACTION = 'GUIDE_SIDEBAR__SET_VISIBLE';
const HIGHLIGHT_ICON_ACTION = 'GUIDE_SIDEBAR__HIGHLIGHT_ICON';

const initialState = {
    isOpen: false,
    highlightIcon: false,
};

export const getIsGuidSidebarOpen = (state: StoreState) =>
    state.guideSidebar.isOpen;

export const getIsGuidSidebarHighlightIcon = (state: StoreState) =>
    state.guideSidebar.highlightIcon;

export type GuideSidebarState = typeof initialState;

export const setVisible = (isOpen: boolean) =>
    ({
        type: SET_VISIBLE_ACTION,
        isOpen,
    } as const);

export const highlightIcon = () =>
    ({
        type: HIGHLIGHT_ICON_ACTION,
    } as const);

export const onSidebarMount = (): Thunk => (dispatch, getState) => {
    const { onboarding } = getState();

    const wasShownGuideSidebar = getLocalStorageItem(
        WS_KEY_GUIDE_SIDEBAR_SHOWN,
    );

    const onboardingWasShown = getLocalStorageItem(
        `${getCurrentUserRole(getState()).id}.${WS_KEY_FLOW_SHOWN}`,
    );

    if (!wasShownGuideSidebar && !onboarding.isShown && onboardingWasShown) {
        dispatch(setVisible(true));
        setLocalStorageItem(WS_KEY_GUIDE_SIDEBAR_SHOWN, true);
    }
};

export const openSidebar = (): Thunk => (dispatch) => {
    dispatch(setVisible(true));

    eventTracking.sendToGTM({
        category: 'Side_menu',
        action: 'Press_help_button',
    });
};

export const closeSidebar = (): Thunk => (dispatch, getState) => {
    const { isOpen } = getState().guideSidebar;

    if (isOpen) {
        dispatch(setVisible(false));

        const iconWasHighlighted = getLocalStorageItem(
            WS_KEY_GUIDE_ICON_HIGHLIGHTED,
        );

        if (!iconWasHighlighted) {
            dispatch(highlightIcon());
            setLocalStorageItem(WS_KEY_GUIDE_ICON_HIGHLIGHTED, true);
        }
    }
};

type ActionTypes = ReturnType<typeof setVisible | typeof highlightIcon>;

export const guideSidebarReducer = (
    state = initialState,
    action: ActionTypes,
) => {
    switch (action.type) {
        case SET_VISIBLE_ACTION:
            return {
                ...state,
                isOpen: action.isOpen,
            };
        case HIGHLIGHT_ICON_ACTION:
            return {
                ...state,
                highlightIcon: true,
            };
        default:
            return state;
    }
};
