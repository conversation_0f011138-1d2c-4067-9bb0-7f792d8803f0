import React from 'react';

import PropTypes from 'prop-types';

import { formatPrice } from '../../../../lib/numbers';
import { XmlProducts } from '../../../../records/xml';
import uuid from '../../../../services/uuid';

import css from './cargoTable.css';

const CargoTable = ({ data }) => (
    <div className={css.root}>
        <table className={css.table}>
            <thead>
                <tr>
                    <th className={css.cell}>№ з/п</th>
                    <th className={css.cell}>
                        Найменування вантажу (номер контейнера), клас
                        небезпечних речовин
                    </th>
                    <th className={css.cell}>Одиниця виміру</th>
                    <th className={css.cell}>Кількість місць</th>
                    <th className={css.cell}>Ціна без ПДВ за одиницю, грн</th>
                    <th className={css.cell}>Загальна сума з ПДВ, грн</th>
                    <th className={css.cell}>Вид пакування</th>
                    <th className={css.cell}>Документи з вантажем</th>
                    <th className={css.cell}>Маса брутто, т</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th className={css.cell}>7</th>
                    <th className={css.cell}>8</th>
                    <th className={css.cell}>9</th>
                    <th className={css.cell}>10</th>
                    <th className={css.cell}>11</th>
                    <th className={css.cell}>12</th>
                    <th className={css.cell}>13</th>
                    <th className={css.cell}>14</th>
                    <th className={css.cell}>15</th>
                </tr>
                {data.items.map((service) => (
                    <tr key={`services-${service.number || uuid()}`}>
                        <td className={css.cell}>{service.number}</td>
                        <td className={css.cell}>{service.name}</td>
                        <td className={css.cell}>{service.unit}</td>
                        <td className={css.cell}>{service.quantity}</td>
                        <td className={css.cell}>
                            {formatPrice(service.price)}
                        </td>
                        <td className={css.cell}>{formatPrice(service.sum)}</td>
                        <td className={css.cell}>{service.packageType}</td>
                        <td className={css.cell}>{service.documents}</td>
                        <td className={css.cell}>{service.grossWeight}</td>
                    </tr>
                ))}
                <tr>
                    <td colSpan={2} className={css.cell}>
                        <b>Усього:</b>
                    </td>
                    <td className={css.cell} />
                    <td className={css.cell}>{data.totalQuantity}</td>
                    <td className={css.cell} />
                    <td className={css.cell}>{formatPrice(data.totalSum)}</td>
                    <td className={css.cell} />
                    <td className={css.cell} />
                    <td className={css.cell} />
                </tr>
            </tbody>
        </table>
    </div>
);

CargoTable.propTypes = {
    data: PropTypes.instanceOf(XmlProducts).isRequired,
};

export default CargoTable;
