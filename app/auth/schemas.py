from __future__ import annotations

from typing import Any

from pydantic import BaseModel, ConfigDict, Field

from app.billing.constants import INTEGRATION_PRICE_PER_DOCUMENT
from app.document_versions.enums import VersionReviewFlow
from app.lib.enums import (
    DocumentStatus,
    RenderSignatureAtPage,
    SignatureFormat,
    UserRole,
)
from app.lib.types import DataDict


class ArchiveSettings(BaseModel):
    allow_uploaded_documents: bool = False
    allow_partially_signed_documents: bool = False
    allow_fully_signed_documents: bool = True
    allow_rejected_documents: bool = True

    @property
    def allowed_document_statuses(self) -> list[DocumentStatus]:
        """
        Get allowed document statuses for archive from settings.
        """
        # Deleted documents can be in archive by default
        allowed_statuses = [DocumentStatus.deleted]

        if self.allow_uploaded_documents:
            allowed_statuses.extend([DocumentStatus.uploaded, DocumentStatus.ready_to_be_signed])
        if self.allow_partially_signed_documents:
            allowed_statuses.extend(
                [
                    DocumentStatus.sent,
                    DocumentStatus.signed,
                    DocumentStatus.signed_and_sent,
                    DocumentStatus.flow,
                ]
            )
        if self.allow_fully_signed_documents:
            allowed_statuses.extend([DocumentStatus.finished])
        if self.allow_rejected_documents:
            allowed_statuses.extend(
                [
                    DocumentStatus.reject,
                    DocumentStatus.revoked,
                ]
            )
        return allowed_statuses


class AntivirusSettings(BaseModel):
    is_download_infected_enabled: bool = False
    is_download_pending_enabled: bool = True


class DefaultRolePermissionsBase(BaseModel):
    # Default permissions for all new users in company
    user_role: UserRole = UserRole.user
    can_view_document: bool = False
    can_comment_document: bool = True
    can_upload_document: bool = True
    can_download_document: bool = True
    can_print_document: bool = True
    can_delete_document: bool = True
    can_sign_and_reject_document: bool = True
    can_sign_and_reject_document_external: bool = True
    can_sign_and_reject_document_internal: bool = True
    can_invite_coworkers: bool = True
    can_edit_company: bool = False
    can_edit_roles: bool = False
    can_create_tags: bool = True
    can_edit_document_automation: bool = True
    can_edit_document_fields: bool = True
    can_edit_document_category: bool = True
    can_archive_documents: bool = True
    can_edit_templates: bool = False
    can_edit_directories: bool = False
    can_remove_itself_from_approval: bool = False
    can_delete_archived_documents: bool = True
    can_edit_security: bool = False
    can_download_actions: bool = False
    can_change_document_signers_and_reviewers: bool = False
    can_delete_document_extended: bool = False
    can_edit_company_contact: bool = False
    can_edit_required_fields: bool = False
    can_view_private_document: bool = False
    can_view_coworkers: bool = True

    # Default notifications for all new users in company
    can_receive_inbox: bool = True
    can_receive_comments: bool = True
    can_receive_rejects: bool = True
    can_receive_reminders: bool = True
    can_receive_reviews: bool = True
    can_receive_review_process_finished: bool = False
    # This column is True by default because previously we use "can_receive_notifications"
    # for this type of notification, which is True by default in a database
    can_receive_review_process_finished_assigner: bool = True
    can_receive_sign_process_finished: bool = False
    # This column is True by default because previously we use "can_receive_notifications"
    # for this type of notification, which is True by default in a database
    can_receive_sign_process_finished_assigner: bool = True
    can_receive_access_to_doc: bool = True
    can_receive_delete_requests: bool = True
    # Values is True for all new admin roles (see set_default_role_permissions)
    can_receive_inbox_as_default: bool = False
    can_receive_new_roles: bool = False
    can_receive_admin_role_deletion: bool = False
    can_receive_email_change: bool = False
    can_receive_token_expiration: bool = False


class DefaultRolePermissionsKey(DefaultRolePermissionsBase):
    """
    Default permissions and notifications settings for new users that registered by key in
    company (signed registration token). Small and mid-size companies usually use registration by
    key, and they are less often using permissions restrictions. In this case, we set more
    permissive defaults targeting small and mid-size companies.
    """

    user_role: UserRole = UserRole.admin
    can_view_document: bool = True


class DefaultRolePermissionsCoworker(DefaultRolePermissionsBase):
    """
    Default permissions and notifications setting for invited or automatically created coworkers
    in company. Usually in big companies, users might not have access to the company key; therefore,
    they more often are invited by another admin. In this case, we set more restrictive defaults
    targeting big companies.
    """

    user_role: UserRole = UserRole.user
    can_view_document: bool = False


class DownloadsConfig(BaseModel):
    """
    Settings for document downloads
    """

    # When True - xml documents will be converted to json on downloads
    use_xml_to_json: bool = False

    # When specified, 'use_xml_to_json' will work only for documents created after this date
    xml_to_json_min_document_date: str | None = None

    # Specifies which document types ignored for 'use_xml_to_json' option
    # ('Акт виконаних робіт' for example)
    xml_to_json_ignore_document_types: list[str] = []

    # Limit rows in xml document when converting to json
    xml_to_json_limit_rows: int = 100


class ReviewRenderConfig(BaseModel):
    """
    Config for rendering a review on document
    """

    # Render user's full name in reviews section
    render_full_name: bool = False

    # Render user's email in reviews section
    render_email: bool = True

    # Render user's position in reviews section
    render_position: bool = True

    @property
    def are_all_rules_disabled(self) -> bool:
        return all(
            [
                not self.render_full_name,
                not self.render_email,
                not self.render_position,
            ]
        )


class UploadsConfig(BaseModel):
    """
    Settings for document uploads
    """

    # When enabled - email of main recipient will be substituted from
    # contacts
    allow_substitute_email_recipient: bool = True

    # Allowed hosts for upload documents from
    allow_download_documents_from_hosts: list[str] = []

    # List of files extensions allowed to upload
    # NOTE: We do not allow upload nested archives
    allowed_extensions: list[str] = [
        '.dbf',
        '.pdf',
        '.txt',
        '.xml',
        '.doc',
        '.docx',
        '.xls',
        '.xlsx',
        '.rtf',
        '.jpg',
        '.jpeg',
        '.png',
        '.xanr',
        '.mmo',
    ]

    # List of archive extensions ready to unzip
    archive_extensions: list[str] = ['.zip']

    # Boolean flag to check owner EDRPOU or allow to upload any files even
    # with wrong owner EDRPOU in filename / XML content
    check_owner_edrpou: bool = True

    # Max file size in MB to be uploaded
    max_file_size: int = 15

    # Max total size in MB of upload (sum size of all files, which been
    # uploaded). Should be less then nginx' `max_body_request_size` value
    max_total_size: int = 100

    # Max total count of files to upload
    max_total_count: int = 500

    # Replace owner EDRPOU to current user EDRPOU on file upload? This is
    # default behaviour for dev instances
    replace_owner_edrpou: bool = True


class VersionSettings(BaseModel):
    # Control review process for company on new version upload
    # by the company
    review_flow: VersionReviewFlow = VersionReviewFlow.continued


class BaseCompanyConfig(BaseModel):
    # Permissions for new users in company registered by key
    default_role_permissions_key: DefaultRolePermissionsKey = Field(
        default_factory=DefaultRolePermissionsKey
    )

    # Completing regular registration (without token) makes user's role admin
    # with `can_edit*` permissions enabled.
    # However, inviting user with token, inviting existing coworker does not
    # make new user's role admin and `can_edit_*` permissions are set to False.
    default_role_permissions: DefaultRolePermissionsCoworker = Field(
        default_factory=DefaultRolePermissionsCoworker
    )

    # Settings for document downloads
    downloads: DownloadsConfig = Field(default_factory=DownloadsConfig)

    # When enabled - pay for received documents. For normal documents - after
    # document owner signed it, for 3P document - after owner sent it
    allow_pay_as_recipient: bool = False

    # Render signature image on viewing / printing PDF documents at that page.
    render_signature_at_page: RenderSignatureAtPage = RenderSignatureAtPage.first

    # TODO: Move to review render config. There has to be a database migration,
    #   these values aren't True for everyone.
    # Render document review results in the UI
    render_review_in_interface: bool = True
    # Render document review results on printing PDF documents
    render_review_on_print_document: bool = True
    # Settings for rendering review on document
    review_render_config: ReviewRenderConfig = Field(default_factory=ReviewRenderConfig)

    # Enables possibility to view documents by invite
    allow_unregistered_document_view: bool = True

    # all tags in company is accessible by any coworker, except assigning tags
    # to role, handle case when user can view and assign tags to any documents
    # or contacts, without direct access via role_tag connection
    soft_access_to_tags: bool = False

    # Should we use sign session in notifications about inbox
    # document, sent from config owner to partner
    use_sign_session_for_outbox_document_notifications: bool = True

    # Should we use sign session in notifications about inbox
    # document, sent from partner to config owner
    use_sign_session_for_inbox_document_notifications: bool = True

    # [Deprecated] Optional custom parsers for uploading XML documents.
    # Path to method, like "api.uploads.xml_utils:parse_fora_1c_xml"
    xml_parser_1c: str | None = None
    xml_parser: str | None = None

    # Optional, used for initialization signature library on frontend
    signer_max_file_size: int | None = None

    # Company config for sending document status to external company API
    allow_send_document_status: bool = False
    allow_send_document_finish_status: bool = False
    # 'api': {
    #     'retry_delay_min': None,
    #     'send_document_status_url': None,
    #     'request_timeout': None,
    #     'retries': 0,
    #     'basic_auth': {
    #         'login': None,
    #         'password': None,
    #     }
    # },
    api: dict[str, Any] | None = None

    # Determines if documents charged from company balance during upload of internal_documents
    paid_internal_documents: bool = False

    # Allow to use external AI service (AWS bedrock)
    # for detecting document meta by its content
    allow_suggesting_document_meta_with_ai: bool = True

    # Hide sender email in notifications to recipients
    # This is useful for companies that don't want to expose their email addresses, due to
    # security reasons or other concerns (e.g., Google)
    hide_sender_email_in_notifications: bool = False

    archive_settings: ArchiveSettings = Field(default_factory=ArchiveSettings)
    antivirus_settings: AntivirusSettings = Field(default_factory=AntivirusSettings)

    # Company may restrict what signature types can be added to documents [DOC-7452]
    allowed_signature_types: list[SignatureFormat] | None = None

    version_settings: VersionSettings = Field(default_factory=VersionSettings)


class AdminCompanyConfig(BaseModel):
    # Does current company use master account or not? When enabled, extra
    # features as Statistics or Special Abilities pages will be enabled for
    # company admins
    master: bool = False

    # Settings for document uploads
    uploads: UploadsConfig = Field(default_factory=UploadsConfig)

    # Optional flag to allow company admin access all data on Statistics or
    # via admin GraphQL requests
    # IMPORTANT: Only Vchasno and internal companies should have this flag
    # turned on. All external companies even master accounts shouldn't have
    # this flag turned on due to security reasons
    admin_is_superadmin: bool = False

    # Trust external users invited by company? When enabled registration
    # completed for external invited users right on "Registration by Token"
    # page. When disabled external invited user will need to verify himself
    # with ECP
    trust_external_invitees: bool = False

    # Send SMS about new documents to recipients
    send_sms_to_document_receivers: bool = False

    # Ability to upload internal documents
    internal_document_enabled: bool = False

    # Is current company is a vendor?
    api_vendor: str | None = None

    # Should the parent company pay for documents?
    parent_company: str | None = None
    allow_parent_company_pay_for_documents: bool = False

    msViewerEnabled: bool = True  # noqa: N815

    price_per_document: float = INTEGRATION_PRICE_PER_DOCUMENT

    # 2FA authorization enabled for all coworkers with phone in profile
    enable_2fa_for_internal_users: bool = False


# TODO: remove separation to config/admin_config, we do not use it
class CompanyConfig(BaseCompanyConfig, AdminCompanyConfig):
    """
    Combination of BaseCompanyConfig and AdminCompanyConfig
    """

    model_config = ConfigDict(extra='allow')

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json')
