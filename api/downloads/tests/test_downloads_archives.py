import datetime
import io
import pathlib
import zipfile

import pytest

from api.downloads.archives import generate_documents_archive
from api.downloads.constants import NTFS_REAL_MAX_PATH_LENGTH
from api.downloads.enums import ArchiveFilenamesMode
from api.downloads.types import ArchiveFile, ArchiveFileType, ArchiveOptions
from app.directories.tests.utils import prepare_directory
from app.document_revoke.tests.utils import prepare_revoke, prepare_revoke_signature
from app.events.document_actions import Action
from app.events.document_actions.db import select_document_actions_for
from app.lib.datetime_utils import to_local_datetime
from app.sign_sessions.enums import SignSessionType
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    cleanup_on_teardown,
    prepare_app_client,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_signature_data,
    prepare_user_data,
    unzip_archive_test_util,
    wait_streaming_response_eof,
)

TEST_CONTENT = b'Hello, world!'
TEST_SIGNATURE_KEY = b'1234567890'
ARCHIVE_README_FILENAME = 'Instruktsiia.pdf'
RECEIPT_FILENAME = 'Kvytantsiia.pdf'


TEST_LONG_FILENAME_BASE = '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки***************************************************'  # noqa: E501


@pytest.mark.parametrize(
    'date_created, ignore_last_byte',
    [
        (to_local_datetime(datetime.datetime(2017, 11, 7)), False),
        (to_local_datetime(datetime.datetime(2017, 11, 8, 17, 4)), True),
        (to_local_datetime(datetime.datetime(2017, 11, 9)), True),
        (to_local_datetime(datetime.datetime(2017, 11, 10, 2, 31)), True),
        (to_local_datetime(datetime.datetime(2017, 11, 11)), False),
    ],
)
async def test_download_archive_n1_all_signatures(aiohttp_client, date_created, ignore_last_byte):
    document_content = TEST_CONTENT
    expected_content = TEST_CONTENT[:-1] if ignore_last_byte else TEST_CONTENT

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        title='hello-world',
        extension='.txt',
        content=document_content,
    )

    await prepare_signature_data(app, user, document, date_created=date_created)

    try:
        response = await client.get(
            f'/downloads/{document.id}/archive', headers=prepare_auth_headers(user)
        )
        await wait_streaming_response_eof(response)

        assert response.status == 200
        assert response.content_type == 'application/zip'

        buffer = io.BytesIO(await response.read())
        with zipfile.ZipFile(buffer) as zip_handler:
            names = zip_handler.namelist()
            assert len(names) == 5
            assert len([item for item in names if '/' not in item]) == 5
            with zip_handler.open('hello-world.txt') as file_handler:
                assert file_handler.read() == expected_content
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('is_internal', (False, True))
async def test_download_archive_n1_diff_signatures(aiohttp_client, is_internal):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        title='hello-world',
        extension='.txt',
        content=TEST_CONTENT,
    )

    await prepare_signature_data(
        app,
        user,
        document,
        date_created=to_local_datetime(datetime.datetime(2017, 11, 9)),
        is_internal=is_internal,
        internal_file_name='.p7s' if is_internal else None,
    )
    await prepare_signature_data(
        app,
        recipient,
        document,
        is_internal=is_internal,
        internal_file_name='.p7s' if is_internal else None,
    )

    response = await client.get(
        f'/downloads/{document.id}/archive', headers=prepare_auth_headers(user)
    )
    await wait_streaming_response_eof(response)

    assert response.status == 200
    assert response.content_type == 'application/zip'

    title = document.title
    user_file_name = f'{title} {user.company_edrpou}/{title}.txt'
    user_signature_file_name = (
        f'{title} {user.company_edrpou}/{title}. Pidpys {user.company_edrpou}.p7s'
    )

    recipient_file_name = f'{title} {recipient.company_edrpou}/{title}.txt'
    recipient_signature_file_name = (
        f'{title} {recipient.company_edrpou}/{title}. Pidpys {recipient.company_edrpou}.p7s'
    )

    expected_names = {
        RECEIPT_FILENAME,
        ARCHIVE_README_FILENAME,
        user_file_name,
        user_signature_file_name,
        recipient_file_name,
        recipient_signature_file_name,
    }
    if not is_internal:
        expected_names.update(
            {
                user_signature_file_name.replace('Pidpys', 'Pechatka'),
                recipient_signature_file_name.replace('Pidpys', 'Pechatka'),
            }
        )

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()

        assert set(names) == expected_names

        with zip_handler.open(user_file_name) as file_handler:
            assert file_handler.read() == TEST_CONTENT[:-1]

        with zip_handler.open(recipient_file_name) as file_handler:
            assert file_handler.read() == TEST_CONTENT


@pytest.mark.parametrize(
    'with_instruction, with_url_param, expected_names ',
    [
        (
            True,
            True,
            {
                'hello-world.txt',
                'hello-world. Pidpys 11111111.p7s',
                'hello-world. Pidpys 11111111_0.p7s',
                'hello-world. Pidpys 00000001.p7s',
                ARCHIVE_README_FILENAME,
                RECEIPT_FILENAME,
            },
        ),
        (
            True,
            False,
            {
                'hello-world.txt',
                'hello-world. Pidpys 11111111.p7s',
                'hello-world. Pidpys 11111111_0.p7s',
                'hello-world. Pidpys 00000001.p7s',
                ARCHIVE_README_FILENAME,
                RECEIPT_FILENAME,
            },
        ),
        (
            False,
            True,
            {
                'hello-world.txt',
                'hello-world. Pidpys 11111111.p7s',
                'hello-world. Pidpys 11111111_0.p7s',
                'hello-world. Pidpys 00000001.p7s',
                RECEIPT_FILENAME,
            },
        ),
    ],
)
async def test_get_archive_without_ignoring_last_byte(
    aiohttp_client, with_instruction, expected_names, with_url_param
):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        owner=user,
        another_recipients=[recipient],
        title='hello-world',
        extension='.txt',
        content=TEST_CONTENT,
    )

    async def prepare_signature(user):
        await prepare_signature_data(
            app, user, document, is_internal=True, internal_file_name='.p7s'
        )

    await prepare_signature(user)
    await prepare_signature(coworker)
    await prepare_signature(recipient)

    if with_url_param:
        url = f'/downloads/{document.id}/archive?with_instruction={with_instruction}'
    else:
        url = f'/downloads/{document.id}/archive'

    response = await client.get(url, headers=prepare_auth_headers(user))
    await wait_streaming_response_eof(response)
    assert response.status == 200
    assert response.content_type == 'application/zip'

    title = document.title
    user_file_name = f'{title}.txt'

    buffer = io.BytesIO(await response.read())
    with zipfile.ZipFile(buffer) as zip_handler:
        names = zip_handler.namelist()
        assert len(names) == len(expected_names)
        assert set(names) == expected_names

        with zip_handler.open(user_file_name) as file_handler:
            assert file_handler.read() == TEST_CONTENT


def _get_archive_files(*args):
    return tuple(
        ArchiveFile(content=b'CONTENT', file_name=name, type=ArchiveFileType.original)
        for name in args
    )


@pytest.mark.parametrize(
    'options, expected',
    [
        # One folder archive
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': False,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a', 'b'),
                    )
                ],
            },
            {
                'filenames': ['a', 'b'],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_no_instruction',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a', 'b'),
                    )
                ],
            },
            {
                'filenames': ['a', 'b', ARCHIVE_README_FILENAME],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_with_instruction',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a.txt', 'b.txt'),
                    )
                ],
            },
            {
                'filenames': ['a.txt', 'a_0.txt', 'b.txt', ARCHIVE_README_FILENAME],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_with_instruction_and_duplicates',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a.txt', 'b.txt', 'a.txt'),
                    )
                ],
            },
            {
                'filenames': ['a.txt', 'a_0.txt', 'b.txt', 'a_1.txt', ARCHIVE_README_FILENAME],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_with_instruction_and_duplicates_2',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': False,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a_1.txt', 'b.txt', 'a.txt', 'a.txt'),
                    )
                ],
            },
            {
                'filenames': ['a.txt', 'a_1.txt', 'b.txt', 'a_0.txt', 'a_2.txt'],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_no_instruction_and_duplicates',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files(ARCHIVE_README_FILENAME, 'a.txt'),
                    )
                ],
            },
            {
                'filenames': [
                    'a.txt',
                    ARCHIVE_README_FILENAME,
                    ARCHIVE_README_FILENAME.replace('.pdf', '_0.pdf'),
                ],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_with_instruction_and_instruction_in_files',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': False,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a.txt', 'b.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a_1.txt', 'a.txt', 'c.txt'),
                    ),
                ],
            },
            {
                'filenames': ['a.txt', 'a_0.txt', 'b.txt', 'a_1.txt', 'a_2.txt', 'c.txt'],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_no_instruction_and_duplicates',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('b.txt'),
                    ),
                ],
            },
            {
                'filenames': ['a.txt', 'b.txt', ARCHIVE_README_FILENAME],
                'archive_filename': 'abc.zip',
            },
            id='one_folder_with_instruction_and_multiple_documents',
        ),
        # Multi folder archive
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': False,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                ],
            },
            {
                'filenames': [
                    'abc/a.txt',
                    'abc_0/a.txt',
                    f'abc/{ARCHIVE_README_FILENAME}',
                    f'abc_0/{ARCHIVE_README_FILENAME}',
                ],
                'archive_filename': 'abc.zip',
            },
            id='multi_folder_with_instruction',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': False,
                'with_instruction': False,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a.txt', 'b.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                ],
            },
            {
                'filenames': ['abc/a.txt', 'abc/a_0.txt', 'abc/b.txt', 'abc_0/a.txt'],
                'archive_filename': 'abc.zip',
            },
            id='multi_folder_no_instruction',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': False,
                'with_instruction': False,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt', 'a_0.txt', 'a.txt', 'b.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                ],
            },
            {
                'filenames': [
                    'abc/a.txt',
                    'abc/a_0.txt',
                    'abc/a_1.txt',
                    'abc/b.txt',
                    'abc_0/a.txt',
                ],
                'archive_filename': 'abc.zip',
            },
            id='multi_folder_no_instruction_and_duplicates',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': False,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.default,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files(ARCHIVE_README_FILENAME),
                    ),
                    ArchiveOptions(
                        archive_filename='cba.zip',
                        document_filename='cba.pdf',
                        document_filename_base='cba',
                        files=_get_archive_files('a.txt'),
                    ),
                ],
            },
            {
                'filenames': [
                    f'abc/{ARCHIVE_README_FILENAME}',
                    f'abc/{ARCHIVE_README_FILENAME.replace(".pdf", "_0.pdf")}',
                    'cba/a.txt',
                    f'cba/{ARCHIVE_README_FILENAME}',
                ],
                'archive_filename': 'abc.zip',
            },
            id='multi_folder_with_instruction_and_multiple_documents',
        ),
        # Windows filename mode
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.windows,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                ],
            },
            {
                'filenames': ['a.txt', 'Instruktsiia.pdf'],
                'archive_filename': 'abc.zip',
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_filenames_one_folder_short_names',
        ),
        pytest.param(
            {
                'archive_name': f'{TEST_LONG_FILENAME_BASE}.zip',
                'in_one_folder': True,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.windows,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename=f'{TEST_LONG_FILENAME_BASE}.zip',
                        document_filename=f'{TEST_LONG_FILENAME_BASE}.pdf',
                        document_filename_base=f'{TEST_LONG_FILENAME_BASE}',
                        files=_get_archive_files(
                            f'{TEST_LONG_FILENAME_BASE}.xml',
                            f'preview_{TEST_LONG_FILENAME_BASE}.pdf',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 77777777.p7s',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 55555555.p7s',
                        ),
                    ),
                ],
            },
            {
                'filenames': [
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділ_0.p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі .p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі .xml',  # noqa: E501
                    'Instruktsiia.pdf',
                    'preview_55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у .pdf',  # noqa: E501
                ],
                'archive_filename': '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі.zip',  # noqa: E501
                'path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_filenames_one_folder_long_names',
        ),
        pytest.param(
            {
                'archive_name': 'abc.zip',
                'in_one_folder': False,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.windows,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('a.txt'),
                    ),
                    ArchiveOptions(
                        archive_filename='abc.zip',
                        document_filename='abc.pdf',
                        document_filename_base='abc',
                        files=_get_archive_files('b.txt'),
                    ),
                ],
            },
            {
                'filenames': [
                    'abc/Instruktsiia.pdf',
                    'abc/a.txt',
                    'abc_0/Instruktsiia.pdf',
                    'abc_0/b.txt',
                ],
                'archive_filename': 'abc.zip',
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_filenames_multi_folder_short_names',
        ),
        pytest.param(
            {
                'archive_name': f'{TEST_LONG_FILENAME_BASE}.zip',
                'in_one_folder': False,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.windows,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename=f'{TEST_LONG_FILENAME_BASE}.zip',
                        document_filename=f'{TEST_LONG_FILENAME_BASE}.pdf',
                        document_filename_base=f'{TEST_LONG_FILENAME_BASE}',
                        files=_get_archive_files(
                            f'{TEST_LONG_FILENAME_BASE}.xml',
                            f'preview_{TEST_LONG_FILENAME_BASE}.pdf',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 77777777.p7s',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 55555555.p7s',
                        ),
                    ),
                    ArchiveOptions(
                        archive_filename='a.zip',
                        document_filename='a.pdf',
                        document_filename_base='a',
                        files=_get_archive_files(
                            'a. Pidpys 55555555.p7s',
                            'a. Pidpys 77777777.p7s',
                            'a.xml',
                        ),
                    ),
                ],
            },
            {
                'filenames': [
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Зві/55555555_77777777_20240521_Звіт перевірки ко_0.p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Зві/55555555_77777777_20240521_Звіт перевірки комі.p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Зві/55555555_77777777_20240521_Звіт перевірки комі.xml',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Зві/Instruktsiia.pdf',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Зві/preview_55555555_77777777_20240521_Звіт переві.pdf',  # noqa: E501
                    'a/Instruktsiia.pdf',
                    'a/a. Pidpys 55555555.p7s',
                    'a/a. Pidpys 77777777.p7s',
                    'a/a.xml',
                ],
                'archive_filename': '55555555_77777777_20240521_Звіт перевірки ком.zip',
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_filenames_multi_folder_long_names',
        ),
        pytest.param(
            {
                'archive_name': 'Vchasno Docs.zip',
                'in_one_folder': False,
                'with_instruction': True,
                'filenames_mode': ArchiveFilenamesMode.windows,
                'filenames_max_length': None,
                'archives': [
                    ArchiveOptions(
                        archive_filename=f'{TEST_LONG_FILENAME_BASE}.zip',
                        document_filename=f'{TEST_LONG_FILENAME_BASE}.pdf',
                        document_filename_base=f'{TEST_LONG_FILENAME_BASE}',
                        files=_get_archive_files(
                            f'{TEST_LONG_FILENAME_BASE}.xml',
                            f'preview_{TEST_LONG_FILENAME_BASE}.pdf',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 77777777.p7s',
                            f'{TEST_LONG_FILENAME_BASE}. Pidpys 55555555.p7s',
                        ),
                    ),
                    ArchiveOptions(
                        archive_filename='a.zip',
                        document_filename='a.pdf',
                        document_filename_base='a',
                        files=_get_archive_files(
                            'a. Pidpys 55555555.p7s',
                            'a. Pidpys 77777777.p7s',
                            'a.xml',
                        ),
                    ),
                ],
            },
            {
                'filenames': [
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки_9343q32/55555555_77777777_20240521_Звіт перевірки комісії з пер_0.p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки_9343q32/55555555_77777777_20240521_Звіт перевірки комісії з перев.p7s',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки_9343q32/55555555_77777777_20240521_Звіт перевірки комісії з перев.xml',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки_9343q32/Instruktsiia.pdf',  # noqa: E501
                    '55555555_77777777_20240521_Звіт перевірки комісії з перевірки перевіряючих документів у відділі Звіту і Перевірки_9343q32/preview_55555555_77777777_20240521_Звіт перевірки комісії.pdf',  # noqa: E501
                    'a/Instruktsiia.pdf',
                    'a/a. Pidpys 55555555.p7s',
                    'a/a. Pidpys 77777777.p7s',
                    'a/a.xml',
                ],
                'archive_filename': 'Vchasno Docs.zip',
                'max_path_length': NTFS_REAL_MAX_PATH_LENGTH,
            },
            id='windows_filenames_multi_folder_long_names_archive_name_short',
        ),
    ],
)
async def test_generate_document_archive(
    aiohttp_client,
    options: dict,
    expected: dict,
):
    await prepare_app_client(aiohttp_client)
    output = await generate_documents_archive(
        archive_filename=options['archive_name'],
        archives=options['archives'],
        with_instruction=options['with_instruction'],
        in_one_folder=options['in_one_folder'],
        filenames_mode=options['filenames_mode'],
        filenames_max_length=options['filenames_max_length'],
    )
    with zipfile.ZipFile(output.buffer) as zip_handler:
        names = zip_handler.namelist()
        names = sorted(names)
        assert names == sorted(expected['filenames'])

    assert output.filename == expected['archive_filename']

    if 'max_path_length' in expected:
        for name in names:
            path = pathlib.Path(output.filename) / name
            assert len(str(path)) <= expected['max_path_length']


async def test_download_archived_documents(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    d0 = await prepare_directory(
        name='d0',
        company_id=user.company_id,
    )
    d1 = await prepare_directory(
        name='d1',
        company_id=user.company_id,
    )
    d2 = await prepare_directory(
        name='d2',
        company_id=user.company_id,
        parent_id=d1.id,
    )
    d3 = await prepare_directory(
        name='d3',
        company_id=user.company_id,
        parent_id=d2.id,
    )
    d4 = await prepare_directory(
        name='d4',
        company_id=user.company_id,
        parent_id=d1.id,
    )
    d5 = await prepare_directory(
        name='d5',
        company_id=user.company_id,
        parent_id=d4.id,
    )

    # Must be present in result archive
    doc1 = await prepare_document_data(
        app,
        user,
        title='doc1',
        is_archived=True,
        directory_id=d3.id,
        content=b'doc1',
    )

    # Should not apper in result
    await prepare_document_data(
        app,
        user,
        is_archived=True,
        directory_id=d4.id,
    )
    await prepare_document_data(
        app,
        user,
        is_archived=True,
        directory_id=d5.id,
    )

    # Try to download empty dir
    response = await client.post(
        '/downloads/archived-documents',
        json={
            'directory_ids': [d0.id],
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400

    # Download non-emtpy dir
    response = await client.post(
        '/downloads/archived-documents',
        json={
            'directory_ids': [d2.id],
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200

    # There are 3 documents + 1 archive
    assert len(s3_emulation.files.keys()) == 4

    archive_content = b''
    for key, val in s3_emulation.files.items():
        if key.startswith('documents_archives/'):
            archive_content = val.body
    assert len(archive_content) > 0

    result = await unzip_archive_test_util(archive_content)
    assert len(result) == 1
    assert result['d1/d2/d3/doc1/doc1.pdf'] == b'doc1'

    actions = await select_document_actions_for(role_id=user.role_id)
    assert len(actions) == 1
    action = actions[0]
    assert action.document_id == doc1.id
    assert action.action == Action.document_download_archived


async def test_download_some_documents_from_dir(aiohttp_client, s3_emulation):
    """
    Test that we correctly create an archive with documents
    when a user selects few documents from some directory
    """
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    d0 = await prepare_directory(
        name='d0',
        company_id=user.company_id,
    )
    d1 = await prepare_directory(
        name='d1',
        company_id=user.company_id,
        parent_id=d0.id,
    )

    doc1 = await prepare_document_data(
        app,
        user,
        title='doc1',
        is_archived=True,
        directory_id=d1.id,
        content=b'doc1',
    )
    await prepare_document_data(
        app,
        user,
        is_archived=True,
        directory_id=d1.id,
    )

    # ACT
    response = await client.post(
        '/downloads/archived-documents',
        json={
            'document_ids': [doc1.id],
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200

    # There are 2 documents + 1 archive
    assert len(s3_emulation.files.keys()) == 3

    archive_content = b''
    for key, val in s3_emulation.files.items():
        if key.startswith('documents_archives/'):
            archive_content = val.body
    assert len(archive_content) > 0

    result = await unzip_archive_test_util(archive_content)
    assert len(result) == 1
    assert result['doc1/doc1.pdf'] == b'doc1'


@pytest.mark.parametrize('with_preview', [True, False])
async def test_download_archive_via_sign_session(aiohttp_client, with_preview, test_flags):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        title='hello-world',
        extension='.xml',
    )
    await prepare_signature_data(app, user, document)
    ss = await prepare_sign_session_data(
        app=app,
        user=user,
        document=document,
        edrpou=user.company_edrpou,
        email=user.email,
        type=SignSessionType.view_session,
        role_id=user.role_id,
    )

    response = await client.get(
        f'/downloads/{document.id}/archive?with_xml_preview={with_preview}',
        headers=prepare_sign_session_headers(ss, client),
    )
    assert response.status == 200
    assert response.content_type == 'application/zip'


@pytest.mark.parametrize('with_original', [True, False])
@pytest.mark.parametrize('with_instruction', [True, False])
@pytest.mark.parametrize('with_revoke_original', [True, False])
@pytest.mark.parametrize('with_signatures', [True, False])
async def test_download_archive_with_revokes(
    aiohttp_client,
    eusign_mock,
    with_revoke_original,
    with_signatures,
    with_instruction,
    with_original,
):
    app, client, user = await prepare_client(aiohttp_client)
    revoke = await prepare_revoke(user)
    await prepare_revoke_signature(client, user, revoke)

    response = await client.get(
        f'/downloads/{revoke.document_id}/archive'
        f'?with_revoke_original={with_revoke_original}'
        f'&with_revoke_signatures={with_signatures}'
        f'&with_instruction={with_instruction}'
        f'&with_original={with_original}',
        headers=prepare_auth_headers(user),
    )
    await wait_streaming_response_eof(response)

    assert response.status == 200
    assert response.content_type == 'application/zip'

    buffer = io.BytesIO(await response.read())

    expected = set()
    if with_original:
        expected.add('Test Document.pdf')
    if with_instruction:
        expected.add(ARCHIVE_README_FILENAME)
    if with_revoke_original:
        expected.add('revoke.xml')
    if with_signatures:
        expected.add('revoke. Pidpys 11111111.p7s')

    with zipfile.ZipFile(buffer) as zip_handler:
        assert set(zip_handler.namelist()) == expected
