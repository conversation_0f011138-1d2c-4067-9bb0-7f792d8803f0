import datetime
import logging

from aiohttp import web

from app.auth import db as auth_db
from app.lib.datetime_utils import (
    utc_now,
)
from app.lib.enums import UserRole
from app.lib.types import DataDict
from app.services import services
from app.telegram import notifications as telegram_notifications
from worker import topics
from worker.emailing.reminders_for_low_billing_balance import db, utils


async def initiate_low_balance_billing_notifications(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Lookup, gather and initiate reminders about low billing balance.
    """

    # Select recipients with low or zero balance
    async with services.db_readonly.acquire() as conn:
        recipients = await db.select_users_with_low_balance(
            conn=conn,
            time_ago=utc_now() - datetime.timedelta(hours=1),
        )

    # Filter out non-admin recipients for non-zero balance notification
    recipients = [
        recipient
        for recipient in recipients
        if not (recipient.total_units_left != 0 and recipient.user_role != UserRole.admin.value)
    ]

    # Produce single kafka record per each recipient
    await services.kafka.send_records(
        topic=topics.SEND_LOW_BALANCE_BILLING_NOTIFICATION,
        values=[
            {
                'recipient_role_id': recipient.role_id,
                'total_units_left': recipient.total_units_left,
            }
            for recipient in recipients
        ],
    )


async def send_low_balance_billing_notification(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Send low-balance billing notification to the recipient
    """
    recipient_role_id: str = data['recipient_role_id']
    total_units_left = int(data['total_units_left'])

    async with services.db_readonly.acquire() as conn:
        recipient = await auth_db.select_user(conn=conn, role_id=recipient_role_id)
        if not recipient:
            logger.info('Recipient not found.', extra={'role_id': recipient_role_id})
            return

    await utils.send_low_balance_email(
        recipient=recipient,
        total_units_left=total_units_left,
    )
    await telegram_notifications.send_low_balance_notification(
        recipient=recipient,
        total_units_left=total_units_left,
    )
