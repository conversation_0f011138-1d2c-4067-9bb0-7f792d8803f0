import React from 'react';
import { useFieldArray } from 'react-final-form-arrays';

import { DocumentParameter } from '../types';

import ParameterField from '../parameterField/parameterField';

import css from './parameterFields.css';

interface ParameterFieldsProps {
    name: string;
}

const ParameterFields: React.FC<ParameterFieldsProps> = ({ name }) => {
    const { fields } = useFieldArray<DocumentParameter>(name);

    const handleRemove = (index: number) => {
        if (fields.length === 1) {
            fields.update(index, { fieldId: undefined, value: '' });
        } else {
            fields.remove(index);
        }
    };

    const handleAdd = () => fields.push({ fieldId: undefined, value: '' });

    return (
        <div className={css.inputs}>
            {fields.map((nameValue, index) => (
                <ParameterField
                    key={nameValue}
                    name={nameValue}
                    onRemove={() => handleRemove(index)}
                    onAdd={handleAdd}
                />
            ))}
        </div>
    );
};

export default ParameterFields;
