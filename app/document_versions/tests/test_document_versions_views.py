from datetime import <PERSON><PERSON><PERSON>
from http import HTTPStatus

from aiohttp import FormData

from app.document_antivirus.db import (
    select_document_antivirus_checks,
)
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check
from app.document_versions.enums import DocumentVersionType
from app.document_versions.utils import (
    add_document_content_version,
    get_version_count,
)
from app.events.document_actions.db import select_document_actions_for
from app.lib.datetime_utils import utc_now
from app.tests.common import (
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
)

UPLOAD_DOCUMENT_VERSION_URL = '/internal-api/document/{document_id}/version'
DELETE_DOCUMENT_VERSION_URL = '/internal-api/document/{document_id}/version/{version_id}'


async def test_upload_document_version(aiohttp_client):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    # mark document as versioned
    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            role_id=user.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content',
            name='Test name',
            extension='.pdf',
        )

    data = FormData()
    data.add_field('file', b'some content', filename='file.doc')

    # Act
    response = await client.post(
        UPLOAD_DOCUMENT_VERSION_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        data=data,
    )

    # Assert
    assert response.status == HTTPStatus.NO_CONTENT, await response.json()

    async with app['db'].acquire() as conn:
        antivirus_checks_count = len(
            await select_document_antivirus_checks(conn=conn, documents_ids=[document.id])
        )
        version_count = await get_version_count(conn=conn, document_id=document.id)

    document_actions = await select_document_actions_for(document_id=document.id)

    assert version_count == 2
    assert len(document_actions) == 1
    # not prepare_document_data nor add_document_content_version don't
    # create antivirus check, so where is only 1 item, after uploading of
    # new version
    assert antivirus_checks_count == 1


async def test_delete_document_version(aiohttp_client):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    now = utc_now()

    # mark document as versioned
    async with app['db'].acquire() as conn:
        document_version_1 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now + timedelta(seconds=1),
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_1.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    # Act
    response = await client.delete(
        DELETE_DOCUMENT_VERSION_URL.format(
            document_id=document.id,
            version_id=document_version_2.id,
        ),
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert response.status == HTTPStatus.NO_CONTENT, await response.json()

    async with app['db'].acquire() as conn:
        antivirus_checks_count = len(
            await select_document_antivirus_checks(conn=conn, documents_ids=[document.id])
        )
        versions_count = await get_version_count(conn=conn, document_id=document.id)

    document_actions = await select_document_actions_for(document_id=document.id)

    assert len(document_actions) == 1
    assert versions_count == 1
    assert antivirus_checks_count == 1
