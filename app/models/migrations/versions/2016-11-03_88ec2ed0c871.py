"""empty message

Revision ID: 88ec2ed0c871
Revises: 4db76890e437
Create Date: 2016-11-03 12:52:23.007692


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '88ec2ed0c871'
down_revision = '4db76890e437'
branch_labels = None
depends_on = None

def upgrade():
    ### commands auto generated by Alembic - please adjust! ###
    op.add_column('company', sa.Column('user_id', sa.VARCHAR(length=32), nullable=True))
    op.create_foreign_key(None, 'company', 'user', ['user_id'], ['id'], ondelete='NO ACTION')
    op.add_column('document', sa.Column('path', sa.VARCHAR(length=512), nullable=False))
    op.add_column('signature', sa.Column('acsk', sa.VARCHAR(length=128), nullable=True))
    op.add_column('signature', sa.Column('serial_num', sa.<PERSON>(length=64), nullable=True))
    op.drop_column('signature', 'date_test')
    op.drop_constraint('user_company_id_fkey', 'user', type_='foreignkey')
    op.drop_column('user', 'receive_all')
    op.drop_column('user', 'customer_type')
    ### end Alembic commands ###


def downgrade():
    ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('user_company_id_fkey', 'user', 'company', ['company_id'], ['id'])
    op.add_column('signature', sa.Column('date_test', postgresql.TIMESTAMP(), autoincrement=False, nullable=False))
    op.drop_column('signature', 'serial_num')
    op.drop_column('signature', 'acsk')
    op.drop_column('document', 'path')
    op.drop_constraint(None, 'company', type_='foreignkey')
    op.drop_column('company', 'user_id')
    op.add_column('user', sa.Column('customer_type', sa.SMALLINT(), autoincrement=False, nullable=False))
    op.add_column('user', sa.Column('receive_all', sa.SMALLINT(), autoincrement=False, nullable=False))
    ### end Alembic commands ###
