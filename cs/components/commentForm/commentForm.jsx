import React, { Component } from 'react';
import { connect } from 'react-redux';
import MediaQuery from 'react-responsive';

import { Button, FlexBox, TextAreaInput } from '@vchasno/ui-kit';

import SvgSend from 'components/commentForm/mobile/images/send.svg';
import PropTypes from 'prop-types';
import { t } from 'ttag';
import Icon from 'ui/icon';

import { mapStatetoHasPermission } from '../../store/utils';

import { MEDIA_WIDTH } from '../../lib/constants';
import CommentFormMobile from './mobile/commentForm';

import css from './commentForm.css';

class CommentForm extends Component {
    static defaultProps = {
        commentAdded: false,
        isInternal: false,
    };
    static propTypes = {
        commentAdded: PropTypes.bool,
        isInternal: PropTypes.bool,
        onSubmit: PropTypes.func.isRequired,
        hasPermission: PropTypes.func,
    };

    state = {
        isLoading: false,
        isQueryPending: false,
        comment: '',
    };

    componentDidUpdate(prevProps) {
        if (
            prevProps.commentAdded !== this.props.commentAdded &&
            this.props.commentAdded &&
            this.state.comment
        ) {
            this.setState({ comment: '' });
        }
    }

    handleCommentChange = (evt) => {
        this.setState({ comment: evt.target.value });
    };

    sendComment = async () => {
        try {
            await this.props.onSubmit(this.state.comment);
            this.setState({ comment: '' });
        } finally {
            this.setState({ isLoading: false });
        }
    };

    submitForm = (evt) => {
        evt.preventDefault();
        this.setState({ isLoading: true }, this.sendComment);
    };

    render() {
        const { comment } = this.state;

        const canCommentDocument = this.props.hasPermission(
            'canCommentDocument',
        );

        return (
            <div>
                <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                    <form className={css.root} onSubmit={this.submitForm}>
                        <FlexBox>
                            <TextAreaInput
                                hideEmptyMeta
                                wide
                                minRows={3}
                                maxRows={6}
                                disabled={!canCommentDocument}
                                onChange={this.handleCommentChange}
                                label={t`Ваш коментар`}
                                value={comment}
                                dataQa="qa_text_comment_input"
                            />
                            <Button
                                loading={this.state.isLoading}
                                disabled={
                                    !canCommentDocument ||
                                    !comment ||
                                    this.state.isLoading
                                }
                                type="submit"
                                theme={
                                    this.props.isInternal
                                        ? 'secondary'
                                        : 'primary'
                                }
                                dataQa="qa_submit_form"
                            >
                                <div style={{ wide: 20, height: 20 }}>
                                    <Icon glyph={SvgSend} />
                                </div>
                            </Button>
                        </FlexBox>
                    </form>
                </MediaQuery>
                <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                    <CommentFormMobile {...this.props} />
                </MediaQuery>
            </div>
        );
    }
}

export default connect(mapStatetoHasPermission)(CommentForm);
