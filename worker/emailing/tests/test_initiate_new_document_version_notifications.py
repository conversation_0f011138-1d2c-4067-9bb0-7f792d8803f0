import logging

from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.documents.enums import FirstSignBy
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    send_document,
)

TEST_DOCUMENT_ID_1 = '84043588-f8fb-4163-a068-e7fc8fead83f'
TEST_DOCUMENT_ID_2 = 'f6eeaf17-d95f-4c5d-82a9-1efe176f770b'

TEST_UUID_1 = '14b7066b-cd16-4af4-88c5-58285c6c8644'
TEST_UUID_2 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
TEST_UUID_3 = '57fabbd0-f085-4c11-890b-74a5892272a6'
TEST_UUID_4 = '2c705883-70ad-4ff2-833f-d72bd06d4ee7'

logger = logging.getLogger(__name__)


async def test_initiate_new_document_version_notifications(aiohttp_client, mailbox):
    app, client, user = await prepare_client(aiohttp_client)

    # Case 1: Recipient exists, so they will receive email
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        can_receive_inbox_as_default=False,
    )

    document1 = await prepare_document_data(
        app=app,
        owner=user,
        extension='.docx',
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
    )

    await prepare_document_version(
        id=TEST_UUID_1,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document1.id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        content=b'version-1',
    )
    await send_document(
        client,
        document_id=document1.id,
        sender=user,
    )

    assert len(mailbox) == 1, [m['To'] for m in mailbox]
    assert mailbox[0]['To'] == recipient.email
    mailbox.clear()

    # Case 2: Document recipient doesn't exist,
    # so users with can_receive_inbox_as_default will receive email
    recipient2 = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT + '42',
        can_receive_inbox_as_default=True,
    )

    document2 = await prepare_document_data(
        app=app,
        owner=user,
        extension='.docx',
        first_sign_by=FirstSignBy.recipient,
        recipient_email='<EMAIL>',
        recipient_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document2.id,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        content=b'version-1',
    )
    await send_document(
        client,
        document_id=document2.id,
        sender=user,
    )

    assert len(mailbox) == 1
    assert mailbox[0]['To'] == recipient2.email
