.root {
    position: relative;
    width: 740px;
    max-width: 100vw;
    padding: 30px;
}

.title {
    display: flex;
    height: 60px;
    box-sizing: border-box;
    align-items: center;
    padding: 30px 0;
    border-bottom: 1px solid var(--default-border);
    font-size: 20px;
    font-weight: 500;
}

.countTitle {
    font-size: 18px;
    font-weight: 500;
    line-height: 22px;
}

.countValue {
    color: var(--grey-color);
}

.label {
    position: relative;
    display: flex;
    width: 100%;
    box-sizing: border-box;
    flex-grow: 1;
    padding: 1rem;
    animation: border-dance 1s infinite linear;
    animation-play-state: paused;
    background-image:
        linear-gradient(
            90deg,
            var(--grey-color) 50%,
            transparent 50%
        ),
        linear-gradient(90deg, var(--grey-color) 50%, transparent 50%),
        linear-gradient(0deg, var(--grey-color) 50%, transparent 50%),
        linear-gradient(0deg, var(--grey-color) 50%, transparent 50%);
    background-position: left top, right bottom, left bottom, right top;
    background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
    background-size: 15px 2px, 15px 2px, 2px 15px, 2px 15px;
    color: var(--grey-color);
    cursor: pointer;
    transition: border 0.3s, color 0.3s, background-color 0.3s;
}

.labelContent {
    margin: auto;
}

.hidden {
    display: none;
}

.label:hover,
.label[data-drop-active='true'] {
    animation-play-state: running;
    background-color: var(--pigeon-bg);
}

.file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
}

.text {
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
}

.uploadIcon {
    display: block;
    width: 150px;
    height: 150px;
}

.badgeList {
    display: flex;
    overflow: auto;
    flex-grow: 1;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 10px;
}

.nextBtn {
    min-width: 150px;
}

@keyframes border-dance {
    0% {
        background-position: left top, right bottom, left bottom, right top;
    }

    100% {
        background-position:
            left 15px top,
            right 15px bottom,
            left bottom 15px,
            right top 15px;
    }
}
