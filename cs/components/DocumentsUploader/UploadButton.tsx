import React from 'react';
import {
    SubmitError<PERSON><PERSON><PERSON>,
    SubmitHandler,
    useFormContext,
} from 'react-hook-form';

import { Button, snackbarToast } from '@vchasno/ui-kit';

import { EditDocumentForm } from 'components/DocumentEdit/types';
import { sendEventsAI } from 'components/DocumentsUploader/AIButton/utils';
import { useUploadActions } from 'components/uploader/useUploadActions';
import { WS_KEY_UPLOAD_DOCS_IDS } from 'lib/constants';
import { getLocalStorageItem } from 'lib/webStorage';
import eventTracking from 'services/analytics/eventTracking';
import { c, t } from 'ttag';

import { DocumentsUploadForm } from './types';

import { useDocumentUploadStateContext } from './useDocumentUploadStateContext';
import { useUploadDocumentsFormContext } from './useUploadDocumentsFormContext';

import css from './DocumentsUploader.css';

interface UploadButtonProps {
    className?: string;
}

const UploadButton: React.FC<UploadButtonProps> = ({ className }) => {
    const methods = useUploadDocumentsFormContext();
    const uploadActions = useUploadActions();
    const files = methods.watch('files');
    const [{ step }] = useDocumentUploadStateContext();
    const { isSubmitting } = methods.formState;
    const { getValues } = useFormContext<EditDocumentForm>();

    const onSubmitOnlySave: SubmitHandler<DocumentsUploadForm> = async (
        formData,
    ) => {
        // якщо у фоні уже активне завантаження, то не дозволяємо запустити нове
        if ((getLocalStorageItem(WS_KEY_UPLOAD_DOCS_IDS) || []).length > 0) {
            snackbarToast.info(
                t`Зачекайте завершення попереднього завантаження`,
            );
            return;
        }

        eventTracking.sendToGTM({
            event: 'doc_upload_click_edo',
        });
        sendEventsAI(getValues());
        // запускаємо процес завантаження, закриваємо попап
        await uploadActions.onUpload({
            files,
            formData,
        });
    };

    if (step !== 'setting') {
        return null;
    }

    // todo remove after production stabilization
    const onErrorSubmit: SubmitErrorHandler<DocumentsUploadForm> = (errors) => {
        console.log('errors');
        console.log(errors);
        console.log('data');
        console.log(methods.getValues());
    };

    return (
        <form
            className={className}
            noValidate
            onSubmit={methods.handleSubmit(onSubmitOnlySave, onErrorSubmit)}
        >
            <Button
                type="submit"
                theme="secondary"
                disabled={isSubmitting}
                className={css.saveButton}
                loading={isSubmitting}
            >
                {c('upload').t`Завантажити`}
            </Button>
        </form>
    );
};

export default UploadButton;
