{% extends "email/layout_v2.jinja2" %}
{% import "email/macros.jinja2" as macros %}
{% import "email/blocks/table_blocks.jinja2" as t %}
{% from "email/blocks/header_macros.jinja2" import header %}
{% from "email/blocks/bill_macros.jinja2" import generate_bill_table %}

{% block content %}
    {{
        header(
            text="Залишився 1 день, щоб оплатити рахунок",
            image_link="images/emails/bill_reminder.png",
            image_style="width: 220px; height: 140px",
            button_link=bill_url,
            button_text="Оплатити рахунок",
        )
    }}
    <table border=0 display=inline style="{{ macros.style_content_body() }};">
        {# Greeting message with emoji #}
        {{ t.greeting_with_emoji(emoji="😊", name=first_name) }}
        {{ t.spacing_row() }}
        <tr>
            <td colspan="2">
                <p>{{ main_text }}</p>
            </td>
        </tr>
        {{ t.spacing_row("32") }}
        {{ generate_bill_table(
            bill_number=bill_number,
            date_created=bill_date_created,
            rate=bill_rate,
            count_documents=bill_count_documents,
            total=bill_total,
            requisites=bill_requisites,
            description=bill_description,
            first_name=first_name,
            edrpou=bill_edrpou,
            email=bill_email
        ) }}
    </table>
    {% if bill_url %}
    <table border=0 display=inline style="{{ macros.style_content_body() }};">
        <td>
            {{ t.header_cell_normal("Увійдіть, щоб переглянути документ.")}}
        </td>
        <tr>
            <td>
                {{ macros.button_link_v2(bill_url, title="Перейти до рахунку") }}
            </td>
        </tr>
    </table>
    {% endif %}
{% endblock %}
