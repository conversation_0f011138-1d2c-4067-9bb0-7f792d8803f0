import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { displayCompanyEdrpou } from 'lib/company';
import { DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import PropTypes from 'prop-types';
import { UserPropType } from 'records/user';
import { getIsArchivePage } from 'selectors/router.selectors';
import {
    MAIN_MENU_STRUCTURE,
    getLocationName,
    mainLinks,
} from 'services/navigation-structure';
import { mapStateToCurrentUser } from 'store/utils';
import { t } from 'ttag';

import Button from '../ui/button/button';
import ButtonGroup from '../ui/buttonGroup/buttonGroup';
import Icon from '../ui/icon/icon';
import ListItem from '../ui/listItem/listItem';
import PanelBottomFixed from '../ui/panelBottomFixed/panelBottomFixed';
import PopupMobile from '../ui/popup/mobile/popup';

import DateFilter from '../dateFilter/dateFilter';
import Navigation from '../navigation/navigation';

// icons
import SvgDownArrow from './images/downarrow.svg';

// styles
import css from './filtersPopup.css';

class FiltersPopup extends React.Component {
    static propTypes = {
        currentUser: UserPropType,
        dateFrom: PropTypes.object,
        dateTo: PropTypes.object,
        filtersQuery: PropTypes.object,
        activeSortButton: PropTypes.string,
        sortButtons: PropTypes.array,
        onChangeFilterDate: PropTypes.func.isRequired,
        onFilter: PropTypes.func.isRequired,
        onSetQuery: PropTypes.func.isRequired,
        onSetActiveSortButton: PropTypes.func.isRequired,
        isArchivePage: PropTypes.bool,
    };

    state = {
        isOpened: false,
    };

    handleClose = () => {
        this.setState({ isOpened: false });
    };

    handleOpenPopup = () => {
        this.props.onSetActiveSortButton();
        this.setState({ isOpened: true });
    };

    handleLinkClick = (query, evt) => {
        evt.preventDefault();
        this.props.onSetQuery(query);
    };

    onViewButtonClick = () => {
        this.props.onFilter();
        this.setState({ isOpened: false });
    };

    render() {
        const props = this.props;

        return (
            <div className={css.root}>
                {props.currentUser.currentCompany && (
                    <div className={css.user}>
                        {[
                            displayCompanyEdrpou(
                                props.currentUser.currentCompany,
                            ),
                            props.currentUser.currentCompany.name,
                        ]
                            .filter(Boolean)
                            .join(', ')}
                    </div>
                )}
                <div className={css.title} onClick={this.handleOpenPopup}>
                    {getLocationName(location)}
                    <div className={css.icon}>
                        <Icon glyph={SvgDownArrow} />
                    </div>
                </div>
                <ul>
                    {props.dateFrom && (
                        <ListItem
                            color="white"
                            sizeSmall
                            label={formatDate(props.dateFrom, DATE_FORMAT)}
                            onDelete={() => {
                                props.onChangeFilterDate({
                                    dateFrom: null,
                                    dateTo: props.dateTo,
                                });
                                props.onFilter();
                            }}
                        />
                    )}
                    {props.dateTo && (
                        <ListItem
                            sizeSmall
                            color="white"
                            label={formatDate(props.dateTo, DATE_FORMAT)}
                            onDelete={() => {
                                props.onChangeFilterDate({
                                    dateFrom: props.dateFrom,
                                    dateTo: null,
                                });
                                props.onFilter();
                            }}
                        />
                    )}
                </ul>
                {this.state.isOpened && (
                    <PopupMobile
                        onClose={this.handleClose}
                        active={this.state.isOpened}
                        title={t`Відфільтрувати документи`}
                    >
                        <div className={css.content}>
                            <div className={css.item}>
                                <ButtonGroup
                                    theme="yellow"
                                    buttons={props.sortButtons}
                                    activeButton={props.activeSortButton}
                                    onChange={props.onSetActiveSortButton}
                                />
                            </div>
                            <div className={css.item}>
                                <DateFilter
                                    dateFrom={props.dateFrom}
                                    dateTo={props.dateTo}
                                    onChangeFilterDate={
                                        props.onChangeFilterDate
                                    }
                                />
                            </div>
                            {!props.isArchivePage && (
                                <div className={css.item}>
                                    <Navigation
                                        filtersQuery={props.filtersQuery}
                                        currentUser={props.currentUser}
                                        navigation={[
                                            ...mainLinks(),
                                            ...MAIN_MENU_STRUCTURE,
                                        ]}
                                        onLinkClick={this.handleLinkClick}
                                    />
                                </div>
                            )}
                            <PanelBottomFixed className={css.panelBottom}>
                                <div className={css.holder}>
                                    <div className={css.button}>
                                        <Link
                                            to="/app"
                                            onClick={() =>
                                                props.onSetQuery(null)
                                            }
                                        >
                                            <Button
                                                width="full"
                                                theme="red"
                                                typeContour
                                            >
                                                {t`Скинути фільтри`}
                                            </Button>
                                        </Link>
                                    </div>
                                    <div className={css.button}>
                                        <Button
                                            width="full"
                                            theme="cta"
                                            onClick={this.onViewButtonClick}
                                        >
                                            {t`Показати`}
                                        </Button>
                                    </div>
                                </div>
                            </PanelBottomFixed>
                        </div>
                    </PopupMobile>
                )}
            </div>
        );
    }
}

function mapStateToProps(state) {
    return {
        ...mapStateToCurrentUser(state),
        isArchivePage: getIsArchivePage(state),
    };
}

export default connect(mapStateToProps)(FiltersPopup);
