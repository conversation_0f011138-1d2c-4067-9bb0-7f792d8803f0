import logging
import re
from typing import Any, cast
from urllib.parse import urlencode

import sqlalchemy as sa
from aiohttp import (
    ClientSession,
    web,
)
from hiku.engine import Context
from sqlalchemy.sql import ClauseElement

from api.errors import (
    Code,
    Error,
)
from api.graph.constants import DB_ENGINE_KEY, DB_READONLY_KEY, ES_KEY
from api.graph.low_level import types as low_level_types
from app.auth.types import User
from app.contacts import db, es
from app.contacts.db import CONTACT_EDRPOU_IS_REGISTERED_FILTER, CONTACT_TABLES_JOIN
from app.contacts.enums import ContactRecipientIndexationEntity
from app.contacts.tables import (
    contact_person_phone_table,
    contact_person_table,
    contact_table,
)
from app.contacts.types import (
    ContactRecipient,
    CreateContactPersonOptions,
    UpdateContactPersonOptions,
    UpdatedContactPerson,
    UploadContactCtx,
)
from app.contacts.validators import validate_phone
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.types import (
    AnyDict,
    DataDict,
    DataMapping,
    StrList,
)
from app.services import services
from worker import topics

BEST_MATCH_MAPPING = {'tender': 1, 'sale': 2, 'zakaz': 3}

best_match_re = re.compile(r'^.*({}).*@.*$'.format('|'.join(BEST_MATCH_MAPPING)))
logger = logging.getLogger(__name__)


def best_match_email_sorter(email: str) -> int:
    found = best_match_re.findall(email)
    if not found:
        return 4
    return BEST_MATCH_MAPPING[found[0]]


def best_matched_email(emails: StrList) -> str | None:
    if len(emails) > 1:
        return sorted(emails, key=best_match_email_sorter)[0]
    return emails[0] if emails else None


async def insert_contacts_on_import(
    conn: DBConnection,
    user: User,
    ctx: UploadContactCtx,
) -> None:
    """
    Gradual insert of Contact Companies -> Persons -> Phones on contact list upload (CSV/XLSX)
    """
    companies = cast(list[AnyDict], ctx.data['companies'])
    emails = cast(list[AnyDict], ctx.data['emails'])
    phones = cast(list[AnyDict], ctx.data['phones'])

    async with conn.begin():
        if ctx.contacts_with_main_recipient:
            await db.reset_contacts_main_recipient(
                conn=conn,
                company_id=user.company_id,
                contacts_edrpous=ctx.contacts_with_main_recipient,
            )

        inserted_contacts = await db.insert_contacts(conn, data=companies)

        # Substitute ContactCompany id
        companies_mapping = {row.edrpou.lower(): row.id for row in inserted_contacts}
        for email in emails:
            email['contact_id'] = companies_mapping[email['edrpou']]
            del email['edrpou']

        # No emails - no persons, no phones
        if not emails:
            return

        inserted_persons = await db.insert_contact_persons(conn, data=emails)

        # Substitute ContactPerson id
        emails_mapping = {
            (row.contact_id, row.email.lower()): row.id for row in inserted_persons if row.email
        }
        for phone in phones:
            phone['contact_person_id'] = emails_mapping[
                (companies_mapping[phone['edrpou']], phone['email'])
            ]
            del phone['email'], phone['edrpou']
        phones_to_insert = [phone for phone in phones if phone['phone']]

        if not phones_to_insert:
            return

        await db.insert_contact_persons_phones(conn, data=phones_to_insert)


async def create_contact_person(
    conn: DBConnection,
    user: User,
    options: CreateContactPersonOptions,
) -> UpdatedContactPerson:
    async with conn.begin():
        contact = await db.soft_create_contact(
            conn=conn,
            company_id=user.company_id,
            contact_edrpou=options.edrpou,
        )
        updated = UpdatedContactPerson(contact=contact, person=None)

        if not options.email:
            return updated

        if options.main_recipient:
            await db.reset_contact_main_recipient(conn, contact_id=contact.id)

        contact_person = await db.insert_contact_person_raw(
            conn=conn,
            data={
                'contact_id': contact.id,
                'email': options.email,
                'first_name': options.first_name,
                'second_name': options.second_name,
                'last_name': options.last_name,
                'main_recipient': options.main_recipient,
            },
        )
        updated.person = contact_person

        if options.phone:
            await db.insert_contact_person_phone(
                conn=conn,
                person_id=contact_person.id,
                phone=options.phone,
            )

    return updated


async def update_contact_person(
    conn: DBConnection, user: User, options: UpdateContactPersonOptions
) -> UpdatedContactPerson:
    async with conn.begin():
        contact = await db.soft_create_contact(
            conn=conn,
            company_id=user.company_id,
            contact_edrpou=options.edrpou,
        )

        if options.main_recipient:
            await db.reset_contact_main_recipient(conn, contact_id=contact.id)

        person = await db.update_contact_person(
            conn=conn,
            contact_person_id=options.contact_person_id,
            data={**options.as_db(), 'contact_id': contact.id},
        )

        if options.phone:
            await db.update_contact_person_phones(
                conn=conn, person_id=options.contact_person_id, phone=options.phone
            )

    return UpdatedContactPerson(contact=contact, person=person)


async def update_contact_names(
    conn: DBConnection,
    contact_id: str,
    company_name: str | None,
    company_short_name: str | None,
) -> None:
    await db.update_contact(
        conn=conn,
        contact_id=contact_id,
        data={'name': company_name, 'short_name': company_short_name},
    )


async def unhide_contact_emails(
    conn: DBConnection,
    *,
    contact_edrpou: str,
    contact_email: str,
    company_id: str | None,
) -> None:
    if not company_id:
        return

    contact_person = await db.select_contact_person(
        conn=conn, email=contact_email, edrpou=contact_edrpou, company_id=company_id
    )
    if not contact_person:
        return

    await db.update_contact_person(
        conn=conn,
        contact_person_id=contact_person.id,
        data={'is_email_hidden': False},
    )


async def get_contact_info_url(app: web.Application, code: str) -> str:
    config = services.config.youcontrol
    if not config:
        raise Error(code=Code.unhandled_error)

    response_data = await request_contact_info(
        session=app['client_session'],
        url=config.url,
        login=config.login,
        password=config.password,
        code=code,
    )
    return response_data['url']


async def request_contact_info(
    session: ClientSession, *, url: str, code: str, login: str, password: str
) -> DataDict:
    headers = {'User-Agent': 'vchasno client'}
    data = {'code': code, 'section': 'open_data', 'login': login, 'password': password}

    # Getting contact info url
    async with session.get(url, params=data, headers=headers) as response:
        response_data = await response.json()

        # For some bad requests YouControl return json in format
        # {"error": error_message, "responseCode": code}
        if 'error' in response_data or response.status != 200 or 'url' not in response_data:
            raise Error(code=Code.youcontrol_request_error, reason=response_data.get('error'))
        return response_data


def build_youcontrol_referral_url(app: web.Application, url: str) -> str:
    config = services.config.youcontrol
    if not config:
        raise Error(code=Code.unhandled_error)
    referral_url: str = config.referral_url
    query = urlencode({'returnUrl': url})
    return f'{referral_url}?{query}'


async def get_contacts_for_search(
    *,
    graph_ctx: Context,
    options: DataMapping,
    user: User,
    use_es: bool,
) -> low_level_types.ContactsList:
    """
    Get contacts for a user search query using ES or DB according to the "use_es" param.
    """
    count: int = 0
    contact_ids: tuple[str, ...] = ()
    search_query: str | None = options['search']

    if search_query is not None:
        search_query = search_query.strip()

    if use_es:
        count, contact_ids = await es.search_contacts(
            index=graph_ctx[ES_KEY].contact_recipients,
            current_user_company_id=user.company_id,
            current_user_edrpou=user.company_edrpou,
            limit=options['limit'],
            offset=options['offset'],
            search_query=search_query,
            is_registered=options['isRegistered'],
        )
    else:
        select_source, filters = _extract_contacts_source_and_filters(
            current_user_edrpou=user.company_edrpou,
            current_user_company_id=user.company_id,
            search_query=search_query,
            is_registered=options['isRegistered'],
        )
        async with graph_ctx[DB_READONLY_KEY].acquire() as conn:
            contacts = await db.select_contacts_for_graph(
                conn=conn,
                select_source=select_source,
                filters=filters,
                limit=options['limit'],
                offset=options['offset'],
            )
            contacts_count = await db.select_count_contacts_for_graph(
                conn=conn,
                select_source=select_source,
                filters=filters,
            )
            count = contacts_count.count
            contact_ids = tuple(contact.id for contact in contacts)

    return low_level_types.ContactsList(
        count=count,
        contact_ids=contact_ids,
    )


async def get_contact_recipients_for_search(
    *,
    graph_ctx: Context,
    company_id: str,
    search_query: str,
    limit: int,
    offset: int,
    use_es: bool,
) -> list[ContactRecipient]:
    """
    Get contacts for recipient search.

    You can control the source of the data (PostgreSQL or Elasticsearch) with the "use_es"
    parameter. Use Elasticsearch by default and switch to PostgreSQL in case of Elasticsearch
    outage. The database version is slower, doesn't support fuzzy search and doesn't return
    companies if a company does not exist in the database.
    """
    recipients: list[ContactRecipient]
    if use_es:
        recipients = await es.search_contact_recipients(
            company_id=company_id,
            search_query=search_query,
            limit=limit,
            offset=offset,
        )
    else:
        async with graph_ctx[DB_ENGINE_KEY].acquire() as conn:
            recipients = await db.select_contact_recipients_for_search(
                conn=conn,
                company_id=company_id,
                search_query=search_query,
                limit=limit,
                offset=offset,
            )

    return recipients


async def get_contact_persons_for_search(
    *,
    graph_ctx: Context,
    options: DataMapping,
    user: User,
    use_es: bool,
) -> low_level_types.ContactPersonsList:
    """
    Get contact persons for a user search query using ES or DB according to the "use_es" param.
    """
    contact_persons_count: int = 0
    contact_person_ids: tuple[str, ...] = ()
    search_query: str | None = options['search']

    if search_query is not None:
        search_query = search_query.strip()

    if use_es:
        contact_persons_count, contact_person_ids = await es.search_contact_persons(
            index=graph_ctx[ES_KEY].contact_recipients,
            current_user_company_id=user.company_id,
            current_user_edrpou=user.company_edrpou,
            limit=options['limit'],
            offset=options['offset'],
            search_query=search_query,
        )
    else:
        # This resolver always requires joining `contact_person_table`, but the
        # extractor might return the SELECT source without the necessary table, so
        # ignore the possible insufficient select source
        _, search_filters = _extract_contacts_source_and_filters(
            current_user_edrpou=user.company_edrpou,
            current_user_company_id=user.company_id,
            search_query=search_query,
        )
        async with graph_ctx[DB_READONLY_KEY].acquire() as conn:
            contact_persons_count, contact_person_ids = await db.select_contact_persons_for_search(
                conn=conn,
                current_user_company_id=user.company_id,
                current_user_edrpou=user.company_edrpou,
                search_filters=search_filters,
                limit=options['limit'],
                offset=options['offset'],
                search_query=search_query,
            )

    return low_level_types.ContactPersonsList(
        count=contact_persons_count,
        contact_person_ids=contact_person_ids,
    )


def _extract_contacts_source_and_filters(
    current_user_edrpou: str,
    current_user_company_id: str,
    search_query: str | None = None,
    is_registered: bool | None = None,
) -> tuple[sa.Table, list[Any]]:
    """
    Build and return a `SELECT` source and a list of SQL filters matching Graph API query context.
    """
    select_source, search_filters = _extract_contacts_source_and_search_filters(search_query)

    current_user_filters = [
        contact_table.c.company_id == current_user_company_id,
        contact_table.c.edrpou != current_user_edrpou,
    ]

    is_registered_filters = []
    if is_registered is not None:
        is_registered_filters = [_contact_is_registered_filter(is_registered)]

    return select_source, [
        *current_user_filters,
        *search_filters,
        *is_registered_filters,
    ]


def _extract_contacts_source_and_search_filters(
    search_query: str | None,
) -> tuple[sa.Table, list[Any]]:
    """Return the most appropriate select source and filters for a search query term."""

    select_source = CONTACT_TABLES_JOIN
    additional_filters: list[Any] = []

    if not search_query:
        return select_source, additional_filters

    if edrpou := pv.EDRPOUOrNoneAdapter.validate_python(search_query):
        select_source = contact_table
        additional_filters.append(contact_table.c.edrpou == edrpou)

    elif maybe_email := pv.EmailOrNoneAdapter.validate_python(search_query):
        select_source = CONTACT_TABLES_JOIN
        additional_filters.append(
            sa.and_(
                contact_person_table.c.is_email_hidden.is_(False),
                contact_person_table.c.email == maybe_email,
            )
        )

    elif phone_search_query := validate_phone(search_query):
        select_source = CONTACT_TABLES_JOIN.outerjoin(
            contact_person_phone_table,
            contact_person_phone_table.c.contact_person_id == contact_person_table.c.id,
        )
        additional_filters.append(contact_person_phone_table.c.phone == phone_search_query)

    elif search_query.isdigit():
        select_source = contact_table
        search_pattern = f'%{search_query}%'
        additional_filters.append(contact_table.c.edrpou.ilike(search_pattern))

    else:
        search_pattern = f'%{search_query}%'
        additional_filters.append(
            sa.or_(
                contact_table.c.name.ilike(search_pattern),
                contact_table.c.short_name.ilike(search_pattern),
                sa.and_(
                    contact_person_table.c.is_email_hidden.is_(False),
                    contact_person_table.c.email.ilike(search_pattern),
                ),
                contact_person_table.c.first_name.ilike(search_pattern),
                contact_person_table.c.second_name.ilike(search_pattern),
                contact_person_table.c.last_name.ilike(search_pattern),
            )
        )

    return select_source, additional_filters


def _contact_is_registered_filter(is_registered: bool) -> ClauseElement:
    if is_registered is True:
        # filter contacts where a registered company exists
        return CONTACT_EDRPOU_IS_REGISTERED_FILTER

    # filter contacts where a registered company doesn't exist
    return sa.not_(CONTACT_EDRPOU_IS_REGISTERED_FILTER)


async def schedule_index_contact_recipients_job() -> None:
    """
    Schedule jobs that index ate contacts and contact persons.

    This job uses "contact_recipients_indexation_table" under the hood to store
    which entities should be indexed. But we need to trigger the job manually
    to avoid pooling that table.

    Most of the time, we don't need near-real-time indexing for contacts, so it's OK not to call
    this function at all after an update in the contact table. We have a cron job that
    periodically checks the table and indexes the entities, so every update will be indexed
    eventually. Good candidates for this function are the cases when a user edits a contact
    manually, and we want to index those changes as soon as possible. But when we create a
    contact automatically (e.g., on document send), it's OK to wait for the cron job to index the
    entity.
    """
    await services.kafka.send_record(topic=topics.INDEX_CONTACT_RECIPIENTS, value={})


async def delete_contacts_from_index_job(contacts_ids: list[str]) -> None:
    """
    Schedule a job that deletes contacts from Elasticsearch index
    """
    await services.kafka.send_record(
        topic=topics.DELETE_CONTACTS_FROM_INDEX,
        value={'contacts_ids': contacts_ids},
    )


async def delete_contact_persons_from_index_job(
    persons_ids: list[str] | None = None,
    person_id: str | None = None,
) -> None:
    """
    Schedule a job that deletes contact persons from Elasticsearch index
    """

    persons_ids = persons_ids or []
    if person_id:
        persons_ids.append(person_id)

    await services.kafka.send_record(
        topic=topics.DELETE_CONTACTS_PERSONS_FROM_INDEX,
        value={'persons_ids': persons_ids},
    )


async def save_to_contacts(
    conn: DBConnection,
    *,
    edrpou: str,
    email: str,
    company_id: str,
    name: str | None = None,
    main_recipient: bool = False,
) -> None:
    """Save simple {Email & Edrpou} contact info"""
    _edrpou = validators.validate_edrpou(str(edrpou))
    if not _edrpou:
        return

    async with conn.begin():
        contact = await db.soft_create_contact(
            conn=conn,
            company_id=company_id,
            contact_edrpou=_edrpou,
            contact_name=name,
        )

        contact_person = await db.select_contact_person(
            conn=conn,
            email=email,
            edrpou=_edrpou,
            company_id=company_id,
        )

        if not contact_person:
            await db.insert_contact_person_raw(
                conn=conn,
                data={
                    'email': email,
                    'main_recipient': main_recipient,
                    'contact_id': contact.id,
                },
            )


async def add_company_to_contact_recipients_indexation(
    conn: DBConnection,
    *,
    company_id: str | None,
) -> None:
    """
    Add a company to contact recipient indexation queue
    """
    if not company_id:
        return

    await db.insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=[company_id],
        entity_type=ContactRecipientIndexationEntity.company,
    )


async def add_role_to_contact_recipients_indexation(
    conn: DBConnection,
    *,
    role_id: str | None = None,
    roles_ids: list[str] | None = None,
) -> None:
    """
    Add a role to contact recipient indexation queue
    """

    entities_ids: list[str] = []
    if role_id:
        entities_ids.append(role_id)
    if roles_ids:
        entities_ids.extend(roles_ids)

    if not entities_ids:
        return

    await db.insert_contact_recipients_indexation(
        conn=conn,
        entities_ids=entities_ids,
        entity_type=ContactRecipientIndexationEntity.role,
    )
