import React, { FC, ReactNode } from 'react';

import { Document } from '../../../services/documents/ts/types';

import { getCurrentExecutor } from '../../../lib/helpers';

import css from './currentExecutorBlock.css';

interface Props {
    isVisible: boolean;
    doc: Document;
    children?: ReactNode;
}

const CurrentExecutorBlock: FC<React.PropsWithChildren<Props>> = ({
    isVisible,
    doc,
}) => {
    if (!isVisible) return null;

    const displayValue = getCurrentExecutor(doc);

    return (
        <div className={css.root} title={displayValue}>
            {displayValue}
        </div>
    );
};

export default CurrentExecutorBlock;
