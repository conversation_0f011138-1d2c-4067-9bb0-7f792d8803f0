import { DocumentsUploadForm } from 'components/DocumentsUploader/types';
import { MBtoBytes } from 'lib/ts/numbers';

import {
    MAX_FILES_COUNT,
    MAX_FILES_SIZE_MB,
    MAX_FILE_SIZE_MB,
} from '../../constants/upload';

export const emptyDefaultValues: DocumentsUploadForm = {
    companyEdrpou: '',
    dataAI: null,
    isPayedRate: false,
    files: [],
    counterparties: [],
    employeesSigners: [],
    employeesSignIsOrdered: false,
    employeesReviewers: [],
    employeesReviewersIsOrdered: false,
    employeesIsSignAfterReview: false,
    employeesViewers: [],
    accessLevel: 'extended',
    dataAI: null,
    documentParameters: [],
    isInternal: false,
    isParallel: false,
    title: '',
    amount: null,
    date: null,
    category: '',
    number: '',
    comment: '',
    isVersioned: false,
    templateId: null,
    selectedTags: [],
    maxTotalCount: MAX_FILES_COUNT,
    maxTotalSize: MBtoBytes(MAX_FILES_SIZE_MB),
    maxFileSize: MBtoBytes(MAX_FILE_SIZE_MB),
};
