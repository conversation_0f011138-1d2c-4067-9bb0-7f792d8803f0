import { RouteComponentProps } from 'react-router-dom';

import { BillAcount, BillDataForActivate } from 'types/billing';

import { ICompany } from '../../types/user';

export interface State {
    isCompanyDataLoaded: boolean;
    isEditableRateMode?: boolean;
    searchString: string;
    company: ICompany;
    billForActivate: Nullable<BillAcount>;
    adminCompanyConfig?: any;
    onChangeAdminConfig?: any;
    currentUser: any;
}

export type loadCompanyDataType = (
    search: string,
    id: string,
    isSkipUpdateBills?: boolean,
) => void;

export interface DispatchToProps {
    loadCompanyData: loadCompanyDataType;
    setBillDataForActivate: (bill: BillAcount) => void;
    clearBillDataForActivate: () => void;
    clearCompanyData: () => void;
    onChangeAdminConfig: () => void;
    setBillDataForActivateFromBilling: (billData: BillDataForActivate) => void;
}

export type Props = DispatchToProps & State;

export interface RouteParams {
    companyId: string;
    search: string;
}

export type ContainerProps = RouteComponentProps<
    RouteParams,
    Record<string, unknown>,
    Props
> &
    Props;
