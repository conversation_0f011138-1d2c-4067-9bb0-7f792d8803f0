import {
    cellToStyledNumber,
    xmlQueries,
} from 'components/txtXmlViewer/carlsbergUkraine/DeliveryAcceptanceTareCertificate/constants';
import { textContent } from 'components/txtXmlViewer/utils';
import { toDate } from 'lib/date';
import { parseXml } from 'services/xml';

const parseTextFloat = (floatString: string) => {
    return parseFloat(floatString.replace(/[\.\n]/gm, '').replace(/,/, '.'));
};

const numberStringToStyled = (number: string, fractionDigits: number) => {
    return parseTextFloat(number).toLocaleString('uk-UA', {
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits,
    });
};

const buildTableObject = (rows: NodeListOf<Element>) => {
    return Array.from(rows).reduce(
        (acc: Array<{ [key: string]: string }>, item) => {
            const rowLine = parseInt(item.getAttribute('LINE')?.trim() || '');
            const cellName = item.getAttribute('NAME')?.trim();
            const cellValue = item.querySelector('VALUE')?.textContent;
            if (!acc[rowLine]) {
                acc[rowLine] = {};
            }
            const row = acc[rowLine];
            row[cellName] = cellToStyledNumber.includes(cellName)
                ? numberStringToStyled(cellValue || '', 2)
                : cellValue;
            return acc;
        },
        [],
    );
};

const DeliveryAcceptanceTareCertificateParser = (content: string) => {
    const xml = parseXml(content);
    const getText = (query: string) => textContent(xml, query) as string;

    const docDate = toDate(getText(xmlQueries.docDate), 'DD.MM.YYYY HH:mm:ss');
    const dogDate = toDate(getText(xmlQueries.dogDate), 'DD.MM.YYYY HH:mm:ss');

    return {
        num: getText(xmlQueries.num),
        firmName: getText(xmlQueries.firmName),
        firmEDRPOU: getText(xmlQueries.firmEDRPOU),
        sideCDK: getText(xmlQueries.sideCDK),
        sideEDRPOUK: getText(xmlQueries.sideEDRPOUK),
        field1: getText(xmlQueries.field1) || '',
        field2: getText(xmlQueries.field2) || '',
        dogNum: getText(xmlQueries.dogNum) || '-',
        voName: getText(xmlQueries.voName) || '',
        sideOTVFIO: getText(xmlQueries.sideOTVFIO) || '',
        docSum: numberStringToStyled(getText(xmlQueries.docSum), 2),
        docDate: docDate
            ? docDate.toLocaleString('uk-UA', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
              })
            : '-',
        dogDate: dogDate
            ? dogDate.toLocaleString('uk-UA', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
              })
            : '-',
        table: buildTableObject(xml.querySelectorAll('ROW[TAB="1"]')),
    } as const;
};

export type DeliveryAcceptanceTareCertificateData = ReturnType<
    typeof DeliveryAcceptanceTareCertificateParser
>;

export default (data: { content: string }) =>
    DeliveryAcceptanceTareCertificateParser(data.content);
