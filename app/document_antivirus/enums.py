from enum import Enum, unique


@unique
class AntivirusProvider(Enum):
    """
    Represents all supported antivirus solutions
    """

    eset = 'eset'  # https://gitlab.evo.dev/evodoc/antivirus

    # DEPRECATED:
    cloud_storage_sec = 'cloud_storage_sec'  # https://cloudstoragesec.com/aws/


@unique
class AntivirusCheckStatus(Enum):
    checking = 'checking'
    infected = 'infected'
    clean = 'clean'

    # DEPRECATED:
    encrypted = 'encrypted'  # Company has enabled s3 files encryption
