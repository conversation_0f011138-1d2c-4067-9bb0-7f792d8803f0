import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.document_antivirus.enums import (
    AntivirusCheckStatus,
    AntivirusProvider,
)
from app.document_antivirus.tables import (
    document_antivirus_check_table,
    draft_antivirus_check_table,
)
from app.document_antivirus.types import (
    DocumentAntivirusCheck,
    DraftAntivirusCheck,
)
from app.lib.database import DBConnection, DBRow
from app.lib.types import (
    DataDict,
    StrList,
)
from app.models import (
    select_all,
    select_one,
)


async def select_document_antivirus_check(
    conn: DBConnection,
    document_id: str,
    document_version_id: str | None = None,
    provider: AntivirusProvider | None = None,
) -> DocumentAntivirusCheck | None:
    filters = [document_antivirus_check_table.c.document_id == document_id]
    if document_version_id:
        filters.append(
            document_antivirus_check_table.c.document_version_id == document_version_id,
        )
    if provider:
        filters.append(document_antivirus_check_table.c.provider == provider)

    row = await select_one(
        conn=conn,
        query=sa.select([document_antivirus_check_table]).where(sa.and_(*filters)),
    )
    return DocumentAntivirusCheck.from_db(row) if row else None


async def select_document_antivirus_checks(
    conn: DBConnection,
    documents_ids: StrList | None = None,
    versions_ids: StrList | None = None,
    provider: AntivirusProvider | None = None,
) -> list[DocumentAntivirusCheck]:
    assert documents_ids or versions_ids, 'documents_ids or versions_ids should be provided'

    filters = []
    if documents_ids:
        filters.append(document_antivirus_check_table.c.document_id.in_(documents_ids))
    if versions_ids:
        filters.append(document_antivirus_check_table.c.document_version_id.in_(versions_ids))
    if provider:
        filters.append(document_antivirus_check_table.c.provider == provider)

    rows = await select_all(
        conn=conn,
        query=sa.select([document_antivirus_check_table]).where(sa.and_(*filters)),
    )
    return [DocumentAntivirusCheck.from_db(row) for row in rows]


async def insert_document_antivirus_checks(
    conn: DBConnection, checks: list[DocumentAntivirusCheck]
) -> None:
    query = (
        insert(document_antivirus_check_table)
        .values([check.to_db() for check in checks])
        .on_conflict_do_nothing()
    )
    await conn.execute(query)


async def insert_document_antivirus_check(
    conn: DBConnection, check: DocumentAntivirusCheck
) -> None:
    query = insert(document_antivirus_check_table).values(check.to_db()).on_conflict_do_nothing()
    await conn.execute(query)


async def update_document_antivirus_check(
    conn: DBConnection,
    data: DataDict,
) -> DBRow:
    id_ = data.pop('id')
    return await select_one(
        conn,
        sa.update(document_antivirus_check_table)
        .values(
            **data,
            date_updated=sa.text('now()'),
        )
        .where(document_antivirus_check_table.c.id == id_)
        .returning(document_antivirus_check_table.c.id),
    )


async def update_antivirus_checks_by_document(
    conn: DBConnection,
    *,
    document_id: str,
    data: DataDict,
) -> None:
    """
    Update all antivirus checks for the document
    """
    await conn.execute(
        document_antivirus_check_table.update()
        .values(data)
        .where(document_antivirus_check_table.c.document_id == document_id),
    )


async def upsert_antivirus_check_for_drafts(
    *,
    conn: DBConnection,
    draft_ids: list[str],
    status: AntivirusCheckStatus,
    provider: AntivirusProvider | None,
) -> list[DraftAntivirusCheck]:
    """
    Add antivirus check for draft.
    Or update status if it already exists.
    Setting status to pending.
    """
    data = {
        'date_updated': sa.text('now()'),
        'status': status,
        'provider': provider,
    }

    rows = await select_all(
        conn=conn,
        query=(
            insert(draft_antivirus_check_table)
            .values([{'draft_id': draft_id, **data} for draft_id in draft_ids])
            .on_conflict_do_update(
                index_elements=[draft_antivirus_check_table.c.draft_id],
                set_=data,
            )
            .returning(draft_antivirus_check_table)
        ),
    )

    return [DraftAntivirusCheck.from_db(row) for row in rows]


async def select_draft_antivirus_check(
    conn: DBConnection,
    draft_id: str,
) -> DraftAntivirusCheck | None:
    row = await select_one(
        conn=conn,
        query=sa.select([draft_antivirus_check_table]).where(
            draft_antivirus_check_table.c.draft_id == draft_id,
        ),
    )
    return DraftAntivirusCheck.from_db(row) if row else None


async def update_draft_antivirus_check(
    conn: DBConnection,
    *,
    draft_id: str,
    status: AntivirusCheckStatus,
    provider: AntivirusProvider | None,
) -> str | None:
    row = await select_one(
        conn,
        sa.update(draft_antivirus_check_table)
        .values(
            status=status,
            provider=provider,
            date_updated=sa.text('now()'),
        )
        .where(sa.and_(draft_antivirus_check_table.c.draft_id == draft_id))
        .returning(draft_antivirus_check_table.c.draft_id),
    )

    return row.draft_id if row else None


async def delete_antivirus_check_for_document_version(conn: DBConnection, version_id: str) -> None:
    await conn.execute(
        document_antivirus_check_table.delete().where(
            document_antivirus_check_table.c.document_version_id == version_id
        )
    )
