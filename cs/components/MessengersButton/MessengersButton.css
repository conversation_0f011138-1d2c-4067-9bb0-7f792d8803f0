.root {
    position: fixed;
    z-index: var(--z-index-highest);
    right: 65px;
    bottom: 50px;
    display: flex;
    flex-flow: column-reverse;
    align-items: center;
    user-select: none;
}

.mainButton {
    z-index: 1;
    width: 60px;
    height: 60px;
    background-color: #08c;
    cursor: pointer;
}

.baseButton.linkButton {
    position: absolute;
    width: 50px;
    height: 50px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translate(0, 0);
}

.messageIcon {
    width: 35px;
    height: 32px;
}

.closeIcon {
    width: 16px;
    height: 16px;
    opacity: 0;
    transform: scale(0);
}

.tooltip {
    position: absolute;
    top: 50%;
    right: 70px;
    padding: 6px 9px;
    background: #292929;
    border-radius: var(--border-radius);
    color: #fff;
    opacity: 0;
    pointer-events: none;
    transform: translateX(0%) translateY(-50%);
    transition: all 0.1s linear;
}

.rootOpened .mainButton {
    background-color: var(--white-bg);
}

.rootOpened .messageIcon {
    opacity: 0;
    transform: scale(0);
}

.rootOpened .closeIcon {
    opacity: 1;
    transform: scale(1);
}

.rootOpened .linkButton {
    opacity: 1;
}

.rootOpened .linkButton:nth-child(2) {
    transform: translate(0, -70px);
}

.rootOpened .linkButton:nth-child(3) {
    transform: translate(0, -130px);
}

.rootOpened .linkButton:nth-child(4) {
    transform: translate(0, -190px);
}

.rootOpened .linkButton:nth-child(5) {
    transform: translate(0, -250px);
}

.baseButton {
    position: relative;
    border-radius: 50%;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease-in-out;
}

.baseButton:hover {
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.4);
}

.linkButton:hover .tooltip {
    opacity: 0.85;
}

.baseIcon {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    transition: all 0.2s ease-in-out;
}

.tooltip::after {
    position: absolute;
    top: 50%;
    right: -20px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-left-color: #292929;
    content: '';
    transform: translateY(-50%);
}
