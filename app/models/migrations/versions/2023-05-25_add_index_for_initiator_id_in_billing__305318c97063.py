"""add index for initiator_id in billing_accounts

Revision ID: 305318c97063
Revises: 585663bf303b
Create Date: 2023-05-25 14:33:58.939067

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '305318c97063'
down_revision = '585663bf303b'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')  # skip transaction for concurrent index creation
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS
           ix_billing_accounts_initiator_id
        ON billing_accounts (initiator_id);
        """
    )


def downgrade():
    op.drop_index(op.f('ix_billing_accounts_initiator_id'), table_name='billing_accounts')
