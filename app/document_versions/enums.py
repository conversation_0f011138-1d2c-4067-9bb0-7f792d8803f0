from enum import auto

from app.lib.enums import NamedEnum


class DocumentVersionType(NamedEnum):
    # Company uploaded new version of document
    new_upload = auto()

    # Created by built-in editor (Collabora)
    editor_created = auto()

    # User converted an office file (dox, xlsx, pptx) to PDF
    converted_format = auto()

    @property
    def is_pdf_convertable(self) -> bool:
        """
        Which version types can be converted to PDF?
        """
        return self in PDF_CONVERTABLE_VERSION_TYPES


PDF_CONVERTABLE_VERSION_TYPES = {
    DocumentVersionType.new_upload,
    DocumentVersionType.editor_created,
}


class VersionReviewFlow(NamedEnum):
    # a process of review will be started from the beginning
    restarted = auto()
    # copy the review from the previous version
    continued = auto()

    @property
    def is_continued(self) -> bool:
        """
        Is the review flow continued from the previous version?
        """
        return self == VersionReviewFlow.continued
