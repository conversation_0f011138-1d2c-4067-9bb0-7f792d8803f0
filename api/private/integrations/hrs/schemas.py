import typing as t

import pydantic

from api.private.integrations.hrs import enums as hrs_enums
from app.lib import validators_pydantic as pv


class GetUsersSchema(pydantic.BaseModel):
    """
    Request schema for retrieving list of users by emails or vchasno_ids.
    """

    emails: list[str] | None = None
    vchasno_ids: list[str] | None = None


class GetUserOutput(pydantic.BaseModel):
    """
    Response schema for retrieving a single user information.
    """

    email: pv.Email | None
    status: hrs_enums.UserStatus
    app_status: hrs_enums.UserMobileStatus
    vchasno_id: str


class GetUsersOutput(pydantic.BaseModel):
    """
    Response schema for retrieving multiple users information.
    """

    users: list[GetUserOutput]


class GetRolesSchema(pydantic.BaseModel):
    """
    Request schema for retrieving list of roles of a single user by email or vchasno_id.
    """

    email: str | None = None
    vchasno_id: str | None = None


class GetRoleOutput(pydantic.BaseModel):
    """
    Response schema for retrieving a single role information.
    """

    company_edrpou: str
    company_name: str
    company_full_name: str
    position: str
    has_hrs_role: bool


class GetRolesOutput(pydantic.BaseModel):
    """
    Response schema for retrieving roles information of a single user.
    """

    user: GetUserOutput
    roles: list[GetRoleOutput]


class GetCompanyRoleOutput(pydantic.BaseModel):
    """
    Slim version of GetRoleOutput.
    """

    position: str
    has_hrs_role: bool


class GetCompanyOutput(pydantic.BaseModel):
    """
    Response schema for retrieving roles for a company.
    """

    company_name: str
    company_full_name: str
    roles: list[GetCompanyRoleOutput]


class GetCompaniesRolesOutput(pydantic.BaseModel):
    """
    Response schema for retrieving companies roles.
    """

    companies: dict[str, GetCompanyOutput]


class GetCompaniesRolesSchema(pydantic.BaseModel):
    """
    Request schema for retrieving companies roles.
    """

    company_edrpous: list[str]


class RolePermissionsSchema(pydantic.BaseModel):
    """
    Request schema for setting a permissions for a role upon invite.
    """

    can_comment_document: bool = True
    can_sign_and_reject_document: bool = True
    can_sign_and_reject_document_external: bool = True
    can_sign_and_reject_document_internal: bool = True
    can_delete_document: bool = True
    can_upload_document: bool = False
    can_invite_coworkers: bool = False
    can_view_coworkers: bool = False


class AddRoleSchema(pydantic.BaseModel):
    """
    Request schema to add a new role to the company.
    """

    email: pv.Email
    edrpou: str
    position: str

    permissions: RolePermissionsSchema | None = None


class RemoveRoleSchema(pydantic.BaseModel):
    """
    Request schema to remove role from the company.
    """

    vchasno_id: str
    edrpou: str


class SendPushNotificationToMobileAppSchema(pydantic.BaseModel):
    """
    Request schema to send push notification to a user.
    """

    vchasno_id: str
    company_edrpou: str
    title: str
    description: str
    payload: dict[str, t.Any] | None = None


class CompanySchema(pydantic.BaseModel):
    """
    Response schema for retrieving a single company information.
    """

    edrpou: pv.EDRPOU
    name: str
