from datetime import timedelta

from api.public.tests.common import (
    TEST_RECIPIENT_EDRPOU,
    TEST_RECIPIENT_EMAIL,
)
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check
from app.document_versions.enums import DocumentVersionType
from app.document_versions.utils import add_document_content_version
from app.documents.db import select_document_by_id
from app.documents.enums import FirstSignBy
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus
from app.tests.common import (
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_signature_form_data,
    prepare_user_data,
)


async def test_signing_versioned_document(mailbox, aiohttp_client):
    """
    Given:
    - Owner uploads document with signature request from recipient only.
    - Owner uploads 2 versions of document.
    - Owner sends document to recipient.
    When:
    - Recipient signs document.
    Then:
    - Document finished.
    """
    # Arrange
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
        expected_owner_signatures=0,
        status_id=DocumentStatus.uploaded.value,
    )

    # mark document as versioned
    async with app['db'].acquire() as conn:
        document_version_1 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=owner.company_edrpou,
            company_id=owner.company_id,
            role_id=owner.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=owner.company_edrpou,
            company_id=owner.company_id,
            role_id=owner.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_1.id,
                status=AntivirusCheckStatus.checking,
            ),
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_2.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    document_id = document.id
    send_url = f'/internal-api/documents/{document_id}/send'

    response = await client.post(
        send_url,
        json={'edrpou': TEST_RECIPIENT_EDRPOU, 'email': TEST_RECIPIENT_EMAIL},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.sent.value

    recipient = await prepare_user_data(
        app, company_edrpou=TEST_RECIPIENT_EDRPOU, email=TEST_RECIPIENT_EMAIL
    )

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(recipient, owner=owner),
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None


async def test_signing_versioned_owner_sign_as_well(mailbox, aiohttp_client):
    """
    Given:
    - Owner uploads document with signature request from owner and recipient.
    - Owner uploads 2 versions of document.
    - Owner sends document to recipient.
    When:
    - Recipient signs document.
    - Owner signs document.
    Then:
    - Document finished.
    """
    # Arrange
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
        expected_owner_signatures=1,
        status_id=DocumentStatus.uploaded.value,
    )

    # mark document as versioned
    async with app['db'].acquire() as conn:
        document_version_1 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=owner.company_edrpou,
            company_id=owner.company_id,
            role_id=owner.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=owner.company_edrpou,
            company_id=owner.company_id,
            role_id=owner.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_1.id,
                status=AntivirusCheckStatus.checking,
            ),
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_2.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    document_id = document.id
    send_url = f'/internal-api/documents/{document_id}/send'

    response = await client.post(
        send_url,
        json={'edrpou': TEST_RECIPIENT_EDRPOU, 'email': TEST_RECIPIENT_EMAIL},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.sent.value

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(recipient, owner=owner),
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.signed_and_sent.value

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(owner),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None


async def test_signing_versioned_owner_signs_first(mailbox, aiohttp_client):
    """
    Given:
    - Owner uploads document with signature request from owner and recipient.
    - Owner sends document to recipient.
    - Recipient upload version of document.
    - Recipient sends document to recipient.
    When:
    - Owner signs document.
    - Recipient signs document.
    Then:
    - Document finished.
    """
    # Arrange
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
        expected_owner_signatures=1,
        status_id=DocumentStatus.uploaded.value,
    )
    now = utc_now()

    # mark document as versioned
    async with app['db'].acquire() as conn:
        document_version_1 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=owner.company_edrpou,
            company_id=owner.company_id,
            date_created=now,
            role_id=owner.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=recipient.company_edrpou,
            company_id=recipient.company_id,
            date_created=now + timedelta(seconds=1),
            role_id=recipient.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_1.id,
                status=AntivirusCheckStatus.checking,
            ),
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=document_version_2.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    document_id = document.id
    send_url = f'/internal-api/documents/{document_id}/send'

    response = await client.post(
        send_url,
        json={'edrpou': TEST_RECIPIENT_EDRPOU, 'email': TEST_RECIPIENT_EMAIL},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.sent.value

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.sent.value

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(owner),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.signed_and_sent.value

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(recipient, owner=owner),
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 201, await response.json()

    response = await client.post(
        send_url,
        json={},
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None
