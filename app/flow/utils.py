from __future__ import annotations

import dataclasses
import logging
from collections.abc import Iterator
from functools import cached_property
from typing import Any
from uuid import uuid4

import sqlalchemy as sa
from aiohttp import web

from app.auth.db import select_companies_by_edrpou, select_role_by_emails_and_edrpous
from app.auth.types import AuthUser, RolesGroup, User
from app.documents.db import (
    insert_listings,
    insert_recipients,
    update_document,
)
from app.documents.enums import AccessSource
from app.documents.types import (
    Document,
    DocumentRecipient,
    DocumentWithUploader,
    ListingDataAggregator,
)
from app.documents.utils import (
    schedule_jobs_about_finished_document,
    send_document_status_callback_job,
    update_recipients_date_sent,
)
from app.documents.validators import _validate_edrpou_send_document
from app.es.utils import send_to_indexator
from app.flow import db
from app.flow.types import (
    AddFlowOptions,
    CreateDocumentFlowsOutput,
    CreateFlowCtx,
    FlowItem,
    FlowMeta,
    ReceiverSchema,
    SendMultilateralDocumentOutput,
    UpdateFlowOnSignOutput,
)
from app.flow.validators import validate_flow_request
from app.lib.database import DBConnection
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus, Source
from app.lib.helpers import (
    ZAKUPKI_EDRPOU,
    group_list,
    unique_list_with_original_order,
)
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.services import services
from app.signatures.db import select_document_signers_counter
from app.signatures.types import DocumentSignerCounter
from app.uploads.types import FlowsUploadSettings
from worker import topics

logger = logging.getLogger(__name__)

get_flows = db.select_flows_by
get_flows_with_recipients = db.select_flows_with_recipients
delete_flows = db.delete_flows


class FlowsState:
    """
    Object that represents group of flows for one document.

    """

    def __init__(self, flows: list[FlowItem]) -> None:
        self.flows = flows
        self.is_ordered = any(flow.is_ordered for flow in flows)
        self._check_document_id()
        self._sort()
        self._check_mixed()

    @classmethod
    def build_empty(cls) -> FlowsState:
        """Build empty flow state"""
        return cls(flows=[])

    def _check_document_id(self) -> None:
        """Check if all flows have the same document_id"""
        document_ids = {flow.document_id for flow in self.flows}
        if len(document_ids) > 1:
            raise ValueError('All flows should have the same document_id')

    def _sort(self) -> None:
        """Sort flows by order and id"""
        if self.is_ordered:
            self.flows = sorted(self.flows, key=lambda flow: (flow.order, flow.id))
        else:
            self.flows = sorted(self.flows, key=lambda flow: flow.id)

    def _check_mixed(self) -> None:
        """Check if flows are mixed"""
        has_ordered = any(flow.is_ordered for flow in self.flows)
        has_unordered = any(flow.is_parallel for flow in self.flows)
        if has_ordered and has_unordered:
            logger.warning(
                msg='Flows are mixed',
                extra={'flows': [flow.to_dict() for flow in self.flows]},
            )

    @cached_property
    def last_signed_ordered_flow(self) -> FlowItem | None:
        """Return last signed flow"""
        for flow in reversed(self.flows):
            if flow.is_signed:
                return flow

        return None

    @cached_property
    def ordered_flows_for_sign(self) -> list[FlowItem]:
        """Return first ordered flow that expects signatures"""
        for flow in self.flows:
            if flow.should_be_signed:
                return [flow]

        return []

    @cached_property
    def parallel_flows_for_sign(self) -> list[FlowItem]:
        """Return all parallel flows that expects signatures"""
        flows = []
        for flow in self.flows:
            if flow.should_be_signed:
                flows.append(flow)

        return flows

    @cached_property
    def flows_for_sign(self) -> list[FlowItem]:
        """Return all flows that expects signatures"""
        if self.is_ordered:
            return self.ordered_flows_for_sign

        return self.parallel_flows_for_sign

    def should_company_sign(self, company_edrpou: str) -> bool:
        """Check if company should sign document"""
        flows = self.flows_for_sign
        return any(flow.edrpou == company_edrpou for flow in flows)

    def is_signed_once_by_company(self, company_edrpou: str) -> bool:
        """Check if company has signed document"""
        flows = self.flows
        return any(flow.is_signed_once for flow in flows if flow.edrpou == company_edrpou)

    @cached_property
    def ordered_flows_for_send(self) -> list[FlowItem]:
        """
        Return next ordered flows for sending to recipient.

        For ordered process it will be flows between last signed flow and first
        flow that expects signatures.
        """
        if not self.is_ordered:
            return []

        current = self.last_signed_ordered_flow

        # If there is no signed flow, we should start from the first flow
        current_order: int = -1
        if current and (order := current.order):
            current_order = order or -1

        flows = []
        for flow in self.flows:
            # skip flows that are already signed before the current flow
            flow_order: int = flow.order or 0
            if flow_order <= current_order:
                continue

            # for all flows that are not expects signatures we should open access
            # without waiting for signatures from them.
            if not flow.is_expects_signatures:
                flows.append(flow)
                continue

            # Iterate until we find the first flow that expects signatures
            if flow.pending_signatures_count > 0:
                flows.append(flow)
                break

        # Remove already sent flows
        flows = [flow for flow in flows if not flow.is_sent]

        return flows

    @cached_property
    def parallel_flows_for_send(self) -> list[FlowItem]:
        """
        Return next parallel flows for sending. For parallel process, it will be
        all flows that have not been sent yet
        """

        if not self.is_parallel:
            return []

        return [f for f in self.flows if not f.is_sent]

    @cached_property
    def flows_for_send(self) -> list[FlowItem]:
        """
        Return next flows for sending

        NOTE: Ignore if flow.meta.unfinished=True
        """

        if self.is_ordered:
            return self.ordered_flows_for_send

        return self.parallel_flows_for_send

    @property
    def flows_for_execute_send_jobs(self) -> list[FlowItem]:
        """
        Return flows for which we should execute jobs about sending a document.

        See "app/flow/types.py::FlowMeta" for more information about "send_jobs_executed"
        """
        return [flow for flow in self.flows if flow.is_sent and not flow.meta.send_jobs_executed]

    @property
    def flows_for_send_notifications(self) -> list[FlowItem]:
        """
        Return flows for which we should send inbox notifications about the document.

        See "app/flow/types.py::FlowMeta" for more information about "send_notifications_executed"
        """
        return [
            flow
            for flow in self.flows
            if flow.is_sent and not flow.meta.send_notifications_executed
        ]

    @property
    def is_parallel(self) -> bool:
        return not self.is_ordered

    @property
    def is_finished(self) -> bool:
        return all(flow.is_finished for flow in self.flows)

    @property
    def next_ordered_flow(self) -> FlowItem | None:
        """Return next ordered flow for signing"""
        next_ordered_flow = next(filter(lambda f: not f.is_finished, self.flows), None)
        return next_ordered_flow

    def get_current_company_flow(self, company_edrpou: str) -> FlowItem | None:
        """
        Return first flow for the company among flows that should be signed next.
        """
        flows = self.flows_for_sign
        return next((f for f in flows if f.edrpou == company_edrpou), None)


async def get_flows_state(
    conn: DBConnection,
    *,
    document_id: str,
) -> FlowsState:
    """Get FlowState object"""
    flows = await db.select_flows_by(conn, document_id=document_id)
    return FlowsState(flows=flows)


async def get_flows_states(
    conn: DBConnection,
    *,
    documents_ids: list[str],
) -> dict[str, FlowsState]:
    flows = await db.select_flows_by(conn, documents_ids=documents_ids)
    flows_mapping = group_list(flows, lambda f: f.document_id)
    return {document_id: FlowsState(flows=flows) for document_id, flows in flows_mapping.items()}


def _iterate_flow_recipients(
    flows: list[FlowItem],
    recipients: list[DocumentRecipient],
) -> Iterator[tuple[FlowItem, DocumentRecipient | None]]:
    """Iterate over flows and recipients by joining them by recipient_id"""

    recipients_mapping = {recipient.id: recipient for recipient in recipients}

    for flow in flows:
        # Get recipient for flow
        recipient: DocumentRecipient | None = None
        if recipient_id := flow.recipient_id:
            recipient = recipients_mapping.get(recipient_id)

        yield flow, recipient


def prepare_zakupki_external_meta(item: ReceiverSchema) -> dict[str, Any]:
    """
    Prepare external_meta for zakupki integration request
    `is_document_owner` - specifies who is initiator of signing process
    `is_source_client` - specifies if client is a zakupki.prom.ua client
    """
    return {
        'is_document_owner': item.is_document_owner,
        'is_source_client': item.is_source_client,
    }


_custom_flows_edrpou = {
    ZAKUPKI_EDRPOU: prepare_zakupki_external_meta,
}


async def prepare_flow_receivers_data(
    conn: DBConnection, receivers_data: list[DataDict]
) -> list[CreateFlowCtx]:
    """Convert receivers data and company_id to FlowReceiver"""
    if not receivers_data:
        return []

    edrpous: list[str] = [item['edrpou'] for item in receivers_data]
    companies = await select_companies_by_edrpou(conn, edrpous)
    edrpous_mapping = {company.edrpou: company for company in companies}

    receivers: list[CreateFlowCtx] = []
    for item in receivers_data:
        company = edrpous_mapping.get(item['edrpou'])
        company_id = company.id if company else None

        receiver = CreateFlowCtx.from_params(
            edrpou=item['edrpou'],
            emails=item['emails'],
            order=item.get('order'),
            sign_num=item.get('sign_num', 0),
            company_id=company_id,
        )
        receivers.append(receiver)

    return unique_list_with_original_order(receivers)


async def select_roles_by_flows(
    conn: DBConnection,
    flows: list[FlowItem],
    recipients: list[DocumentRecipient],
) -> RolesGroup:
    """Select roles by flows with emails"""

    roles_data = []
    for flow, recipient in _iterate_flow_recipients(flows, recipients=recipients):
        # Do not select roles for flows without emails
        if not recipient or not recipient.emails:
            continue

        for email in recipient.emails:
            roles_data.append({'email': email, 'edrpou': flow.edrpou})

    roles = await select_role_by_emails_and_edrpous(conn=conn, data=roles_data)
    return RolesGroup(roles=roles)


async def _get_flows_listing_data(
    conn: DBConnection,
    flows: list[FlowItem],
) -> list[DataDict]:
    """
    Generate access to document data based on given flows. For user found
    in database by edrpou and email this function grants direct access to role.
    In other case grants access just for company.

    NOTE: be aware that flows can be from different documents.
    """
    from app.documents import utils as documents

    recipients_ids: list[str] = [f.recipient_id for f in flows if f.recipient_id]
    recipients = await documents.get_document_recipients(
        conn=conn,
        recipients_ids=recipients_ids,
    )

    # Get roles for flows
    roles = await select_roles_by_flows(conn, flows=flows, recipients=recipients)

    listings = ListingDataAggregator()
    for flow, recipient in _iterate_flow_recipients(flows, recipients=recipients):
        # If not emails at all, provide access to company
        if not recipient or not recipient.emails:
            listings.add(
                document_id=flow.document_id,
                role_id=None,
                access_edrpou=flow.edrpou,
                source=AccessSource.default,
            )
            continue

        # In other case provide direct access to role if it exists
        for email in recipient.emails:
            role = roles.get(email=email, edrpou=flow.edrpou)
            listings.add(
                document_id=flow.document_id,
                role_id=role.id_ if role else None,
                access_edrpou=flow.edrpou,
                source=AccessSource.default,
            )

    return listings.as_db()


def prepare_recipients_and_flows(
    items: list[CreateFlowCtx],
    documents: list[Document],
    assigner_role_id: str | None,
    signers_counter: DocumentSignerCounter,
) -> tuple[list[FlowItem], list[DocumentRecipient]]:
    """
    Prepare recipient data for insertion in document_recipients table based
    on given flows inputs and prepare flows' item and combine them with existing
    recipients.
    """
    recipients: list[DocumentRecipient] = []
    flows: list[FlowItem] = []

    for document in documents:
        for item in items:
            # Set the expected signatures from this company flow based on document signers,
            # if they are set on the document.
            signers_count = signers_counter.get(
                document_id=document.id,
                company_edrpou=item.flow_edrpou,
            )
            if signers_count:
                item = dataclasses.replace(item, flow_signatures_count=signers_count)

            # In some cases, like when we are recreating flow, recipients from previously removed
            # flows, it's possible that "flow_recipient_id" and "flow_id" are set.
            recipient_id: str = item.recipient_id or str(uuid4())
            flow_id: str = item.flow_id or str(uuid4())

            recipient = DocumentRecipient(
                id=recipient_id,
                document_id=document.id,
                edrpou=item.flow_edrpou,
                emails=item.recipient_emails,
                is_emails_hidden=item.recipient_is_emails_hidden or False,
                from_flow=True,
                external_meta=item.recipient_external_meta,
                date_sent=item.recipient_date_sent,
                date_received=item.recipient_date_received,
                date_delivered=item.recipient_date_delivered,
                date_created=item.recipient_date_created or utc_now(),
                assigner_role_id=assigner_role_id,
            )
            flow = FlowItem(
                id=flow_id,
                document_id=document.id,
                company_id=item.flow_company_id,
                edrpou=item.flow_edrpou,
                order=item.flow_order,
                signatures_count=item.flow_signatures_count,
                pending_signatures_count=(
                    # when creating new flow from scratch
                    item.flow_signatures_count
                    if item.flow_pending_signatures_count is None
                    # when recreating deleted flow
                    else item.flow_pending_signatures_count
                ),
                meta=item.flow_meta or FlowMeta(),
                date_sent=item.flow_date_sent,
                receivers_id=recipient_id,
            )

            recipients.append(recipient)
            flows.append(flow)

    return flows, recipients


async def create_flows(
    conn: DBConnection,
    options: AddFlowOptions,
) -> dict[str, CreateDocumentFlowsOutput]:
    """
    This function handles the full process of creating a flow. Its main responsibilities include
    inserting flows and recipients into a database and generating access to documents.

    Be aware that it can be used in contexts where we're recreating previously removed flows. See
    DocumentUpdate._update_recipients_to_multilateral for such an example.
    """

    documents = options.documents
    items = options.receivers

    output = {
        document.id: CreateDocumentFlowsOutput(
            document_id=document.id,
            sent_flows=[],
            document_status_prev=document.status,
            document_status_new=None,
        )
        for document in documents
    }

    if not items or not documents:
        logger.warning(
            msg='No recipients or documents for creating flow',
            extra={
                'documents': options.documents_ids,
                'items': [receiver.to_dict() for receiver in items],
            },
        )
        return output

    signers_counter = DocumentSignerCounter()
    if options.should_count_signers:
        signers_counter = await select_document_signers_counter(
            conn=conn,
            documents_ids=list(options.documents_ids),
        )

    flows, recipients = prepare_recipients_and_flows(
        items=items,
        documents=documents,
        assigner_role_id=options.assigner_role_id,
        signers_counter=signers_counter,
    )
    if not flows:
        logger.warning('No flows created')
        return output

    recipients_data = [recipient.as_db() for recipient in recipients]
    flows_data = [flow.to_db() for flow in flows]

    async with conn.begin():
        await insert_recipients(conn, recipients=recipients_data)
        await db.insert_flows(conn, flows_data)

        if options.should_send:
            states = await get_flows_states(conn, documents_ids=list(options.documents_ids))
            documents_mapping = {document.id: document for document in documents}

            # Most of the time we process only one document, so it's OK to process documents
            # in loop to avoid the extra complexity of processing multiple documents at once.
            for document_id, state in states.items():
                document = documents_mapping[document_id]
                send_output = await send_multilateral_document_to_recipients(
                    conn=conn,
                    document=document,
                    state=state,
                    should_update_document_status=options.should_update_document_status,
                )
                document_output = output[document_id]
                document_output.sent_flows = send_output.sent_flows
                document_output.document_status_new = send_output.document_status_new

    return output


async def update_flow_signatures_count(
    conn: DBConnection,
    *,
    flow: FlowItem,
    signatures_count: int,
    pending_signatures_count: int,
) -> None:
    await db.update_flow(
        conn=conn,
        flow_id=flow.id,
        update_data={
            'signatures_count': signatures_count,
            'pending_signatures_count': pending_signatures_count,
        },
    )


async def set_flow_unfinished_status(
    conn: DBConnection, *, flow: FlowItem, unfinished: bool
) -> None:
    """
    Mark and unmark flow as unfinished.

    Used to mark flow as unfinished when the last signer for the flow
     was removed before he sign the document.
    Used to unmark flow as unfinished when the send document function was used.
    """
    meta = flow.meta
    if unfinished:
        meta.unfinished = True
    else:
        # remove field from meta completely
        mata_dict = meta.to_db()
        mata_dict.pop('unfinished', None)
        meta = FlowMeta.from_db(mata_dict)

    await db.update_flow(
        conn=conn,
        flow_id=flow.id,
        update_data={'meta': meta},
        # We modified old meta, so can safely override it
        override_meta=True,
    )


async def update_flow_on_sign(
    conn: DBConnection,
    document: Document,
    flow: FlowItem | None,
    user: AuthUser | User | None,
) -> UpdateFlowOnSignOutput | None:
    """
    Add signature to flow and send flow to recipients.

    NOTE (1): This function should be called only in transaction, so avoid scheduling
    async jobs inside of it. But also don't forget to call all async jobs outside of
    transaction.

    NOTE (2): Keep in sync sending logic with "send_multilateral_document" function to have
    the same behavior.
    """

    if not document.is_multilateral:
        return None

    # probably it's extra sign
    if not flow:
        logging.info(
            msg='Multilateral document does not have a flow for sign',
            extra={'flow': flow, 'document': document},
        )
        return None

    update: dict[str, Any] = {
        'pending_signatures_count': flow.pending_signatures_count - 1,
    }

    # Add current user to list of signers in flow meta
    if user and user.role_id:
        meta = flow.meta
        meta.role_ids = [*meta.role_ids, user.role_id]
        update['meta'] = meta

    flow = await db.update_flow(conn, flow_id=flow.id_, update_data=update)

    output = UpdateFlowOnSignOutput(
        updated_flow=flow,
        sent_flows=[],
        document_status_new=None,
    )

    # Send a document to next recipients after last signature on current flow
    if flow.pending_signatures_count == 0:
        state = await get_flows_state(conn, document_id=document.id)
        send_output = await send_multilateral_document_to_recipients(
            conn=conn,
            document=document,
            state=state,
            should_update_document_status=True,
        )
        output.sent_flows = send_output.sent_flows
        output.document_status_new = send_output.document_status_new

    # WARN: remember to call "send_multilateral_documents_to_recipients_job" and
    # "schedule_jobs_about_finished_document" outside of transaction

    return output


async def add_flow(
    app: web.Application,
    user: User,
    data: DataDict,
    source: Source,
) -> None:
    """Create flows for multilateral documents"""

    # Prevent race condition on adding flows
    async with redis_lock(f'lock_add_flow_company_{user.company_id}'):
        async with app['db'].acquire() as conn:
            options = await validate_flow_request(conn, user, data)
            await create_flows(conn, options)

        if options.should_send:
            await send_multilateral_documents_to_recipients_job(
                documents_ids=list(options.documents_ids),
                source=source,
            )

    await send_to_indexator(
        redis=app['redis'], document_ids=list(options.documents_ids), to_slow_queue=True
    )


def get_receiver_external_meta(
    item: ReceiverSchema, company_edrpou: str | None
) -> dict[str, Any] | None:
    if company_edrpou and _custom_flows_edrpou.get(company_edrpou):
        return _custom_flows_edrpou[company_edrpou](item)
    return None


def to_flows_upload_settings(
    items: list[ReceiverSchema] | None,
    company_edrpou: str | None,
    should_send: bool,
) -> FlowsUploadSettings | None:
    """
    Prepare flows upload settings on document upload.
    """
    if not items:
        return None

    flows = []
    for item in items:
        external_meta = get_receiver_external_meta(item, company_edrpou)
        flow = CreateFlowCtx.from_params(
            edrpou=item.edrpou,
            emails=item.emails,
            order=item.order,
            sign_num=item.sign_num,
            external_meta=external_meta,
        )
        flows.append(flow)

    return FlowsUploadSettings(
        flows=flows,
        should_send=should_send,
    )


async def send_multilateral_documents_to_recipients_job(
    *,
    documents_ids: list[str],
    source: Source,
    company_edrpou: str | None = None,
    check_reviews: bool = True,
) -> None:
    """
    Send async job to send multilateral documents to recipients:
     - update document date_delivered for recipients
     - create tags for recipients by contacts
     - send emails to recipients
     - start document automation for recipients

    For more information see:
     - worker.emailing.jobs.send_multilateral_document_to_recipients

    WARNING: This job is not idempotent, so it should be called only once
    when document should be sent to recipients, in other cases it can cause
    duplicates in the system.
    """

    # Most of the time len(documents_ids) == 1, so we can create a separate task
    # for each document and reduce logical complexity of the job
    await services.kafka.send_records(
        topic=topics.SEND_MULTILATERAL_DOCUMENT_TO_RECIPIENTS,
        values=[
            {
                'document_id': document_id,
                'source': source,
            }
            for document_id in documents_ids
        ],
    )


async def send_multilateral_document_notification_job(
    document_id: str, company_edrpou: str | None = None
) -> None:
    """
    Schedule async job to send notification about multilateral document.

    NOTE: If you want to send multilateral document to recipient in general, not just
    notification, consider using "send_multilateral_documents_to_recipients_jobs"
    which includes more logic, like sending notifications, creating tags, etc.
    """
    await services.kafka.send_record(
        topic=topics.SEND_MULTILATERAL_DOCUMENT_NOTIFICATION,
        value={
            'document_id': document_id,
            'company_edrpou': company_edrpou,
        },
    )


async def send_multilateral_document(
    conn: DBConnection,
    user: User | None,
    document: DocumentWithUploader,
    state: FlowsState,
    company_edrpou: str,
    request_source: Source,
) -> None:
    """
    Most of the time, sending a multilateral document occurs during the addition of signatures
    and handled by logic "add_signature" logic from app.signatures.utils module. However, there
    are instances where a user can send a multilateral document without adding a signature.

    One example is when a document has an internal signing process (document_signers), some users
    have signed the document and then, someone through the "edit document" logic, removed other
    signers who have not yet signed. In such cases, the document becomes ready to be sent to the
    recipient, and this function should be called to do it.

    Another example is when a user uploads a document without automatic sending to recipients
    and then sends it manually. Or when a document was changed from bilateral to multilateral,
    with a disabled option to send it automatically. In those cases, we provide a way to send
    the document without adding a signature.

    NOTE: keep in sync with sending logic in "update_flow_on_sign" function. If you see some logic
    that can be grouped into a separate reusable function and used in both places, consider
    to do it.
    """

    _validate_edrpou_send_document(company_edrpou, user)

    # This condition handles the case of parallel signing with three parties.
    # For example, Company 1 and 3 has signed the document, and Company 2 added two signers,
    # signed it with the first signer, and removed the second signer from the signing process.
    # Without this additional check, when the "Send" button is pressed in the interface,
    # the request would return a 200 status but the document would not be sent.
    # This is because it would fall into the `if not state.flows_for_send` condition.
    # By extending the condition to include `not current_flow_is_unfinished`,
    # we ensure that the document is sent and its status is updated to "Finished".
    current_flow = state.get_current_company_flow(company_edrpou=company_edrpou)
    current_flow_is_unfinished = current_flow and current_flow.meta and current_flow.meta.unfinished

    if not state.flows_for_send and not current_flow_is_unfinished:
        logger.info(
            msg='Could not send multilateral document',
            extra={
                'document': document.to_log_extra(),
                'company_edrpou': company_edrpou,
            },
        )
        return

    logging.info(
        msg='Send multilateral document',
        extra={
            'document': document.to_log_extra(),
            'flow_id': current_flow and current_flow.id,
        },
    )

    async with conn.begin():
        if current_flow and current_flow.meta.unfinished:
            await set_flow_unfinished_status(conn=conn, flow=current_flow, unfinished=False)

        state = await get_flows_state(conn, document_id=document.id)
        send_output = await send_multilateral_document_to_recipients(
            conn=conn,
            document=document,
            state=state,
            should_update_document_status=True,
        )

    await send_multilateral_documents_to_recipients_job(
        documents_ids=[document.id],
        source=request_source,
    )
    await send_document_status_callback_job(
        document_id=document.id,
        uploaded_by_edrpou=document.uploaded_by_edrpou,
    )
    await send_to_indexator(document_ids=[document.id], to_slow_queue=False)

    if send_output.document_status_new == DocumentStatus.finished:
        await schedule_jobs_about_finished_document(document=document)


async def mark_flows_as_sent(
    conn: DBConnection,
    flows_ids: list[str],
) -> None:
    """
    Mark flows as sent to recipients.

    This function should be called after sending multilateral document to recipients.
    """
    await db.update_flows(
        conn=conn,
        flows_ids=flows_ids,
        update_data={'date_sent': sa.text('now()')},
    )


async def mark_send_flows_jobs_as_executed(
    conn: DBConnection,
    flows_ids: list[str],
) -> None:
    """
    Mark flows as executed for sending to recipients.

    This function should be called after executing execution
    of "send_multilateral_documents_to_recipients_job" job.
    """

    await db.update_flows(
        conn=conn,
        flows_ids=flows_ids,
        update_data={
            'meta': FlowMeta(send_jobs_executed=True),
        },
        override_meta=False,
    )


async def mark_send_flows_notifications_as_executed(
    conn: DBConnection,
    flows_ids: list[str],
) -> None:
    """
    Mark flows as executed for sending notification to recipients.

    This function should be called after executing execution
    of "send_multilateral_document_notification_job" job.
    """

    await db.update_flows(
        conn=conn,
        flows_ids=flows_ids,
        update_data={
            'meta': FlowMeta(send_notifications_executed=True),
        },
        override_meta=False,
    )


async def send_multilateral_document_to_recipients(
    conn: DBConnection,
    document: Document,
    state: FlowsState,
    should_update_document_status: bool,
) -> SendMultilateralDocumentOutput:
    """
    Send a multilateral document to recipients by opening access to the document and also
    move the document to the next status if needed.

    Related functions:
     - "send_bilateral_document_to_owner"
     - "send_bilateral_document_to_recipient"
     - "send_multilateral_document_to_recipients"
    """
    flows = state.flows_for_send
    flows_ids = [flow.id for flow in flows]

    logger.info(
        msg='Send multilateral document to recipients',
        extra={'document': document, 'flows': flows},
    )

    output = SendMultilateralDocumentOutput(
        sent_flows=flows,
        document_status_new=None,
    )

    async with conn.begin():
        # Update document status
        if should_update_document_status:
            next_status: DocumentStatus | None = None
            if state.is_finished and document.status != DocumentStatus.finished:
                next_status = DocumentStatus.finished
            elif document.status != DocumentStatus.flow:
                next_status = DocumentStatus.flow

            if next_status:
                output.document_status_new = next_status
                await update_document(
                    conn=conn,
                    data={
                        'document_id': document.id,
                        'status_id': next_status.value,
                    },
                )

        # Generate access to document data based on given flows
        listing_data = await _get_flows_listing_data(conn, flows=flows)

        await insert_listings(conn, data=listing_data)

        await mark_flows_as_sent(conn, flows_ids=flows_ids)

        companies_edrpous = {flow.edrpou for flow in flows}
        await update_recipients_date_sent(
            conn=conn,
            document_id=document.id,
            companies_edrpous=list(companies_edrpous),
        )

    # Remember to call second async part of this function outside of transaction:
    #  - "send_multilateral_documents_to_recipients_job"
    #  - "schedule_jobs_about_finished_document" (if new status is "finished")
    return output
