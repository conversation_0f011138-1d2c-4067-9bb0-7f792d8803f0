import { BillingCompanyConfig, UserOnboarding } from 'gql-types';
import I from 'immutable';
import { Bill<PERSON>A<PERSON>untsList, BillsList, RatesList } from 'records/types';
import { DocumentField } from 'services/documentFields/types';
import { AccountRate } from 'services/enums';
import { ITag } from 'ui/tags/tagsTypes';

import { CompanyRate, RateExtensionsProps } from './billing';
import { Nullable } from './general';

export interface ArchiveSettings {
    allow_uploaded_documents: boolean;
    allow_partially_signed_documents: boolean;
    allow_fully_signed_documents: boolean;
    allow_rejected_documents: boolean;
}

export interface VersionSettings {
    review_flow: 'restarted' | 'continued';
}

export interface AntivirusSettings {
    is_download_infected_enabled: boolean;
    is_download_pending_enabled: boolean;
}

interface UploadCompanyConfig {
    allow_substitute_email_recipient: boolean;
    allow_download_documents_from_hosts: any[];
    allowed_extensions: string[];
    archive_extensions: string[];
    check_owner_edrpou: boolean;
    // in Megabytes
    max_file_size: number;
    // in Megabytes
    max_total_size: number;
    max_total_count: number;
    replace_owner_edrpou: boolean;
}

export type RegistrationMethods = 'main' | 'google';

export interface DefaultRolePermissionsConfig extends RolePermissionsPayload {
    user_role: number;
}

export interface RolePermissionsPayload {
    can_view_document: boolean;
    can_view_private_document: boolean;
    can_comment_document: boolean;
    can_upload_document: boolean;
    can_download_document: boolean;
    can_print_document: boolean;
    can_delete_document: boolean;
    can_download_actions: boolean;
    can_edit_company_contact: boolean;
    can_edit_security: boolean;
    can_change_document_signers_and_reviewers: boolean;
    can_delete_document_extended: boolean;
    can_edit_required_fields: boolean;
    can_sign_and_reject_document: boolean;
    can_invite_coworkers: boolean;
    can_edit_company: boolean;
    can_edit_roles: boolean;
    can_create_tags: boolean;
    can_edit_document_automation: boolean;
    can_edit_document_fields: boolean;
    can_archive_documents: boolean;
    can_edit_templates: boolean;
    can_edit_directories: boolean;
    can_remove_itself_from_approval: boolean;
    can_delete_archived_documents: boolean;
    can_receive_inbox: boolean;
    can_receive_inbox_as_default: boolean;
    can_receive_comments: boolean;
    can_receive_rejects: boolean;
    can_receive_reminders: boolean;
    can_receive_reviews: boolean;
    can_receive_review_process_finished: boolean;
    can_receive_review_process_finished_assigner: boolean;
    can_receive_sign_process_finished: boolean;
    can_receive_sign_process_finished_assigner: boolean;
    can_receive_access_to_doc: boolean;
    can_receive_delete_requests: boolean;
    can_edit_document_category: boolean;
    can_view_coworkers: boolean;
    can_sign_and_reject_document_external: boolean;
    can_sign_and_reject_document_internal: boolean;
}

/**
 * Налаштування компанії, але конвертовані у camelCase формат для зручності.
 *
 * В графі це поле "Company.config", яке конвертується в "I.Record" у функції "apiCompanyConfig".
 * Якщо потрібно скористатися налаштуваннями компанії, у форматі як на бекенді, то треба використовувати "CompanyConfigSettings".
 */
export interface ICompanyConfig {
    master: boolean;
    adminIsSuperAdmin: boolean;
    renderSignatureAtPage: string;
    renderSignatureInInterface: boolean;
    renderSignatureOnPrintDocument: boolean;
    renderReviewInInterface: boolean;
    renderReviewOnPrintDocument: boolean;
    renderReviewConfig: {
        renderFullName: boolean;
        renderEmail: boolean;
        renderPosition: boolean;
    };
    syncContactsEnabled: boolean;
    allowUnregisteredDocumentView: boolean;
}

/**
 * Налаштування компанії, в такому форматі як вони зберігаються в базі даних і якими оперує бекенд.
 *
 * В графі це "Company.config поле". Воно має тип "Record", тому типів немає у схемі GraphQL, їх треба додати
 * вручну в цей інтерфейс.
 */
export interface CompanyConfigSettings {
    enable_2fa_for_internal_users?: boolean;
    render_review_in_interface?: boolean;
    render_review_on_print_document?: boolean;
    review_render_config?: {
        render_full_name: boolean;
        render_email: boolean;
        render_position: boolean;
    };
    render_signature_at_page?: string;
    allow_unregistered_document_view: boolean;
    uploads?: UploadCompanyConfig;
    antivirus_settings?: AntivirusSettings;
    msViewerEnabled?: boolean;
    allow_suggesting_document_meta_with_ai?: boolean;
    archive_settings: ArchiveSettings;
    version_settings: VersionSettings;
    // Права для нових ролей, які самостійно зареєструвалися по ключу підпису. Якщо потрібні права для ролей,
    // яких запрошують в компанію, то треба додати і використовувати поле "default_role_permissions"
    default_role_permissions_key?: DefaultRolePermissionsConfig;
}

/**
 * Налаштування компанії, які може змінити супер адмін.
 */
export type CompanyAdminConfig = Pick<
    CompanyConfigSettings,
    'enable_2fa_for_internal_users'
>;

export const roleActivationSource = {
    // The role was activated by signing a registration token
    signature: 'signature',
    // Invited by coworker
    invite: 'invite',
    // Restored by coworker
    restore: 'restore',
    // The role was created by synchronization with crm-proxy (other products)
    sync_role: 'sync_role',
    // Activated by super admin
    super_admin: 'super_admin',
} as const;

export type RoleActivationSource = typeof roleActivationSource[keyof typeof roleActivationSource];

export interface AdminRole {
    isAdmin: boolean;
}

export interface MasterAdminRole {
    isMasterAdmin: boolean;
}

export interface IRole
    extends DocumentRolePermissions,
        FunctionalityRolePermissions,
        AdministrationRolePermissions,
        AdminRole,
        MasterAdminRole,
        MasterAdminPermissions,
        CommonDocumentEmailSettings,
        DocumentCompletedEmailSettings,
        AdministrativeEmailSettings {
    id: string;
    status: string;

    dateCreated: string;

    // eslint-disable-next-line no-use-before-define
    user: IUser;
    userRole: number;

    registrationReferralUrl?: string;

    hasFewSignatures: boolean;

    isDefaultRecipient: boolean;

    allowedIps?: Set<string>;
    allowedApiIps?: Set<string>;
    hasToken: boolean;
    tags?: Array<ITag>;
    fields?: Array<DocumentField>;

    // eslint-disable-next-line no-use-before-define
    company: ICompany;
    position: string;

    dateDeleted?: Nullable<string>;
    deletedBy?: Nullable<string>;

    dateInvited: Nullable<string>;
    invitedBy: Nullable<string>;

    activatedBy: Nullable<string>;
    activationSource: Nullable<RoleActivationSource>;
    dateActivated: Nullable<string>;
}

export interface OrderedIRole extends IRole {
    order?: number;
}

export interface ICompany {
    id: string;
    edrpou: string;
    ipn: string;
    name: string;
    isLegal: boolean;

    phone: string;

    emailDomains: Nullable<string[]>;
    allowedIps: Nullable<string[]>;
    allowedApiIps: Nullable<string[]>;
    inactivityTimeout: Nullable<number>;

    config?: CompanyConfigSettings;
    billingCompanyConfig?: BillingCompanyConfig;

    renderSignatureOnPrintDocument: boolean;
    renderSignatureInInterface: boolean;

    roles: Array<unknown>;
    billingAccounts: BillingAccountsList;
    rates: RatesList;
    bills: BillsList;
    trialRates: Array<CompanyRate & { source: string }>;
    activeRates: I.List<AccountRate>;
    hasInvalidSignedDocuments: boolean;
    rateExtensions: Array<RateExtensionsProps>;
    dateCreated: string;
    usedDocumentCount: number;
}

export interface UserMeta {
    /**
     * Чи використовував користувач мобільний версію ВЕБ сторінки
     */
    mobileUsage: boolean;
    /**
     * Чи використовував користувач мобільний додаток ЕДО
     */
    hasMobileApp: boolean;
    /**
     * Чи має користувач в мобільному додатку ЕДО активну сесію (згенерований токен сесії - але не факт що користується додатком)
     */
    hasActiveMobileApp: boolean;
}

export interface IUser {
    id: string;

    email: string;
    phone: string;
    firstName: string;
    dateCreated: string;
    secondName: Nullable<string>;
    lastName: Nullable<string>;
    label: string;
    source: string;

    emailConfirmed: boolean;
    registrationCompleted: boolean;
    registrationMethod: RegistrationMethods;
    isAutogeneratedPassword: boolean;
    is2FAEnabledInProfile: boolean;
    is2FAEnabledByRule: boolean;
    isAuthPhoneEnabled: boolean;
    isPhoneVerified: boolean;
    isSubscribedEsputnik: boolean;
    showKEPAppPopup: boolean;
    bannerPromoKasaShowCount: Nullable<number>;
    createdBy: string;

    currentCompany: ICompany;
    currentCompanyConfig: ICompanyConfig;
    currentRole: IRole;

    roleId: string;
    roles: IRole[];

    trialAutoEnable: boolean;

    userMeta: UserMeta;

    onboarding: UserOnboarding;

    hasPassword: boolean;
    isRegistered: boolean;
    activeSurveys: string[];
}

export interface GroupMembers {
    id: string;
    role: {
        id: string;
        user: {
            email: string;
            firstName: string;
            secondName: string;
            lastName: string;
        };
    };
}

export interface SignerReviewerGroup {
    id: string;
    name: string;
    members: GroupMembers[];
}

export interface SignerReviewerRole
    extends Pick<DocumentRolePermissions, 'canSignAndRejectDocument'> {
    id: string;
    isAdmin: boolean;
    user: {
        email: string;
        firstName: string;
        secondName: string;
        lastName: string;
    };
}

export type Comment = {
    role: IRole;
    text: string;
};

export type RoleEmailSettings =
    | keyof CommonDocumentEmailSettings
    | keyof DocumentCompletedEmailSettings
    | keyof AdministrativeEmailSettings;

export interface CommonDocumentEmailSettings {
    /**
     * Отримання документів
     */
    canReceiveInbox: boolean;
    /**
     * Отримання коментарів
     */
    canReceiveComments: boolean;
    /**
     * Відхилення документів
     */
    canReceiveRejects: boolean;
    /**
     * Емейл про погодження документів
     */
    canReceiveReviews: boolean;
    /**
     * Нагадування про неопрацьовані запити в сервісі
     */
    canReceiveReminders: boolean;
    /**
     * Отримання доступу до документу
     */
    canReceiveAccessToDoc: boolean;
    /**
     * Запит на видалення документів
     */
    canReceiveDeleteRequests: boolean;
}

export interface DocumentCompletedEmailSettings {
    /**
     * Завершення процесу погодження в завантажених мною документах
     */
    canReceiveReviewProcessFinished: boolean;
    /**
     * Завершення процесу погодження, що ініційоване мною
     */
    canReceiveReviewProcessFinishedAssigner: boolean;
    /**
     * Завершення процесу підписання в завантажених мною документах
     */
    canReceiveSignProcessFinished: boolean;
    /**
     * Завершення процесу підписання, що ініційоване мною
     */
    canReceiveSignProcessFinishedAssigner: boolean;
    /**
     * Про завершені документи
     */
    canReceiveFinishedDocs: boolean;
}

export interface AdministrativeEmailSettings {
    /**
     * Отримувати всі сповіщення про події в цій компанії
     * @deprecated
     */
    canReceiveNotifications: boolean;
    /**
     * Додавання нового користувача в компанію
     */
    canReceiveNewRoles: boolean;
    /**
     * Завершення інтеграційного токену по співробітникам
     */
    canReceiveTokenExpiration: boolean;
    /**
     * Заміна email співробітником
     */
    canReceiveEmailChange: boolean;
    /**
     * Видалення адміністратора з компанії
     */
    canReceiveAdminRoleDeletion: boolean;
    /**
     * Документи для незареєстрованих користувачів
     */
    canReceiveInboxAsDefault: boolean;
}

export type CommonRolePermissions =
    | keyof DocumentRolePermissions
    | keyof FunctionalityRolePermissions
    | keyof AdministrationRolePermissions;

export interface DocumentRolePermissions {
    /**
     * Перегляд всіх документів із спільним доступом
     */
    canViewDocument: boolean;
    /**
     * Перегляд всіх документів із приватним доступом
     */
    canViewPrivateDocument: boolean;
    /**
     * Коментування документів
     */
    canCommentDocument: boolean;
    /**
     * Завантаження документів у сервіс
     */
    canUploadDocument: boolean;
    /**
     * Вивантаження документів на локальний комп’ютер
     */
    canDownloadDocument: boolean;
    /**
     * Друк документів
     */
    canPrintDocument: boolean;
    /**
     * Видалення документів
     */
    canDeleteDocument: boolean;
    /**
     * Підписання та відхилення документів
     * https://vchasno-group.atlassian.net/browse/DOC-7494
     * Розділяємо це право на 2 частини, щоб можна було окремо підписувати та відхиляти внутрішні документи та зовнішні документи.
     */
    canSignAndRejectDocument: boolean;
    /**
     * Підписання та відхилення ЗОВНІШНІХ документів
     */
    canSignAndRejectDocumentExternal: boolean;
    /**
     * Підписання та відхилення ВНУТРІШНІХ документів
     */
    canSignAndRejectDocumentInternal: boolean;
    /**
     * Видалення себе зі списку погоджувачів
     */
    canRemoveItselfFromApproval: boolean;
}

export interface FunctionalityRolePermissions {
    /**
     * Налаштування сценаріїв документів
     */
    canEditDocumentTemplates: boolean;
    /**
     * Налаштування додаткових параметрів документів
     */
    canEditDocumentFields: boolean;
    /**
     * Створення та редагування шаблонів
     */
    canEditTemplates: boolean;
    /**
     * Переміщення документів у архів
     */
    canArchiveDocuments: boolean;
    /**
     * Видалення архівних документів
     */
    canDeleteArchivedDocuments: boolean;
    /**
     * Створення нових ярликів
     */
    canCreateTags: boolean;
    /**
     * Керування папками
     */
    canEditDirectories: boolean;
    /**
     * Налаштування обовʼязкових полів для вхідних документів
     */
    canEditRequiredFields: boolean;
    /**
     * Налаштування типів внутрішніх документів
     */
    canEditDocumentCategory: boolean;
    /**
     * для роботи із кадровими ролями реалізували налаштування, що дозволяє приховати список співробітників
     * для користувача (розділ “Співробітники” стає недоступний, проте доропдаун із користувачами в погодженнях/підписаннях/доступах залишаємо)
     */
    canViewCoworkers: boolean;
}

export interface AdministrationRolePermissions {
    /**
     * Запрошення співробітників
     */
    canInviteCoworkers: boolean;
    /**
     * Редагування та видалення співробітників
     */
    canEditRoles: boolean;
    /**
     * Редагування інформації про компанію
     */
    canEditCompany: boolean;
    /**
     * Збереження історії документів/дій користувачів
     */
    canDownloadActions: boolean;
    /**
     * Керування контактами контрагентів
     */
    canEditCompanyContact: boolean;
    /**
     * Зміна налаштувань безпеки
     */
    canEditSecurity: boolean;
    /**
     * Зміна розпочатого процесу підписання/погодження
     */
    canChangeDocumentSignersAndReviewers: boolean;
    /**
     * Видалення будь-якого документу компанії
     */
    canDeleteDocumentExtended: boolean;
}

/**
 * Права для майстер-адміна (isMasterAdmin)
 */
export interface MasterAdminPermissions {
    /**
     * Пошук інформації про клієнтів
     */
    canViewClientData: boolean;
    /**
     * Редагування налаштувань клієнтів
     */
    canEditClientData: boolean;
    /**
     * Доступ до спеціальних можливостей
     */
    canEditSpecialFeatures: boolean;
}

export type HasPermission = (permission: CommonRolePermissions) => boolean;

export interface RoleOrGroup extends SignerReviewerRole, SignerReviewerGroup {}
