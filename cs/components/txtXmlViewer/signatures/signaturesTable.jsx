import React from 'react';

import { formatDate } from 'lib/date';
import { TxtOrXmlDocumentPropType } from 'records/document';
import { t } from 'ttag';

import { DATETIME_FORMAT } from './constants';
import SignatureDetails from './signatureDetails';

import css from './signaturesTable.css';

const SignaturesTable = ({ doc }) => {
    const { signatures: data } = doc;
    if (!data.count()) {
        return null;
    }

    const dateSentRow = doc.dateSent && (
        <tr>
            <td colSpan={2}>
                {t`Документ відправлено`}:{' '}
                {formatDate(doc.dateSent, DATETIME_FORMAT)}
            </td>
        </tr>
    );
    const dateDeliveredRow = doc.dateDelivered && (
        <tr>
            <td colSpan={2}>
                {t`Документ переглянуто`}:{' '}
                {formatDate(doc.dateDelivered, DATETIME_FORMAT)}
            </td>
        </tr>
    );
    const dateRejectedRow = doc.dateRejected && (
        <tr>
            <td colSpan={2}>
                {t`Документ відхилено`}:{' '}
                {formatDate(doc.dateRejected, DATETIME_FORMAT)}
            </td>
        </tr>
    );

    return (
        <div className={css.root}>
            <table className={css.detailsTable}>
                <tbody>
                    <tr>
                        <td>
                            <SignatureDetails
                                label={t`Власник`}
                                signatures={data.filter(({ isOwner }) =>
                                    doc.is3p ? !isOwner : isOwner,
                                )}
                            />
                        </td>
                        <td>
                            <SignatureDetails
                                label={t`Контрагент`}
                                signatures={data.filter(({ isOwner }) =>
                                    doc.is3p ? isOwner : !isOwner,
                                )}
                            />
                        </td>
                    </tr>
                </tbody>
            </table>

            <div>
                <b>{t`Документ підписано у сервісі ${config.BRAND_NAME}`}:</b>{' '}
                {doc.title}
            </div>

            <table className={css.detailsTable}>
                <tbody>
                    {dateSentRow}
                    {dateDeliveredRow}
                    {dateRejectedRow}
                    {doc.dateSent || doc.dateDelivered || doc.dateRejected ? (
                        <tr>
                            <td colSpan={2}>&nbsp;</td>
                        </tr>
                    ) : null}
                </tbody>
            </table>

            <div>
                <b>{t`Документ підписано у сервісі ${config.BRAND_NAME}`}:</b>{' '}
                {doc.title}
            </div>
        </div>
    );
};

SignaturesTable.propTypes = {
    doc: TxtOrXmlDocumentPropType.isRequired,
};

export default SignaturesTable;
