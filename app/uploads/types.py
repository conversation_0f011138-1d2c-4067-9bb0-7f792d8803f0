from __future__ import annotations

import datetime
from dataclasses import dataclass, field
from typing import NamedTuple, TypedDict

from app.auth.types import User
from app.comments.types import Comment
from app.contacts.types import Contact
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_automation.types import DocumentAutomationAutomationTemplate
from app.document_versions.types import DocumentVersion
from app.documents.enums import FirstSignBy
from app.documents.types import Document, UpdateRecipientsCtx, UploadDocumentAccessSettingsCtx
from app.documents_fields.types import AddDocumentFieldSchema
from app.flow.types import CreateFlowCtx
from app.lib.enums import SignatureFormat
from app.lib.types import DataDict
from app.reviews.enums import ReviewRequestStatus
from app.reviews.types import ReviewsInfoCtx
from app.signatures.types import AddSignatureResultCtx, SignersInfoCtx


@dataclass
class ExpectedSignaturesCtx:
    owner: int | None
    recipient: int | None


class RawFile(NamedTuple):
    body: bytes
    content_type: str
    filename: str


class UploadedSignature(NamedTuple):
    file: RawFile
    format_: SignatureFormat

    # extracted info about signature
    info: DataDict | None = None


@dataclass(frozen=True)
class ShareToGroup:
    id: str
    role_ids: list[str]


@dataclass
class FlowsUploadSettings:
    flows: list[CreateFlowCtx]
    should_send: bool

    @property
    def is_ordered(self) -> bool:
        return any(flow.order is not None for flow in self.flows)


class File(NamedTuple):
    id: str
    uploaded_by: str | None

    title: str
    extension: str
    status_id: int
    archive_name: str | None

    edrpou_owner: str
    ipn_owner: str | None
    edrpou_recipient: str | None
    ipn_recipient: str | None
    recipient_emails: list[str]
    hide_recipient_emails: bool
    company_name_recipient: str | None
    company_short_name_recipient: str | None

    date_document: datetime.datetime | None
    type_: str | None  # Will be renamed to `type` on insert to database
    category: int | None
    number: str | None
    amount: int | None

    source: str
    vendor: str | None
    vendor_id: str | None

    first_sign_by: str
    is_internal: bool
    is_multilateral: bool
    expected_owner_signatures: int
    expected_recipient_signatures: int

    has_changed_for_public_api: bool

    signature_format: SignatureFormat

    # Will be ignored on insert to database
    body: bytes
    content_type: str
    file_name: str
    file_size: int

    # base64 hash calculated with eusign lib
    content_hash: str

    flows_settings: FlowsUploadSettings | None

    signature: UploadedSignature | None = None
    stamp: UploadedSignature | None = None

    version_id: str | None = None

    def to_document_data(self) -> DataDict:
        """
        Return data that will be inserted into the "documents" table.
        """
        return {
            'id': self.id,
            'uploaded_by': self.uploaded_by,
            'title': self.title,
            'extension': self.extension,
            'status_id': self.status_id,
            'archive_name': self.archive_name,
            'edrpou_owner': self.edrpou_owner,
            'edrpou_recipient': self.edrpou_recipient,
            'date_document': self.date_document,
            'type': self.type_,
            'category': self.category,
            'number': self.number,
            'amount': self.amount,
            'source': self.source,
            'vendor': self.vendor,
            'vendor_id': self.vendor_id,
            'first_sign_by': self.first_sign_by,
            'is_internal': self.is_internal,
            'is_multilateral': self.is_multilateral,
            'expected_owner_signatures': self.expected_owner_signatures,
            'expected_recipient_signatures': self.expected_recipient_signatures,
            'has_changed_for_public_api': self.has_changed_for_public_api,
            'signature_format': self.signature_format,
        }

    @property
    def s3_key(self) -> str:
        from app.documents.utils import get_document_s3_key

        return get_document_s3_key(
            document_id=self.id,
            version_id=self.version_id,
        )

    def to_api(self) -> DataDict:
        """Return data for upload response."""
        return {'id': self.id}


@dataclass
class FileDetails:
    title: str
    extension: str
    name: str

    @property
    def is_hidden(self) -> bool:
        return self.title.startswith('.')


class UploadOptions(NamedTuple):
    # From user details
    payer_id: str | None
    user: User | None
    company_edrpou: str

    # From GET params
    email_owner: str | None
    recipient_edrpou: str | None
    recipient_emails: list[str]
    hide_recipient_emails: bool
    first_sign_by: str | None
    is_internal: bool
    is_multilateral: bool
    is_required_review: bool
    expected_owner_signatures: int | None
    expected_recipient_signatures: int | None
    source: str | None
    vendor: str | None
    vendor_id: str | None
    share_to: set[str]
    share_to_groups: list[ShareToGroup]
    signers: list[SignersInfoCtx]
    parallel_signing: bool
    parent_id: str | None
    tags: list[str]
    new_tags: list[str]
    reviewers: list[ReviewsInfoCtx]
    parallel_review: bool
    title: str | None
    doc_number: str | None
    date_document: datetime.datetime | None
    amount: int | None
    parameters: list[AddDocumentFieldSchema] | None
    flows_settings: FlowsUploadSettings | None
    signature_format: SignatureFormat | None

    # From company config
    allow_download_documents_from_hosts: set[str] | None
    allow_substitute_email_recipient: bool
    allowed_extensions: set[str]
    archive_extensions: set[str]
    check_owner_edrpou: bool
    max_file_size: int  # Bytes
    max_total_size: int  # Bytes
    max_total_count: int
    replace_owner_edrpou: bool

    company_api_enabled: bool
    template: DocumentAutomationAutomationTemplate | None
    category: int | None

    is_versioned: bool

    comment: UploadCommentCtx | None

    access_settings: UploadDocumentAccessSettingsCtx | None

    # Public API selectors, not used for other upload cases
    show_recipients: bool
    with_access_settings: bool


class ReviewRequestData(TypedDict):
    document_id: str
    document_version_id: str | None
    status: ReviewRequestStatus
    from_role_id: str
    to_role_id: str | None
    to_group_id: str | None
    order: int | None


class ReviewerRawDict(TypedDict):
    id: str
    type: ReviewsInfoCtx.EntityType | None  # Field 'type' may be missing, not empty key


@dataclass
class UploadCommentCtx:
    text: str
    is_internal: bool

    @classmethod
    def from_dict(cls, data: DataDict) -> UploadCommentCtx:
        return cls(
            text=data['text'],
            is_internal=data['is_internal'],
        )


@dataclass
class InsertedDocumentsCtx:
    """
    Class for storing inserted documents with all related entities
    """

    docs: list[InsertedDocument] = field(default_factory=list)
    signatures: list[AddSignatureResultCtx] = field(default_factory=list)

    contacts: list[Contact] = field(default_factory=list)

    antivirus_checks: list[DocumentAntivirusCheck] = field(default_factory=list)

    # Internal mapping for fast access to inserted files by document id
    _documents_map: dict[str, InsertedDocument] = field(default_factory=dict)

    @property
    def document_versions_map(self) -> dict[str, str]:
        """
        Dictionary for quick access to document versions by document id
        """
        return {
            item.document_id: item.version_id for item in self.docs if item.version_id is not None
        }

    @property
    def documents(self) -> list[Document]:
        return [item.document for item in self.docs]

    @property
    def comments_ids(self) -> list[str]:
        return [item.comment.id for item in self.docs if item.comment is not None]

    @property
    def contacts_ids(self) -> list[str]:
        return [item.id for item in self.contacts]

    def add_documents(self, documents: list[Document]) -> None:
        for document in documents:
            file = InsertedDocument(document=document)
            self._documents_map[file.document_id] = file
            self.docs.append(file)

    def add_comments(self, comments: list[Comment]) -> None:
        """
        Add comments to inserted files.
        """
        for comment in comments:
            file = self._documents_map[comment.document_id]
            file.comment = comment

    def add_versions(self, versions: list[DocumentVersion]) -> None:
        """
        Add a document versions to an inserted file
        """
        for version in versions:
            file = self._documents_map[version.document_id]
            file.version = version

    def add_contacts(self, contacts: list[Contact]) -> None:
        """
        Add contacts to inserted files.
        """
        self.contacts.extend(contacts)

    def add_antivirus_checks(self) -> None:
        self.antivirus_checks.extend(
            DocumentAntivirusCheck(
                document_id=inserted_document.document_id,
                document_version_id=inserted_document.version_id,
                status=AntivirusCheckStatus.checking,
            )
            for inserted_document in self.docs
        )


@dataclass
class InsertedDocument:
    """
    Uploaded document with all related entities
    """

    document: Document
    comment: Comment | None = None
    version: DocumentVersion | None = None

    @property
    def document_id(self) -> str:
        return self.document.id

    @property
    def version_id(self) -> str | None:
        return self.version.id if self.version else None


@dataclass
class UploadDocumentRecipientsLegacyOptions:
    """
    Options extracted from the "recipients" field of the request body and mapped to legacy options
    format to keep backward compatibility.
    """

    # Legacy attributes
    is_internal: bool
    is_multilateral: bool
    expected_owner_signatures: int
    expected_recipient_signatures: int
    first_sign_by: FirstSignBy
    recipient_edrpou: str | None
    recipient_emails: list[str] | None
    hide_recipient_emails: bool
    flows: list[CreateFlowCtx]

    # Modern attributes
    recipients_ctx: UpdateRecipientsCtx

    def to_legacy_dict(self) -> DataDict:
        """
        Create dict with legacy attributes
        """
        return {
            'is_internal': self.is_internal,
            'is_multilateral': self.is_multilateral,
            'expected_owner_signatures': self.expected_owner_signatures,
            'expected_recipient_signatures': self.expected_recipient_signatures,
            'first_sign_by': self.first_sign_by.value,
            'recipient_edrpou': self.recipient_edrpou,
            'recipient_emails': self.recipient_emails,
            # always None, because it's deprecated and "recipient_emails" should be used instead
            'email_recipient': None,
            'hide_recipient_emails': self.hide_recipient_emails,
            'flows': [flow.to_upload_dict() for flow in self.flows] if self.flows else None,
        }
