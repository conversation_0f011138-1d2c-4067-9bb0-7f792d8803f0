"""Add enable_2fa_for_internal_users for db config

Revision ID: c7333544f5a4
Revises: 75dbc9fe4563
Create Date: 2020-04-07 17:08:35.197828


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c7333544f5a4'
down_revision = '75dbc9fe4563'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        INSERT INTO companies_config
        SELECT
            id AS company_id,
            '{}' AS config,
            '{"enable_2fa_for_internal_users": true}' AS admin_config
        FROM companies
        WHERE companies.edrpou in ('22859846', '34716896', '41231992', '03358819')
        ON CONFLICT (company_id)
        DO UPDATE SET
            config = companies_config.config || EXCLUDED.config,
            admin_config = companies_config.admin_config || EXCLUDED.admin_config;
        """
    )


def downgrade():
    pass
