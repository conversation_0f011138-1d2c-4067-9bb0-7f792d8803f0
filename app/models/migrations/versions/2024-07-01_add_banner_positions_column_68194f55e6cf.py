"""Add banner.positions column

Revision ID: 68194f55e6cf
Revises: 6f320a11830f
Create Date: 2024-07-01 14:06:24.742853

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.types import SoftEnum
from app.models.migrations.utils import DummyEnum

# revision identifiers, used by Alembic.
revision = '68194f55e6cf'
down_revision = '6f320a11830f'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'banners',
        sa.Column('positions', postgresql.ARRAY(SoftEnum(DummyEnum)), nullable=True),
    )


def downgrade():
    op.drop_column('banners', 'positions')
