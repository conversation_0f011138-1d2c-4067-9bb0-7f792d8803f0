import {
    ICellRendererParams,
    IHeaderParams,
    RowEvent,
} from '@ag-grid-community/core';

import {
    DocumentListDocumentItem,
    DocumentListTableSettingItem,
} from '../types';

export interface SuppressRowClick {
    event: RowEvent<'rowClicked'> & { suppressRowClick: boolean };
}

export type ExtendedRowEvent = RowEvent<'rowClicked'> & SuppressRowClick;

interface DocumnetCellRendererParamsData extends DocumentListDocumentItem {
    dateUpdated?: ISODate;
    name?: string;
    isEditRow?: boolean;
    isDirectory?: boolean;
}

export interface DocumentCellRendererParams {
    data: DocumnetCellRendererParamsData;
}

export interface HeaderCellComponentParams
    extends IHeaderParams,
        DocumentCellRendererParams {
    item: DocumentListTableSettingItem;
}

export interface CustomFieldRendererParams extends ICellRendererParams {
    data: DocumnetCellRendererParamsData;
    item: DocumentListTableSettingItem;
}
