import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useRouteMatch } from 'react-router-dom';

import documentActionCreators from 'components/document/documentActionCreators';
import { getCurrentDocument } from 'selectors/document.selectors';

import { KEP_SIGN_FLOW_QUERY_PARAM } from '../constants';
import { signWithKepFlowActions } from '../signWithKepFlowSlice';

export const useListenForAutoStart = () => {
    const dispatch = useDispatch();
    const location = useLocation();

    const currentDocument = useSelector(getCurrentDocument);
    const isDocumentPage = useRouteMatch('/app/documents/:docId');
    const isTriggered = useRef(false);

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const flag = searchParams.get(KEP_SIGN_FLOW_QUERY_PARAM);

        const clearUrl = () => {
            const url = new URL(window.location.href);
            url.searchParams.delete(KEP_SIGN_FLOW_QUERY_PARAM);
            // HACK: using window.history.replaceState to update URL without triggering navigation
            window.history.replaceState({}, '', url.toString());
        };

        if (flag) {
            if (!isDocumentPage || isTriggered.current) {
                clearUrl();
                return;
            }

            if (!currentDocument.id) {
                return;
            }

            isTriggered.current = true;

            // legal keys are required to be reviewed by admins so they are not available immediately
            // so we need to show a popup that everything went ok but kep is not activated yet
            const isLegal = flag === 'legal';
            dispatch(
                signWithKepFlowActions.setMeta({
                    isWaitingForKepActivation: isLegal,
                }),
            );
            dispatch(
                documentActionCreators.onSign({
                    isKepNew: true,
                }),
            );
        }
    }, [currentDocument, isDocumentPage, dispatch, location]);
};
