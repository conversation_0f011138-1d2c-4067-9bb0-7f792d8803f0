import datetime
import uuid
from collections.abc import Iterable
from typing import assert_never

from api.errors import (
    DoesNotExist,
    Object,
)
from app.auth.types import User
from app.auth.utils import get_company_config
from app.comments.db import delete_comment_for_document_version
from app.document_antivirus import utils as antivirus_utils
from app.document_antivirus.db import delete_antivirus_check_for_document_version
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check
from app.document_versions import db
from app.document_versions.const import (
    LATEST_DOCUMENT_VERSION_MARKER,
    ORIGINAL_DOCUMENT_VERSION_MARKER,
)
from app.document_versions.db import (
    count_document_versions,
    delete_version,
    insert_document_versions,
    select_document_versions_for_deletion_on_send,
    update_document_version,
)
from app.document_versions.enums import DocumentVersionType, VersionReviewFlow
from app.document_versions.types import (
    DocumentVersion,
    InsertDocumentVersionParameters,
    UpdateDocumentVersionSettingsOutput,
    ValidatedVersionDelete,
    ValidatedVersionUpload,
)
from app.documents.db import insert_recipients, select_listings, update_document
from app.documents.types import Document, DocumentWithUploader, UpsertDocumentRecipientDict
from app.events import document_actions
from app.lib import (
    eusign_utils,
    s3_utils,
)
from app.lib.database import DBConnection
from app.lib.enums import Source
from app.lib.helpers import not_none
from app.lib.s3_utils import CopyFile
from app.reviews import utils as reviews_utils
from app.reviews.db import (
    delete_review_for_document_version,
    delete_review_requests_for_document_version,
    select_review_requests_by_version_id,
    update_review_request,
)
from app.reviews.utils import start_reviews_update_transaction, update_review_statuses_in_db
from app.services import services
from worker import topics


def get_document_version_key(version_id: str) -> str:
    return f'document_versions/{version_id}'


async def get_expected_document_version(
    conn: DBConnection,
    *,
    document_id: str | None = None,
    version_id: str | None = None,
) -> DocumentVersion:
    version = await db.select_document_version(
        conn=conn,
        document_id=document_id,
        version_id=version_id,
    )
    if not version:
        raise DoesNotExist(Object.document_version, id=version_id)
    return version


async def convert_document_version_marker(
    conn: DBConnection,
    document_id: str,
    version_marker: str | None,
    company_edrpou: str,
) -> DocumentVersion | None:
    """
    Convert "version" to DocumentVersion object.

    The "version ID" is the DocumentVersion object of a specific document version or the "None"
    for the original document.

    The "version" parameter can be:
     - None - request for original
     - "original" - request for original
     - "latest" - request for the latest version ID available for company
     - str(uuid4()) - request for specific version
    """

    # If the user does not pass anything in the version parameter,
    # then original document will be returned
    if version_marker is None or version_marker == ORIGINAL_DOCUMENT_VERSION_MARKER:
        return await get_first_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=company_edrpou,
        )

    # # If the user requested the latest document, then we are selecting
    # the latest version ID from database.
    if version_marker == LATEST_DOCUMENT_VERSION_MARKER:
        return await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=company_edrpou,
        )

    # Might return None if a version does not exist or is not available for company
    return await db.select_document_version(
        conn=conn,
        document_id=document_id,
        version_id=version_marker,
        company_edrpou=company_edrpou,
    )


async def get_document_version_available_for_document_and_company(
    conn: DBConnection,
    document_ids: Iterable[str],
    company_edrpou: str,
) -> list[DocumentVersion]:
    return await db.select_document_versions(
        conn=conn,
        document_ids=document_ids,
        uploader_edrpou=company_edrpou,
    )


async def get_latest_document_version(
    conn: DBConnection,
    document_id: str,
    *,
    version_type: DocumentVersionType | None = None,
    versions_types: list[DocumentVersionType] | None = None,
    is_sent: bool | None = None,
) -> DocumentVersion | None:
    version = await db.select_latest_document_version(
        conn,
        document_id=document_id,
        version_type=version_type,
        versions_types=versions_types,
        is_sent=is_sent,
    )
    return version


async def get_latest_document_version_available_for_company(
    conn: DBConnection,
    document_id: str,
    company_edrpou: str,
    version_type: DocumentVersionType | None = None,
) -> DocumentVersion | None:
    """
    Get the latest version of document that available for company.
    This version is shared (is_sent=True) or uploaded by company.
    """
    return await db.select_latest_document_version(
        conn=conn,
        document_id=document_id,
        uploader_edrpou=company_edrpou,
        version_type=version_type,
    )


async def get_first_document_version_available_for_company(
    conn: DBConnection,
    document_id: str,
    company_edrpou: str,
    version_type: DocumentVersionType | None = None,
) -> DocumentVersion | None:
    """
    Get the first version of document that available for company.
    This version is shared (is_sent=True) or uploaded by company.
    """
    return await db.select_first_document_version(
        conn=conn,
        document_id=document_id,
        uploader_edrpou=company_edrpou,
        version_type=version_type,
    )


async def get_latest_document_versions_available_for_company(
    conn: DBConnection,
    document_ids: list[str],
    company_edrpou: str,
) -> list[DocumentVersion]:
    """
    Get the latest versions of document that available for company.
    This version is shared (is_sent=True) or uploaded by company.
    """

    versions = await db.select_latest_document_versions(
        conn=conn,
        document_ids=document_ids,
        uploader_edrpou=company_edrpou,
    )
    return versions


async def add_document_content_version(
    *,
    conn: DBConnection,
    document_id: str,
    content: bytes,
    company_edrpou: str,
    company_id: str,
    role_id: str,
    name: str,
    upload_type: DocumentVersionType,
    extension: str,
    date_created: datetime.datetime | None = None,
    version_id: str | None = None,
    is_sent: bool | None = None,
) -> DocumentVersion:
    """
    Update content of document file. Create new version in database, update document
    record and upload new version to S3
    """

    version_id = version_id or str(uuid.uuid4())

    # We upload the content of the new version to S3 before inserting it into the
    # database to be sure that after the transaction is committed, our version will
    # be loaded.
    s3_key = get_document_version_key(version_id=version_id)
    upload_file = s3_utils.UploadFile(
        key=s3_key,
        body=content,
    )
    await s3_utils.upload(upload_file)

    async with conn.begin():
        latest_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=company_edrpou,
        )
        item = await insert_document_versions(
            conn=conn,
            params=InsertDocumentVersionParameters(
                document_id=document_id,
                version_id=version_id,
                type=upload_type,
                date_created=date_created,
                name=name,
                extension=extension,
                role_id=role_id,
                company_edrpou=company_edrpou,
                content_length=len(content),
                content_hash=await eusign_utils.generate_hash_base64(content),
                is_sent=is_sent,
            ),
        )
        if latest_version:
            # is_sent is True only when we create a system version
            # such as converted PDF. In this case we copy reviews
            if item.is_sent:
                await reviews_utils.copy_reviews_between_versions(
                    conn=conn,
                    document_id=document_id,
                    prev_version_id=latest_version.id,
                    new_version_id=item.id,
                )
            else:
                # Copy reviews from the previous version
                # only if the company enabled it
                config = await get_company_config(conn, company_edrpou=company_edrpou)
                match config.version_settings.review_flow:
                    case VersionReviewFlow.continued:
                        await reviews_utils.copy_reviews_between_versions(
                            conn=conn,
                            document_id=document_id,
                            prev_version_id=latest_version.id,
                            new_version_id=item.id,
                            company_edrpou=company_edrpou,
                        )
                    case VersionReviewFlow.restarted:
                        await update_review_statuses_in_db(
                            conn=conn,
                            documents_ids=[document_id],
                            company_edrpou=company_edrpou,
                            company_id=company_id,
                        )
                    case _:
                        assert_never(config.version_settings.review_flow)

    return item


async def flip_document_owner_and_recipient(
    conn: DBConnection,
    document: DocumentWithUploader | Document,
) -> None:
    """
    Use this function to flip document owner and recipient.
    Cases: Send / delete latest version of document
    """
    new_edrpou_owner: str = not_none(document.edrpou_recipient)
    new_edrpou_recipient: str = document.edrpou_owner

    new_expected_owner_signatures: int = document.expected_recipient_signatures
    new_expected_recipient_signatures: int = document.expected_owner_signatures

    await update_document(
        conn=conn,
        data={
            'document_id': document.id,
            'edrpou_owner': new_edrpou_owner,
            'expected_owner_signatures': new_expected_owner_signatures,
            'expected_recipient_signatures': new_expected_recipient_signatures,
            '_unsafe_edrpou_recipient': new_edrpou_recipient,
            # NOTE: pay attention that "email_recipient" is not updated
            # todo: investigate why we don't update email here
        },
    )
    await insert_recipients(
        conn=conn,
        recipient=UpsertDocumentRecipientDict(
            document_id=document.id,
            # this might be dangerous, because it will overwrite all emails that
            # existed previously
            emails=None,
            edrpou=new_edrpou_recipient,
            is_emails_hidden=False,
        ),
    )


async def _delete_version_cleanups(
    conn: DBConnection,
    version_id: str,
    *,
    migrate_to_version_id: str | None = None,
) -> None:
    """
    Deletes version all related entities including file from S3.

    Args:
        version_id: version to delete.
        migrate_to_version_id: Review requests for given version will be migrated to given version.
    """

    from app.comments.utils import schedule_remove_comments_from_index

    # {{ db cleanups
    async with conn.begin():
        await delete_version(conn=conn, version_id=version_id)
        await delete_review_for_document_version(
            conn=conn,
            version_id=version_id,
        )

        if migrate_to_version_id:
            review_requests = await select_review_requests_by_version_id(
                conn=conn,
                document_version_id=version_id,
            )
            for review_request in review_requests:
                await update_review_request(
                    conn=conn,
                    review_request_id=review_request.id,
                    data={'document_version_id': migrate_to_version_id},
                )

        await delete_review_requests_for_document_version(
            conn=conn,
            version_id=version_id,
        )

        comment_ids = await delete_comment_for_document_version(
            conn=conn,
            version_id=version_id,
        )
        if comment_ids:
            await schedule_remove_comments_from_index(comment_ids=comment_ids)

        await delete_antivirus_check_for_document_version(
            conn=conn,
            version_id=version_id,
        )
    # }}


async def delete_latest_version(
    conn: DBConnection,
    ctx: ValidatedVersionDelete,
) -> None:
    """
    Removes the latest document, update review and send notification.
    """

    from app.es.utils import send_to_indexator

    # opens transaction and updates review on exit
    async with start_reviews_update_transaction(
        conn=conn,
        documents_ids=[ctx.document.id],
        user=ctx.user,
        request_source=ctx.request_source,
    ):
        await _delete_version_cleanups(
            conn=conn,
            version_id=ctx.version.id,
        )

        # Get latest version after deletion
        latest_version = await get_latest_document_version(conn, document_id=ctx.document.id)
        if latest_version and latest_version.company_edrpou != ctx.document.edrpou_owner:
            # flip owner and recipient if send by other company
            await flip_document_owner_and_recipient(
                conn,
                document=ctx.document,
            )

        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_version_delete,
                document_id=ctx.document.id,
                document_edrpou_owner=ctx.document.edrpou_owner,
                document_title=ctx.document.title,
                company_id=ctx.user.company_id,
                company_edrpou=ctx.user.company_edrpou,
                email=ctx.user.email,
                role_id=ctx.user.role_id,
            )
        )

    # cleanup s3 with no longer accessed file
    s3_key = get_document_version_key(version_id=ctx.version.id)
    await s3_utils.delete(s3_key)

    await send_to_indexator(services.redis, [ctx.document.id], to_slow_queue=True)


async def send_document_version(
    conn: DBConnection,
    *,
    document_id: str,
    sender_edrpou: str,
    is_first_version: bool,
    user: User | None = None,
) -> None:
    from app.documents.utils import get_document

    latest_version = await get_latest_document_version_available_for_company(
        conn=conn,
        document_id=document_id,
        company_edrpou=sender_edrpou,
    )
    # not versioned document. Skip
    if not latest_version:
        return

    # mark the latest version as sent
    await update_document_version(
        conn=conn,
        version_id=latest_version.id,
        data={
            'is_sent': True,
        },
    )
    document = await get_document(conn, document_id=document_id)
    if not document:
        raise DoesNotExist(Object.document, id=document_id)

    update_indexes = False
    if document.edrpou_recipient == sender_edrpou:
        update_indexes = True
        async with conn.begin():
            await flip_document_owner_and_recipient(
                conn,
                document=document,
            )

    # {{ delete all previous versions
    versions = await select_document_versions_for_deletion_on_send(
        conn,
        document_id=document_id,
        uploader_edrpou=sender_edrpou,
    )

    versions_for_s3_cleanup = []
    async with conn.begin():
        # latest version is always the last, because versions is ordered by date_created.
        # TODO: Think about moving this logic to worker.
        for version in versions:
            await _delete_version_cleanups(
                conn=conn,
                version_id=version.id,
                migrate_to_version_id=latest_version.id,
            )
            versions_for_s3_cleanup.append(version.id)
            if not update_indexes and version.is_sent:
                update_indexes = True

    # cleanup s3 with no longer accessed file
    for version_id in versions_for_s3_cleanup:
        s3_key = get_document_version_key(version_id=version_id)
        await s3_utils.delete(s3_key)
    # }}

    if update_indexes:
        from app.es.utils import send_to_indexator

        await send_to_indexator(services.redis, [document_id], to_slow_queue=True)

    # send notifications
    if user and not is_first_version:
        await services.kafka.send_record(
            topic=topics.INITIATE_NEW_DOCUMENT_VERSION_NOTIFICATIONS,
            value={
                'publisher_role_id': user.role_id,
                'document_version_id': latest_version.id,
            },
        )


async def is_versioned_document(
    conn: DBConnection,
    *,
    document_id: str,
) -> bool:
    version_count = await get_version_count(
        conn=conn,
        document_id=document_id,
    )
    return version_count > 0


async def get_version_count(
    conn: DBConnection,
    *,
    document_id: str,
    is_sent: bool | None = None,
) -> int:
    version_count = (
        await count_document_versions(
            conn,
            document_ids=[document_id],
            is_sent=is_sent,
        )
    )[document_id]
    return version_count


async def remove_document_version_settings(
    conn: DBConnection,
    *,
    document: Document,
    version: DocumentVersion,
) -> None:
    """
    Set document version to None in all tables related to the document version.

    WARNING: that function works correctly only when a document has only one or zero versions

    WARNING: Remember to delete the version from S3 after the parent database transaction
    is committed.
    """
    from app.comments import utils as comments_utils
    from app.documents.utils import get_document_s3_key

    # Copy content of the document:
    #   - from S3 location "document_versions/{version_id}" for a versioned document
    #   - to S3 location "{document_id}" for a non-versioned document
    source_key = get_document_version_key(version_id=version.id)
    destination_key = get_document_s3_key(document_id=document.id, version_id=None)
    await s3_utils.copy(
        CopyFile(
            source_key=source_key,
            destination_key=destination_key,
        )
    )

    await reviews_utils.set_version_for_review_process(
        conn=conn,
        document_id=document.id,
        version_id=None,
    )

    await comments_utils.set_version_for_comments(
        conn=conn,
        document_id=document.id,
        version_id=None,
    )

    await antivirus_utils.set_version_for_antivirus_check(
        conn=conn,
        document_id=document.id,
        version_id=None,
    )

    await delete_version(conn=conn, version_id=version.id)


async def create_document_version_settings(
    conn: DBConnection,
    *,
    document: Document,
    company_edrpou: str,
    role_id: str,
) -> DocumentVersion:
    """
    Create and set a version for the document that was not versioned before.

    WARNING: that function works correctly only when a document has zero versions
    """
    from api.downloads.utils import download_document_content, get_download_document
    from app.comments import utils as comments_utils

    version_id = str(uuid.uuid4())

    # Copy content of the document:
    #   - from S3 location "{document_id}" for non-versioned document
    #   - to S3 location "document_versions/{version_id}" for a versioned document
    download_document = await get_download_document(conn, document_id=document.id)
    original = await download_document_content(conn, document=download_document, version_id=None)
    await s3_utils.upload(
        item=s3_utils.UploadFile(
            key=get_document_version_key(version_id=version_id),
            body=original,
        ),
    )

    # Create a document version in the database
    content_hash = await eusign_utils.generate_hash_base64(original)
    params = InsertDocumentVersionParameters.prepare_upload_version(
        version_id=version_id,
        document_id=document.id,
        role_id=role_id,
        company_edrpou=company_edrpou,
        content_hash=content_hash,
        content_length=len(original),
        extension=not_none(document.extension),
    )
    version = await insert_document_versions(conn, params=params)

    # Set the version for the document in all related tables
    await reviews_utils.set_version_for_review_process(
        conn=conn,
        document_id=document.id,
        version_id=version.id,
    )

    await comments_utils.set_version_for_comments(
        conn=conn,
        document_id=document.id,
        version_id=version.id,
    )

    await antivirus_utils.set_version_for_antivirus_check(
        conn=conn,
        document_id=document.id,
        version_id=version.id,
    )

    return version


async def update_document_version_settings(
    conn: DBConnection,
    *,
    document: Document,
    user: User,
    is_versioned: bool,
) -> UpdateDocumentVersionSettingsOutput | None:
    """
    Update a document from versioned to non-versioned or vice versa.
    """
    versions = await db.select_document_versions(conn, document_ids=[document.id])
    versions_count = len(versions)

    is_versioned_now = versions_count > 0
    is_versioned_expected = is_versioned

    # The document is already in the desired state, so nothing needs to be done here.
    if is_versioned_expected == is_versioned_now:
        return None

    # That should be validated in validators, here we just in case check it again
    assert versions_count <= 1

    # From versioned to non-versioned document
    if not is_versioned_expected:
        version = versions[0]
        await remove_document_version_settings(
            conn=conn,
            document=document,
            version=version,
        )
        return UpdateDocumentVersionSettingsOutput(
            is_version_deleted=True,
            is_version_created=False,
            # be aware that this object was already deleted from the database,
            # so don't try to find it in the database
            version=version,
        )

    # From non-versioned to versioned document
    if is_versioned_expected:
        # Create a new version with the content of the original document
        version = await create_document_version_settings(
            conn=conn,
            document=document,
            company_edrpou=user.company_edrpou,
            role_id=user.role_id,
        )
        return UpdateDocumentVersionSettingsOutput(
            is_version_deleted=False,
            is_version_created=True,
            version=version,
        )

    raise AssertionError('This code should not be reached')


async def add_document_action_about_new_document_version(document: Document, user: User) -> None:
    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_version_upload,
            document_id=document.id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            email=user.email,
            role_id=user.role_id,
        )
    )


async def reset_date_delivered_on_new_document_version(
    conn: DBConnection,
    document_id: str,
) -> None:
    users_with_access = await select_listings(
        conn=conn,
        document_ids=[document_id],
    )
    # TODO[optimisation]: refactor to not make io in loop
    for access in users_with_access:
        if access.role_id:
            from app.documents.utils import update_documents_date_delivered_job

            await update_documents_date_delivered_job(
                role_id=access.role_id,
                documents_ids=[document_id],
                date_delivered=None,
            )


async def add_new_upload_document_version(
    conn: DBConnection,
    validated: ValidatedVersionUpload,
    user: User,
    source: Source,
) -> DocumentVersion:
    """
    Add a new uploaded by user version to the document
    """
    from app.es.utils import send_to_indexator

    version = await add_document_content_version(
        conn=conn,
        document_id=validated.document.id,
        content=validated.content,
        extension=validated.extension,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        upload_type=DocumentVersionType.new_upload,
        name=f'#{validated.version_count + 1}',
        date_created=validated.date_created,
    )

    # send document to antivirus
    await add_document_antivirus_check(
        conn=conn,
        check=DocumentAntivirusCheck(
            document_id=validated.document.id,
            document_version_id=version.id,
            status=AntivirusCheckStatus.checking,
        ),
    )

    await reset_date_delivered_on_new_document_version(conn, document_id=validated.document.id)

    await send_to_indexator(document_ids=[validated.document.id], to_slow_queue=True)

    await add_document_action_about_new_document_version(document=validated.document, user=user)

    return version


async def add_editor_created_document_version(
    conn: DBConnection,
    validated: ValidatedVersionUpload,
    user: User,
    source: Source,
) -> DocumentVersion:
    """
    Add a new version to the document created by the built-in editor (Collabora)
    """
    from app.es.utils import send_to_indexator

    version = await add_document_content_version(
        conn=conn,
        document_id=validated.document.id,
        content=validated.content,
        extension=validated.extension,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        upload_type=DocumentVersionType.editor_created,
        name=f'#{validated.version_count + 1}',
        date_created=validated.date_created,
    )

    await add_document_antivirus_check(
        conn=conn,
        check=DocumentAntivirusCheck(
            document_id=validated.document.id,
            document_version_id=version.id,
            status=AntivirusCheckStatus.checking,
        ),
    )

    await reset_date_delivered_on_new_document_version(conn, document_id=validated.document.id)

    await send_to_indexator(document_ids=[validated.document.id], to_slow_queue=True)

    # Add document action about document version creation from draft
    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_version_create_from_draft,
            document_id=validated.document.id,
            document_edrpou_owner=validated.document.edrpou_owner,
            document_title=validated.document.title,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            email=user.email,
            role_id=user.role_id,
        )
    )
    return version


async def add_convert_format_document_version(
    conn: DBConnection,
    document: Document,
    latest_version: DocumentVersion,
    content: bytes,
    extension: str,
    user: User,
    source: Source,
) -> None:
    """
    Add a new version to the document after converting an MS Office document to PDF.

    Don't use this function in transaction because it schedules background tasks
    """
    from app.es.utils import send_to_indexator

    new_version = await add_document_content_version(
        conn=conn,
        document_id=document.id,
        content=content,
        extension=extension,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        upload_type=DocumentVersionType.converted_format,
        name=f'{latest_version.name} (PDF)',
        # Converted version becomes available immediately after creation, because we convert
        # to an office document right before signing, which means that both sides already
        # agreed to the version that is being converted
        is_sent=True,
    )

    # Maybe we don't need to check for viruses in converted PDF, because in general, we trust
    # that Gotenberg doesn't return a malicious PDF. But for now, we will check it anyway to
    # have nice status in the document details that the document is checked for viruses.
    await add_document_antivirus_check(
        conn=conn,
        check=DocumentAntivirusCheck(
            document_id=document.id,
            document_version_id=new_version.id,
            status=AntivirusCheckStatus.checking,
        ),
    )

    await send_to_indexator(document_ids=[document.id], to_slow_queue=True)

    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_version_format_convert,
            document_id=document.id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            email=user.email,
            role_id=user.role_id,
        )
    )
