.root {
    position: relative;
    display: flex;
    width: 40px;
    height: 40px;
    align-items: center;
    margin-bottom: 10px;
    margin-left: 5px;
    border-radius: calc(var(--border-radius) / 2);
    transition: background-color 0.3s, width 0.3s, margin-left 0.3s;
}

:global(.vchasno-dark-theme) .root {
    --grey-bg: var(--dark-3-color);
}

:global(.AppSidebar--expanded) .root {
    width: 100%;
    margin-left: 0;
}

.root:hover {
    background-color: var(--grey-bg);
    cursor: pointer;
}

.active {
    background-color: var(--grey-bg);
    color: var(--content-color);
}

.icon {
    display: flex;
    width: 40px;
    height: 40px;
    max-height: 60%;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    color: var(--grey-color);
    transition: width 0.3s, color 0.3s;
}

:global(.AppSidebar--expanded) .icon {
    width: 50px;
}

.big.info {
    width: 25px;
    height: 25px;
}

.iconHighlightRounded {
    animation: pulse 0.8s 0.3s 3 ease-out;
    border-radius: 50%;
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
}

.root:hover .icon,
.active .icon {
    color: var(--content-color);
}

.icon > svg {
    width: 20px;
    height: 20px;
    max-height: 100%;
}

.title {
    display: inline-flex;
    overflow: hidden;
    flex-shrink: 1;
    align-items: center;
    font-size: 14px;
    line-height: 16px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.active .title {
    font-weight: 600;
}

.proLabelBadge {
    margin-left: auto;
}

.proLabelTooltip {
    margin-left: 5px;
    background-color: var(--white-bg);
}

.tooltip {
    z-index: 100;
    display: inline-flex;
    max-width: 230px;
    min-height: 30px;
    box-sizing: border-box;
    align-items: center;
    padding: 4px 20px;
    border: 1px solid var(--content-color);
    background: var(--content-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 2px 0 rgba(28, 54, 72, 0.11);
    color: var(--white-bg);
    font-size: 14px;
    line-height: 22px;
}

.tooltip.menu {
    z-index: 101;
    min-width: 210px;
    box-sizing: border-box;
    padding: 5px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.1);
}

.subMenu {
    display: flex;
    width: 100%;
    flex-direction: column;
    padding: 5px 0;
    margin: 0;
    background-color: var(--white-bg);
    gap: 4px;
    list-style: none;
}

.subItem {
    display: flex;
    min-height: 30px;
    align-items: center;
    padding: 4px 5px 4px 10px;
    border-radius: calc(var(--border-radius) / 2);
    color: var(--content-color);
    font-size: 14px;
    line-height: 16px;

    &:hover,
    &.subItemActive {
        background-color: var(--grey-bg);
        cursor: pointer;
    }

    &.subItemActive {
        font-weight: 600;
    }
}

.subMenuContainer {
    .subItem {
        padding-left: 50px;
    }
}

.newLabel {
    padding: 4px 8px;
    margin-right: 10px;
    margin-left: auto;
    background-color: var(--white-bg);
    border-radius: 4px;
    color: #1a66c8;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 var(--primary-cta-color);
    }
}
