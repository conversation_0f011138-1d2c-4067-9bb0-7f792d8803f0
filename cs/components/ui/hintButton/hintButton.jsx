import React from 'react';

import PropTypes from 'prop-types';

import Icon from '../icon/icon';

// icons
import SvgIdea from './images/idea.svg';

// styles
import css from './hintButton.css';

const HintButton = (props) => {
    return (
        <div className={css.hint} onClick={props.onClick}>
            <div className={css.wrapper}>
                <div className={css.icon}>
                    <Icon glyph={SvgIdea} />
                </div>
                <div className={css.text}>
                    <b>Підказка</b>
                    <div>{props.text}</div>
                </div>
            </div>
        </div>
    );
};

HintButton.propTypes = {
    text: PropTypes.string,
    onClick: PropTypes.func.isRequired,
};

export default HintButton;
