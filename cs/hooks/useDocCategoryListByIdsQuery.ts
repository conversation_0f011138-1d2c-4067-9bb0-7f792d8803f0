import { useQuery } from '@tanstack/react-query';

import {
    CACHE_TIME_MS,
    GET_DOC_CATEGORIES_BY_IDS_QUERY,
} from 'lib/queriesConstants';
import { getDocCategories } from 'services/documentCategories/api';

export const useDocCategoryListByIdsQuery = (ids: Array<string | number>) => {
    return useQuery({
        queryFn: () =>
            getDocCategories({
                ids: ids.map(Number),
            }),
        queryKey: [GET_DOC_CATEGORIES_BY_IDS_QUERY, ids],
        staleTime: CACHE_TIME_MS,
        enabled: ids.length > 0,
        retryOnMount: false,
        keepPreviousData: true,
    });
};
