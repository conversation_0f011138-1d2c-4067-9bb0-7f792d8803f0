"""Add column roles.has_signed_documents

Revision ID: 486273bb36f6
Revises: 76dfcb365ce3
Create Date: 2019-08-27 13:05:57.016770


Author: <PERSON><PERSON><PERSON><PERSON>
"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '486273bb36f6'
down_revision = '76dfcb365ce3'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'roles',
        sa.Column(
            'has_signed_documents',
            sa.<PERSON>(),
            server_default='0',
            nullable=False
        ),
    )


def downgrade():
    op.drop_column('roles', 'has_signed_documents')
