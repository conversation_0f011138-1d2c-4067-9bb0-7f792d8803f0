import { useDispatch } from 'react-redux';

import { useMutation } from '@tanstack/react-query';

import documentsActions from 'components/document/documentActionCreators';
import documentAction from 'components/document/documentActionCreators';
import notificationCenterActionCreators from 'components/notificationCenter/notificationCenterActionCreators';
import { createAnnulmentAct } from 'services/documents/ts/api';
import { closeAnnulmentActPopup } from 'store/annulmentAct';
import { t } from 'ttag';

export const useCreateAnnulmentAct = () => {
    const dispatch = useDispatch();

    return useMutation({
        mutationFn: (params: Parameters<typeof createAnnulmentAct>) => {
            return createAnnulmentAct(...params);
        },
        onSuccess: async (_, [documentID]) => {
            await dispatch(documentsActions.onLoadDocument(documentID));
            await dispatch(closeAnnulmentActPopup());
            dispatch(
                documentAction.onSign({
                    isSignActAnnulment: true,
                }),
            );
        },
        onError: () => {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    text: t`Не вдалося створити акт анулювання`,
                    type: 'text',
                    textType: 'error',
                    autoClose: 5000,
                }),
            );
        },
    });
};
