"""add_delete_requests_notification

Revision ID: c42bfda2ff48
Revises: 45d3c67d7c8e
Create Date: 2022-09-26 14:47:41.902282

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c42bfda2ff48"
down_revision = "45d3c67d7c8e"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "roles",
        sa.Column(
            "can_receive_delete_requests",
            sa.<PERSON>(),
            server_default="1",
            nullable=False,
        ),
    )


def downgrade():
    op.drop_column("roles", "can_receive_delete_requests")
