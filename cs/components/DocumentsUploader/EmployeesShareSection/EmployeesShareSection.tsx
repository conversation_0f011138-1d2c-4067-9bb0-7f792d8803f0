import React from 'react';
import { useFieldArray } from 'react-hook-form';

import { FlexBox, Title } from '@vchasno/ui-kit';

import DocumentAccessToggle from 'components/DocumentAccessToggle';
import { FeatureShow } from 'components/FeatureDisplay';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import { TabComponent } from '../EmployeesSection/types';

import ActorAutoSuggest from '../ActorAutoSuggest';
import EmptyEmployeePlaceholder from '../EmptyEmployeePlaceholder';
import SelectedActorList from '../SelectedActorList';
import { useUploadDocumentsFormContext } from '../useUploadDocumentsFormContext';

interface EmployeesShareSectionProps extends TabComponent {}

const EmployeesShareSection: React.FC<EmployeesShareSectionProps> = ({
    disabled,
    hideEmptyPlaceholder,
}) => {
    const methods = useUploadDocumentsFormContext();
    const actorSigners = useFieldArray({
        control: methods.control,
        name: 'employeesViewers',
        shouldUnregister: false,
    });
    const isInternal = methods.watch('isInternal');
    const employeesViewers = methods.watch('employeesViewers');
    const accessLevel = methods.watch('accessLevel');

    return (
        <FlexBox direction="column" gap={15}>
            <FeatureShow feature="DOCUMENTS_PRIVATE_ACCESS">
                <Title level={4}>{t`Тип доступу до документа`}</Title>
                <DocumentAccessToggle
                    isInternal={isInternal}
                    active={accessLevel}
                    onSetActive={(value) => {
                        methods.setValue('accessLevel', value);

                        if (value === 'private') {
                            // увімкнення приватності документу при завантаженні документу
                            eventTracking.sendToGTMV4({
                                event: 'ec_enable_private_upload',
                            });
                        }

                        if (value === 'extended') {
                            // перемикання із приватності у спільний доступ при завантаженні документу
                            eventTracking.sendToGTMV4({
                                event: 'ec_enable_shared_upload',
                            });
                        }
                    }}
                    disabled={methods.formState.disabled || disabled}
                />
            </FeatureShow>
            <Title level={4}>{t`Хто отримає доступ?`}</Title>

            <ActorAutoSuggest
                disabled={methods.formState.disabled || disabled}
                placeholder={t`Вкажіть email, ім’я співробітника або назву команди`}
                onSelect={actorSigners.append}
                filterOptions={({ id }) =>
                    !employeesViewers.map((item) => item.id).includes(id)
                }
            />

            <SelectedActorList
                disabled={disabled}
                actorFieldArray={actorSigners}
                isInternalDocument={isInternal}
            />

            {employeesViewers.length === 0 && !hideEmptyPlaceholder && (
                <EmptyEmployeePlaceholder />
            )}
        </FlexBox>
    );
};

export default EmployeesShareSection;
