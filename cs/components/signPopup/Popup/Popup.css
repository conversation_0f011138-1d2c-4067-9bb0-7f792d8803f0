.root {
    position: relative;
    z-index: 1;
    width: 500px;
    box-sizing: border-box;
    padding: 78px 40px 48px;
    margin: 40px auto auto;
    background-color: var(--white-bg);
    border-radius: 5px;
    opacity: 0;
    white-space: normal;
}

.root.active {
    opacity: 1;
    transform: scale(1);
    transition-duration: 0.1s;
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.draggableWrapper {
    margin: 40px auto auto;
    cursor: move;
}

.fullContent {
    padding: 0;
}

.fullContent.header {
    margin: 25px 35px;
}

.rounded {
    border-radius: 10px;
}

.closeButton {
    position: absolute;
    z-index: 10;
    top: 23px;
    right: 20px;
    color: #8ba3b7;
}

.rootGrey {
    background-color: var(--grey-bg);
}

.rootLowered {
    margin-top: 150px;
}

.rootCentered {
    margin: auto;
}

.header {
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
    word-break: break-word;
    word-wrap: break-word;
}

.subtitle {
    display: inline-block;
    padding: 0 7px;
    margin-bottom: 20px;
    background: #f1f7fa;
    border-radius: 2px;
    color: var(--grey-color);
    font-size: 11px;
}

.prevStatusBtn {
    position: absolute;
    z-index: 10;
    top: 20px;
    left: 40px;
    display: flex;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    background: var(--grey-bg);
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s;
}

.prevStatusBtn:hover {
    background: var(--hover-bg);
}

.main {
    display: flex;
    min-height: 374px;
    flex-direction: column;
}

.footer {
    padding: 50px 0 0;
    margin: auto 0 0;
}
