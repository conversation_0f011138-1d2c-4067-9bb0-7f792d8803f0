import pytest
from aiohttp import web

from api.errors import AccessDenied, Error
from app.auth.db import (
    delete_tokens,
    select_company_by_id,
    select_token_by,
    update_company_by_id,
)
from app.auth.enums import RoleStatus
from app.auth.schemas import CompanyConfig, DefaultRolePermissionsKey
from app.auth.tables import (
    role_table,
    token_table,
)
from app.auth.types import User, to_auth_user
from app.auth.validators import (
    LoginValidator,
    UpdateCompanyConfigSchema,
    validate_activate_role,
    validate_add_employee,
    validate_add_token,
    validate_delete_token,
    validate_update_company_config,
    validate_upload_counter,
    validate_user_permission,
    validate_verify_phone_2fa,
)
from app.billing.db import update_billing_company_config
from app.billing.enums import CompanyLimit
from app.lib import validators
from app.lib.datetime_utils import local_now
from app.lib.enums import User<PERSON>ole
from app.lib.helpers import to_json
from app.models import select_one
from app.services import services
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_UPLOAD_LEFT_COUNTER,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    cleanup_on_teardown,
    make_mocked_request,
    prepare_app_client,
    prepare_client,
    prepare_company_config,
    prepare_user_data,
)


@pytest.mark.parametrize(
    'data, expected',
    [
        ({'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD}, None),
        (
            {
                'email': TEST_USER_EMAIL,
                'password': TEST_USER_PASSWORD,
                'remember': False,
            },
            False,
        ),
        (
            {
                'email': TEST_USER_EMAIL,
                'password': TEST_USER_PASSWORD,
                'remember': False,
            },
            False,
        ),
    ],
)
def test_login_validator(data, expected):
    valid_data = validators.validate(LoginValidator, data)
    assert valid_data['remember'] is expected


async def test_validate_activate_role(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)

    data = {'role_id': user.role_id, 'user_id': user.id}

    try:
        async with app['db'].acquire() as conn:
            valid_data = await validate_activate_role(conn, data)
            assert valid_data.company_id == user.company_id
            assert valid_data.status == RoleStatus.active
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('role_status', [RoleStatus.pending, RoleStatus.denied, RoleStatus.deleted])
async def test_validate_activate_role_invalid(aiohttp_client, role_status):
    app, _, user = await prepare_client(aiohttp_client, role_status=role_status)

    try:
        async with app['db'].acquire() as conn:
            role = await select_one(conn, (role_table.select()))
            invalid_data = {'role_id': role.id, 'user_id': role.user_id}
            assert role.status == role_status

            with pytest.raises(Error):
                await validate_activate_role(conn, invalid_data)
    finally:
        await cleanup_on_teardown(app)


async def test_validate_add_token(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client, is_admin=True)
    data = {'role_id': user.role_id, 'expire_days': 10}

    async with app['db'].acquire() as conn:
        await delete_tokens(conn, [user.role_id])
        user_role_id, date_expired = await validate_add_token(conn, user, data)
        assert user_role_id == user.role_id
        assert date_expired
        assert date_expired > local_now()


async def test_validate_delete_token(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    try:
        async with app['db'].acquire() as conn:
            token = await select_token_by(conn, token_table.c.role_id == user.role_id)
            data = {'role_id': token.role_id}
            assert token.role_id == await validate_delete_token(conn, user, data)
    finally:
        await cleanup_on_teardown(app)


async def test_validate_delete_token_invalid(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    try:
        async with app['db'].acquire() as conn:
            with pytest.raises(Error) as err:
                await validate_delete_token(conn, user, {'role_id': 'invalid_token'})
            assert err.value.status == 400
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'user_role, permission_kwargs',
    [
        (UserRole.user.value, {'can_view_document': True}),
        (UserRole.admin.value, {'can_view_document': False}),
        (UserRole.admin.value, {'can_view_document': True}),
        (UserRole.user.value, {'can_comment_document': True}),
        (UserRole.admin.value, {'can_comment_document': False}),
        (UserRole.admin.value, {'can_comment_document': True}),
        (UserRole.user.value, {'can_upload_document': True}),
        (UserRole.admin.value, {'can_upload_document': False}),
        (UserRole.admin.value, {'can_upload_document': True}),
        (UserRole.user.value, {'can_download_document': True}),
        (UserRole.admin.value, {'can_download_document': False}),
        (UserRole.admin.value, {'can_download_document': True}),
        (UserRole.user.value, {'can_print_document': True}),
        (UserRole.admin.value, {'can_print_document': False}),
        (UserRole.admin.value, {'can_print_document': True}),
        (UserRole.user.value, {'can_delete_document': True}),
        (UserRole.admin.value, {'can_delete_document': False}),
        (UserRole.admin.value, {'can_delete_document': True}),
        (UserRole.user.value, {'can_sign_and_reject_document_internal': True}),
        (UserRole.user.value, {'can_sign_and_reject_document_external': True}),
        (UserRole.admin.value, {'can_sign_and_reject_document_internal': False}),
        (UserRole.admin.value, {'can_sign_and_reject_document_internal': True}),
        (UserRole.admin.value, {'can_sign_and_reject_document_external': False}),
        (UserRole.admin.value, {'can_sign_and_reject_document_external': True}),
        (UserRole.user.value, {'can_invite_coworkers': True}),
        (UserRole.admin.value, {'can_invite_coworkers': False}),
        (UserRole.admin.value, {'can_invite_coworkers': True}),
        (UserRole.user.value, {'can_edit_company': True}),
        (UserRole.admin.value, {'can_edit_company': False}),
        (UserRole.admin.value, {'can_edit_company': True}),
        (UserRole.user.value, {'can_edit_roles': True}),
        (UserRole.admin.value, {'can_edit_roles': False}),
        (UserRole.admin.value, {'can_edit_roles': True}),
    ],
)
async def test_validate_permission(aiohttp_client, user_role, permission_kwargs):
    app, client = await prepare_app_client(aiohttp_client)

    user = to_auth_user(await prepare_user_data(app, user_role=user_role, **permission_kwargs))
    permission = list(permission_kwargs.keys())[0]

    try:
        validate_user_permission(user, {permission})
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'permission, expected',
    [
        ('can_view_document', 403),
        ('can_comment_document', 403),
        ('can_upload_document', 403),
        ('can_download_document', 403),
        ('can_print_document', 403),
        ('can_delete_document', 403),
        ('can_sign_and_reject_document_external', 403),
        ('can_sign_and_reject_document_internal', 403),
        ('can_invite_coworkers', 403),
        ('can_edit_company', 403),
        ('can_edit_roles', 403),
    ],
)
async def test_validate_permission_invalid(aiohttp_client, permission, expected):
    app, client = await prepare_app_client(aiohttp_client)

    user = to_auth_user(
        await prepare_user_data(app, user_role=UserRole.user.value, **{permission: False})
    )

    try:
        with pytest.raises(Exception) as err:
            validate_user_permission(user, permission={permission})
        if isinstance(err.value, Error):
            assert err.value.status == expected
        else:
            assert isinstance(err.value, web.HTTPException) is True
            assert err.value.status_code == expected
    finally:
        await cleanup_on_teardown(app)


async def test_validate_upload_counter(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    company_id = user.company_id

    try:
        async with app['db'].acquire() as conn:
            company = await select_company_by_id(conn, company_id)
            assert getattr(company, 'upload_documents_left', None) == TEST_UPLOAD_LEFT_COUNTER
            await validate_upload_counter(conn, company_id)
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('count_left, count_to_upload', [(0, 1), (-1, 0), (-1, 1), (9, 10)])
async def test_validate_upload_counter_failed(aiohttp_client, count_left, count_to_upload):
    app, _, user = await prepare_client(aiohttp_client)
    company_id = user.company_id

    try:
        async with app['db'].acquire() as conn:
            await update_company_by_id(conn, company_id, {'upload_documents_left': count_left})
            with pytest.raises(Error) as err:
                await validate_upload_counter(conn, company_id, count_to_upload)
                assert err.code == 'upload_overlimit'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'is_unlimited_upload, upload_documents_left, is_allowed',
    [
        (True, 0, True),
        (True, 1, True),
        (True, -1, True),
        (False, 0, False),
        (False, 1, True),
        (False, -1, False),
    ],
)
async def test_validate_upload_counter_unlimited_upload(
    aiohttp_client, is_unlimited_upload, upload_documents_left, is_allowed
):
    app, _, user = await prepare_client(aiohttp_client)
    company_id = user.company_id

    try:
        async with app['db'].acquire() as conn:
            await update_company_by_id(
                conn,
                company_id,
                {
                    'upload_documents_left': upload_documents_left,
                    'is_unlimited_upload': is_unlimited_upload,
                },
            )

            upload = validate_upload_counter(conn, company_id)
            if not is_allowed:
                with pytest.raises(Error) as err:
                    await upload
                data = err.value.to_dict()
                assert data['code'] == 'upload_overlimit'
            else:
                await upload
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('invalid_code', ['', '123', '12312341'])
async def test_verify_2fa_validator_invalid_code(aiohttp_client, invalid_code):
    app, client = await prepare_app_client(aiohttp_client)

    user = await prepare_user_data(app)
    request = make_mocked_request('GET', '/', to_json({'code': invalid_code}).encode(), app=app)

    with pytest.raises(Error):
        await validate_verify_phone_2fa(request, user)


async def test_verify_2fa_validator_invalid_totp(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    user = await prepare_user_data(app)
    request = make_mocked_request('GET', '/', to_json({'code': 'bruh'}).encode(), app=app)

    with pytest.raises(Error):
        await validate_verify_phone_2fa(request, user)


@pytest.mark.parametrize(
    'config',
    [
        # Config with limit
        {CompanyLimit.employees.value: 5},
        # Config with unlimited employees
        {CompanyLimit.employees.value: None},
        # Empty config is equal to unlimited employees
        {},
    ],
)
async def test_validate_add_employee(aiohttp_client, config, test_flags):
    app, _, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    async with services.db.acquire() as conn:
        if config:
            await update_billing_company_config(
                conn=conn,
                company_id=user.company_id,
                config=config,
            )

        limit: int | None = config.get(CompanyLimit.employees.value)
        # Create employees close to limit
        if limit:
            for i in range(limit - 2):
                await prepare_user_data(
                    app=app,
                    email=f'another{i}@vchasno.com.ua',
                    enable_pro_functionality=False,
                )

        # no errors
        await validate_add_employee(conn, user.company_id)

        if limit:
            await prepare_user_data(
                app=app,
                email='<EMAIL>',
                enable_pro_functionality=False,
            )
            with pytest.raises(Error):
                await validate_add_employee(conn, user.company_id)


@pytest.mark.parametrize(
    'user_data, raw_data, company_config, expected',
    [
        pytest.param(
            {
                'can_edit_company': False,
                'is_admin': False,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'msViewerEnabled': False,
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    can_view_document=False,
                    user_role=UserRole.user.value,
                )
            ),
            {
                'exception': AccessDenied,
                'reason': 'Доступ заборонено',
            },
            id='user_without_edit_company_permission',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': True,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'msViewerEnabled': False,
                'default_role_permissions_key': {
                    'user_role': UserRole.admin.value,  # Changing to admin
                    'can_view_document': True,
                    'can_edit_company': True,
                    'can_view_private_document': True,  # Sensitive permission
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,  # Previous was regular user
                    can_view_document=False,
                    can_edit_company=False,
                    can_view_private_document=False,
                )
            ),
            {
                'exception': None,
                'reason': None,
            },
            id='admin_can_update_any_permission',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.admin.value,  # Changing to admin - not allowed
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,  # Previous was regular user
                )
            ),
            {
                'exception': AccessDenied,
                'reason': 'У вас недостатньо прав, щоб змінювати налаштування ролі',
            },
            id='regular_user_cannot_change_to_admin_role',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'can_view_private_document': False,  # User doesn't have this
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.user.value,
                    'can_view_private_document': True,  # Trying to enable
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,
                    can_view_private_document=False,  # Previously disabled
                )
            ),
            {
                'exception': AccessDenied,
                'reason': 'У вас недостатньо прав, щоб увімкнути це налаштування',
            },
            id='regular_user_cannot_enable_permission_they_dont_have',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'can_view_private_document': False,  # User doesn't have this
                'can_edit_roles': False,  # User doesn't have this
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.user.value,
                    'can_view_private_document': True,  # Trying to enable
                    'can_edit_roles': True,  # Trying to enable
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,
                    can_view_private_document=False,  # Previously disabled
                    can_edit_roles=False,  # Previously disabled
                )
            ),
            {
                'exception': AccessDenied,
                'reason': 'У вас недостатньо прав, щоб увімкнути це налаштування',
            },
            id='regular_user_cannot_enable_multiple_permissions_they_dont_have',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'can_upload_document': True,
                'can_comment_document': True,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.user.value,
                    'can_upload_document': True,  # User has this
                    'can_comment_document': True,  # User has this
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,
                    can_upload_document=False,  # Previously disabled
                    can_comment_document=False,  # Previously disabled
                )
            ),
            {
                'exception': None,
                'reason': None,
            },
            id='regular_user_can_enable_permissions_they_have',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'can_view_private_document': False,  # User doesn't have this
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.user.value,
                    'can_view_private_document': False,  # Trying to disable (should be allowed)
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,
                    can_view_private_document=True,  # Previously enabled
                )
            ),
            {
                'exception': None,
                'reason': None,
            },
            id='regular_user_can_disable_any_permission',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'can_view_private_document': False,  # User doesn't have this
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'default_role_permissions_key': {
                    'user_role': UserRole.user.value,
                    'can_view_private_document': False,  # Not changing (remains disabled)
                },
            },
            CompanyConfig(
                default_role_permissions_key=DefaultRolePermissionsKey(
                    user_role=UserRole.user.value,
                    can_view_private_document=False,  # Previously disabled
                )
            ),
            {
                'exception': None,
                'reason': None,
            },
            id='unchanged_permissions_should_be_allowed',
        ),
        pytest.param(
            {
                'can_edit_company': True,
                'is_admin': False,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'msViewerEnabled': False,
                'allow_unregistered_document_view': False,
                'render_review_in_interface': False,
            },
            CompanyConfig(),  # Default config
            {
                'exception': None,
                'reason': None,
            },
            id='update_non_permission_fields',
        ),
    ],
)
async def test_validate_update_company_config(
    aiohttp_client,
    user_data: dict,
    raw_data: dict,
    company_config: CompanyConfig,
    expected: dict,
):
    app, _, user = await prepare_client(aiohttp_client, **user_data)

    await prepare_company_config(company_edrpou=user_data['company_edrpou'], config=company_config)

    async with services.db.acquire() as conn:
        if expected['exception'] is None:
            # Test case expects success
            result, _ = await validate_update_company_config(
                conn=conn,
                user=User.from_row(user),
                data=raw_data,
            )
            assert isinstance(result, UpdateCompanyConfigSchema)
        else:
            # Test case expects exception
            with pytest.raises(expected['exception']) as e:
                await validate_update_company_config(
                    conn=conn,
                    user=User.from_row(user),
                    data=raw_data,
                )

            exc = e.value
            error_dict = exc.to_dict()
            assert error_dict['reason'] == expected['reason'], error_dict
