import { expect } from 'chai';
import { RateStatus } from 'services/enums';

import { CompanyRate } from '../../../../types/billing';
import { ICompany } from '../../../../types/user';

import { checkIsTrialRateAlreadyActivatesForRate } from '../../utils';

describe('cs/components/DisplayTryTrial/utils.ts', () => {
    describe('#checkIsTrialRateAlreadyActivatesForRate', () => {
        const checkRate = {
            rate: 'free',
            status: 'active',
            startDate: '2025-05-06T15:19:00+00:00',
            endDate: '2026-05-06T23:59:59+00:00',
        } as CompanyRate;
        const makeTrialRateList = (
            startDate: string,
            status: RateStatus = RateStatus.ACTIVE,
        ) => [{ startDate, status }] as ICompany['trialRates'];

        it('should return true if trial was activated in rate time range includes - (seconds and time zones)', () => {
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList('2025-05-06T15:19:00+00:00'),
                    checkRate,
                ),
            ).to.be.true;
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList('2025-05-06T15:20:00+00:00'),
                    checkRate,
                ),
            ).to.be.true;
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList('2026-05-06T23:59:59+00:00'),
                    checkRate,
                ),
            ).to.be.true;
        });
        it('should return false not trial was activated in rate time range includes - (seconds and time zones)', () => {
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList('2025-05-06T15:18:00+00:00'),
                    checkRate,
                ),
            ).to.be.false;
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList('2026-05-07T23:59:59+00:00'),
                    checkRate,
                ),
            ).to.be.false;
        });
        it('should return false if trial was canceled - should ignore such trials', () => {
            expect(
                checkIsTrialRateAlreadyActivatesForRate(
                    makeTrialRateList(
                        '2025-05-06T15:19:00+00:00',
                        RateStatus.CANCELED,
                    ),
                    checkRate,
                ),
            ).to.be.false;
        });
    });
});
