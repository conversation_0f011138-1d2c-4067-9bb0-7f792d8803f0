.exportLinks {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 20px;
    gap: 5px;
}

.button {
    margin-left: 20px;
    white-space: nowrap;
}

.cardsItems {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.cardsItems > div {
    margin: 0;
}

.cardsItems .title {
    margin-bottom: 10px;
}

.cardTitle {
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 28px;
}

.cardContent {
    max-width: 700px;
    margin-top: 22px;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

.cardContentWide {
    max-width: 100%;
}


.settingsNav {
    position: sticky;
    z-index: 9;
    top: 0;
}

.tabs {
    border-top: 1px solid var(--default-border);
    margin-bottom: 30px;
    background-color: var(--white-bg);
}

:global(.vchasno-autumn-theme) .tabs {
    --white-bg: var(--autumn-3-color);
    --link-color: var(--autumn-2-color);
}

:global(.vchasno-spring-theme) .tabs {
    --white-bg: var(--spring-3-color);
    --link-color: var(--spring-2-color);
}

.employeesCount {
    margin-left: 10px;
    color: var(--grey-color);
    font-size: 14px;
}

@media all and (max-width: 768px) {
    .cardTitle {
        margin-bottom: 8px;
        font-size: 16px;
        line-height: 18px;
    }

    .settingsNav {
        position: static;
    }

    .tabs {
        flex-direction: column;
    }

    .tabs :global(.vchasno-ui-tabs__label-item) {
        padding: 0 20px;
    }

    .tabs :global(.vchasno-ui-tabs__label-item)::after {
        position: absolute;
        left: 0;
        width: 3px;
        height: 100%;
        background-color: transparent;
        border-radius: 2px;
    }

    .tabs :global(.vchasno-ui-tabs__label-item.--active)::after {
        background-color: var(--blue-bg);
    }

    .tabs :global(.vchasno-ui-tabs__label-item + .vchasno-ui-tabs__label-item) {
        border-top: 1px solid var(--default-border);
    }

    .tabs :global(.vchasno-ui-tabs__indicator) {
        display: none;
    }
}

@media all and (max-width: 1024px) {
    .settingsNav {
        top: 48px;
    }
}
