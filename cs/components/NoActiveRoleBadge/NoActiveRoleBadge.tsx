import React from 'react';

import { Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useCompanyRolesQuery } from 'hooks/useCompanyRolesQuery';
import { t } from 'ttag';

import css from './NoActiveRoleBadge.css';

export interface NoActiveRoleBadgeProps {
    roleId: string;
    className?: string;
    badgeText?: string | null;
}

const NoActiveRoleBadge: React.FC<NoActiveRoleBadgeProps> = React.memo(
    ({ className, roleId, badgeText = t`неактивний`, children }) => {
        const { data, isFetching, isSuccess } = useCompanyRolesQuery({
            search: '',
        });

        const active =
            isSuccess &&
            data.currentCompanyRoles.find((item) => item.id === roleId);

        if (isFetching || active) {
            return null;
        }

        return (
            <>
                {badgeText && (
                    <Text className={cn(css.root, className)}>{badgeText}</Text>
                )}
                {children}
            </>
        );
    },
);

export default NoActiveRoleBadge;
