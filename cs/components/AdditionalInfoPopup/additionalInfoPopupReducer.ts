import { AnyAction } from 'redux';

import { AdditionalInfoPopupState } from './types';

import actions from './additionalInfoPopupActions';

const initState: AdditionalInfoPopupState = {
    displayStatus: 'hidden',
};

const additionalInfoPopupReducer = (state = initState, action: AnyAction) => {
    switch (action.type) {
        case actions.ADDITIONAL_INFO_POPUP__SET_DISPLAY_STATUS:
            return {
                ...state,
                displayStatus: action.displayStatus,
            };
        default:
            return state;
    }
};

export default additionalInfoPopupReducer;
