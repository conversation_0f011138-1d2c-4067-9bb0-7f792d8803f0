.root {
    --section-offset: 24px;

    padding-top: var(--section-offset);
    padding-bottom: var(--section-offset);
}

.root:not(.isShow):hover {
    cursor: pointer;
}

.topDivider {
    border-top: 1px solid var(--default-border);
}

.bottomDivider {
    border-bottom: 1px solid var(--default-border);
}

.bar {
    display: flex;
    min-height: 30px;
    align-items: center;
    justify-content: space-between;
    border-radius: var(--border-radius);
    user-select: none;
}

.bar:hover {
    cursor: pointer;
}

.infoBox {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 5px;
}

.title {
    display: inline-flex;
    align-items: center;
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
}

.checkedIcon {
    display: inline-flex;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    background-color: #e1eec6;
    border-radius: 50%;
    color: var(--green-color);
}

.checkedIcon > svg {
    width: 10px;
    height: 10px;
}

.subTitle {
    color: var(--grey-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.iconBox {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: var(--content-color);
    transition: transform 0.3s ease-in-out;
}

.isShow .iconBox {
    transform: rotate(180deg);
}

.content {
    display: none;
}

.isShow .content {
    display: block;
}

.isShow .subTitle {
    display: none;
}

.isShow .bar {
    margin-bottom: 32px;
}

@media all and (max-width: 480px) {
    .title {
        font-size: 20px;
    }
}
