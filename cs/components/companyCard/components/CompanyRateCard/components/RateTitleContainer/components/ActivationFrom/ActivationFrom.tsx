import React, { <PERSON> } from 'react';

import cn from 'classnames';
import { t } from 'ttag';

import Icon from '../../../../../../../ui/icon/icon';

import StatusText from '../../../../../../../statusText/statusText';

import CheckSvg from './images/check.svg';

import css from './ActivationFrom.css';

interface ActivationFromProps {
    activationDate: string;
    className?: string;
}

const ActivationFrom: FC<ActivationFromProps> = ({
    className,
    activationDate,
}) => {
    return (
        <div className={cn(css.container, className)}>
            <span
                className={css.description}
            >{`${t`Активація від`} ${activationDate}`}</span>
            <StatusText
                className={css.statusBadge}
                statusColor="Green"
                variant="badge"
            >
                <Icon className={css.checkIcon} glyph={CheckSvg} />
                {t`Оплачено`}
            </StatusText>
        </div>
    );
};

export default ActivationFrom;
