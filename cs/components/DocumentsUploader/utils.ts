import { EXTENSIONS_TO_SKIP_GET_TITLE_FROM_FILENAME } from 'components/DocumentsUploader/constants';
import { UploadApiErrorResponseBody } from 'components/uploader/types';
import { queryClient } from 'lib/queries';
import {
    CACHE_TIME_MS,
    SEARCH_COMPANY_REQUIRED_FIELDS,
} from 'lib/queriesConstants';
import { getAllDocumentRequiredFields } from 'services/documents/api';
import { AllDocumentRequiredFields } from 'services/documents/ts/types';
import { DocumentCategory } from 'services/enums';
import { ApplicationMode } from 'services/enums';
import logger from 'services/logger';
import { t } from 'ttag';
import { IRole } from 'types/user';

import { Actor } from './types';

import { userRoleHasPermission } from '../document/helpers';

import { SIGN_PERMISSION_REASON_MAP } from './constants';

const getFormField = (field: string) => {
    switch (field) {
        // тут наведений не весь список полів, можна розширювати по мірі виявлення випадків
        // бажано додавати unit тест на кожне поле
        case 'reviewers':
            return t`Співробітники (погодження)`;
        case 'signers':
            return t`Співробітники (підписання)`;
        default:
            return field;
    }
};

export const isUploadApiError = (
    error: unknown,
): error is UploadApiErrorResponseBody => {
    return (
        typeof error === 'object' &&
        error !== null &&
        'reason' in error &&
        'code' in error &&
        'details' in error &&
        typeof error.details === 'object'
    );
};

export const getErrorDetailsMessage = (
    error: UploadApiErrorResponseBody,
): string => {
    try {
        const keys = Object.keys(error.details);

        if (keys.length === 0) {
            return '';
        }

        return keys
            .map((key) => {
                return `${getFormField(key)}: ${error.details[key]}`;
            })
            .join(', ');
    } catch (_) {
        return '';
    }
};

export const getFileNameExtension = (fileName: string) => {
    return fileName.split('.').reverse()[0].toLowerCase();
};

export const allowGetFilename = (fileName: string) =>
    !EXTENSIONS_TO_SKIP_GET_TITLE_FROM_FILENAME.includes(
        getFileNameExtension(fileName),
    );

export const composeTitleFromFileName = (fileName: string) => {
    return fileName.slice(0, -getFileNameExtension(fileName).length - 1);
};

export const findActorByRole = (actorList: Actor[], roleId: string) => {
    return actorList.find((signer) => {
        if (signer.type === 'role') {
            return signer.id === roleId;
        }

        if (signer.type === 'group') {
            return signer.source.members.some(
                (member) => member.role.id === roleId,
            );
        }

        return false;
    });
};

export const canCurrentUserSignAfterUpload = (
    employeesSigners: Actor[],
    roleId: string,
    isOrdered: boolean,
) => {
    if (employeesSigners.length === 0) {
        return true;
    }

    //signersList contain currentUser
    const currentSigner = findActorByRole(employeesSigners, roleId);

    if (!currentSigner) {
        return false;
    }

    // signing flow is unordered
    if (!isOrdered) {
        return true;
    }

    // signing flow is ordered
    if (currentSigner.type === 'role') {
        return employeesSigners[0].id === currentSigner.id;
    }

    if (
        currentSigner.type === 'group' &&
        employeesSigners[0].type === 'group'
    ) {
        return employeesSigners[0].source.members.some(
            (item) => item.role.id === roleId,
        );
    }

    return false;
};

type RequiredFields = Pick<
    AllDocumentRequiredFields,
    | 'isDateRequired'
    | 'isAmountRequired'
    | 'isNumberRequired'
    | 'isTypeRequired'
>;

export const mapFieldsRulesToRequiredFields = (
    fieldsRules: AllDocumentRequiredFields[],
): RequiredFields => {
    return {
        isDateRequired: fieldsRules.some((item) => item.isDateRequired),
        isAmountRequired: fieldsRules.some((item) => item.isAmountRequired),
        isNumberRequired: fieldsRules.some((item) => item.isNumberRequired),
        isTypeRequired: fieldsRules.some((item) => item.isTypeRequired),
    };
};

export const getRequiredFields = (
    fields: AllDocumentRequiredFields[],
    // є проблема в, тому що category може бути як number, так і string, потрібно вручну приводити до string
    category: number | string | null = null,
): RequiredFields => {
    const stringCategory = category ? String(category) : null;

    const fieldsRules = !stringCategory
        ? // якщо категорія документа не вказана, то відфільтровуємо правила у яких -1 або null
          fields.filter(
              (item) =>
                  !item.documentCategory ||
                  item.documentCategory === DocumentCategory.ANY,
          )
        : fields.filter(
              // якщо категорія документа вказана, накладаємо правила для всіх документів (-1) та правила для контретної категорії
              (item) =>
                  item.documentCategory === stringCategory ||
                  item.documentCategory === DocumentCategory.ANY,
          );

    return mapFieldsRulesToRequiredFields(fieldsRules);
};

export const fetchDocumentRequiredFields = async (
    edrpous: string[],
): Promise<AllDocumentRequiredFields[]> => {
    try {
        return await queryClient.fetchQuery(
            [SEARCH_COMPANY_REQUIRED_FIELDS, edrpous],
            () =>
                getAllDocumentRequiredFields({
                    edrpous,
                }),
            {
                staleTime: CACHE_TIME_MS,
            },
        );
    } catch (error) {
        logger.error('Failed to fetch required fields', { edrpous, error });
        return [];
    }
};

export const checkSignPermissionByUserRole = (
    userRole: IRole,
    enableSplitSignPermission: boolean,
    appMode: ApplicationMode,
) => {
    const commonCanSign = userRoleHasPermission(
        userRole,
        'canSignAndRejectDocument',
    );
    const canInternal = userRoleHasPermission(
        userRole,
        'canSignAndRejectDocumentInternal',
    );
    const canExternal = userRoleHasPermission(
        userRole,
        'canSignAndRejectDocumentExternal',
    );

    if (
        appMode === ApplicationMode.SIGN_SESSION ||
        appMode === ApplicationMode.SHARED_DOCUMENT_VIEW
    ) {
        // Якщо це сесія підписання або спільний перегляд документів, не потрібно перевіряти права на підписання та відхилення документів
        return {
            canSignAndRejectExternalDocs: true,
            canSignAndRejectInternalDocs: true,
            internalReason: null,
            externalReason: null,
        };
    }

    // якщо не уввімкнено прапор на розділення перевірок на зовнішні та внутрішні документи,
    // використовуємо загальне право на підписання та відхилення документів

    if (!enableSplitSignPermission) {
        return {
            canSignAndRejectExternalDocs: commonCanSign,
            canSignAndRejectInternalDocs: commonCanSign,
            internalReason: commonCanSign
                ? null
                : SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocument,
            externalReason: commonCanSign
                ? null
                : SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocument,
        };
    }
    if (!canInternal && !canExternal) {
        return {
            canSignAndRejectExternalDocs: false,
            canSignAndRejectInternalDocs: false,
            internalReason: SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocument,
            externalReason: SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocument,
        };
    }

    return {
        canSignAndRejectExternalDocs: canExternal,
        canSignAndRejectInternalDocs: canInternal,
        internalReason: canInternal
            ? null
            : SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocumentInternal,
        externalReason: canExternal
            ? null
            : SIGN_PERMISSION_REASON_MAP.canSignAndRejectDocumentExternal,
    };
};
