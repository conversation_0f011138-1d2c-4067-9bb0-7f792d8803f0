import sqlalchemy as sa

from app.events.tables import metadata
from app.events.user_actions import types
from app.models import columns

# WARNING: If you want to create an index for an existing or new column,
# use the "create_partitioned_table_index" function
user_actions_table = sa.Table(
    'user_actions',
    metadata,
    columns.UUID(primary_key=True),
    columns.UUID('user_id', nullable=True, index=True),
    columns.SoftEnum('action', types.Action, nullable=False, enum_type=int),
    columns.SoftEnum('source', types.Source, nullable=False, enum_type=int),
    columns.Email('email', nullable=False, index=True),
    columns.Phone('phone', nullable=True, index=True),
    columns.SimpleUUID('company_id', nullable=True, index=True),
    columns.JSONObjectLegacy('extra'),
    columns.DateCreated(index=True),
    sa.CheckConstraint(
        sqltext='email IS NOT NULL OR phone IS NOT NULL',
        name='user_actions_email_or_phone_not_null',
    ),
)
