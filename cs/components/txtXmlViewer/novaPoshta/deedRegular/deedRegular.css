.headerTable {
    width: 100%;
    margin-bottom: 1.5cm;
    text-align: left;
}

.headerTable tbody td {
    width: 50%;
    padding-right: 0.25cm;
    font-size: 90%;
    vertical-align: top;
}

@media (min-width: 720px) {
    .headerTable tbody td {
        padding-right: 1.25cm;
    }
}

.headerDate {
    text-align: right;
}

.documentTitle {
    font-size: 110%;
    font-weight: bold;
    text-indent: 0.1cm;
}

.documentTitle + .documentTitle {
    padding-bottom: 0.4cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
}

.documentTrivia,
.documentTriviaIndent,
.documentTriviaBold {
    font-size: 85%;
    line-height: 110%;
    text-align: justify;
}

.documentTriviaIndent {
    text-indent: 0.2cm;
}

.documentTriviaBold {
    font-weight: bold;
}

.documentDetailsTable {
    width: 100%;
    margin-bottom: 0.4cm;
}

.documentDetailsTable td {
    padding-top: 0.1cm;
    padding-bottom: 0.2cm;
    font-size: 80%;
}

.documentDetailsTable .detailName {
    width: 25%;
    padding-right: 1cm;
    text-align: right;
}

.footerTable {
    width: 100%;
    margin-top: 1cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.signature {
    position: absolute;
    width: 20vw;
    height: 20vw;
    font-size: 80%;
    text-align: center;
}

@media (min-width: 21cm) {
    .signature {
        width: 5.25cm;
        height: 5.25cm;
    }
}

.place {
    margin-top: 0.5cm;
    font-size: 85%;
    line-height: 110%;
}
